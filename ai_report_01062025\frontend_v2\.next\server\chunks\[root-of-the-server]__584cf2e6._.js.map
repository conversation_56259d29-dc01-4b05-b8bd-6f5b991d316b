{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth, { NextAuthOptions } from \"next-auth\";\r\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  debug: true, // Enable debug logs\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: \"Credentials\",\r\n      credentials: {\r\n        email: { label: \"Email\", type: \"email\" },\r\n        password: { label: \"Password\", type: \"password\" }\r\n      },\r\n      async authorize(credentials, req) {\r\n        console.log(\"🔑 Starting authentication process...\");\r\n        console.log(\"📧 Email being used:\", credentials?.email);\r\n\r\n        if (!credentials?.email || !credentials?.password) {\r\n          console.error(\"❌ Missing credentials\");\r\n          throw new Error(\"Missing credentials\");\r\n        }\r\n\r\n        try {\r\n          const loginUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/auth/login`;\r\n          console.log(\"🌐 Attempting login at:\", loginUrl);\r\n\r\n          const formData = new URLSearchParams({\r\n            username: credentials.email,\r\n            password: credentials.password,\r\n          });\r\n\r\n          console.log(\"📦 Request payload:\", {\r\n            url: loginUrl,\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n              \"Accept\": \"application/json\",\r\n            },\r\n            body: formData.toString()\r\n          });\r\n\r\n          const response = await fetch(loginUrl, {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n              \"Accept\": \"application/json\",\r\n            },\r\n            body: formData,\r\n          });\r\n\r\n          console.log(\"📥 Login response status:\", response.status);\r\n          const responseText = await response.text();\r\n          console.log(\"📄 Raw response:\", responseText);\r\n\r\n          if (!response.ok) {\r\n            console.error(\"❌ Authentication failed:\", responseText);\r\n            throw new Error(responseText);\r\n          }\r\n\r\n          let authResponse;\r\n          try {\r\n            authResponse = JSON.parse(responseText);\r\n            console.log(\"🔓 Auth response parsed:\", authResponse);\r\n          } catch (e) {\r\n            console.error(\"❌ Failed to parse auth response:\", e);\r\n            throw new Error(\"Invalid response format from server\");\r\n          }\r\n\r\n          if (!authResponse.access_token) {\r\n            console.error(\"❌ No access token in response\");\r\n            throw new Error(\"No access token received\");\r\n          }\r\n\r\n          // Decode JWT to extract role\r\n          let role = 'candidate';\r\n          try {\r\n            const payload = JSON.parse(Buffer.from(authResponse.access_token.split('.')[1], 'base64').toString());\r\n            role = payload.role || 'candidate';\r\n          } catch (e) {\r\n            console.warn('Could not decode JWT for role, defaulting to candidate', e);\r\n          }\r\n\r\n          // For now, return a basic user object with the token and role\r\n          return {\r\n            id: credentials.email,\r\n            email: credentials.email,\r\n            name: credentials.email.split('@')[0],\r\n            access_token: authResponse.access_token,\r\n            role,\r\n          };\r\n\r\n        } catch (error) {\r\n          console.error(\"❌ Authorization error:\", error);\r\n          if (error instanceof Error) {\r\n            try {\r\n              // Try to parse the error message as JSON\r\n              const errorData = JSON.parse(error.message);\r\n              throw new Error(errorData.detail || error.message);\r\n            } catch (e) {\r\n              // If parsing fails, use the original error message\r\n              throw error;\r\n            }\r\n          }\r\n          throw error;\r\n        }\r\n      },\r\n    }),\r\n  ],\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      console.log(\"🔄 JWT Callback - Input:\", { \r\n        token: { ...token, access_token: token.access_token ? '[REDACTED]' : undefined },\r\n        user: user ? { ...user, access_token: '[REDACTED]' } : undefined \r\n      });\r\n\r\n      if (user) {\r\n        // Store the raw token exactly as received from the backend\r\n        token.access_token = user.access_token;\r\n        token.id = user.id;\r\n        token.email = user.email;\r\n        token.name = user.name;\r\n        token.role = user.role;\r\n        \r\n        // Log the first few characters of the token for debugging\r\n        if (user.access_token) {\r\n          console.log(\"Raw token first 20 chars:\", user.access_token.substring(0, 20));\r\n        }\r\n      }\r\n\r\n      console.log(\"🔄 JWT Callback - Output:\", { \r\n        ...token, \r\n        access_token: '[REDACTED]' \r\n      });\r\n      return token;\r\n    },\r\n    async session({ session, token }) {\r\n      console.log(\"🔄 Session Callback - Input:\", {\r\n        session: { ...session, user: { ...session.user, access_token: undefined } },\r\n        token: { ...token, access_token: '[REDACTED]' }\r\n      });\r\n\r\n      if (token) {\r\n        session.user.id = token.id;\r\n        session.user.access_token = token.access_token;\r\n        session.user.email = token.email;\r\n        session.user.name = token.name;\r\n        session.user.role = token.role;\r\n      }\r\n\r\n      console.log(\"🔄 Session Callback - Output:\", {\r\n        ...session,\r\n        user: { ...session.user, access_token: '[REDACTED]' }\r\n      });\r\n      return session;\r\n    },\r\n  },\r\n  pages: {\r\n    signIn: \"/login\",\r\n  },\r\n  session: {\r\n    strategy: \"jwt\",\r\n    maxAge: 24 * 60 * 60, // 24 hours\r\n  },\r\n};\r\n\r\nconst handler = NextAuth(authOptions);\r\n\r\nexport { handler as GET, handler as POST }; "], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,MAAM,cAA+B;IAC1C,OAAO;IACP,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW,EAAE,GAAG;gBAC9B,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,wBAAwB,aAAa;gBAEjD,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,QAAQ,KAAK,CAAC;oBACd,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI;oBACF,MAAM,WAAW,GAAG,6DAAmC,wBAAwB,WAAW,CAAC;oBAC3F,QAAQ,GAAG,CAAC,2BAA2B;oBAEvC,MAAM,WAAW,IAAI,gBAAgB;wBACnC,UAAU,YAAY,KAAK;wBAC3B,UAAU,YAAY,QAAQ;oBAChC;oBAEA,QAAQ,GAAG,CAAC,uBAAuB;wBACjC,KAAK;wBACL,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,UAAU;wBACZ;wBACA,MAAM,SAAS,QAAQ;oBACzB;oBAEA,MAAM,WAAW,MAAM,MAAM,UAAU;wBACrC,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,UAAU;wBACZ;wBACA,MAAM;oBACR;oBAEA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,MAAM;oBACxD,MAAM,eAAe,MAAM,SAAS,IAAI;oBACxC,QAAQ,GAAG,CAAC,oBAAoB;oBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,MAAM,IAAI,MAAM;oBAClB;oBAEA,IAAI;oBACJ,IAAI;wBACF,eAAe,KAAK,KAAK,CAAC;wBAC1B,QAAQ,GAAG,CAAC,4BAA4B;oBAC1C,EAAE,OAAO,GAAG;wBACV,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,MAAM,IAAI,MAAM;oBAClB;oBAEA,IAAI,CAAC,aAAa,YAAY,EAAE;wBAC9B,QAAQ,KAAK,CAAC;wBACd,MAAM,IAAI,MAAM;oBAClB;oBAEA,6BAA6B;oBAC7B,IAAI,OAAO;oBACX,IAAI;wBACF,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,aAAa,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,QAAQ;wBAClG,OAAO,QAAQ,IAAI,IAAI;oBACzB,EAAE,OAAO,GAAG;wBACV,QAAQ,IAAI,CAAC,0DAA0D;oBACzE;oBAEA,8DAA8D;oBAC9D,OAAO;wBACL,IAAI,YAAY,KAAK;wBACrB,OAAO,YAAY,KAAK;wBACxB,MAAM,YAAY,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wBACrC,cAAc,aAAa,YAAY;wBACvC;oBACF;gBAEF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,IAAI,iBAAiB,OAAO;wBAC1B,IAAI;4BACF,yCAAyC;4BACzC,MAAM,YAAY,KAAK,KAAK,CAAC,MAAM,OAAO;4BAC1C,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,MAAM,OAAO;wBACnD,EAAE,OAAO,GAAG;4BACV,mDAAmD;4BACnD,MAAM;wBACR;oBACF;oBACA,MAAM;gBACR;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,QAAQ,GAAG,CAAC,4BAA4B;gBACtC,OAAO;oBAAE,GAAG,KAAK;oBAAE,cAAc,MAAM,YAAY,GAAG,eAAe;gBAAU;gBAC/E,MAAM,OAAO;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAa,IAAI;YACzD;YAEA,IAAI,MAAM;gBACR,2DAA2D;gBAC3D,MAAM,YAAY,GAAG,KAAK,YAAY;gBACtC,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,IAAI,GAAG,KAAK,IAAI;gBAEtB,0DAA0D;gBAC1D,IAAI,KAAK,YAAY,EAAE;oBACrB,QAAQ,GAAG,CAAC,6BAA6B,KAAK,YAAY,CAAC,SAAS,CAAC,GAAG;gBAC1E;YACF;YAEA,QAAQ,GAAG,CAAC,6BAA6B;gBACvC,GAAG,KAAK;gBACR,cAAc;YAChB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,QAAQ,GAAG,CAAC,gCAAgC;gBAC1C,SAAS;oBAAE,GAAG,OAAO;oBAAE,MAAM;wBAAE,GAAG,QAAQ,IAAI;wBAAE,cAAc;oBAAU;gBAAE;gBAC1E,OAAO;oBAAE,GAAG,KAAK;oBAAE,cAAc;gBAAa;YAChD;YAEA,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,YAAY;gBAC9C,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YAEA,QAAQ,GAAG,CAAC,iCAAiC;gBAC3C,GAAG,OAAO;gBACV,MAAM;oBAAE,GAAG,QAAQ,IAAI;oBAAE,cAAc;gBAAa;YACtD;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;AACF;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/app/api/assessment/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { getServerSession } from 'next-auth';\r\nimport { authOptions } from '../auth/[...nextauth]/route';\r\n\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    // Get email from query params\r\n    const url = new URL(request.url);\r\n    const email = url.searchParams.get('email');\r\n\r\n    if (!email) {\r\n      return NextResponse.json(\r\n        { error: 'Email parameter is required' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Get session and token\r\n    const session = await getServerSession(authOptions);\r\n    const accessToken = session?.user?.access_token;\r\n    if (!accessToken) {\r\n      // Return 401 with a special header to indicate auth error\r\n      const response = NextResponse.json(\r\n        { error: 'Unauthorized: No access token', redirectToLogin: true },\r\n        { status: 401 }\r\n      );\r\n      // Add a special header to indicate this is an auth error\r\n      response.headers.set('X-Auth-Required', 'true');\r\n      return response;\r\n    }\r\n\r\n    // Call the backend API endpoint\r\n    const backendUrl = 'http://localhost:8000/api/v1/assessment?force_new=false';\r\n    const response = await fetch(backendUrl, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Accept': 'application/json',\r\n        'Authorization': `Bearer ${accessToken}`\r\n      },\r\n      body: JSON.stringify({ email }),\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      console.error('Backend API error:', errorText);\r\n      return NextResponse.json(\r\n        { error: `Backend API error: ${response.status}` },\r\n        { status: response.status }\r\n      );\r\n    }\r\n    \r\n    // Return the data from the backend\r\n    const assessmentData = await response.json();\r\n    return NextResponse.json(assessmentData);\r\n    \r\n  } catch (error) {\r\n    console.error('API error:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n} "], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,8BAA8B;QAC9B,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,QAAQ,IAAI,YAAY,CAAC,GAAG,CAAC;QAEnC,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,mJAAA,CAAA,cAAW;QAClD,MAAM,cAAc,SAAS,MAAM;QACnC,IAAI,CAAC,aAAa;YAChB,0DAA0D;YAC1D,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAChC;gBAAE,OAAO;gBAAiC,iBAAiB;YAAK,GAChE;gBAAE,QAAQ;YAAI;YAEhB,yDAAyD;YACzD,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;YACxC,OAAO;QACT;QAEA,gCAAgC;QAChC,MAAM,aAAa;QACnB,MAAM,WAAW,MAAM,MAAM,YAAY;YACvC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,UAAU;gBACV,iBAAiB,CAAC,OAAO,EAAE,aAAa;YAC1C;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,mBAAmB,EAAE,SAAS,MAAM,EAAE;YAAC,GACjD;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,mCAAmC;QACnC,MAAM,iBAAiB,MAAM,SAAS,IAAI;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}