{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/digest.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nconst digest = (algorithm, data) => (0, crypto_1.createHash)(algorithm).update(data).digest();\nexports.default = digest;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM,SAAS,CAAC,WAAW,OAAS,CAAC,GAAG,SAAS,UAAU,EAAE,WAAW,MAAM,CAAC,MAAM,MAAM;AAC3F,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/buffer_utils.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.concatKdf = exports.lengthAndInput = exports.uint32be = exports.uint64be = exports.p2s = exports.concat = exports.decoder = exports.encoder = void 0;\nconst digest_js_1 = require(\"../runtime/digest.js\");\nexports.encoder = new TextEncoder();\nexports.decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nfunction concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    buffers.forEach((buffer) => {\n        buf.set(buffer, i);\n        i += buffer.length;\n    });\n    return buf;\n}\nexports.concat = concat;\nfunction p2s(alg, p2sInput) {\n    return concat(exports.encoder.encode(alg), new Uint8Array([0]), p2sInput);\n}\nexports.p2s = p2s;\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nfunction uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexports.uint64be = uint64be;\nfunction uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nexports.uint32be = uint32be;\nfunction lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nexports.lengthAndInput = lengthAndInput;\nasync function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for (let iter = 0; iter < iterations; iter++) {\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await (0, digest_js_1.default)('sha256', buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\nexports.concatKdf = concatKdf;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,QAAQ,cAAc,GAAG,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,GAAG,QAAQ,GAAG,GAAG,QAAQ,MAAM,GAAG,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG,KAAK;AAC3J,MAAM;AACN,QAAQ,OAAO,GAAG,IAAI;AACtB,QAAQ,OAAO,GAAG,IAAI;AACtB,MAAM,YAAY,KAAK;AACvB,SAAS,OAAO,GAAG,OAAO;IACtB,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,GAAK,MAAM,QAAQ;IAC/D,MAAM,MAAM,IAAI,WAAW;IAC3B,IAAI,IAAI;IACR,QAAQ,OAAO,CAAC,CAAC;QACb,IAAI,GAAG,CAAC,QAAQ;QAChB,KAAK,OAAO,MAAM;IACtB;IACA,OAAO;AACX;AACA,QAAQ,MAAM,GAAG;AACjB,SAAS,IAAI,GAAG,EAAE,QAAQ;IACtB,OAAO,OAAO,QAAQ,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,WAAW;QAAC;KAAE,GAAG;AACpE;AACA,QAAQ,GAAG,GAAG;AACd,SAAS,cAAc,GAAG,EAAE,KAAK,EAAE,MAAM;IACrC,IAAI,QAAQ,KAAK,SAAS,WAAW;QACjC,MAAM,IAAI,WAAW,CAAC,0BAA0B,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO;IACxF;IACA,IAAI,GAAG,CAAC;QAAC,UAAU;QAAI,UAAU;QAAI,UAAU;QAAG,QAAQ;KAAK,EAAE;AACrE;AACA,SAAS,SAAS,KAAK;IACnB,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;IAChC,MAAM,MAAM,QAAQ;IACpB,MAAM,MAAM,IAAI,WAAW;IAC3B,cAAc,KAAK,MAAM;IACzB,cAAc,KAAK,KAAK;IACxB,OAAO;AACX;AACA,QAAQ,QAAQ,GAAG;AACnB,SAAS,SAAS,KAAK;IACnB,MAAM,MAAM,IAAI,WAAW;IAC3B,cAAc,KAAK;IACnB,OAAO;AACX;AACA,QAAQ,QAAQ,GAAG;AACnB,SAAS,eAAe,KAAK;IACzB,OAAO,OAAO,SAAS,MAAM,MAAM,GAAG;AAC1C;AACA,QAAQ,cAAc,GAAG;AACzB,eAAe,UAAU,MAAM,EAAE,IAAI,EAAE,KAAK;IACxC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI;IAC3C,MAAM,MAAM,IAAI,WAAW,aAAa;IACxC,IAAK,IAAI,OAAO,GAAG,OAAO,YAAY,OAAQ;QAC1C,MAAM,MAAM,IAAI,WAAW,IAAI,OAAO,MAAM,GAAG,MAAM,MAAM;QAC3D,IAAI,GAAG,CAAC,SAAS,OAAO;QACxB,IAAI,GAAG,CAAC,QAAQ;QAChB,IAAI,GAAG,CAAC,OAAO,IAAI,OAAO,MAAM;QAChC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,YAAY,OAAO,EAAE,UAAU,MAAM,OAAO;IAClE;IACA,OAAO,IAAI,KAAK,CAAC,GAAG,QAAQ;AAChC;AACA,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/base64url.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decode = exports.encode = exports.encodeBase64 = exports.decodeBase64 = void 0;\nconst buffer_1 = require(\"buffer\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nlet encode;\nfunction normalize(input) {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = buffer_utils_js_1.decoder.decode(encoded);\n    }\n    return encoded;\n}\nif (buffer_1.Buffer.isEncoding('base64url')) {\n    exports.encode = encode = (input) => buffer_1.Buffer.from(input).toString('base64url');\n}\nelse {\n    exports.encode = encode = (input) => buffer_1.Buffer.from(input).toString('base64').replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\nconst decodeBase64 = (input) => buffer_1.Buffer.from(input, 'base64');\nexports.decodeBase64 = decodeBase64;\nconst encodeBase64 = (input) => buffer_1.Buffer.from(input).toString('base64');\nexports.encodeBase64 = encodeBase64;\nconst decode = (input) => buffer_1.Buffer.from(normalize(input), 'base64');\nexports.decode = decode;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAG,QAAQ,YAAY,GAAG,QAAQ,YAAY,GAAG,KAAK;AACrF,MAAM;AACN,MAAM;AACN,IAAI;AACJ,SAAS,UAAU,KAAK;IACpB,IAAI,UAAU;IACd,IAAI,mBAAmB,YAAY;QAC/B,UAAU,kBAAkB,OAAO,CAAC,MAAM,CAAC;IAC/C;IACA,OAAO;AACX;AACA,IAAI,SAAS,MAAM,CAAC,UAAU,CAAC,cAAc;IACzC,QAAQ,MAAM,GAAG,SAAS,CAAC,QAAU,SAAS,MAAM,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC;AAC9E,OACK;IACD,QAAQ,MAAM,GAAG,SAAS,CAAC,QAAU,SAAS,MAAM,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC,UAAU,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;AAC7I;AACA,MAAM,eAAe,CAAC,QAAU,SAAS,MAAM,CAAC,IAAI,CAAC,OAAO;AAC5D,QAAQ,YAAY,GAAG;AACvB,MAAM,eAAe,CAAC,QAAU,SAAS,MAAM,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC;AACrE,QAAQ,YAAY,GAAG;AACvB,MAAM,SAAS,CAAC,QAAU,SAAS,MAAM,CAAC,IAAI,CAAC,UAAU,QAAQ;AACjE,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/util/errors.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.JWSSignatureVerificationFailed = exports.JWKSTimeout = exports.JWKSMultipleMatchingKeys = exports.JWKSNoMatchingKey = exports.JWKSInvalid = exports.JWKInvalid = exports.JWTInvalid = exports.JWSInvalid = exports.JWEInvalid = exports.JWEDecompressionFailed = exports.JWEDecryptionFailed = exports.JOSENotSupported = exports.JOSEAlgNotAllowed = exports.JWTExpired = exports.JWTClaimValidationFailed = exports.JOSEError = void 0;\nclass JOSEError extends Error {\n    static get code() {\n        return 'ERR_JOSE_GENERIC';\n    }\n    constructor(message) {\n        var _a;\n        super(message);\n        this.code = 'ERR_JOSE_GENERIC';\n        this.name = this.constructor.name;\n        (_a = Error.captureStackTrace) === null || _a === void 0 ? void 0 : _a.call(Error, this, this.constructor);\n    }\n}\nexports.JOSEError = JOSEError;\nclass JWTClaimValidationFailed extends JOSEError {\n    static get code() {\n        return 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    }\n    constructor(message, claim = 'unspecified', reason = 'unspecified') {\n        super(message);\n        this.code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n        this.claim = claim;\n        this.reason = reason;\n    }\n}\nexports.JWTClaimValidationFailed = JWTClaimValidationFailed;\nclass JWTExpired extends JOSEError {\n    static get code() {\n        return 'ERR_JWT_EXPIRED';\n    }\n    constructor(message, claim = 'unspecified', reason = 'unspecified') {\n        super(message);\n        this.code = 'ERR_JWT_EXPIRED';\n        this.claim = claim;\n        this.reason = reason;\n    }\n}\nexports.JWTExpired = JWTExpired;\nclass JOSEAlgNotAllowed extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    }\n    static get code() {\n        return 'ERR_JOSE_ALG_NOT_ALLOWED';\n    }\n}\nexports.JOSEAlgNotAllowed = JOSEAlgNotAllowed;\nclass JOSENotSupported extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JOSE_NOT_SUPPORTED';\n    }\n    static get code() {\n        return 'ERR_JOSE_NOT_SUPPORTED';\n    }\n}\nexports.JOSENotSupported = JOSENotSupported;\nclass JWEDecryptionFailed extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWE_DECRYPTION_FAILED';\n        this.message = 'decryption operation failed';\n    }\n    static get code() {\n        return 'ERR_JWE_DECRYPTION_FAILED';\n    }\n}\nexports.JWEDecryptionFailed = JWEDecryptionFailed;\nclass JWEDecompressionFailed extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWE_DECOMPRESSION_FAILED';\n        this.message = 'decompression operation failed';\n    }\n    static get code() {\n        return 'ERR_JWE_DECOMPRESSION_FAILED';\n    }\n}\nexports.JWEDecompressionFailed = JWEDecompressionFailed;\nclass JWEInvalid extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWE_INVALID';\n    }\n    static get code() {\n        return 'ERR_JWE_INVALID';\n    }\n}\nexports.JWEInvalid = JWEInvalid;\nclass JWSInvalid extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWS_INVALID';\n    }\n    static get code() {\n        return 'ERR_JWS_INVALID';\n    }\n}\nexports.JWSInvalid = JWSInvalid;\nclass JWTInvalid extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWT_INVALID';\n    }\n    static get code() {\n        return 'ERR_JWT_INVALID';\n    }\n}\nexports.JWTInvalid = JWTInvalid;\nclass JWKInvalid extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWK_INVALID';\n    }\n    static get code() {\n        return 'ERR_JWK_INVALID';\n    }\n}\nexports.JWKInvalid = JWKInvalid;\nclass JWKSInvalid extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWKS_INVALID';\n    }\n    static get code() {\n        return 'ERR_JWKS_INVALID';\n    }\n}\nexports.JWKSInvalid = JWKSInvalid;\nclass JWKSNoMatchingKey extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWKS_NO_MATCHING_KEY';\n        this.message = 'no applicable key found in the JSON Web Key Set';\n    }\n    static get code() {\n        return 'ERR_JWKS_NO_MATCHING_KEY';\n    }\n}\nexports.JWKSNoMatchingKey = JWKSNoMatchingKey;\nclass JWKSMultipleMatchingKeys extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n        this.message = 'multiple matching keys found in the JSON Web Key Set';\n    }\n    static get code() {\n        return 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    }\n}\nexports.JWKSMultipleMatchingKeys = JWKSMultipleMatchingKeys;\nSymbol.asyncIterator;\nclass JWKSTimeout extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWKS_TIMEOUT';\n        this.message = 'request timed out';\n    }\n    static get code() {\n        return 'ERR_JWKS_TIMEOUT';\n    }\n}\nexports.JWKSTimeout = JWKSTimeout;\nclass JWSSignatureVerificationFailed extends JOSEError {\n    constructor() {\n        super(...arguments);\n        this.code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n        this.message = 'signature verification failed';\n    }\n    static get code() {\n        return 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    }\n}\nexports.JWSSignatureVerificationFailed = JWSSignatureVerificationFailed;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,8BAA8B,GAAG,QAAQ,WAAW,GAAG,QAAQ,wBAAwB,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,WAAW,GAAG,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,QAAQ,sBAAsB,GAAG,QAAQ,mBAAmB,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,UAAU,GAAG,QAAQ,wBAAwB,GAAG,QAAQ,SAAS,GAAG,KAAK;AAC/a,MAAM,kBAAkB;IACpB,WAAW,OAAO;QACd,OAAO;IACX;IACA,YAAY,OAAO,CAAE;QACjB,IAAI;QACJ,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,CAAC,KAAK,MAAM,iBAAiB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,IAAI,CAAC,WAAW;IAC7G;AACJ;AACA,QAAQ,SAAS,GAAG;AACpB,MAAM,iCAAiC;IACnC,WAAW,OAAO;QACd,OAAO;IACX;IACA,YAAY,OAAO,EAAE,QAAQ,aAAa,EAAE,SAAS,aAAa,CAAE;QAChE,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;IAClB;AACJ;AACA,QAAQ,wBAAwB,GAAG;AACnC,MAAM,mBAAmB;IACrB,WAAW,OAAO;QACd,OAAO;IACX;IACA,YAAY,OAAO,EAAE,QAAQ,aAAa,EAAE,SAAS,aAAa,CAAE;QAChE,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;IAClB;AACJ;AACA,QAAQ,UAAU,GAAG;AACrB,MAAM,0BAA0B;IAC5B,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,iBAAiB,GAAG;AAC5B,MAAM,yBAAyB;IAC3B,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,gBAAgB,GAAG;AAC3B,MAAM,4BAA4B;IAC9B,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,mBAAmB,GAAG;AAC9B,MAAM,+BAA+B;IACjC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,sBAAsB,GAAG;AACjC,MAAM,mBAAmB;IACrB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,UAAU,GAAG;AACrB,MAAM,mBAAmB;IACrB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,UAAU,GAAG;AACrB,MAAM,mBAAmB;IACrB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,UAAU,GAAG;AACrB,MAAM,mBAAmB;IACrB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,UAAU,GAAG;AACrB,MAAM,oBAAoB;IACtB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,WAAW,GAAG;AACtB,MAAM,0BAA0B;IAC5B,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,iBAAiB,GAAG;AAC5B,MAAM,iCAAiC;IACnC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,wBAAwB,GAAG;AACnC,OAAO,aAAa;AACpB,MAAM,oBAAoB;IACtB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,WAAW,GAAG;AACtB,MAAM,uCAAuC;IACzC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,WAAW,OAAO;QACd,OAAO;IACX;AACJ;AACA,QAAQ,8BAA8B,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/random.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = void 0;\nvar crypto_1 = require(\"crypto\");\nObject.defineProperty(exports, \"default\", { enumerable: true, get: function () { return crypto_1.randomFillSync; } });\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,KAAK;AACvB,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,WAAW;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,SAAS,cAAc;IAAE;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/iv.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bitLength = void 0;\nconst errors_js_1 = require(\"../util/errors.js\");\nconst random_js_1 = require(\"../runtime/random.js\");\nfunction bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A128GCMKW':\n        case 'A192GCM':\n        case 'A192GCMKW':\n        case 'A256GCM':\n        case 'A256GCMKW':\n            return 96;\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return 128;\n        default:\n            throw new errors_js_1.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\nexports.bitLength = bitLength;\nexports.default = (alg) => (0, random_js_1.default)(new Uint8Array(bitLength(alg) >> 3));\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,MAAM;AACN,SAAS,UAAU,GAAG;IAClB,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,2BAA2B,EAAE,KAAK;IAClF;AACJ;AACA,QAAQ,SAAS,GAAG;AACpB,QAAQ,OAAO,GAAG,CAAC,MAAQ,CAAC,GAAG,YAAY,OAAO,EAAE,IAAI,WAAW,UAAU,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/check_iv_length.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst errors_js_1 = require(\"../util/errors.js\");\nconst iv_js_1 = require(\"./iv.js\");\nconst checkIvLength = (enc, iv) => {\n    if (iv.length << 3 !== (0, iv_js_1.bitLength)(enc)) {\n        throw new errors_js_1.JWEInvalid('Invalid Initialization Vector length');\n    }\n};\nexports.default = checkIvLength;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM,gBAAgB,CAAC,KAAK;IACxB,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,QAAQ,SAAS,EAAE,MAAM;QAChD,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/is_key_object.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nconst util = require(\"util\");\nexports.default = util.types.isKeyObject\n    ? (obj) => util.types.isKeyObject(obj)\n    : (obj) => obj != null && obj instanceof crypto_1.KeyObject;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,QAAQ,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,GAClC,CAAC,MAAQ,KAAK,KAAK,CAAC,WAAW,CAAC,OAChC,CAAC,MAAQ,OAAO,QAAQ,eAAe,SAAS,SAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/check_cek_length.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst errors_js_1 = require(\"../util/errors.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nconst checkCekLength = (enc, cek) => {\n    let expected;\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            expected = parseInt(enc.slice(-3), 10);\n            break;\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            expected = parseInt(enc.slice(1, 4), 10);\n            break;\n        default:\n            throw new errors_js_1.JOSENotSupported(`Content Encryption Algorithm ${enc} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (cek instanceof Uint8Array) {\n        const actual = cek.byteLength << 3;\n        if (actual !== expected) {\n            throw new errors_js_1.JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    if ((0, is_key_object_js_1.default)(cek) && cek.type === 'secret') {\n        const actual = cek.symmetricKeySize << 3;\n        if (actual !== expected) {\n            throw new errors_js_1.JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    throw new TypeError('Invalid Content Encryption Key type');\n};\nexports.default = checkCekLength;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM,iBAAiB,CAAC,KAAK;IACzB,IAAI;IACJ,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,WAAW,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;YACnC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YACrC;QACJ;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,6BAA6B,EAAE,IAAI,2DAA2D,CAAC;IAC/I;IACA,IAAI,eAAe,YAAY;QAC3B,MAAM,SAAS,IAAI,UAAU,IAAI;QACjC,IAAI,WAAW,UAAU;YACrB,MAAM,IAAI,YAAY,UAAU,CAAC,CAAC,gDAAgD,EAAE,SAAS,WAAW,EAAE,OAAO,KAAK,CAAC;QAC3H;QACA;IACJ;IACA,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,QAAQ,IAAI,IAAI,KAAK,UAAU;QAC/D,MAAM,SAAS,IAAI,gBAAgB,IAAI;QACvC,IAAI,WAAW,UAAU;YACrB,MAAM,IAAI,YAAY,UAAU,CAAC,CAAC,gDAAgD,EAAE,SAAS,WAAW,EAAE,OAAO,KAAK,CAAC;QAC3H;QACA;IACJ;IACA,MAAM,IAAI,UAAU;AACxB;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/timing_safe_equal.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nconst timingSafeEqual = crypto_1.timingSafeEqual;\nexports.default = timingSafeEqual;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM,kBAAkB,SAAS,eAAe;AAChD,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/cbc_tag.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nfunction cbcTag(aad, iv, ciphertext, macSize, macKey, keySize) {\n    const macData = (0, buffer_utils_js_1.concat)(aad, iv, ciphertext, (0, buffer_utils_js_1.uint64be)(aad.length << 3));\n    const hmac = (0, crypto_1.createHmac)(`sha${macSize}`, macKey);\n    hmac.update(macData);\n    return hmac.digest().slice(0, keySize >> 3);\n}\nexports.default = cbcTag;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,SAAS,OAAO,GAAG,EAAE,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO;IACzD,MAAM,UAAU,CAAC,GAAG,kBAAkB,MAAM,EAAE,KAAK,IAAI,YAAY,CAAC,GAAG,kBAAkB,QAAQ,EAAE,IAAI,MAAM,IAAI;IACjH,MAAM,OAAO,CAAC,GAAG,SAAS,UAAU,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE;IACvD,KAAK,MAAM,CAAC;IACZ,OAAO,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG,WAAW;AAC7C;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/webcrypto.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isCryptoKey = void 0;\nconst crypto = require(\"crypto\");\nconst util = require(\"util\");\nconst webcrypto = crypto.webcrypto;\nexports.default = webcrypto;\nexports.isCryptoKey = util.types.isCryptoKey\n    ? (key) => util.types.isCryptoKey(key)\n    :\n        (key) => false;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,MAAM;AACN,MAAM;AACN,MAAM,YAAY,OAAO,SAAS;AAClC,QAAQ,OAAO,GAAG;AAClB,QAAQ,WAAW,GAAG,KAAK,KAAK,CAAC,WAAW,GACtC,CAAC,MAAQ,KAAK,KAAK,CAAC,WAAW,CAAC,OAE9B,CAAC,MAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/crypto_key.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.checkEncCryptoKey = exports.checkSigCryptoKey = void 0;\nfunction unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usages) {\n    if (usages.length && !usages.some((expected) => key.usages.includes(expected))) {\n        let msg = 'CryptoKey does not support this operation, its usages must include ';\n        if (usages.length > 2) {\n            const last = usages.pop();\n            msg += `one of ${usages.join(', ')}, or ${last}.`;\n        }\n        else if (usages.length === 2) {\n            msg += `one of ${usages[0]} or ${usages[1]}.`;\n        }\n        else {\n            msg += `${usages[0]}.`;\n        }\n        throw new TypeError(msg);\n    }\n}\nfunction checkSigCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'EdDSA': {\n            if (key.algorithm.name !== 'Ed25519' && key.algorithm.name !== 'Ed448') {\n                throw unusable('Ed25519 or Ed448');\n            }\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\nexports.checkSigCryptoKey = checkSigCryptoKey;\nfunction checkEncCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                case 'X448':\n                    break;\n                default:\n                    throw unusable('ECDH, X25519, or X448');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\nexports.checkEncCryptoKey = checkEncCryptoKey;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,QAAQ,iBAAiB,GAAG,KAAK;AAC7D,SAAS,SAAS,IAAI,EAAE,OAAO,gBAAgB;IAC3C,OAAO,IAAI,UAAU,CAAC,+CAA+C,EAAE,KAAK,SAAS,EAAE,MAAM;AACjG;AACA,SAAS,YAAY,SAAS,EAAE,IAAI;IAChC,OAAO,UAAU,IAAI,KAAK;AAC9B;AACA,SAAS,cAAc,IAAI;IACvB,OAAO,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;AACxC;AACA,SAAS,cAAc,GAAG;IACtB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,MAAM;IACxB;AACJ;AACA,SAAS,WAAW,GAAG,EAAE,MAAM;IAC3B,IAAI,OAAO,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,WAAa,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY;QAC5E,IAAI,MAAM;QACV,IAAI,OAAO,MAAM,GAAG,GAAG;YACnB,MAAM,OAAO,OAAO,GAAG;YACvB,OAAO,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,MAAM,KAAK,EAAE,KAAK,CAAC,CAAC;QACrD,OACK,IAAI,OAAO,MAAM,KAAK,GAAG;YAC1B,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,OACK;YACD,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1B;QACA,MAAM,IAAI,UAAU;IACxB;AACJ;AACA,SAAS,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM;IAC1C,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,SAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,sBAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;YAAS;gBACV,IAAI,IAAI,SAAS,CAAC,IAAI,KAAK,aAAa,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS;oBACpE,MAAM,SAAS;gBACnB;gBACA;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,UAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,cAAc;gBAC/B,MAAM,SAAS,IAAI,SAAS,CAAC,UAAU;gBACvC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,WAAW,KAAK;AACpB;AACA,QAAQ,iBAAiB,GAAG;AAC5B,SAAS,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM;IAC1C,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YAAW;gBACZ,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;gBAC3C,MAAM,SAAS,IAAI,SAAS,CAAC,MAAM;gBACnC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,WAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;gBAC3C,MAAM,SAAS,IAAI,SAAS,CAAC,MAAM;gBACnC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA,KAAK;YAAQ;gBACT,OAAQ,IAAI,SAAS,CAAC,IAAI;oBACtB,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD;oBACJ;wBACI,MAAM,SAAS;gBACvB;gBACA;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,WAC5B,MAAM,SAAS;YACnB;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACjB,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,aAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI,OAAO;gBAC/C,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,WAAW,KAAK;AACpB;AACA,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/invalid_key_input.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.withAlg = void 0;\nfunction message(msg, actual, ...types) {\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor && actual.constructor.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexports.default = (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nfunction withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\nexports.withAlg = withAlg;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,KAAK;AACvB,SAAS,QAAQ,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK;IAClC,IAAI,MAAM,MAAM,GAAG,GAAG;QAClB,MAAM,OAAO,MAAM,GAAG;QACtB,OAAO,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,MAAM,KAAK,EAAE,KAAK,CAAC,CAAC;IACzD,OACK,IAAI,MAAM,MAAM,KAAK,GAAG;QACzB,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACpD,OACK;QACD,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACjC;IACA,IAAI,UAAU,MAAM;QAChB,OAAO,CAAC,UAAU,EAAE,QAAQ;IAChC,OACK,IAAI,OAAO,WAAW,cAAc,OAAO,IAAI,EAAE;QAClD,OAAO,CAAC,mBAAmB,EAAE,OAAO,IAAI,EAAE;IAC9C,OACK,IAAI,OAAO,WAAW,YAAY,UAAU,MAAM;QACnD,IAAI,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,IAAI,EAAE;YAC/C,OAAO,CAAC,yBAAyB,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE;QAChE;IACJ;IACA,OAAO;AACX;AACA,QAAQ,OAAO,GAAG,CAAC,QAAQ,GAAG;IAC1B,OAAO,QAAQ,gBAAgB,WAAW;AAC9C;AACA,SAAS,QAAQ,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK;IAClC,OAAO,QAAQ,CAAC,YAAY,EAAE,IAAI,mBAAmB,CAAC,EAAE,WAAW;AACvE;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/ciphers.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nlet ciphers;\nexports.default = (algorithm) => {\n    ciphers || (ciphers = new Set((0, crypto_1.getCiphers)()));\n    return ciphers.has(algorithm);\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,IAAI;AACJ,QAAQ,OAAO,GAAG,CAAC;IACf,WAAW,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,SAAS,UAAU,IAAI;IACzD,OAAO,QAAQ,GAAG,CAAC;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/is_key_like.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.types = void 0;\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nexports.default = (key) => (0, is_key_object_js_1.default)(key) || (0, webcrypto_js_1.isCryptoKey)(key);\nconst types = ['KeyObject'];\nexports.types = types;\nif (globalThis.CryptoKey || (webcrypto_js_1.default === null || webcrypto_js_1.default === void 0 ? void 0 : webcrypto_js_1.default.CryptoKey)) {\n    types.push('CryptoKey');\n}\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,KAAK,GAAG,KAAK;AACrB,MAAM;AACN,MAAM;AACN,QAAQ,OAAO,GAAG,CAAC,MAAQ,CAAC,GAAG,mBAAmB,OAAO,EAAE,QAAQ,CAAC,GAAG,eAAe,WAAW,EAAE;AACnG,MAAM,QAAQ;IAAC;CAAY;AAC3B,QAAQ,KAAK,GAAG;AAChB,IAAI,WAAW,SAAS,IAAI,CAAC,eAAe,OAAO,KAAK,QAAQ,eAAe,OAAO,KAAK,KAAK,IAAI,KAAK,IAAI,eAAe,OAAO,CAAC,SAAS,GAAG;IAC5I,MAAM,IAAI,CAAC;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/decrypt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nconst check_iv_length_js_1 = require(\"../lib/check_iv_length.js\");\nconst check_cek_length_js_1 = require(\"./check_cek_length.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst timing_safe_equal_js_1 = require(\"./timing_safe_equal.js\");\nconst cbc_tag_js_1 = require(\"./cbc_tag.js\");\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst crypto_key_js_1 = require(\"../lib/crypto_key.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nconst invalid_key_input_js_1 = require(\"../lib/invalid_key_input.js\");\nconst ciphers_js_1 = require(\"./ciphers.js\");\nconst is_key_like_js_1 = require(\"./is_key_like.js\");\nfunction cbcDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if ((0, is_key_object_js_1.default)(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const macSize = parseInt(enc.slice(-3), 10);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!(0, ciphers_js_1.default)(algorithm)) {\n        throw new errors_js_1.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const expectedTag = (0, cbc_tag_js_1.default)(aad, iv, ciphertext, macSize, macKey, keySize);\n    let macCheckPassed;\n    try {\n        macCheckPassed = (0, timing_safe_equal_js_1.default)(tag, expectedTag);\n    }\n    catch {\n    }\n    if (!macCheckPassed) {\n        throw new errors_js_1.JWEDecryptionFailed();\n    }\n    let plaintext;\n    try {\n        const decipher = (0, crypto_1.createDecipheriv)(algorithm, encKey, iv);\n        plaintext = (0, buffer_utils_js_1.concat)(decipher.update(ciphertext), decipher.final());\n    }\n    catch {\n    }\n    if (!plaintext) {\n        throw new errors_js_1.JWEDecryptionFailed();\n    }\n    return plaintext;\n}\nfunction gcmDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!(0, ciphers_js_1.default)(algorithm)) {\n        throw new errors_js_1.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    try {\n        const decipher = (0, crypto_1.createDecipheriv)(algorithm, cek, iv, { authTagLength: 16 });\n        decipher.setAuthTag(tag);\n        if (aad.byteLength) {\n            decipher.setAAD(aad, { plaintextLength: ciphertext.length });\n        }\n        const plaintext = decipher.update(ciphertext);\n        decipher.final();\n        return plaintext;\n    }\n    catch {\n        throw new errors_js_1.JWEDecryptionFailed();\n    }\n}\nconst decrypt = (enc, cek, ciphertext, iv, tag, aad) => {\n    let key;\n    if ((0, webcrypto_js_1.isCryptoKey)(cek)) {\n        (0, crypto_key_js_1.checkEncCryptoKey)(cek, enc, 'decrypt');\n        key = crypto_1.KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || (0, is_key_object_js_1.default)(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError((0, invalid_key_input_js_1.default)(cek, ...is_key_like_js_1.types, 'Uint8Array'));\n    }\n    (0, check_cek_length_js_1.default)(enc, key);\n    (0, check_iv_length_js_1.default)(enc, iv);\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcDecrypt(enc, key, ciphertext, iv, tag, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmDecrypt(enc, key, ciphertext, iv, tag, aad);\n        default:\n            throw new errors_js_1.JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\nexports.default = decrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,WAAW,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG;IAClD,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IAC1C,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QACtC,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,SAAS,IAAI,QAAQ,CAAC,WAAW;IACvC,MAAM,SAAS,IAAI,QAAQ,CAAC,GAAG,WAAW;IAC1C,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;IACxC,MAAM,YAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC;IACtC,IAAI,CAAC,CAAC,GAAG,aAAa,OAAO,EAAE,YAAY;QACvC,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,4CAA4C,CAAC;IACnG;IACA,MAAM,cAAc,CAAC,GAAG,aAAa,OAAO,EAAE,KAAK,IAAI,YAAY,SAAS,QAAQ;IACpF,IAAI;IACJ,IAAI;QACA,iBAAiB,CAAC,GAAG,uBAAuB,OAAO,EAAE,KAAK;IAC9D,EACA,OAAM,CACN;IACA,IAAI,CAAC,gBAAgB;QACjB,MAAM,IAAI,YAAY,mBAAmB;IAC7C;IACA,IAAI;IACJ,IAAI;QACA,MAAM,WAAW,CAAC,GAAG,SAAS,gBAAgB,EAAE,WAAW,QAAQ;QACnE,YAAY,CAAC,GAAG,kBAAkB,MAAM,EAAE,SAAS,MAAM,CAAC,aAAa,SAAS,KAAK;IACzF,EACA,OAAM,CACN;IACA,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,YAAY,mBAAmB;IAC7C;IACA,OAAO;AACX;AACA,SAAS,WAAW,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG;IAClD,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IAC1C,MAAM,YAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC;IACtC,IAAI,CAAC,CAAC,GAAG,aAAa,OAAO,EAAE,YAAY;QACvC,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,4CAA4C,CAAC;IACnG;IACA,IAAI;QACA,MAAM,WAAW,CAAC,GAAG,SAAS,gBAAgB,EAAE,WAAW,KAAK,IAAI;YAAE,eAAe;QAAG;QACxF,SAAS,UAAU,CAAC;QACpB,IAAI,IAAI,UAAU,EAAE;YAChB,SAAS,MAAM,CAAC,KAAK;gBAAE,iBAAiB,WAAW,MAAM;YAAC;QAC9D;QACA,MAAM,YAAY,SAAS,MAAM,CAAC;QAClC,SAAS,KAAK;QACd,OAAO;IACX,EACA,OAAM;QACF,MAAM,IAAI,YAAY,mBAAmB;IAC7C;AACJ;AACA,MAAM,UAAU,CAAC,KAAK,KAAK,YAAY,IAAI,KAAK;IAC5C,IAAI;IACJ,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,MAAM;QACtC,CAAC,GAAG,gBAAgB,iBAAiB,EAAE,KAAK,KAAK;QACjD,MAAM,SAAS,SAAS,CAAC,IAAI,CAAC;IAClC,OACK,IAAI,eAAe,cAAc,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QACxE,MAAM;IACV,OACK;QACD,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK,EAAE;IAC5F;IACA,CAAC,GAAG,sBAAsB,OAAO,EAAE,KAAK;IACxC,CAAC,GAAG,qBAAqB,OAAO,EAAE,KAAK;IACvC,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,WAAW,KAAK,KAAK,YAAY,IAAI,KAAK;QACrD,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,WAAW,KAAK,KAAK,YAAY,IAAI,KAAK;QACrD;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/zlib.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.deflate = exports.inflate = void 0;\nconst util_1 = require(\"util\");\nconst zlib_1 = require(\"zlib\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst inflateRaw = (0, util_1.promisify)(zlib_1.inflateRaw);\nconst deflateRaw = (0, util_1.promisify)(zlib_1.deflateRaw);\nconst inflate = (input) => inflateRaw(input, { maxOutputLength: 250000 }).catch(() => {\n    throw new errors_js_1.JWEDecompressionFailed();\n});\nexports.inflate = inflate;\nconst deflate = (input) => deflateRaw(input);\nexports.deflate = deflate;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG,KAAK;AACzC,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,aAAa,CAAC,GAAG,OAAO,SAAS,EAAE,OAAO,UAAU;AAC1D,MAAM,aAAa,CAAC,GAAG,OAAO,SAAS,EAAE,OAAO,UAAU;AAC1D,MAAM,UAAU,CAAC,QAAU,WAAW,OAAO;QAAE,iBAAiB;IAAO,GAAG,KAAK,CAAC;QAC5E,MAAM,IAAI,YAAY,sBAAsB;IAChD;AACA,QAAQ,OAAO,GAAG;AAClB,MAAM,UAAU,CAAC,QAAU,WAAW;AACtC,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/is_disjoint.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst isDisjoint = (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\nexports.default = isDisjoint;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM,aAAa,CAAC,GAAG;IACnB,MAAM,UAAU,QAAQ,MAAM,CAAC;IAC/B,IAAI,QAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;QAC9C,OAAO;IACX;IACA,IAAI;IACJ,KAAK,MAAM,UAAU,QAAS;QAC1B,MAAM,aAAa,OAAO,IAAI,CAAC;QAC/B,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,GAAG;YACxB,MAAM,IAAI,IAAI;YACd;QACJ;QACA,KAAK,MAAM,aAAa,WAAY;YAChC,IAAI,IAAI,GAAG,CAAC,YAAY;gBACpB,OAAO;YACX;YACA,IAAI,GAAG,CAAC;QACZ;IACJ;IACA,OAAO;AACX;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/is_object.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nfunction isObject(input) {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n}\nexports.default = isObject;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,SAAS,aAAa,KAAK;IACvB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACA,SAAS,SAAS,KAAK;IACnB,IAAI,CAAC,aAAa,UAAU,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,mBAAmB;QACrF,OAAO;IACX;IACA,IAAI,OAAO,cAAc,CAAC,WAAW,MAAM;QACvC,OAAO;IACX;IACA,IAAI,QAAQ;IACZ,MAAO,OAAO,cAAc,CAAC,WAAW,KAAM;QAC1C,QAAQ,OAAO,cAAc,CAAC;IAClC;IACA,OAAO,OAAO,cAAc,CAAC,WAAW;AAC5C;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/aeskw.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.unwrap = exports.wrap = void 0;\nconst buffer_1 = require(\"buffer\");\nconst crypto_1 = require(\"crypto\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst crypto_key_js_1 = require(\"../lib/crypto_key.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nconst invalid_key_input_js_1 = require(\"../lib/invalid_key_input.js\");\nconst ciphers_js_1 = require(\"./ciphers.js\");\nconst is_key_like_js_1 = require(\"./is_key_like.js\");\nfunction checkKeySize(key, alg) {\n    if (key.symmetricKeySize << 3 !== parseInt(alg.slice(1, 4), 10)) {\n        throw new TypeError(`Invalid key size for alg: ${alg}`);\n    }\n}\nfunction ensureKeyObject(key, alg, usage) {\n    if ((0, is_key_object_js_1.default)(key)) {\n        return key;\n    }\n    if (key instanceof Uint8Array) {\n        return (0, crypto_1.createSecretKey)(key);\n    }\n    if ((0, webcrypto_js_1.isCryptoKey)(key)) {\n        (0, crypto_key_js_1.checkEncCryptoKey)(key, alg, usage);\n        return crypto_1.KeyObject.from(key);\n    }\n    throw new TypeError((0, invalid_key_input_js_1.default)(key, ...is_key_like_js_1.types, 'Uint8Array'));\n}\nconst wrap = (alg, key, cek) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!(0, ciphers_js_1.default)(algorithm)) {\n        throw new errors_js_1.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = (0, crypto_1.createCipheriv)(algorithm, keyObject, buffer_1.Buffer.alloc(8, 0xa6));\n    return (0, buffer_utils_js_1.concat)(cipher.update(cek), cipher.final());\n};\nexports.wrap = wrap;\nconst unwrap = (alg, key, encryptedKey) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!(0, ciphers_js_1.default)(algorithm)) {\n        throw new errors_js_1.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = (0, crypto_1.createDecipheriv)(algorithm, keyObject, buffer_1.Buffer.alloc(8, 0xa6));\n    return (0, buffer_utils_js_1.concat)(cipher.update(encryptedKey), cipher.final());\n};\nexports.unwrap = unwrap;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,MAAM,GAAG,QAAQ,IAAI,GAAG,KAAK;AACrC,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,aAAa,GAAG,EAAE,GAAG;IAC1B,IAAI,IAAI,gBAAgB,IAAI,MAAM,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK;QAC7D,MAAM,IAAI,UAAU,CAAC,0BAA0B,EAAE,KAAK;IAC1D;AACJ;AACA,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IACpC,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QACtC,OAAO;IACX;IACA,IAAI,eAAe,YAAY;QAC3B,OAAO,CAAC,GAAG,SAAS,eAAe,EAAE;IACzC;IACA,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,MAAM;QACtC,CAAC,GAAG,gBAAgB,iBAAiB,EAAE,KAAK,KAAK;QACjD,OAAO,SAAS,SAAS,CAAC,IAAI,CAAC;IACnC;IACA,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK,EAAE;AAC5F;AACA,MAAM,OAAO,CAAC,KAAK,KAAK;IACpB,MAAM,OAAO,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IACvC,MAAM,YAAY,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC;IACnC,IAAI,CAAC,CAAC,GAAG,aAAa,OAAO,EAAE,YAAY;QACvC,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IAClH;IACA,MAAM,YAAY,gBAAgB,KAAK,KAAK;IAC5C,aAAa,WAAW;IACxB,MAAM,SAAS,CAAC,GAAG,SAAS,cAAc,EAAE,WAAW,WAAW,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG;IAC3F,OAAO,CAAC,GAAG,kBAAkB,MAAM,EAAE,OAAO,MAAM,CAAC,MAAM,OAAO,KAAK;AACzE;AACA,QAAQ,IAAI,GAAG;AACf,MAAM,SAAS,CAAC,KAAK,KAAK;IACtB,MAAM,OAAO,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IACvC,MAAM,YAAY,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC;IACnC,IAAI,CAAC,CAAC,GAAG,aAAa,OAAO,EAAE,YAAY;QACvC,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IAClH;IACA,MAAM,YAAY,gBAAgB,KAAK,KAAK;IAC5C,aAAa,WAAW;IACxB,MAAM,SAAS,CAAC,GAAG,SAAS,gBAAgB,EAAE,WAAW,WAAW,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG;IAC7F,OAAO,CAAC,GAAG,kBAAkB,MAAM,EAAE,OAAO,MAAM,CAAC,eAAe,OAAO,KAAK;AAClF;AACA,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/get_named_curve.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setCurve = exports.weakMap = void 0;\nconst buffer_1 = require(\"buffer\");\nconst crypto_1 = require(\"crypto\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nconst invalid_key_input_js_1 = require(\"../lib/invalid_key_input.js\");\nconst is_key_like_js_1 = require(\"./is_key_like.js\");\nconst p256 = buffer_1.Buffer.from([42, 134, 72, 206, 61, 3, 1, 7]);\nconst p384 = buffer_1.Buffer.from([43, 129, 4, 0, 34]);\nconst p521 = buffer_1.Buffer.from([43, 129, 4, 0, 35]);\nconst secp256k1 = buffer_1.Buffer.from([43, 129, 4, 0, 10]);\nexports.weakMap = new WeakMap();\nconst namedCurveToJOSE = (namedCurve) => {\n    switch (namedCurve) {\n        case 'prime256v1':\n            return 'P-256';\n        case 'secp384r1':\n            return 'P-384';\n        case 'secp521r1':\n            return 'P-521';\n        case 'secp256k1':\n            return 'secp256k1';\n        default:\n            throw new errors_js_1.JOSENotSupported('Unsupported key curve for this operation');\n    }\n};\nconst getNamedCurve = (kee, raw) => {\n    var _a;\n    let key;\n    if ((0, webcrypto_js_1.isCryptoKey)(kee)) {\n        key = crypto_1.KeyObject.from(kee);\n    }\n    else if ((0, is_key_object_js_1.default)(kee)) {\n        key = kee;\n    }\n    else {\n        throw new TypeError((0, invalid_key_input_js_1.default)(kee, ...is_key_like_js_1.types));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError('only \"private\" or \"public\" type keys can be used for this operation');\n    }\n    switch (key.asymmetricKeyType) {\n        case 'ed25519':\n        case 'ed448':\n            return `Ed${key.asymmetricKeyType.slice(2)}`;\n        case 'x25519':\n        case 'x448':\n            return `X${key.asymmetricKeyType.slice(1)}`;\n        case 'ec': {\n            if (exports.weakMap.has(key)) {\n                return exports.weakMap.get(key);\n            }\n            let namedCurve = (_a = key.asymmetricKeyDetails) === null || _a === void 0 ? void 0 : _a.namedCurve;\n            if (!namedCurve && key.type === 'private') {\n                namedCurve = getNamedCurve((0, crypto_1.createPublicKey)(key), true);\n            }\n            else if (!namedCurve) {\n                const buf = key.export({ format: 'der', type: 'spki' });\n                const i = buf[1] < 128 ? 14 : 15;\n                const len = buf[i];\n                const curveOid = buf.slice(i + 1, i + 1 + len);\n                if (curveOid.equals(p256)) {\n                    namedCurve = 'prime256v1';\n                }\n                else if (curveOid.equals(p384)) {\n                    namedCurve = 'secp384r1';\n                }\n                else if (curveOid.equals(p521)) {\n                    namedCurve = 'secp521r1';\n                }\n                else if (curveOid.equals(secp256k1)) {\n                    namedCurve = 'secp256k1';\n                }\n                else {\n                    throw new errors_js_1.JOSENotSupported('Unsupported key curve for this operation');\n                }\n            }\n            if (raw)\n                return namedCurve;\n            const curve = namedCurveToJOSE(namedCurve);\n            exports.weakMap.set(key, curve);\n            return curve;\n        }\n        default:\n            throw new TypeError('Invalid asymmetric key type for this operation');\n    }\n};\nfunction setCurve(keyObject, curve) {\n    exports.weakMap.set(keyObject, curve);\n}\nexports.setCurve = setCurve;\nexports.default = getNamedCurve;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG,QAAQ,OAAO,GAAG,KAAK;AAC1C,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,OAAO,SAAS,MAAM,CAAC,IAAI,CAAC;IAAC;IAAI;IAAK;IAAI;IAAK;IAAI;IAAG;IAAG;CAAE;AACjE,MAAM,OAAO,SAAS,MAAM,CAAC,IAAI,CAAC;IAAC;IAAI;IAAK;IAAG;IAAG;CAAG;AACrD,MAAM,OAAO,SAAS,MAAM,CAAC,IAAI,CAAC;IAAC;IAAI;IAAK;IAAG;IAAG;CAAG;AACrD,MAAM,YAAY,SAAS,MAAM,CAAC,IAAI,CAAC;IAAC;IAAI;IAAK;IAAG;IAAG;CAAG;AAC1D,QAAQ,OAAO,GAAG,IAAI;AACtB,MAAM,mBAAmB,CAAC;IACtB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;AACJ;AACA,MAAM,gBAAgB,CAAC,KAAK;IACxB,IAAI;IACJ,IAAI;IACJ,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,MAAM;QACtC,MAAM,SAAS,SAAS,CAAC,IAAI,CAAC;IAClC,OACK,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QAC3C,MAAM;IACV,OACK;QACD,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK;IAC1F;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,MAAM,IAAI,UAAU;IACxB;IACA,OAAQ,IAAI,iBAAiB;QACzB,KAAK;QACL,KAAK;YACD,OAAO,CAAC,EAAE,EAAE,IAAI,iBAAiB,CAAC,KAAK,CAAC,IAAI;QAChD,KAAK;QACL,KAAK;YACD,OAAO,CAAC,CAAC,EAAE,IAAI,iBAAiB,CAAC,KAAK,CAAC,IAAI;QAC/C,KAAK;YAAM;gBACP,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;oBAC1B,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC;gBAC/B;gBACA,IAAI,aAAa,CAAC,KAAK,IAAI,oBAAoB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU;gBACnG,IAAI,CAAC,cAAc,IAAI,IAAI,KAAK,WAAW;oBACvC,aAAa,cAAc,CAAC,GAAG,SAAS,eAAe,EAAE,MAAM;gBACnE,OACK,IAAI,CAAC,YAAY;oBAClB,MAAM,MAAM,IAAI,MAAM,CAAC;wBAAE,QAAQ;wBAAO,MAAM;oBAAO;oBACrD,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,MAAM,KAAK;oBAC9B,MAAM,MAAM,GAAG,CAAC,EAAE;oBAClB,MAAM,WAAW,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI;oBAC1C,IAAI,SAAS,MAAM,CAAC,OAAO;wBACvB,aAAa;oBACjB,OACK,IAAI,SAAS,MAAM,CAAC,OAAO;wBAC5B,aAAa;oBACjB,OACK,IAAI,SAAS,MAAM,CAAC,OAAO;wBAC5B,aAAa;oBACjB,OACK,IAAI,SAAS,MAAM,CAAC,YAAY;wBACjC,aAAa;oBACjB,OACK;wBACD,MAAM,IAAI,YAAY,gBAAgB,CAAC;oBAC3C;gBACJ;gBACA,IAAI,KACA,OAAO;gBACX,MAAM,QAAQ,iBAAiB;gBAC/B,QAAQ,OAAO,CAAC,GAAG,CAAC,KAAK;gBACzB,OAAO;YACX;QACA;YACI,MAAM,IAAI,UAAU;IAC5B;AACJ;AACA,SAAS,SAAS,SAAS,EAAE,KAAK;IAC9B,QAAQ,OAAO,CAAC,GAAG,CAAC,WAAW;AACnC;AACA,QAAQ,QAAQ,GAAG;AACnB,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/ecdhes.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ecdhAllowed = exports.generateEpk = exports.deriveKey = void 0;\nconst crypto_1 = require(\"crypto\");\nconst util_1 = require(\"util\");\nconst get_named_curve_js_1 = require(\"./get_named_curve.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst crypto_key_js_1 = require(\"../lib/crypto_key.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nconst invalid_key_input_js_1 = require(\"../lib/invalid_key_input.js\");\nconst is_key_like_js_1 = require(\"./is_key_like.js\");\nconst generateKeyPair = (0, util_1.promisify)(crypto_1.generateKeyPair);\nasync function deriveKey(publicKee, privateKee, algorithm, keyLength, apu = new Uint8Array(0), apv = new Uint8Array(0)) {\n    let publicKey;\n    if ((0, webcrypto_js_1.isCryptoKey)(publicKee)) {\n        (0, crypto_key_js_1.checkEncCryptoKey)(publicKee, 'ECDH');\n        publicKey = crypto_1.KeyObject.from(publicKee);\n    }\n    else if ((0, is_key_object_js_1.default)(publicKee)) {\n        publicKey = publicKee;\n    }\n    else {\n        throw new TypeError((0, invalid_key_input_js_1.default)(publicKee, ...is_key_like_js_1.types));\n    }\n    let privateKey;\n    if ((0, webcrypto_js_1.isCryptoKey)(privateKee)) {\n        (0, crypto_key_js_1.checkEncCryptoKey)(privateKee, 'ECDH', 'deriveBits');\n        privateKey = crypto_1.KeyObject.from(privateKee);\n    }\n    else if ((0, is_key_object_js_1.default)(privateKee)) {\n        privateKey = privateKee;\n    }\n    else {\n        throw new TypeError((0, invalid_key_input_js_1.default)(privateKee, ...is_key_like_js_1.types));\n    }\n    const value = (0, buffer_utils_js_1.concat)((0, buffer_utils_js_1.lengthAndInput)(buffer_utils_js_1.encoder.encode(algorithm)), (0, buffer_utils_js_1.lengthAndInput)(apu), (0, buffer_utils_js_1.lengthAndInput)(apv), (0, buffer_utils_js_1.uint32be)(keyLength));\n    const sharedSecret = (0, crypto_1.diffieHellman)({ privateKey, publicKey });\n    return (0, buffer_utils_js_1.concatKdf)(sharedSecret, keyLength, value);\n}\nexports.deriveKey = deriveKey;\nasync function generateEpk(kee) {\n    let key;\n    if ((0, webcrypto_js_1.isCryptoKey)(kee)) {\n        key = crypto_1.KeyObject.from(kee);\n    }\n    else if ((0, is_key_object_js_1.default)(kee)) {\n        key = kee;\n    }\n    else {\n        throw new TypeError((0, invalid_key_input_js_1.default)(kee, ...is_key_like_js_1.types));\n    }\n    switch (key.asymmetricKeyType) {\n        case 'x25519':\n            return generateKeyPair('x25519');\n        case 'x448': {\n            return generateKeyPair('x448');\n        }\n        case 'ec': {\n            const namedCurve = (0, get_named_curve_js_1.default)(key);\n            return generateKeyPair('ec', { namedCurve });\n        }\n        default:\n            throw new errors_js_1.JOSENotSupported('Invalid or unsupported EPK');\n    }\n}\nexports.generateEpk = generateEpk;\nconst ecdhAllowed = (key) => ['P-256', 'P-384', 'P-521', 'X25519', 'X448'].includes((0, get_named_curve_js_1.default)(key));\nexports.ecdhAllowed = ecdhAllowed;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,QAAQ,WAAW,GAAG,QAAQ,SAAS,GAAG,KAAK;AACrE,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,kBAAkB,CAAC,GAAG,OAAO,SAAS,EAAE,SAAS,eAAe;AACtE,eAAe,UAAU,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,IAAI,WAAW,EAAE,EAAE,MAAM,IAAI,WAAW,EAAE;IAClH,IAAI;IACJ,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,YAAY;QAC5C,CAAC,GAAG,gBAAgB,iBAAiB,EAAE,WAAW;QAClD,YAAY,SAAS,SAAS,CAAC,IAAI,CAAC;IACxC,OACK,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,YAAY;QACjD,YAAY;IAChB,OACK;QACD,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,cAAc,iBAAiB,KAAK;IAChG;IACA,IAAI;IACJ,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,aAAa;QAC7C,CAAC,GAAG,gBAAgB,iBAAiB,EAAE,YAAY,QAAQ;QAC3D,aAAa,SAAS,SAAS,CAAC,IAAI,CAAC;IACzC,OACK,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa;QAClD,aAAa;IACjB,OACK;QACD,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,eAAe,iBAAiB,KAAK;IACjG;IACA,MAAM,QAAQ,CAAC,GAAG,kBAAkB,MAAM,EAAE,CAAC,GAAG,kBAAkB,cAAc,EAAE,kBAAkB,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,kBAAkB,cAAc,EAAE,MAAM,CAAC,GAAG,kBAAkB,cAAc,EAAE,MAAM,CAAC,GAAG,kBAAkB,QAAQ,EAAE;IACxP,MAAM,eAAe,CAAC,GAAG,SAAS,aAAa,EAAE;QAAE;QAAY;IAAU;IACzE,OAAO,CAAC,GAAG,kBAAkB,SAAS,EAAE,cAAc,WAAW;AACrE;AACA,QAAQ,SAAS,GAAG;AACpB,eAAe,YAAY,GAAG;IAC1B,IAAI;IACJ,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,MAAM;QACtC,MAAM,SAAS,SAAS,CAAC,IAAI,CAAC;IAClC,OACK,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QAC3C,MAAM;IACV,OACK;QACD,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK;IAC1F;IACA,OAAQ,IAAI,iBAAiB;QACzB,KAAK;YACD,OAAO,gBAAgB;QAC3B,KAAK;YAAQ;gBACT,OAAO,gBAAgB;YAC3B;QACA,KAAK;YAAM;gBACP,MAAM,aAAa,CAAC,GAAG,qBAAqB,OAAO,EAAE;gBACrD,OAAO,gBAAgB,MAAM;oBAAE;gBAAW;YAC9C;QACA;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;AACJ;AACA,QAAQ,WAAW,GAAG;AACtB,MAAM,cAAc,CAAC,MAAQ;QAAC;QAAS;QAAS;QAAS;QAAU;KAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,qBAAqB,OAAO,EAAE;AACtH,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/check_p2s.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst errors_js_1 = require(\"../util/errors.js\");\nfunction checkP2s(p2s) {\n    if (!(p2s instanceof Uint8Array) || p2s.length < 8) {\n        throw new errors_js_1.JWEInvalid('PBES2 Salt Input must be 8 or more octets');\n    }\n}\nexports.default = checkP2s;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,SAAS,SAAS,GAAG;IACjB,IAAI,CAAC,CAAC,eAAe,UAAU,KAAK,IAAI,MAAM,GAAG,GAAG;QAChD,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/pbes2kw.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decrypt = exports.encrypt = void 0;\nconst util_1 = require(\"util\");\nconst crypto_1 = require(\"crypto\");\nconst random_js_1 = require(\"./random.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst base64url_js_1 = require(\"./base64url.js\");\nconst aeskw_js_1 = require(\"./aeskw.js\");\nconst check_p2s_js_1 = require(\"../lib/check_p2s.js\");\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst crypto_key_js_1 = require(\"../lib/crypto_key.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nconst invalid_key_input_js_1 = require(\"../lib/invalid_key_input.js\");\nconst is_key_like_js_1 = require(\"./is_key_like.js\");\nconst pbkdf2 = (0, util_1.promisify)(crypto_1.pbkdf2);\nfunction getPassword(key, alg) {\n    if ((0, is_key_object_js_1.default)(key)) {\n        return key.export();\n    }\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if ((0, webcrypto_js_1.isCryptoKey)(key)) {\n        (0, crypto_key_js_1.checkEncCryptoKey)(key, alg, 'deriveBits', 'deriveKey');\n        return crypto_1.KeyObject.from(key).export();\n    }\n    throw new TypeError((0, invalid_key_input_js_1.default)(key, ...is_key_like_js_1.types, 'Uint8Array'));\n}\nconst encrypt = async (alg, key, cek, p2c = 2048, p2s = (0, random_js_1.default)(new Uint8Array(16))) => {\n    (0, check_p2s_js_1.default)(p2s);\n    const salt = (0, buffer_utils_js_1.p2s)(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    const encryptedKey = await (0, aeskw_js_1.wrap)(alg.slice(-6), derivedKey, cek);\n    return { encryptedKey, p2c, p2s: (0, base64url_js_1.encode)(p2s) };\n};\nexports.encrypt = encrypt;\nconst decrypt = async (alg, key, encryptedKey, p2c, p2s) => {\n    (0, check_p2s_js_1.default)(p2s);\n    const salt = (0, buffer_utils_js_1.p2s)(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    return (0, aeskw_js_1.unwrap)(alg.slice(-6), derivedKey, encryptedKey);\n};\nexports.decrypt = decrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG,KAAK;AACzC,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,SAAS,CAAC,GAAG,OAAO,SAAS,EAAE,SAAS,MAAM;AACpD,SAAS,YAAY,GAAG,EAAE,GAAG;IACzB,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QACtC,OAAO,IAAI,MAAM;IACrB;IACA,IAAI,eAAe,YAAY;QAC3B,OAAO;IACX;IACA,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,MAAM;QACtC,CAAC,GAAG,gBAAgB,iBAAiB,EAAE,KAAK,KAAK,cAAc;QAC/D,OAAO,SAAS,SAAS,CAAC,IAAI,CAAC,KAAK,MAAM;IAC9C;IACA,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK,EAAE;AAC5F;AACA,MAAM,UAAU,OAAO,KAAK,KAAK,KAAK,MAAM,IAAI,EAAE,MAAM,CAAC,GAAG,YAAY,OAAO,EAAE,IAAI,WAAW,IAAI;IAChG,CAAC,GAAG,eAAe,OAAO,EAAE;IAC5B,MAAM,OAAO,CAAC,GAAG,kBAAkB,GAAG,EAAE,KAAK;IAC7C,MAAM,SAAS,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO;IAClD,MAAM,WAAW,YAAY,KAAK;IAClC,MAAM,aAAa,MAAM,OAAO,UAAU,MAAM,KAAK,QAAQ,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC,GAAG,KAAK;IACrF,MAAM,eAAe,MAAM,CAAC,GAAG,WAAW,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,YAAY;IAC3E,OAAO;QAAE;QAAc;QAAK,KAAK,CAAC,GAAG,eAAe,MAAM,EAAE;IAAK;AACrE;AACA,QAAQ,OAAO,GAAG;AAClB,MAAM,UAAU,OAAO,KAAK,KAAK,cAAc,KAAK;IAChD,CAAC,GAAG,eAAe,OAAO,EAAE;IAC5B,MAAM,OAAO,CAAC,GAAG,kBAAkB,GAAG,EAAE,KAAK;IAC7C,MAAM,SAAS,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO;IAClD,MAAM,WAAW,YAAY,KAAK;IAClC,MAAM,aAAa,MAAM,OAAO,UAAU,MAAM,KAAK,QAAQ,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC,GAAG,KAAK;IACrF,OAAO,CAAC,GAAG,WAAW,MAAM,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,YAAY;AAC7D;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/check_modulus_length.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setModulusLength = exports.weakMap = void 0;\nexports.weakMap = new WeakMap();\nconst getLength = (buf, index) => {\n    let len = buf.readUInt8(1);\n    if ((len & 0x80) === 0) {\n        if (index === 0) {\n            return len;\n        }\n        return getLength(buf.subarray(2 + len), index - 1);\n    }\n    const num = len & 0x7f;\n    len = 0;\n    for (let i = 0; i < num; i++) {\n        len <<= 8;\n        const j = buf.readUInt8(2 + i);\n        len |= j;\n    }\n    if (index === 0) {\n        return len;\n    }\n    return getLength(buf.subarray(2 + len), index - 1);\n};\nconst getLengthOfSeqIndex = (sequence, index) => {\n    const len = sequence.readUInt8(1);\n    if ((len & 0x80) === 0) {\n        return getLength(sequence.subarray(2), index);\n    }\n    const num = len & 0x7f;\n    return getLength(sequence.subarray(2 + num), index);\n};\nconst getModulusLength = (key) => {\n    var _a, _b;\n    if (exports.weakMap.has(key)) {\n        return exports.weakMap.get(key);\n    }\n    const modulusLength = (_b = (_a = key.asymmetricKeyDetails) === null || _a === void 0 ? void 0 : _a.modulusLength) !== null && _b !== void 0 ? _b : (getLengthOfSeqIndex(key.export({ format: 'der', type: 'pkcs1' }), key.type === 'private' ? 1 : 0) -\n        1) <<\n        3;\n    exports.weakMap.set(key, modulusLength);\n    return modulusLength;\n};\nconst setModulusLength = (keyObject, modulusLength) => {\n    exports.weakMap.set(keyObject, modulusLength);\n};\nexports.setModulusLength = setModulusLength;\nexports.default = (key, alg) => {\n    if (getModulusLength(key) < 2048) {\n        throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n    }\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,QAAQ,OAAO,GAAG,KAAK;AAClD,QAAQ,OAAO,GAAG,IAAI;AACtB,MAAM,YAAY,CAAC,KAAK;IACpB,IAAI,MAAM,IAAI,SAAS,CAAC;IACxB,IAAI,CAAC,MAAM,IAAI,MAAM,GAAG;QACpB,IAAI,UAAU,GAAG;YACb,OAAO;QACX;QACA,OAAO,UAAU,IAAI,QAAQ,CAAC,IAAI,MAAM,QAAQ;IACpD;IACA,MAAM,MAAM,MAAM;IAClB,MAAM;IACN,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC1B,QAAQ;QACR,MAAM,IAAI,IAAI,SAAS,CAAC,IAAI;QAC5B,OAAO;IACX;IACA,IAAI,UAAU,GAAG;QACb,OAAO;IACX;IACA,OAAO,UAAU,IAAI,QAAQ,CAAC,IAAI,MAAM,QAAQ;AACpD;AACA,MAAM,sBAAsB,CAAC,UAAU;IACnC,MAAM,MAAM,SAAS,SAAS,CAAC;IAC/B,IAAI,CAAC,MAAM,IAAI,MAAM,GAAG;QACpB,OAAO,UAAU,SAAS,QAAQ,CAAC,IAAI;IAC3C;IACA,MAAM,MAAM,MAAM;IAClB,OAAO,UAAU,SAAS,QAAQ,CAAC,IAAI,MAAM;AACjD;AACA,MAAM,mBAAmB,CAAC;IACtB,IAAI,IAAI;IACR,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;QAC1B,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC/B;IACA,MAAM,gBAAgB,CAAC,KAAK,CAAC,KAAK,IAAI,oBAAoB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,AAAC,oBAAoB,IAAI,MAAM,CAAC;QAAE,QAAQ;QAAO,MAAM;IAAQ,IAAI,IAAI,IAAI,KAAK,YAAY,IAAI,KAChP,KACA;IACJ,QAAQ,OAAO,CAAC,GAAG,CAAC,KAAK;IACzB,OAAO;AACX;AACA,MAAM,mBAAmB,CAAC,WAAW;IACjC,QAAQ,OAAO,CAAC,GAAG,CAAC,WAAW;AACnC;AACA,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,OAAO,GAAG,CAAC,KAAK;IACpB,IAAI,iBAAiB,OAAO,MAAM;QAC9B,MAAM,IAAI,UAAU,GAAG,IAAI,qDAAqD,CAAC;IACrF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/rsaes.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decrypt = exports.encrypt = void 0;\nconst crypto_1 = require(\"crypto\");\nconst check_modulus_length_js_1 = require(\"./check_modulus_length.js\");\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst crypto_key_js_1 = require(\"../lib/crypto_key.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nconst invalid_key_input_js_1 = require(\"../lib/invalid_key_input.js\");\nconst is_key_like_js_1 = require(\"./is_key_like.js\");\nconst checkKey = (key, alg) => {\n    if (key.asymmetricKeyType !== 'rsa') {\n        throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n    }\n    (0, check_modulus_length_js_1.default)(key, alg);\n};\nconst resolvePadding = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            return crypto_1.constants.RSA_PKCS1_OAEP_PADDING;\n        case 'RSA1_5':\n            return crypto_1.constants.RSA_PKCS1_PADDING;\n        default:\n            return undefined;\n    }\n};\nconst resolveOaepHash = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n            return 'sha1';\n        case 'RSA-OAEP-256':\n            return 'sha256';\n        case 'RSA-OAEP-384':\n            return 'sha384';\n        case 'RSA-OAEP-512':\n            return 'sha512';\n        default:\n            return undefined;\n    }\n};\nfunction ensureKeyObject(key, alg, ...usages) {\n    if ((0, is_key_object_js_1.default)(key)) {\n        return key;\n    }\n    if ((0, webcrypto_js_1.isCryptoKey)(key)) {\n        (0, crypto_key_js_1.checkEncCryptoKey)(key, alg, ...usages);\n        return crypto_1.KeyObject.from(key);\n    }\n    throw new TypeError((0, invalid_key_input_js_1.default)(key, ...is_key_like_js_1.types));\n}\nconst encrypt = (alg, key, cek) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey', 'encrypt');\n    checkKey(keyObject, alg);\n    return (0, crypto_1.publicEncrypt)({ key: keyObject, oaepHash, padding }, cek);\n};\nexports.encrypt = encrypt;\nconst decrypt = (alg, key, encryptedKey) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey', 'decrypt');\n    checkKey(keyObject, alg);\n    return (0, crypto_1.privateDecrypt)({ key: keyObject, oaepHash, padding }, encryptedKey);\n};\nexports.decrypt = decrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG,KAAK;AACzC,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,WAAW,CAAC,KAAK;IACnB,IAAI,IAAI,iBAAiB,KAAK,OAAO;QACjC,MAAM,IAAI,UAAU;IACxB;IACA,CAAC,GAAG,0BAA0B,OAAO,EAAE,KAAK;AAChD;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,SAAS,SAAS,CAAC,sBAAsB;QACpD,KAAK;YACD,OAAO,SAAS,SAAS,CAAC,iBAAiB;QAC/C;YACI,OAAO;IACf;AACJ;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM;IACxC,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QACtC,OAAO;IACX;IACA,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,MAAM;QACtC,CAAC,GAAG,gBAAgB,iBAAiB,EAAE,KAAK,QAAQ;QACpD,OAAO,SAAS,SAAS,CAAC,IAAI,CAAC;IACnC;IACA,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK;AAC1F;AACA,MAAM,UAAU,CAAC,KAAK,KAAK;IACvB,MAAM,UAAU,eAAe;IAC/B,MAAM,WAAW,gBAAgB;IACjC,MAAM,YAAY,gBAAgB,KAAK,KAAK,WAAW;IACvD,SAAS,WAAW;IACpB,OAAO,CAAC,GAAG,SAAS,aAAa,EAAE;QAAE,KAAK;QAAW;QAAU;IAAQ,GAAG;AAC9E;AACA,QAAQ,OAAO,GAAG;AAClB,MAAM,UAAU,CAAC,KAAK,KAAK;IACvB,MAAM,UAAU,eAAe;IAC/B,MAAM,WAAW,gBAAgB;IACjC,MAAM,YAAY,gBAAgB,KAAK,KAAK,aAAa;IACzD,SAAS,WAAW;IACpB,OAAO,CAAC,GAAG,SAAS,cAAc,EAAE;QAAE,KAAK;QAAW;QAAU;IAAQ,GAAG;AAC/E;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/cek.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bitLength = void 0;\nconst errors_js_1 = require(\"../util/errors.js\");\nconst random_js_1 = require(\"../runtime/random.js\");\nfunction bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n            return 128;\n        case 'A192GCM':\n            return 192;\n        case 'A256GCM':\n        case 'A128CBC-HS256':\n            return 256;\n        case 'A192CBC-HS384':\n            return 384;\n        case 'A256CBC-HS512':\n            return 512;\n        default:\n            throw new errors_js_1.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\nexports.bitLength = bitLength;\nexports.default = (alg) => (0, random_js_1.default)(new Uint8Array(bitLength(alg) >> 3));\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,MAAM;AACN,SAAS,UAAU,GAAG;IAClB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,2BAA2B,EAAE,KAAK;IAClF;AACJ;AACA,QAAQ,SAAS,GAAG;AACpB,QAAQ,OAAO,GAAG,CAAC,MAAQ,CAAC,GAAG,YAAY,OAAO,EAAE,IAAI,WAAW,UAAU,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/asn1.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.fromX509 = exports.fromSPKI = exports.fromPKCS8 = exports.toPKCS8 = exports.toSPKI = void 0;\nconst crypto_1 = require(\"crypto\");\nconst buffer_1 = require(\"buffer\");\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nconst invalid_key_input_js_1 = require(\"../lib/invalid_key_input.js\");\nconst is_key_like_js_1 = require(\"./is_key_like.js\");\nconst genericExport = (keyType, keyFormat, key) => {\n    let keyObject;\n    if ((0, webcrypto_js_1.isCryptoKey)(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = crypto_1.KeyObject.from(key);\n    }\n    else if ((0, is_key_object_js_1.default)(key)) {\n        keyObject = key;\n    }\n    else {\n        throw new TypeError((0, invalid_key_input_js_1.default)(key, ...is_key_like_js_1.types));\n    }\n    if (keyObject.type !== keyType) {\n        throw new TypeError(`key is not a ${keyType} key`);\n    }\n    return keyObject.export({ format: 'pem', type: keyFormat });\n};\nconst toSPKI = (key) => {\n    return genericExport('public', 'spki', key);\n};\nexports.toSPKI = toSPKI;\nconst toPKCS8 = (key) => {\n    return genericExport('private', 'pkcs8', key);\n};\nexports.toPKCS8 = toPKCS8;\nconst fromPKCS8 = (pem) => (0, crypto_1.createPrivateKey)({\n    key: buffer_1.Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\\s)/g, ''), 'base64'),\n    type: 'pkcs8',\n    format: 'der',\n});\nexports.fromPKCS8 = fromPKCS8;\nconst fromSPKI = (pem) => (0, crypto_1.createPublicKey)({\n    key: buffer_1.Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\\s)/g, ''), 'base64'),\n    type: 'spki',\n    format: 'der',\n});\nexports.fromSPKI = fromSPKI;\nconst fromX509 = (pem) => (0, crypto_1.createPublicKey)({\n    key: pem,\n    type: 'spki',\n    format: 'pem',\n});\nexports.fromX509 = fromX509;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,GAAG,QAAQ,SAAS,GAAG,QAAQ,OAAO,GAAG,QAAQ,MAAM,GAAG,KAAK;AAClG,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,gBAAgB,CAAC,SAAS,WAAW;IACvC,IAAI;IACJ,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,MAAM;QACtC,IAAI,CAAC,IAAI,WAAW,EAAE;YAClB,MAAM,IAAI,UAAU;QACxB;QACA,YAAY,SAAS,SAAS,CAAC,IAAI,CAAC;IACxC,OACK,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QAC3C,YAAY;IAChB,OACK;QACD,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK;IAC1F;IACA,IAAI,UAAU,IAAI,KAAK,SAAS;QAC5B,MAAM,IAAI,UAAU,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC;IACrD;IACA,OAAO,UAAU,MAAM,CAAC;QAAE,QAAQ;QAAO,MAAM;IAAU;AAC7D;AACA,MAAM,SAAS,CAAC;IACZ,OAAO,cAAc,UAAU,QAAQ;AAC3C;AACA,QAAQ,MAAM,GAAG;AACjB,MAAM,UAAU,CAAC;IACb,OAAO,cAAc,WAAW,SAAS;AAC7C;AACA,QAAQ,OAAO,GAAG;AAClB,MAAM,YAAY,CAAC,MAAQ,CAAC,GAAG,SAAS,gBAAgB,EAAE;QACtD,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,+CAA+C,KAAK;QAC1F,MAAM;QACN,QAAQ;IACZ;AACA,QAAQ,SAAS,GAAG;AACpB,MAAM,WAAW,CAAC,MAAQ,CAAC,GAAG,SAAS,eAAe,EAAE;QACpD,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,8CAA8C,KAAK;QACzF,MAAM;QACN,QAAQ;IACZ;AACA,QAAQ,QAAQ,GAAG;AACnB,MAAM,WAAW,CAAC,MAAQ,CAAC,GAAG,SAAS,eAAe,EAAE;QACpD,KAAK;QACL,MAAM;QACN,QAAQ;IACZ;AACA,QAAQ,QAAQ,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/asn1_sequence_encoder.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst buffer_1 = require(\"buffer\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst tagInteger = 0x02;\nconst tagBitStr = 0x03;\nconst tagOctStr = 0x04;\nconst tagSequence = 0x30;\nconst bZero = buffer_1.Buffer.from([0x00]);\nconst bTagInteger = buffer_1.Buffer.from([tagInteger]);\nconst bTagBitStr = buffer_1.Buffer.from([tagBitStr]);\nconst bTagSequence = buffer_1.Buffer.from([tagSequence]);\nconst bTagOctStr = buffer_1.Buffer.from([tagOctStr]);\nconst encodeLength = (len) => {\n    if (len < 128)\n        return buffer_1.Buffer.from([len]);\n    const buffer = buffer_1.Buffer.alloc(5);\n    buffer.writeUInt32BE(len, 1);\n    let offset = 1;\n    while (buffer[offset] === 0)\n        offset++;\n    buffer[offset - 1] = 0x80 | (5 - offset);\n    return buffer.slice(offset - 1);\n};\nconst oids = new Map([\n    ['P-256', buffer_1.Buffer.from('06 08 2A 86 48 CE 3D 03 01 07'.replace(/ /g, ''), 'hex')],\n    ['secp256k1', buffer_1.Buffer.from('06 05 2B 81 04 00 0A'.replace(/ /g, ''), 'hex')],\n    ['P-384', buffer_1.Buffer.from('06 05 2B 81 04 00 22'.replace(/ /g, ''), 'hex')],\n    ['P-521', buffer_1.Buffer.from('06 05 2B 81 04 00 23'.replace(/ /g, ''), 'hex')],\n    ['ecPublicKey', buffer_1.Buffer.from('06 07 2A 86 48 CE 3D 02 01'.replace(/ /g, ''), 'hex')],\n    ['X25519', buffer_1.Buffer.from('06 03 2B 65 6E'.replace(/ /g, ''), 'hex')],\n    ['X448', buffer_1.Buffer.from('06 03 2B 65 6F'.replace(/ /g, ''), 'hex')],\n    ['Ed25519', buffer_1.Buffer.from('06 03 2B 65 70'.replace(/ /g, ''), 'hex')],\n    ['Ed448', buffer_1.Buffer.from('06 03 2B 65 71'.replace(/ /g, ''), 'hex')],\n]);\nclass DumbAsn1Encoder {\n    constructor() {\n        this.length = 0;\n        this.elements = [];\n    }\n    oidFor(oid) {\n        const bOid = oids.get(oid);\n        if (!bOid) {\n            throw new errors_js_1.JOSENotSupported('Invalid or unsupported OID');\n        }\n        this.elements.push(bOid);\n        this.length += bOid.length;\n    }\n    zero() {\n        this.elements.push(bTagInteger, buffer_1.Buffer.from([0x01]), bZero);\n        this.length += 3;\n    }\n    one() {\n        this.elements.push(bTagInteger, buffer_1.Buffer.from([0x01]), buffer_1.Buffer.from([0x01]));\n        this.length += 3;\n    }\n    unsignedInteger(integer) {\n        if (integer[0] & 0x80) {\n            const len = encodeLength(integer.length + 1);\n            this.elements.push(bTagInteger, len, bZero, integer);\n            this.length += 2 + len.length + integer.length;\n        }\n        else {\n            let i = 0;\n            while (integer[i] === 0 && (integer[i + 1] & 0x80) === 0)\n                i++;\n            const len = encodeLength(integer.length - i);\n            this.elements.push(bTagInteger, encodeLength(integer.length - i), integer.slice(i));\n            this.length += 1 + len.length + integer.length - i;\n        }\n    }\n    octStr(octStr) {\n        const len = encodeLength(octStr.length);\n        this.elements.push(bTagOctStr, encodeLength(octStr.length), octStr);\n        this.length += 1 + len.length + octStr.length;\n    }\n    bitStr(bitS) {\n        const len = encodeLength(bitS.length + 1);\n        this.elements.push(bTagBitStr, encodeLength(bitS.length + 1), bZero, bitS);\n        this.length += 1 + len.length + bitS.length + 1;\n    }\n    add(seq) {\n        this.elements.push(seq);\n        this.length += seq.length;\n    }\n    end(tag = bTagSequence) {\n        const len = encodeLength(this.length);\n        return buffer_1.Buffer.concat([tag, len, ...this.elements], 1 + len.length + this.length);\n    }\n}\nexports.default = DumbAsn1Encoder;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM,aAAa;AACnB,MAAM,YAAY;AAClB,MAAM,YAAY;AAClB,MAAM,cAAc;AACpB,MAAM,QAAQ,SAAS,MAAM,CAAC,IAAI,CAAC;IAAC;CAAK;AACzC,MAAM,cAAc,SAAS,MAAM,CAAC,IAAI,CAAC;IAAC;CAAW;AACrD,MAAM,aAAa,SAAS,MAAM,CAAC,IAAI,CAAC;IAAC;CAAU;AACnD,MAAM,eAAe,SAAS,MAAM,CAAC,IAAI,CAAC;IAAC;CAAY;AACvD,MAAM,aAAa,SAAS,MAAM,CAAC,IAAI,CAAC;IAAC;CAAU;AACnD,MAAM,eAAe,CAAC;IAClB,IAAI,MAAM,KACN,OAAO,SAAS,MAAM,CAAC,IAAI,CAAC;QAAC;KAAI;IACrC,MAAM,SAAS,SAAS,MAAM,CAAC,KAAK,CAAC;IACrC,OAAO,aAAa,CAAC,KAAK;IAC1B,IAAI,SAAS;IACb,MAAO,MAAM,CAAC,OAAO,KAAK,EACtB;IACJ,MAAM,CAAC,SAAS,EAAE,GAAG,OAAQ,IAAI;IACjC,OAAO,OAAO,KAAK,CAAC,SAAS;AACjC;AACA,MAAM,OAAO,IAAI,IAAI;IACjB;QAAC;QAAS,SAAS,MAAM,CAAC,IAAI,CAAC,gCAAgC,OAAO,CAAC,MAAM,KAAK;KAAO;IACzF;QAAC;QAAa,SAAS,MAAM,CAAC,IAAI,CAAC,uBAAuB,OAAO,CAAC,MAAM,KAAK;KAAO;IACpF;QAAC;QAAS,SAAS,MAAM,CAAC,IAAI,CAAC,uBAAuB,OAAO,CAAC,MAAM,KAAK;KAAO;IAChF;QAAC;QAAS,SAAS,MAAM,CAAC,IAAI,CAAC,uBAAuB,OAAO,CAAC,MAAM,KAAK;KAAO;IAChF;QAAC;QAAe,SAAS,MAAM,CAAC,IAAI,CAAC,6BAA6B,OAAO,CAAC,MAAM,KAAK;KAAO;IAC5F;QAAC;QAAU,SAAS,MAAM,CAAC,IAAI,CAAC,iBAAiB,OAAO,CAAC,MAAM,KAAK;KAAO;IAC3E;QAAC;QAAQ,SAAS,MAAM,CAAC,IAAI,CAAC,iBAAiB,OAAO,CAAC,MAAM,KAAK;KAAO;IACzE;QAAC;QAAW,SAAS,MAAM,CAAC,IAAI,CAAC,iBAAiB,OAAO,CAAC,MAAM,KAAK;KAAO;IAC5E;QAAC;QAAS,SAAS,MAAM,CAAC,IAAI,CAAC,iBAAiB,OAAO,CAAC,MAAM,KAAK;KAAO;CAC7E;AACD,MAAM;IACF,aAAc;QACV,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG,EAAE;IACtB;IACA,OAAO,GAAG,EAAE;QACR,MAAM,OAAO,KAAK,GAAG,CAAC;QACtB,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,YAAY,gBAAgB,CAAC;QAC3C;QACA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,MAAM,IAAI,KAAK,MAAM;IAC9B;IACA,OAAO;QACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,SAAS,MAAM,CAAC,IAAI,CAAC;YAAC;SAAK,GAAG;QAC9D,IAAI,CAAC,MAAM,IAAI;IACnB;IACA,MAAM;QACF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,SAAS,MAAM,CAAC,IAAI,CAAC;YAAC;SAAK,GAAG,SAAS,MAAM,CAAC,IAAI,CAAC;YAAC;SAAK;QACzF,IAAI,CAAC,MAAM,IAAI;IACnB;IACA,gBAAgB,OAAO,EAAE;QACrB,IAAI,OAAO,CAAC,EAAE,GAAG,MAAM;YACnB,MAAM,MAAM,aAAa,QAAQ,MAAM,GAAG;YAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,KAAK,OAAO;YAC5C,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,GAAG,QAAQ,MAAM;QAClD,OACK;YACD,IAAI,IAAI;YACR,MAAO,OAAO,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,MAAM,EACnD;YACJ,MAAM,MAAM,aAAa,QAAQ,MAAM,GAAG;YAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,aAAa,QAAQ,MAAM,GAAG,IAAI,QAAQ,KAAK,CAAC;YAChF,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,GAAG,QAAQ,MAAM,GAAG;QACrD;IACJ;IACA,OAAO,MAAM,EAAE;QACX,MAAM,MAAM,aAAa,OAAO,MAAM;QACtC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,aAAa,OAAO,MAAM,GAAG;QAC5D,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,GAAG,OAAO,MAAM;IACjD;IACA,OAAO,IAAI,EAAE;QACT,MAAM,MAAM,aAAa,KAAK,MAAM,GAAG;QACvC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,aAAa,KAAK,MAAM,GAAG,IAAI,OAAO;QACrE,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,MAAM,GAAG,KAAK,MAAM,GAAG;IAClD;IACA,IAAI,GAAG,EAAE;QACL,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM;IAC7B;IACA,IAAI,MAAM,YAAY,EAAE;QACpB,MAAM,MAAM,aAAa,IAAI,CAAC,MAAM;QACpC,OAAO,SAAS,MAAM,CAAC,MAAM,CAAC;YAAC;YAAK;eAAQ,IAAI,CAAC,QAAQ;SAAC,EAAE,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM;IAC5F;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/flags.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.jwkImport = exports.jwkExport = exports.rsaPssParams = exports.oneShotCallback = void 0;\nconst [major, minor] = process.versions.node.split('.').map((str) => parseInt(str, 10));\nexports.oneShotCallback = major >= 16 || (major === 15 && minor >= 13);\nexports.rsaPssParams = !('electron' in process.versions) && (major >= 17 || (major === 16 && minor >= 9));\nexports.jwkExport = major >= 16 || (major === 15 && minor >= 9);\nexports.jwkImport = major >= 16 || (major === 15 && minor >= 12);\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,QAAQ,SAAS,GAAG,QAAQ,YAAY,GAAG,QAAQ,eAAe,GAAG,KAAK;AAC9F,MAAM,CAAC,OAAO,MAAM,GAAG,QAAQ,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAQ,SAAS,KAAK;AACnF,QAAQ,eAAe,GAAG,SAAS,MAAO,UAAU,MAAM,SAAS;AACnE,QAAQ,YAAY,GAAG,CAAC,CAAC,cAAc,QAAQ,QAAQ,KAAK,CAAC,SAAS,MAAO,UAAU,MAAM,SAAS,CAAE;AACxG,QAAQ,SAAS,GAAG,SAAS,MAAO,UAAU,MAAM,SAAS;AAC7D,QAAQ,SAAS,GAAG,SAAS,MAAO,UAAU,MAAM,SAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1623, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/jwk_to_key.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst buffer_1 = require(\"buffer\");\nconst crypto_1 = require(\"crypto\");\nconst base64url_js_1 = require(\"./base64url.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst get_named_curve_js_1 = require(\"./get_named_curve.js\");\nconst check_modulus_length_js_1 = require(\"./check_modulus_length.js\");\nconst asn1_sequence_encoder_js_1 = require(\"./asn1_sequence_encoder.js\");\nconst flags_js_1 = require(\"./flags.js\");\nconst parse = (jwk) => {\n    if (flags_js_1.jwkImport && jwk.kty !== 'oct') {\n        return jwk.d\n            ? (0, crypto_1.createPrivateKey)({ format: 'jwk', key: jwk })\n            : (0, crypto_1.createPublicKey)({ format: 'jwk', key: jwk });\n    }\n    switch (jwk.kty) {\n        case 'oct': {\n            return (0, crypto_1.createSecretKey)((0, base64url_js_1.decode)(jwk.k));\n        }\n        case 'RSA': {\n            const enc = new asn1_sequence_encoder_js_1.default();\n            const isPrivate = jwk.d !== undefined;\n            const modulus = buffer_1.Buffer.from(jwk.n, 'base64');\n            const exponent = buffer_1.Buffer.from(jwk.e, 'base64');\n            if (isPrivate) {\n                enc.zero();\n                enc.unsignedInteger(modulus);\n                enc.unsignedInteger(exponent);\n                enc.unsignedInteger(buffer_1.Buffer.from(jwk.d, 'base64'));\n                enc.unsignedInteger(buffer_1.Buffer.from(jwk.p, 'base64'));\n                enc.unsignedInteger(buffer_1.Buffer.from(jwk.q, 'base64'));\n                enc.unsignedInteger(buffer_1.Buffer.from(jwk.dp, 'base64'));\n                enc.unsignedInteger(buffer_1.Buffer.from(jwk.dq, 'base64'));\n                enc.unsignedInteger(buffer_1.Buffer.from(jwk.qi, 'base64'));\n            }\n            else {\n                enc.unsignedInteger(modulus);\n                enc.unsignedInteger(exponent);\n            }\n            const der = enc.end();\n            const createInput = {\n                key: der,\n                format: 'der',\n                type: 'pkcs1',\n            };\n            const keyObject = isPrivate ? (0, crypto_1.createPrivateKey)(createInput) : (0, crypto_1.createPublicKey)(createInput);\n            (0, check_modulus_length_js_1.setModulusLength)(keyObject, modulus.length << 3);\n            return keyObject;\n        }\n        case 'EC': {\n            const enc = new asn1_sequence_encoder_js_1.default();\n            const isPrivate = jwk.d !== undefined;\n            const pub = buffer_1.Buffer.concat([\n                buffer_1.Buffer.alloc(1, 4),\n                buffer_1.Buffer.from(jwk.x, 'base64'),\n                buffer_1.Buffer.from(jwk.y, 'base64'),\n            ]);\n            if (isPrivate) {\n                enc.zero();\n                const enc$1 = new asn1_sequence_encoder_js_1.default();\n                enc$1.oidFor('ecPublicKey');\n                enc$1.oidFor(jwk.crv);\n                enc.add(enc$1.end());\n                const enc$2 = new asn1_sequence_encoder_js_1.default();\n                enc$2.one();\n                enc$2.octStr(buffer_1.Buffer.from(jwk.d, 'base64'));\n                const enc$3 = new asn1_sequence_encoder_js_1.default();\n                enc$3.bitStr(pub);\n                const f2 = enc$3.end(buffer_1.Buffer.from([0xa1]));\n                enc$2.add(f2);\n                const f = enc$2.end();\n                const enc$4 = new asn1_sequence_encoder_js_1.default();\n                enc$4.add(f);\n                const f3 = enc$4.end(buffer_1.Buffer.from([0x04]));\n                enc.add(f3);\n                const der = enc.end();\n                const keyObject = (0, crypto_1.createPrivateKey)({ key: der, format: 'der', type: 'pkcs8' });\n                (0, get_named_curve_js_1.setCurve)(keyObject, jwk.crv);\n                return keyObject;\n            }\n            const enc$1 = new asn1_sequence_encoder_js_1.default();\n            enc$1.oidFor('ecPublicKey');\n            enc$1.oidFor(jwk.crv);\n            enc.add(enc$1.end());\n            enc.bitStr(pub);\n            const der = enc.end();\n            const keyObject = (0, crypto_1.createPublicKey)({ key: der, format: 'der', type: 'spki' });\n            (0, get_named_curve_js_1.setCurve)(keyObject, jwk.crv);\n            return keyObject;\n        }\n        case 'OKP': {\n            const enc = new asn1_sequence_encoder_js_1.default();\n            const isPrivate = jwk.d !== undefined;\n            if (isPrivate) {\n                enc.zero();\n                const enc$1 = new asn1_sequence_encoder_js_1.default();\n                enc$1.oidFor(jwk.crv);\n                enc.add(enc$1.end());\n                const enc$2 = new asn1_sequence_encoder_js_1.default();\n                enc$2.octStr(buffer_1.Buffer.from(jwk.d, 'base64'));\n                const f = enc$2.end(buffer_1.Buffer.from([0x04]));\n                enc.add(f);\n                const der = enc.end();\n                return (0, crypto_1.createPrivateKey)({ key: der, format: 'der', type: 'pkcs8' });\n            }\n            const enc$1 = new asn1_sequence_encoder_js_1.default();\n            enc$1.oidFor(jwk.crv);\n            enc.add(enc$1.end());\n            enc.bitStr(buffer_1.Buffer.from(jwk.x, 'base64'));\n            const der = enc.end();\n            return (0, crypto_1.createPublicKey)({ key: der, format: 'der', type: 'spki' });\n        }\n        default:\n            throw new errors_js_1.JOSENotSupported('Invalid or unsupported JWK \"kty\" (Key Type) Parameter value');\n    }\n};\nexports.default = parse;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC;IACX,IAAI,WAAW,SAAS,IAAI,IAAI,GAAG,KAAK,OAAO;QAC3C,OAAO,IAAI,CAAC,GACN,CAAC,GAAG,SAAS,gBAAgB,EAAE;YAAE,QAAQ;YAAO,KAAK;QAAI,KACzD,CAAC,GAAG,SAAS,eAAe,EAAE;YAAE,QAAQ;YAAO,KAAK;QAAI;IAClE;IACA,OAAQ,IAAI,GAAG;QACX,KAAK;YAAO;gBACR,OAAO,CAAC,GAAG,SAAS,eAAe,EAAE,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,CAAC;YACzE;QACA,KAAK;YAAO;gBACR,MAAM,MAAM,IAAI,2BAA2B,OAAO;gBAClD,MAAM,YAAY,IAAI,CAAC,KAAK;gBAC5B,MAAM,UAAU,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC5C,MAAM,WAAW,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC7C,IAAI,WAAW;oBACX,IAAI,IAAI;oBACR,IAAI,eAAe,CAAC;oBACpB,IAAI,eAAe,CAAC;oBACpB,IAAI,eAAe,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAChD,IAAI,eAAe,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAChD,IAAI,eAAe,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAChD,IAAI,eAAe,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;oBACjD,IAAI,eAAe,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;oBACjD,IAAI,eAAe,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;gBACrD,OACK;oBACD,IAAI,eAAe,CAAC;oBACpB,IAAI,eAAe,CAAC;gBACxB;gBACA,MAAM,MAAM,IAAI,GAAG;gBACnB,MAAM,cAAc;oBAChB,KAAK;oBACL,QAAQ;oBACR,MAAM;gBACV;gBACA,MAAM,YAAY,YAAY,CAAC,GAAG,SAAS,gBAAgB,EAAE,eAAe,CAAC,GAAG,SAAS,eAAe,EAAE;gBAC1G,CAAC,GAAG,0BAA0B,gBAAgB,EAAE,WAAW,QAAQ,MAAM,IAAI;gBAC7E,OAAO;YACX;QACA,KAAK;YAAM;gBACP,MAAM,MAAM,IAAI,2BAA2B,OAAO;gBAClD,MAAM,YAAY,IAAI,CAAC,KAAK;gBAC5B,MAAM,MAAM,SAAS,MAAM,CAAC,MAAM,CAAC;oBAC/B,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG;oBACzB,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAC5B,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;iBAC/B;gBACD,IAAI,WAAW;oBACX,IAAI,IAAI;oBACR,MAAM,QAAQ,IAAI,2BAA2B,OAAO;oBACpD,MAAM,MAAM,CAAC;oBACb,MAAM,MAAM,CAAC,IAAI,GAAG;oBACpB,IAAI,GAAG,CAAC,MAAM,GAAG;oBACjB,MAAM,QAAQ,IAAI,2BAA2B,OAAO;oBACpD,MAAM,GAAG;oBACT,MAAM,MAAM,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACzC,MAAM,QAAQ,IAAI,2BAA2B,OAAO;oBACpD,MAAM,MAAM,CAAC;oBACb,MAAM,KAAK,MAAM,GAAG,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC;wBAAC;qBAAK;oBAChD,MAAM,GAAG,CAAC;oBACV,MAAM,IAAI,MAAM,GAAG;oBACnB,MAAM,QAAQ,IAAI,2BAA2B,OAAO;oBACpD,MAAM,GAAG,CAAC;oBACV,MAAM,KAAK,MAAM,GAAG,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC;wBAAC;qBAAK;oBAChD,IAAI,GAAG,CAAC;oBACR,MAAM,MAAM,IAAI,GAAG;oBACnB,MAAM,YAAY,CAAC,GAAG,SAAS,gBAAgB,EAAE;wBAAE,KAAK;wBAAK,QAAQ;wBAAO,MAAM;oBAAQ;oBAC1F,CAAC,GAAG,qBAAqB,QAAQ,EAAE,WAAW,IAAI,GAAG;oBACrD,OAAO;gBACX;gBACA,MAAM,QAAQ,IAAI,2BAA2B,OAAO;gBACpD,MAAM,MAAM,CAAC;gBACb,MAAM,MAAM,CAAC,IAAI,GAAG;gBACpB,IAAI,GAAG,CAAC,MAAM,GAAG;gBACjB,IAAI,MAAM,CAAC;gBACX,MAAM,MAAM,IAAI,GAAG;gBACnB,MAAM,YAAY,CAAC,GAAG,SAAS,eAAe,EAAE;oBAAE,KAAK;oBAAK,QAAQ;oBAAO,MAAM;gBAAO;gBACxF,CAAC,GAAG,qBAAqB,QAAQ,EAAE,WAAW,IAAI,GAAG;gBACrD,OAAO;YACX;QACA,KAAK;YAAO;gBACR,MAAM,MAAM,IAAI,2BAA2B,OAAO;gBAClD,MAAM,YAAY,IAAI,CAAC,KAAK;gBAC5B,IAAI,WAAW;oBACX,IAAI,IAAI;oBACR,MAAM,QAAQ,IAAI,2BAA2B,OAAO;oBACpD,MAAM,MAAM,CAAC,IAAI,GAAG;oBACpB,IAAI,GAAG,CAAC,MAAM,GAAG;oBACjB,MAAM,QAAQ,IAAI,2BAA2B,OAAO;oBACpD,MAAM,MAAM,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACzC,MAAM,IAAI,MAAM,GAAG,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC;wBAAC;qBAAK;oBAC/C,IAAI,GAAG,CAAC;oBACR,MAAM,MAAM,IAAI,GAAG;oBACnB,OAAO,CAAC,GAAG,SAAS,gBAAgB,EAAE;wBAAE,KAAK;wBAAK,QAAQ;wBAAO,MAAM;oBAAQ;gBACnF;gBACA,MAAM,QAAQ,IAAI,2BAA2B,OAAO;gBACpD,MAAM,MAAM,CAAC,IAAI,GAAG;gBACpB,IAAI,GAAG,CAAC,MAAM,GAAG;gBACjB,IAAI,MAAM,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACvC,MAAM,MAAM,IAAI,GAAG;gBACnB,OAAO,CAAC,GAAG,SAAS,eAAe,EAAE;oBAAE,KAAK;oBAAK,QAAQ;oBAAO,MAAM;gBAAO;YACjF;QACA;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/key/import.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.importJWK = exports.importPKCS8 = exports.importX509 = exports.importSPKI = void 0;\nconst base64url_js_1 = require(\"../runtime/base64url.js\");\nconst asn1_js_1 = require(\"../runtime/asn1.js\");\nconst jwk_to_key_js_1 = require(\"../runtime/jwk_to_key.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst is_object_js_1 = require(\"../lib/is_object.js\");\nasync function importSPKI(spki, alg, options) {\n    if (typeof spki !== 'string' || spki.indexOf('-----BEGIN PUBLIC KEY-----') !== 0) {\n        throw new TypeError('\"spki\" must be SPKI formatted string');\n    }\n    return (0, asn1_js_1.fromSPKI)(spki, alg, options);\n}\nexports.importSPKI = importSPKI;\nasync function importX509(x509, alg, options) {\n    if (typeof x509 !== 'string' || x509.indexOf('-----BEGIN CERTIFICATE-----') !== 0) {\n        throw new TypeError('\"x509\" must be X.509 formatted string');\n    }\n    return (0, asn1_js_1.fromX509)(x509, alg, options);\n}\nexports.importX509 = importX509;\nasync function importPKCS8(pkcs8, alg, options) {\n    if (typeof pkcs8 !== 'string' || pkcs8.indexOf('-----BEGIN PRIVATE KEY-----') !== 0) {\n        throw new TypeError('\"pkcs8\" must be PKCS#8 formatted string');\n    }\n    return (0, asn1_js_1.fromPKCS8)(pkcs8, alg, options);\n}\nexports.importPKCS8 = importPKCS8;\nasync function importJWK(jwk, alg, octAsKeyObject) {\n    var _a;\n    if (!(0, is_object_js_1.default)(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    alg || (alg = jwk.alg);\n    switch (jwk.kty) {\n        case 'oct':\n            if (typeof jwk.k !== 'string' || !jwk.k) {\n                throw new TypeError('missing \"k\" (Key Value) Parameter value');\n            }\n            octAsKeyObject !== null && octAsKeyObject !== void 0 ? octAsKeyObject : (octAsKeyObject = jwk.ext !== true);\n            if (octAsKeyObject) {\n                return (0, jwk_to_key_js_1.default)({ ...jwk, alg, ext: (_a = jwk.ext) !== null && _a !== void 0 ? _a : false });\n            }\n            return (0, base64url_js_1.decode)(jwk.k);\n        case 'RSA':\n            if (jwk.oth !== undefined) {\n                throw new errors_js_1.JOSENotSupported('RSA JWK \"oth\" (Other Primes Info) Parameter value is not supported');\n            }\n        case 'EC':\n        case 'OKP':\n            return (0, jwk_to_key_js_1.default)({ ...jwk, alg });\n        default:\n            throw new errors_js_1.JOSENotSupported('Unsupported \"kty\" (Key Type) Parameter value');\n    }\n}\nexports.importJWK = importJWK;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,QAAQ,WAAW,GAAG,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,KAAK;AACzF,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO;IACxC,IAAI,OAAO,SAAS,YAAY,KAAK,OAAO,CAAC,kCAAkC,GAAG;QAC9E,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,CAAC,GAAG,UAAU,QAAQ,EAAE,MAAM,KAAK;AAC9C;AACA,QAAQ,UAAU,GAAG;AACrB,eAAe,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO;IACxC,IAAI,OAAO,SAAS,YAAY,KAAK,OAAO,CAAC,mCAAmC,GAAG;QAC/E,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,CAAC,GAAG,UAAU,QAAQ,EAAE,MAAM,KAAK;AAC9C;AACA,QAAQ,UAAU,GAAG;AACrB,eAAe,YAAY,KAAK,EAAE,GAAG,EAAE,OAAO;IAC1C,IAAI,OAAO,UAAU,YAAY,MAAM,OAAO,CAAC,mCAAmC,GAAG;QACjF,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,CAAC,GAAG,UAAU,SAAS,EAAE,OAAO,KAAK;AAChD;AACA,QAAQ,WAAW,GAAG;AACtB,eAAe,UAAU,GAAG,EAAE,GAAG,EAAE,cAAc;IAC7C,IAAI;IACJ,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,MAAM;QACnC,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,CAAC,MAAM,IAAI,GAAG;IACrB,OAAQ,IAAI,GAAG;QACX,KAAK;YACD,IAAI,OAAO,IAAI,CAAC,KAAK,YAAY,CAAC,IAAI,CAAC,EAAE;gBACrC,MAAM,IAAI,UAAU;YACxB;YACA,mBAAmB,QAAQ,mBAAmB,KAAK,IAAI,iBAAkB,iBAAiB,IAAI,GAAG,KAAK;YACtG,IAAI,gBAAgB;gBAChB,OAAO,CAAC,GAAG,gBAAgB,OAAO,EAAE;oBAAE,GAAG,GAAG;oBAAE;oBAAK,KAAK,CAAC,KAAK,IAAI,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;gBAAM;YAClH;YACA,OAAO,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,CAAC;QAC3C,KAAK;YACD,IAAI,IAAI,GAAG,KAAK,WAAW;gBACvB,MAAM,IAAI,YAAY,gBAAgB,CAAC;YAC3C;QACJ,KAAK;QACL,KAAK;YACD,OAAO,CAAC,GAAG,gBAAgB,OAAO,EAAE;gBAAE,GAAG,GAAG;gBAAE;YAAI;QACtD;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;AACJ;AACA,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1848, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/check_key_type.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst invalid_key_input_js_1 = require(\"./invalid_key_input.js\");\nconst is_key_like_js_1 = require(\"../runtime/is_key_like.js\");\nconst symmetricTypeCheck = (alg, key) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (!(0, is_key_like_js_1.default)(key)) {\n        throw new TypeError((0, invalid_key_input_js_1.withAlg)(alg, key, ...is_key_like_js_1.types, 'Uint8Array'));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${is_key_like_js_1.types.join(' or ')} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage) => {\n    if (!(0, is_key_like_js_1.default)(key)) {\n        throw new TypeError((0, invalid_key_input_js_1.withAlg)(alg, key, ...is_key_like_js_1.types));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${is_key_like_js_1.types.join(' or ')} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (usage === 'sign' && key.type === 'public') {\n        throw new TypeError(`${is_key_like_js_1.types.join(' or ')} instances for asymmetric algorithm signing must be of type \"private\"`);\n    }\n    if (usage === 'decrypt' && key.type === 'public') {\n        throw new TypeError(`${is_key_like_js_1.types.join(' or ')} instances for asymmetric algorithm decryption must be of type \"private\"`);\n    }\n    if (key.algorithm && usage === 'verify' && key.type === 'private') {\n        throw new TypeError(`${is_key_like_js_1.types.join(' or ')} instances for asymmetric algorithm verifying must be of type \"public\"`);\n    }\n    if (key.algorithm && usage === 'encrypt' && key.type === 'private') {\n        throw new TypeError(`${is_key_like_js_1.types.join(' or ')} instances for asymmetric algorithm encryption must be of type \"public\"`);\n    }\n};\nconst checkKeyType = (alg, key, usage) => {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A\\d{3}(?:GCM)?KW$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage);\n    }\n};\nexports.default = checkKeyType;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM,qBAAqB,CAAC,KAAK;IAC7B,IAAI,eAAe,YACf;IACJ,IAAI,CAAC,CAAC,GAAG,iBAAiB,OAAO,EAAE,MAAM;QACrC,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,KAAK,QAAQ,iBAAiB,KAAK,EAAE;IACjG;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,MAAM,IAAI,UAAU,GAAG,iBAAiB,KAAK,CAAC,IAAI,CAAC,QAAQ,4DAA4D,CAAC;IAC5H;AACJ;AACA,MAAM,sBAAsB,CAAC,KAAK,KAAK;IACnC,IAAI,CAAC,CAAC,GAAG,iBAAiB,OAAO,EAAE,MAAM;QACrC,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,KAAK,QAAQ,iBAAiB,KAAK;IAC/F;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,MAAM,IAAI,UAAU,GAAG,iBAAiB,KAAK,CAAC,IAAI,CAAC,QAAQ,iEAAiE,CAAC;IACjI;IACA,IAAI,UAAU,UAAU,IAAI,IAAI,KAAK,UAAU;QAC3C,MAAM,IAAI,UAAU,GAAG,iBAAiB,KAAK,CAAC,IAAI,CAAC,QAAQ,qEAAqE,CAAC;IACrI;IACA,IAAI,UAAU,aAAa,IAAI,IAAI,KAAK,UAAU;QAC9C,MAAM,IAAI,UAAU,GAAG,iBAAiB,KAAK,CAAC,IAAI,CAAC,QAAQ,wEAAwE,CAAC;IACxI;IACA,IAAI,IAAI,SAAS,IAAI,UAAU,YAAY,IAAI,IAAI,KAAK,WAAW;QAC/D,MAAM,IAAI,UAAU,GAAG,iBAAiB,KAAK,CAAC,IAAI,CAAC,QAAQ,sEAAsE,CAAC;IACtI;IACA,IAAI,IAAI,SAAS,IAAI,UAAU,aAAa,IAAI,IAAI,KAAK,WAAW;QAChE,MAAM,IAAI,UAAU,GAAG,iBAAiB,KAAK,CAAC,IAAI,CAAC,QAAQ,uEAAuE,CAAC;IACvI;AACJ;AACA,MAAM,eAAe,CAAC,KAAK,KAAK;IAC5B,MAAM,YAAY,IAAI,UAAU,CAAC,SAC7B,QAAQ,SACR,IAAI,UAAU,CAAC,YACf,qBAAqB,IAAI,CAAC;IAC9B,IAAI,WAAW;QACX,mBAAmB,KAAK;IAC5B,OACK;QACD,oBAAoB,KAAK,KAAK;IAClC;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/encrypt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nconst check_iv_length_js_1 = require(\"../lib/check_iv_length.js\");\nconst check_cek_length_js_1 = require(\"./check_cek_length.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst cbc_tag_js_1 = require(\"./cbc_tag.js\");\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst crypto_key_js_1 = require(\"../lib/crypto_key.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nconst invalid_key_input_js_1 = require(\"../lib/invalid_key_input.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst ciphers_js_1 = require(\"./ciphers.js\");\nconst is_key_like_js_1 = require(\"./is_key_like.js\");\nfunction cbcEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if ((0, is_key_object_js_1.default)(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!(0, ciphers_js_1.default)(algorithm)) {\n        throw new errors_js_1.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = (0, crypto_1.createCipheriv)(algorithm, encKey, iv);\n    const ciphertext = (0, buffer_utils_js_1.concat)(cipher.update(plaintext), cipher.final());\n    const macSize = parseInt(enc.slice(-3), 10);\n    const tag = (0, cbc_tag_js_1.default)(aad, iv, ciphertext, macSize, macKey, keySize);\n    return { ciphertext, tag };\n}\nfunction gcmEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!(0, ciphers_js_1.default)(algorithm)) {\n        throw new errors_js_1.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = (0, crypto_1.createCipheriv)(algorithm, cek, iv, { authTagLength: 16 });\n    if (aad.byteLength) {\n        cipher.setAAD(aad, { plaintextLength: plaintext.length });\n    }\n    const ciphertext = cipher.update(plaintext);\n    cipher.final();\n    const tag = cipher.getAuthTag();\n    return { ciphertext, tag };\n}\nconst encrypt = (enc, plaintext, cek, iv, aad) => {\n    let key;\n    if ((0, webcrypto_js_1.isCryptoKey)(cek)) {\n        (0, crypto_key_js_1.checkEncCryptoKey)(cek, enc, 'encrypt');\n        key = crypto_1.KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || (0, is_key_object_js_1.default)(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError((0, invalid_key_input_js_1.default)(cek, ...is_key_like_js_1.types, 'Uint8Array'));\n    }\n    (0, check_cek_length_js_1.default)(enc, key);\n    (0, check_iv_length_js_1.default)(enc, iv);\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcEncrypt(enc, plaintext, key, iv, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmEncrypt(enc, plaintext, key, iv, aad);\n        default:\n            throw new errors_js_1.JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\nexports.default = encrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,WAAW,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;IAC5C,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IAC1C,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QACtC,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,SAAS,IAAI,QAAQ,CAAC,WAAW;IACvC,MAAM,SAAS,IAAI,QAAQ,CAAC,GAAG,WAAW;IAC1C,MAAM,YAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC;IACtC,IAAI,CAAC,CAAC,GAAG,aAAa,OAAO,EAAE,YAAY;QACvC,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,4CAA4C,CAAC;IACnG;IACA,MAAM,SAAS,CAAC,GAAG,SAAS,cAAc,EAAE,WAAW,QAAQ;IAC/D,MAAM,aAAa,CAAC,GAAG,kBAAkB,MAAM,EAAE,OAAO,MAAM,CAAC,YAAY,OAAO,KAAK;IACvF,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;IACxC,MAAM,MAAM,CAAC,GAAG,aAAa,OAAO,EAAE,KAAK,IAAI,YAAY,SAAS,QAAQ;IAC5E,OAAO;QAAE;QAAY;IAAI;AAC7B;AACA,SAAS,WAAW,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;IAC5C,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IAC1C,MAAM,YAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC;IACtC,IAAI,CAAC,CAAC,GAAG,aAAa,OAAO,EAAE,YAAY;QACvC,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,4CAA4C,CAAC;IACnG;IACA,MAAM,SAAS,CAAC,GAAG,SAAS,cAAc,EAAE,WAAW,KAAK,IAAI;QAAE,eAAe;IAAG;IACpF,IAAI,IAAI,UAAU,EAAE;QAChB,OAAO,MAAM,CAAC,KAAK;YAAE,iBAAiB,UAAU,MAAM;QAAC;IAC3D;IACA,MAAM,aAAa,OAAO,MAAM,CAAC;IACjC,OAAO,KAAK;IACZ,MAAM,MAAM,OAAO,UAAU;IAC7B,OAAO;QAAE;QAAY;IAAI;AAC7B;AACA,MAAM,UAAU,CAAC,KAAK,WAAW,KAAK,IAAI;IACtC,IAAI;IACJ,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,MAAM;QACtC,CAAC,GAAG,gBAAgB,iBAAiB,EAAE,KAAK,KAAK;QACjD,MAAM,SAAS,SAAS,CAAC,IAAI,CAAC;IAClC,OACK,IAAI,eAAe,cAAc,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QACxE,MAAM;IACV,OACK;QACD,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK,EAAE;IAC5F;IACA,CAAC,GAAG,sBAAsB,OAAO,EAAE,KAAK;IACxC,CAAC,GAAG,qBAAqB,OAAO,EAAE,KAAK;IACvC,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,WAAW,KAAK,WAAW,KAAK,IAAI;QAC/C,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,WAAW,KAAK,WAAW,KAAK,IAAI;QAC/C;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/aesgcmkw.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.unwrap = exports.wrap = void 0;\nconst encrypt_js_1 = require(\"../runtime/encrypt.js\");\nconst decrypt_js_1 = require(\"../runtime/decrypt.js\");\nconst iv_js_1 = require(\"./iv.js\");\nconst base64url_js_1 = require(\"../runtime/base64url.js\");\nasync function wrap(alg, key, cek, iv) {\n    const jweAlgorithm = alg.slice(0, 7);\n    iv || (iv = (0, iv_js_1.default)(jweAlgorithm));\n    const { ciphertext: encryptedKey, tag } = await (0, encrypt_js_1.default)(jweAlgorithm, cek, key, iv, new Uint8Array(0));\n    return { encryptedKey, iv: (0, base64url_js_1.encode)(iv), tag: (0, base64url_js_1.encode)(tag) };\n}\nexports.wrap = wrap;\nasync function unwrap(alg, key, encryptedKey, iv, tag) {\n    const jweAlgorithm = alg.slice(0, 7);\n    return (0, decrypt_js_1.default)(jweAlgorithm, key, encryptedKey, iv, tag, new Uint8Array(0));\n}\nexports.unwrap = unwrap;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,MAAM,GAAG,QAAQ,IAAI,GAAG,KAAK;AACrC,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACjC,MAAM,eAAe,IAAI,KAAK,CAAC,GAAG;IAClC,MAAM,CAAC,KAAK,CAAC,GAAG,QAAQ,OAAO,EAAE,aAAa;IAC9C,MAAM,EAAE,YAAY,YAAY,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,aAAa,OAAO,EAAE,cAAc,KAAK,KAAK,IAAI,IAAI,WAAW;IACrH,OAAO;QAAE;QAAc,IAAI,CAAC,GAAG,eAAe,MAAM,EAAE;QAAK,KAAK,CAAC,GAAG,eAAe,MAAM,EAAE;IAAK;AACpG;AACA,QAAQ,IAAI,GAAG;AACf,eAAe,OAAO,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,GAAG;IACjD,MAAM,eAAe,IAAI,KAAK,CAAC,GAAG;IAClC,OAAO,CAAC,GAAG,aAAa,OAAO,EAAE,cAAc,KAAK,cAAc,IAAI,KAAK,IAAI,WAAW;AAC9F;AACA,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2016, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/decrypt_key_management.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst aeskw_js_1 = require(\"../runtime/aeskw.js\");\nconst ECDH = require(\"../runtime/ecdhes.js\");\nconst pbes2kw_js_1 = require(\"../runtime/pbes2kw.js\");\nconst rsaes_js_1 = require(\"../runtime/rsaes.js\");\nconst base64url_js_1 = require(\"../runtime/base64url.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst cek_js_1 = require(\"../lib/cek.js\");\nconst import_js_1 = require(\"../key/import.js\");\nconst check_key_type_js_1 = require(\"./check_key_type.js\");\nconst is_object_js_1 = require(\"./is_object.js\");\nconst aesgcmkw_js_1 = require(\"./aesgcmkw.js\");\nasync function decryptKeyManagement(alg, key, encrypted<PERSON>ey, jose<PERSON><PERSON>er, options) {\n    (0, check_key_type_js_1.default)(alg, key, 'decrypt');\n    switch (alg) {\n        case 'dir': {\n            if (encryptedKey !== undefined)\n                throw new errors_js_1.JWEInvalid('Encountered unexpected JWE Encrypted Key');\n            return key;\n        }\n        case 'ECDH-ES':\n            if (encryptedKey !== undefined)\n                throw new errors_js_1.JWEInvalid('Encountered unexpected JWE Encrypted Key');\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!(0, is_object_js_1.default)(joseHeader.epk))\n                throw new errors_js_1.JWEInvalid(`JOSE Header \"epk\" (Ephemeral Public Key) missing or invalid`);\n            if (!ECDH.ecdhAllowed(key))\n                throw new errors_js_1.JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            const epk = await (0, import_js_1.importJWK)(joseHeader.epk, alg);\n            let partyUInfo;\n            let partyVInfo;\n            if (joseHeader.apu !== undefined) {\n                if (typeof joseHeader.apu !== 'string')\n                    throw new errors_js_1.JWEInvalid(`JOSE Header \"apu\" (Agreement PartyUInfo) invalid`);\n                try {\n                    partyUInfo = (0, base64url_js_1.decode)(joseHeader.apu);\n                }\n                catch {\n                    throw new errors_js_1.JWEInvalid('Failed to base64url decode the apu');\n                }\n            }\n            if (joseHeader.apv !== undefined) {\n                if (typeof joseHeader.apv !== 'string')\n                    throw new errors_js_1.JWEInvalid(`JOSE Header \"apv\" (Agreement PartyVInfo) invalid`);\n                try {\n                    partyVInfo = (0, base64url_js_1.decode)(joseHeader.apv);\n                }\n                catch {\n                    throw new errors_js_1.JWEInvalid('Failed to base64url decode the apv');\n                }\n            }\n            const sharedSecret = await ECDH.deriveKey(epk, key, alg === 'ECDH-ES' ? joseHeader.enc : alg, alg === 'ECDH-ES' ? (0, cek_js_1.bitLength)(joseHeader.enc) : parseInt(alg.slice(-5, -2), 10), partyUInfo, partyVInfo);\n            if (alg === 'ECDH-ES')\n                return sharedSecret;\n            if (encryptedKey === undefined)\n                throw new errors_js_1.JWEInvalid('JWE Encrypted Key missing');\n            return (0, aeskw_js_1.unwrap)(alg.slice(-6), sharedSecret, encryptedKey);\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (encryptedKey === undefined)\n                throw new errors_js_1.JWEInvalid('JWE Encrypted Key missing');\n            return (0, rsaes_js_1.decrypt)(alg, key, encryptedKey);\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            if (encryptedKey === undefined)\n                throw new errors_js_1.JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.p2c !== 'number')\n                throw new errors_js_1.JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) missing or invalid`);\n            const p2cLimit = (options === null || options === void 0 ? void 0 : options.maxPBES2Count) || 10000;\n            if (joseHeader.p2c > p2cLimit)\n                throw new errors_js_1.JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) out is of acceptable bounds`);\n            if (typeof joseHeader.p2s !== 'string')\n                throw new errors_js_1.JWEInvalid(`JOSE Header \"p2s\" (PBES2 Salt) missing or invalid`);\n            let p2s;\n            try {\n                p2s = (0, base64url_js_1.decode)(joseHeader.p2s);\n            }\n            catch {\n                throw new errors_js_1.JWEInvalid('Failed to base64url decode the p2s');\n            }\n            return (0, pbes2kw_js_1.decrypt)(alg, key, encryptedKey, joseHeader.p2c, p2s);\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (encryptedKey === undefined)\n                throw new errors_js_1.JWEInvalid('JWE Encrypted Key missing');\n            return (0, aeskw_js_1.unwrap)(alg, key, encryptedKey);\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            if (encryptedKey === undefined)\n                throw new errors_js_1.JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.iv !== 'string')\n                throw new errors_js_1.JWEInvalid(`JOSE Header \"iv\" (Initialization Vector) missing or invalid`);\n            if (typeof joseHeader.tag !== 'string')\n                throw new errors_js_1.JWEInvalid(`JOSE Header \"tag\" (Authentication Tag) missing or invalid`);\n            let iv;\n            try {\n                iv = (0, base64url_js_1.decode)(joseHeader.iv);\n            }\n            catch {\n                throw new errors_js_1.JWEInvalid('Failed to base64url decode the iv');\n            }\n            let tag;\n            try {\n                tag = (0, base64url_js_1.decode)(joseHeader.tag);\n            }\n            catch {\n                throw new errors_js_1.JWEInvalid('Failed to base64url decode the tag');\n            }\n            return (0, aesgcmkw_js_1.unwrap)(alg, key, encryptedKey, iv, tag);\n        }\n        default: {\n            throw new errors_js_1.JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n}\nexports.default = decryptKeyManagement;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,qBAAqB,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO;IAC3E,CAAC,GAAG,oBAAoB,OAAO,EAAE,KAAK,KAAK;IAC3C,OAAQ;QACJ,KAAK;YAAO;gBACR,IAAI,iBAAiB,WACjB,MAAM,IAAI,YAAY,UAAU,CAAC;gBACrC,OAAO;YACX;QACA,KAAK;YACD,IAAI,iBAAiB,WACjB,MAAM,IAAI,YAAY,UAAU,CAAC;QACzC,KAAK;QACL,KAAK;QACL,KAAK;YAAkB;gBACnB,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,WAAW,GAAG,GAC3C,MAAM,IAAI,YAAY,UAAU,CAAC,CAAC,2DAA2D,CAAC;gBAClG,IAAI,CAAC,KAAK,WAAW,CAAC,MAClB,MAAM,IAAI,YAAY,gBAAgB,CAAC;gBAC3C,MAAM,MAAM,MAAM,CAAC,GAAG,YAAY,SAAS,EAAE,WAAW,GAAG,EAAE;gBAC7D,IAAI;gBACJ,IAAI;gBACJ,IAAI,WAAW,GAAG,KAAK,WAAW;oBAC9B,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,YAAY,UAAU,CAAC,CAAC,gDAAgD,CAAC;oBACvF,IAAI;wBACA,aAAa,CAAC,GAAG,eAAe,MAAM,EAAE,WAAW,GAAG;oBAC1D,EACA,OAAM;wBACF,MAAM,IAAI,YAAY,UAAU,CAAC;oBACrC;gBACJ;gBACA,IAAI,WAAW,GAAG,KAAK,WAAW;oBAC9B,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,YAAY,UAAU,CAAC,CAAC,gDAAgD,CAAC;oBACvF,IAAI;wBACA,aAAa,CAAC,GAAG,eAAe,MAAM,EAAE,WAAW,GAAG;oBAC1D,EACA,OAAM;wBACF,MAAM,IAAI,YAAY,UAAU,CAAC;oBACrC;gBACJ;gBACA,MAAM,eAAe,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,QAAQ,YAAY,WAAW,GAAG,GAAG,KAAK,QAAQ,YAAY,CAAC,GAAG,SAAS,SAAS,EAAE,WAAW,GAAG,IAAI,SAAS,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY;gBACzM,IAAI,QAAQ,WACR,OAAO;gBACX,IAAI,iBAAiB,WACjB,MAAM,IAAI,YAAY,UAAU,CAAC;gBACrC,OAAO,CAAC,GAAG,WAAW,MAAM,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,cAAc;YAC/D;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACjB,IAAI,iBAAiB,WACjB,MAAM,IAAI,YAAY,UAAU,CAAC;gBACrC,OAAO,CAAC,GAAG,WAAW,OAAO,EAAE,KAAK,KAAK;YAC7C;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAsB;gBACvB,IAAI,iBAAiB,WACjB,MAAM,IAAI,YAAY,UAAU,CAAC;gBACrC,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,YAAY,UAAU,CAAC,CAAC,kDAAkD,CAAC;gBACzF,MAAM,WAAW,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,aAAa,KAAK;gBAC9F,IAAI,WAAW,GAAG,GAAG,UACjB,MAAM,IAAI,YAAY,UAAU,CAAC,CAAC,2DAA2D,CAAC;gBAClG,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,YAAY,UAAU,CAAC,CAAC,iDAAiD,CAAC;gBACxF,IAAI;gBACJ,IAAI;oBACA,MAAM,CAAC,GAAG,eAAe,MAAM,EAAE,WAAW,GAAG;gBACnD,EACA,OAAM;oBACF,MAAM,IAAI,YAAY,UAAU,CAAC;gBACrC;gBACA,OAAO,CAAC,GAAG,aAAa,OAAO,EAAE,KAAK,KAAK,cAAc,WAAW,GAAG,EAAE;YAC7E;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,IAAI,iBAAiB,WACjB,MAAM,IAAI,YAAY,UAAU,CAAC;gBACrC,OAAO,CAAC,GAAG,WAAW,MAAM,EAAE,KAAK,KAAK;YAC5C;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAa;gBACd,IAAI,iBAAiB,WACjB,MAAM,IAAI,YAAY,UAAU,CAAC;gBACrC,IAAI,OAAO,WAAW,EAAE,KAAK,UACzB,MAAM,IAAI,YAAY,UAAU,CAAC,CAAC,2DAA2D,CAAC;gBAClG,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,YAAY,UAAU,CAAC,CAAC,yDAAyD,CAAC;gBAChG,IAAI;gBACJ,IAAI;oBACA,KAAK,CAAC,GAAG,eAAe,MAAM,EAAE,WAAW,EAAE;gBACjD,EACA,OAAM;oBACF,MAAM,IAAI,YAAY,UAAU,CAAC;gBACrC;gBACA,IAAI;gBACJ,IAAI;oBACA,MAAM,CAAC,GAAG,eAAe,MAAM,EAAE,WAAW,GAAG;gBACnD,EACA,OAAM;oBACF,MAAM,IAAI,YAAY,UAAU,CAAC;gBACrC;gBACA,OAAO,CAAC,GAAG,cAAc,MAAM,EAAE,KAAK,KAAK,cAAc,IAAI;YACjE;QACA;YAAS;gBACL,MAAM,IAAI,YAAY,gBAAgB,CAAC;YAC3C;IACJ;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/validate_crit.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst errors_js_1 = require(\"../util/errors.js\");\nfunction validateCrit(Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) {\n    if (joseHeader.crit !== undefined && protectedHeader.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new errors_js_1.JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        else if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n}\nexports.default = validateCrit;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,SAAS,aAAa,GAAG,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,UAAU;IACvF,IAAI,WAAW,IAAI,KAAK,aAAa,gBAAgB,IAAI,KAAK,WAAW;QACrE,MAAM,IAAI,IAAI;IAClB;IACA,IAAI,CAAC,mBAAmB,gBAAgB,IAAI,KAAK,WAAW;QACxD,OAAO,IAAI;IACf;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,gBAAgB,IAAI,KACnC,gBAAgB,IAAI,CAAC,MAAM,KAAK,KAChC,gBAAgB,IAAI,CAAC,IAAI,CAAC,CAAC,QAAU,OAAO,UAAU,YAAY,MAAM,MAAM,KAAK,IAAI;QACvF,MAAM,IAAI,IAAI;IAClB;IACA,IAAI;IACJ,IAAI,qBAAqB,WAAW;QAChC,aAAa,IAAI,IAAI;eAAI,OAAO,OAAO,CAAC;eAAsB,kBAAkB,OAAO;SAAG;IAC9F,OACK;QACD,aAAa;IACjB;IACA,KAAK,MAAM,aAAa,gBAAgB,IAAI,CAAE;QAC1C,IAAI,CAAC,WAAW,GAAG,CAAC,YAAY;YAC5B,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,4BAA4B,EAAE,UAAU,mBAAmB,CAAC;QACxG;QACA,IAAI,UAAU,CAAC,UAAU,KAAK,WAAW;YACrC,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,UAAU,YAAY,CAAC;QACxE,OACK,IAAI,WAAW,GAAG,CAAC,cAAc,eAAe,CAAC,UAAU,KAAK,WAAW;YAC5E,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,UAAU,6BAA6B,CAAC;QACzF;IACJ;IACA,OAAO,IAAI,IAAI,gBAAgB,IAAI;AACvC;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/validate_algorithms.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst validateAlgorithms = (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\nexports.default = validateAlgorithms;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM,qBAAqB,CAAC,QAAQ;IAChC,IAAI,eAAe,aACf,CAAC,CAAC,MAAM,OAAO,CAAC,eAAe,WAAW,IAAI,CAAC,CAAC,IAAM,OAAO,MAAM,SAAS,GAAG;QAC/E,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,OAAO,oCAAoC,CAAC;IACxE;IACA,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,OAAO,IAAI,IAAI;AACnB;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwe/flattened/decrypt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.flattenedDecrypt = void 0;\nconst base64url_js_1 = require(\"../../runtime/base64url.js\");\nconst decrypt_js_1 = require(\"../../runtime/decrypt.js\");\nconst zlib_js_1 = require(\"../../runtime/zlib.js\");\nconst errors_js_1 = require(\"../../util/errors.js\");\nconst is_disjoint_js_1 = require(\"../../lib/is_disjoint.js\");\nconst is_object_js_1 = require(\"../../lib/is_object.js\");\nconst decrypt_key_management_js_1 = require(\"../../lib/decrypt_key_management.js\");\nconst buffer_utils_js_1 = require(\"../../lib/buffer_utils.js\");\nconst cek_js_1 = require(\"../../lib/cek.js\");\nconst validate_crit_js_1 = require(\"../../lib/validate_crit.js\");\nconst validate_algorithms_js_1 = require(\"../../lib/validate_algorithms.js\");\nasync function flattenedDecrypt(jwe, key, options) {\n    var _a;\n    if (!(0, is_object_js_1.default)(jwe)) {\n        throw new errors_js_1.JWEInvalid('Flattened JWE must be an object');\n    }\n    if (jwe.protected === undefined && jwe.header === undefined && jwe.unprotected === undefined) {\n        throw new errors_js_1.JWEInvalid('JOSE Header missing');\n    }\n    if (typeof jwe.iv !== 'string') {\n        throw new errors_js_1.JWEInvalid('JWE Initialization Vector missing or incorrect type');\n    }\n    if (typeof jwe.ciphertext !== 'string') {\n        throw new errors_js_1.JWEInvalid('JWE Ciphertext missing or incorrect type');\n    }\n    if (typeof jwe.tag !== 'string') {\n        throw new errors_js_1.JWEInvalid('JWE Authentication Tag missing or incorrect type');\n    }\n    if (jwe.protected !== undefined && typeof jwe.protected !== 'string') {\n        throw new errors_js_1.JWEInvalid('JWE Protected Header incorrect type');\n    }\n    if (jwe.encrypted_key !== undefined && typeof jwe.encrypted_key !== 'string') {\n        throw new errors_js_1.JWEInvalid('JWE Encrypted Key incorrect type');\n    }\n    if (jwe.aad !== undefined && typeof jwe.aad !== 'string') {\n        throw new errors_js_1.JWEInvalid('JWE AAD incorrect type');\n    }\n    if (jwe.header !== undefined && !(0, is_object_js_1.default)(jwe.header)) {\n        throw new errors_js_1.JWEInvalid('JWE Shared Unprotected Header incorrect type');\n    }\n    if (jwe.unprotected !== undefined && !(0, is_object_js_1.default)(jwe.unprotected)) {\n        throw new errors_js_1.JWEInvalid('JWE Per-Recipient Unprotected Header incorrect type');\n    }\n    let parsedProt;\n    if (jwe.protected) {\n        try {\n            const protectedHeader = (0, base64url_js_1.decode)(jwe.protected);\n            parsedProt = JSON.parse(buffer_utils_js_1.decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new errors_js_1.JWEInvalid('JWE Protected Header is invalid');\n        }\n    }\n    if (!(0, is_disjoint_js_1.default)(parsedProt, jwe.header, jwe.unprotected)) {\n        throw new errors_js_1.JWEInvalid('JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jwe.header,\n        ...jwe.unprotected,\n    };\n    (0, validate_crit_js_1.default)(errors_js_1.JWEInvalid, new Map(), options === null || options === void 0 ? void 0 : options.crit, parsedProt, joseHeader);\n    if (joseHeader.zip !== undefined) {\n        if (!parsedProt || !parsedProt.zip) {\n            throw new errors_js_1.JWEInvalid('JWE \"zip\" (Compression Algorithm) Header MUST be integrity protected');\n        }\n        if (joseHeader.zip !== 'DEF') {\n            throw new errors_js_1.JOSENotSupported('Unsupported JWE \"zip\" (Compression Algorithm) Header Parameter value');\n        }\n    }\n    const { alg, enc } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new errors_js_1.JWEInvalid('missing JWE Algorithm (alg) in JWE Header');\n    }\n    if (typeof enc !== 'string' || !enc) {\n        throw new errors_js_1.JWEInvalid('missing JWE Encryption Algorithm (enc) in JWE Header');\n    }\n    const keyManagementAlgorithms = options && (0, validate_algorithms_js_1.default)('keyManagementAlgorithms', options.keyManagementAlgorithms);\n    const contentEncryptionAlgorithms = options &&\n        (0, validate_algorithms_js_1.default)('contentEncryptionAlgorithms', options.contentEncryptionAlgorithms);\n    if (keyManagementAlgorithms && !keyManagementAlgorithms.has(alg)) {\n        throw new errors_js_1.JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter not allowed');\n    }\n    if (contentEncryptionAlgorithms && !contentEncryptionAlgorithms.has(enc)) {\n        throw new errors_js_1.JOSEAlgNotAllowed('\"enc\" (Encryption Algorithm) Header Parameter not allowed');\n    }\n    let encryptedKey;\n    if (jwe.encrypted_key !== undefined) {\n        try {\n            encryptedKey = (0, base64url_js_1.decode)(jwe.encrypted_key);\n        }\n        catch {\n            throw new errors_js_1.JWEInvalid('Failed to base64url decode the encrypted_key');\n        }\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jwe);\n        resolvedKey = true;\n    }\n    let cek;\n    try {\n        cek = await (0, decrypt_key_management_js_1.default)(alg, key, encryptedKey, joseHeader, options);\n    }\n    catch (err) {\n        if (err instanceof TypeError || err instanceof errors_js_1.JWEInvalid || err instanceof errors_js_1.JOSENotSupported) {\n            throw err;\n        }\n        cek = (0, cek_js_1.default)(enc);\n    }\n    let iv;\n    let tag;\n    try {\n        iv = (0, base64url_js_1.decode)(jwe.iv);\n    }\n    catch {\n        throw new errors_js_1.JWEInvalid('Failed to base64url decode the iv');\n    }\n    try {\n        tag = (0, base64url_js_1.decode)(jwe.tag);\n    }\n    catch {\n        throw new errors_js_1.JWEInvalid('Failed to base64url decode the tag');\n    }\n    const protectedHeader = buffer_utils_js_1.encoder.encode((_a = jwe.protected) !== null && _a !== void 0 ? _a : '');\n    let additionalData;\n    if (jwe.aad !== undefined) {\n        additionalData = (0, buffer_utils_js_1.concat)(protectedHeader, buffer_utils_js_1.encoder.encode('.'), buffer_utils_js_1.encoder.encode(jwe.aad));\n    }\n    else {\n        additionalData = protectedHeader;\n    }\n    let ciphertext;\n    try {\n        ciphertext = (0, base64url_js_1.decode)(jwe.ciphertext);\n    }\n    catch {\n        throw new errors_js_1.JWEInvalid('Failed to base64url decode the ciphertext');\n    }\n    let plaintext = await (0, decrypt_js_1.default)(enc, cek, ciphertext, iv, tag, additionalData);\n    if (joseHeader.zip === 'DEF') {\n        plaintext = await ((options === null || options === void 0 ? void 0 : options.inflateRaw) || zlib_js_1.inflate)(plaintext);\n    }\n    const result = { plaintext };\n    if (jwe.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jwe.aad !== undefined) {\n        try {\n            result.additionalAuthenticatedData = (0, base64url_js_1.decode)(jwe.aad);\n        }\n        catch {\n            throw new errors_js_1.JWEInvalid('Failed to base64url decode the aad');\n        }\n    }\n    if (jwe.unprotected !== undefined) {\n        result.sharedUnprotectedHeader = jwe.unprotected;\n    }\n    if (jwe.header !== undefined) {\n        result.unprotectedHeader = jwe.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\nexports.flattenedDecrypt = flattenedDecrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,iBAAiB,GAAG,EAAE,GAAG,EAAE,OAAO;IAC7C,IAAI;IACJ,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,MAAM;QACnC,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,IAAI,MAAM,KAAK,aAAa,IAAI,WAAW,KAAK,WAAW;QAC1F,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,OAAO,IAAI,EAAE,KAAK,UAAU;QAC5B,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,OAAO,IAAI,UAAU,KAAK,UAAU;QACpC,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,OAAO,IAAI,GAAG,KAAK,UAAU;QAC7B,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,UAAU;QAClE,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,IAAI,aAAa,KAAK,aAAa,OAAO,IAAI,aAAa,KAAK,UAAU;QAC1E,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,IAAI,GAAG,KAAK,aAAa,OAAO,IAAI,GAAG,KAAK,UAAU;QACtD,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,IAAI,MAAM,KAAK,aAAa,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,IAAI,MAAM,GAAG;QACtE,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,IAAI,WAAW,KAAK,aAAa,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,IAAI,WAAW,GAAG;QAChF,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI;IACJ,IAAI,IAAI,SAAS,EAAE;QACf,IAAI;YACA,MAAM,kBAAkB,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,SAAS;YAChE,aAAa,KAAK,KAAK,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC;QAC7D,EACA,OAAM;YACF,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;IACJ;IACA,IAAI,CAAC,CAAC,GAAG,iBAAiB,OAAO,EAAE,YAAY,IAAI,MAAM,EAAE,IAAI,WAAW,GAAG;QACzE,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,aAAa;QACf,GAAG,UAAU;QACb,GAAG,IAAI,MAAM;QACb,GAAG,IAAI,WAAW;IACtB;IACA,CAAC,GAAG,mBAAmB,OAAO,EAAE,YAAY,UAAU,EAAE,IAAI,OAAO,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI,EAAE,YAAY;IAC/I,IAAI,WAAW,GAAG,KAAK,WAAW;QAC9B,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,EAAE;YAChC,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,IAAI,WAAW,GAAG,KAAK,OAAO;YAC1B,MAAM,IAAI,YAAY,gBAAgB,CAAC;QAC3C;IACJ;IACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACrB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,0BAA0B,WAAW,CAAC,GAAG,yBAAyB,OAAO,EAAE,2BAA2B,QAAQ,uBAAuB;IAC3I,MAAM,8BAA8B,WAChC,CAAC,GAAG,yBAAyB,OAAO,EAAE,+BAA+B,QAAQ,2BAA2B;IAC5G,IAAI,2BAA2B,CAAC,wBAAwB,GAAG,CAAC,MAAM;QAC9D,MAAM,IAAI,YAAY,iBAAiB,CAAC;IAC5C;IACA,IAAI,+BAA+B,CAAC,4BAA4B,GAAG,CAAC,MAAM;QACtE,MAAM,IAAI,YAAY,iBAAiB,CAAC;IAC5C;IACA,IAAI;IACJ,IAAI,IAAI,aAAa,KAAK,WAAW;QACjC,IAAI;YACA,eAAe,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,aAAa;QAC/D,EACA,OAAM;YACF,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;IACJ;IACA,IAAI,cAAc;IAClB,IAAI,OAAO,QAAQ,YAAY;QAC3B,MAAM,MAAM,IAAI,YAAY;QAC5B,cAAc;IAClB;IACA,IAAI;IACJ,IAAI;QACA,MAAM,MAAM,CAAC,GAAG,4BAA4B,OAAO,EAAE,KAAK,KAAK,cAAc,YAAY;IAC7F,EACA,OAAO,KAAK;QACR,IAAI,eAAe,aAAa,eAAe,YAAY,UAAU,IAAI,eAAe,YAAY,gBAAgB,EAAE;YAClH,MAAM;QACV;QACA,MAAM,CAAC,GAAG,SAAS,OAAO,EAAE;IAChC;IACA,IAAI;IACJ,IAAI;IACJ,IAAI;QACA,KAAK,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,EAAE;IAC1C,EACA,OAAM;QACF,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI;QACA,MAAM,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,GAAG;IAC5C,EACA,OAAM;QACF,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,kBAAkB,kBAAkB,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,IAAI,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAC/G,IAAI;IACJ,IAAI,IAAI,GAAG,KAAK,WAAW;QACvB,iBAAiB,CAAC,GAAG,kBAAkB,MAAM,EAAE,iBAAiB,kBAAkB,OAAO,CAAC,MAAM,CAAC,MAAM,kBAAkB,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG;IACnJ,OACK;QACD,iBAAiB;IACrB;IACA,IAAI;IACJ,IAAI;QACA,aAAa,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,UAAU;IAC1D,EACA,OAAM;QACF,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,YAAY,MAAM,CAAC,GAAG,aAAa,OAAO,EAAE,KAAK,KAAK,YAAY,IAAI,KAAK;IAC/E,IAAI,WAAW,GAAG,KAAK,OAAO;QAC1B,YAAY,MAAM,CAAC,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,KAAK,UAAU,OAAO,EAAE;IACpH;IACA,MAAM,SAAS;QAAE;IAAU;IAC3B,IAAI,IAAI,SAAS,KAAK,WAAW;QAC7B,OAAO,eAAe,GAAG;IAC7B;IACA,IAAI,IAAI,GAAG,KAAK,WAAW;QACvB,IAAI;YACA,OAAO,2BAA2B,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,GAAG;QAC3E,EACA,OAAM;YACF,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;IACJ;IACA,IAAI,IAAI,WAAW,KAAK,WAAW;QAC/B,OAAO,uBAAuB,GAAG,IAAI,WAAW;IACpD;IACA,IAAI,IAAI,MAAM,KAAK,WAAW;QAC1B,OAAO,iBAAiB,GAAG,IAAI,MAAM;IACzC;IACA,IAAI,aAAa;QACb,OAAO;YAAE,GAAG,MAAM;YAAE;QAAI;IAC5B;IACA,OAAO;AACX;AACA,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwe/compact/decrypt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.compactDecrypt = void 0;\nconst decrypt_js_1 = require(\"../flattened/decrypt.js\");\nconst errors_js_1 = require(\"../../util/errors.js\");\nconst buffer_utils_js_1 = require(\"../../lib/buffer_utils.js\");\nasync function compactDecrypt(jwe, key, options) {\n    if (jwe instanceof Uint8Array) {\n        jwe = buffer_utils_js_1.decoder.decode(jwe);\n    }\n    if (typeof jwe !== 'string') {\n        throw new errors_js_1.JWEInvalid('Compact JWE must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: encryptedKey, 2: iv, 3: ciphertext, 4: tag, length, } = jwe.split('.');\n    if (length !== 5) {\n        throw new errors_js_1.JWEInvalid('Invalid Compact JWE');\n    }\n    const decrypted = await (0, decrypt_js_1.flattenedDecrypt)({\n        ciphertext,\n        iv: (iv || undefined),\n        protected: protectedHeader || undefined,\n        tag: (tag || undefined),\n        encrypted_key: encryptedKey || undefined,\n    }, key, options);\n    const result = { plaintext: decrypted.plaintext, protectedHeader: decrypted.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\nexports.compactDecrypt = compactDecrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,eAAe,GAAG,EAAE,GAAG,EAAE,OAAO;IAC3C,IAAI,eAAe,YAAY;QAC3B,MAAM,kBAAkB,OAAO,CAAC,MAAM,CAAC;IAC3C;IACA,IAAI,OAAO,QAAQ,UAAU;QACzB,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,EAAE,GAAG,eAAe,EAAE,GAAG,YAAY,EAAE,GAAG,EAAE,EAAE,GAAG,UAAU,EAAE,GAAG,GAAG,EAAE,MAAM,EAAG,GAAG,IAAI,KAAK,CAAC;IACjG,IAAI,WAAW,GAAG;QACd,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,YAAY,MAAM,CAAC,GAAG,aAAa,gBAAgB,EAAE;QACvD;QACA,IAAK,MAAM;QACX,WAAW,mBAAmB;QAC9B,KAAM,OAAO;QACb,eAAe,gBAAgB;IACnC,GAAG,KAAK;IACR,MAAM,SAAS;QAAE,WAAW,UAAU,SAAS;QAAE,iBAAiB,UAAU,eAAe;IAAC;IAC5F,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,UAAU,GAAG;QAAC;IAC3C;IACA,OAAO;AACX;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwe/general/decrypt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.generalDecrypt = void 0;\nconst decrypt_js_1 = require(\"../flattened/decrypt.js\");\nconst errors_js_1 = require(\"../../util/errors.js\");\nconst is_object_js_1 = require(\"../../lib/is_object.js\");\nasync function generalDecrypt(jwe, key, options) {\n    if (!(0, is_object_js_1.default)(jwe)) {\n        throw new errors_js_1.JWEInvalid('General JWE must be an object');\n    }\n    if (!Array.isArray(jwe.recipients) || !jwe.recipients.every(is_object_js_1.default)) {\n        throw new errors_js_1.JWEInvalid('JWE Recipients missing or incorrect type');\n    }\n    if (!jwe.recipients.length) {\n        throw new errors_js_1.JWEInvalid('JWE Recipients has no members');\n    }\n    for (const recipient of jwe.recipients) {\n        try {\n            return await (0, decrypt_js_1.flattenedDecrypt)({\n                aad: jwe.aad,\n                ciphertext: jwe.ciphertext,\n                encrypted_key: recipient.encrypted_key,\n                header: recipient.header,\n                iv: jwe.iv,\n                protected: jwe.protected,\n                tag: jwe.tag,\n                unprotected: jwe.unprotected,\n            }, key, options);\n        }\n        catch {\n        }\n    }\n    throw new errors_js_1.JWEDecryptionFailed();\n}\nexports.generalDecrypt = generalDecrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,eAAe,GAAG,EAAE,GAAG,EAAE,OAAO;IAC3C,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,MAAM;QACnC,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,OAAO,GAAG;QACjF,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE;QACxB,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,KAAK,MAAM,aAAa,IAAI,UAAU,CAAE;QACpC,IAAI;YACA,OAAO,MAAM,CAAC,GAAG,aAAa,gBAAgB,EAAE;gBAC5C,KAAK,IAAI,GAAG;gBACZ,YAAY,IAAI,UAAU;gBAC1B,eAAe,UAAU,aAAa;gBACtC,QAAQ,UAAU,MAAM;gBACxB,IAAI,IAAI,EAAE;gBACV,WAAW,IAAI,SAAS;gBACxB,KAAK,IAAI,GAAG;gBACZ,aAAa,IAAI,WAAW;YAChC,GAAG,KAAK;QACZ,EACA,OAAM,CACN;IACJ;IACA,MAAM,IAAI,YAAY,mBAAmB;AAC7C;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/asn1_sequence_decoder.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst tagInteger = 0x02;\nconst tagSequence = 0x30;\nclass Asn1SequenceDecoder {\n    constructor(buffer) {\n        if (buffer[0] !== tagSequence) {\n            throw new TypeError();\n        }\n        this.buffer = buffer;\n        this.offset = 1;\n        const len = this.decodeLength();\n        if (len !== buffer.length - this.offset) {\n            throw new TypeError();\n        }\n    }\n    decodeLength() {\n        let length = this.buffer[this.offset++];\n        if (length & 0x80) {\n            const nBytes = length & ~0x80;\n            length = 0;\n            for (let i = 0; i < nBytes; i++)\n                length = (length << 8) | this.buffer[this.offset + i];\n            this.offset += nBytes;\n        }\n        return length;\n    }\n    unsignedInteger() {\n        if (this.buffer[this.offset++] !== tagInteger) {\n            throw new TypeError();\n        }\n        let length = this.decodeLength();\n        if (this.buffer[this.offset] === 0) {\n            this.offset++;\n            length--;\n        }\n        const result = this.buffer.slice(this.offset, this.offset + length);\n        this.offset += length;\n        return result;\n    }\n    end() {\n        if (this.offset !== this.buffer.length) {\n            throw new TypeError();\n        }\n    }\n}\nexports.default = Asn1SequenceDecoder;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM;IACF,YAAY,MAAM,CAAE;QAChB,IAAI,MAAM,CAAC,EAAE,KAAK,aAAa;YAC3B,MAAM,IAAI;QACd;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,MAAM,MAAM,IAAI,CAAC,YAAY;QAC7B,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YACrC,MAAM,IAAI;QACd;IACJ;IACA,eAAe;QACX,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG;QACvC,IAAI,SAAS,MAAM;YACf,MAAM,SAAS,SAAS,CAAC;YACzB,SAAS;YACT,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IACxB,SAAS,AAAC,UAAU,IAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE;YACzD,IAAI,CAAC,MAAM,IAAI;QACnB;QACA,OAAO;IACX;IACA,kBAAkB;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,YAAY;YAC3C,MAAM,IAAI;QACd;QACA,IAAI,SAAS,IAAI,CAAC,YAAY;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;YAChC,IAAI,CAAC,MAAM;YACX;QACJ;QACA,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG;QAC5D,IAAI,CAAC,MAAM,IAAI;QACf,OAAO;IACX;IACA,MAAM;QACF,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACpC,MAAM,IAAI;QACd;IACJ;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/key_to_jwk.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nconst base64url_js_1 = require(\"./base64url.js\");\nconst asn1_sequence_decoder_js_1 = require(\"./asn1_sequence_decoder.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst get_named_curve_js_1 = require(\"./get_named_curve.js\");\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst is_key_object_js_1 = require(\"./is_key_object.js\");\nconst invalid_key_input_js_1 = require(\"../lib/invalid_key_input.js\");\nconst is_key_like_js_1 = require(\"./is_key_like.js\");\nconst flags_js_1 = require(\"./flags.js\");\nconst keyToJWK = (key) => {\n    let keyObject;\n    if ((0, webcrypto_js_1.isCryptoKey)(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = crypto_1.KeyObject.from(key);\n    }\n    else if ((0, is_key_object_js_1.default)(key)) {\n        keyObject = key;\n    }\n    else if (key instanceof Uint8Array) {\n        return {\n            kty: 'oct',\n            k: (0, base64url_js_1.encode)(key),\n        };\n    }\n    else {\n        throw new TypeError((0, invalid_key_input_js_1.default)(key, ...is_key_like_js_1.types, 'Uint8Array'));\n    }\n    if (flags_js_1.jwkExport) {\n        if (keyObject.type !== 'secret' &&\n            !['rsa', 'ec', 'ed25519', 'x25519', 'ed448', 'x448'].includes(keyObject.asymmetricKeyType)) {\n            throw new errors_js_1.JOSENotSupported('Unsupported key asymmetricKeyType');\n        }\n        return keyObject.export({ format: 'jwk' });\n    }\n    switch (keyObject.type) {\n        case 'secret':\n            return {\n                kty: 'oct',\n                k: (0, base64url_js_1.encode)(keyObject.export()),\n            };\n        case 'private':\n        case 'public': {\n            switch (keyObject.asymmetricKeyType) {\n                case 'rsa': {\n                    const der = keyObject.export({ format: 'der', type: 'pkcs1' });\n                    const dec = new asn1_sequence_decoder_js_1.default(der);\n                    if (keyObject.type === 'private') {\n                        dec.unsignedInteger();\n                    }\n                    const n = (0, base64url_js_1.encode)(dec.unsignedInteger());\n                    const e = (0, base64url_js_1.encode)(dec.unsignedInteger());\n                    let jwk;\n                    if (keyObject.type === 'private') {\n                        jwk = {\n                            d: (0, base64url_js_1.encode)(dec.unsignedInteger()),\n                            p: (0, base64url_js_1.encode)(dec.unsignedInteger()),\n                            q: (0, base64url_js_1.encode)(dec.unsignedInteger()),\n                            dp: (0, base64url_js_1.encode)(dec.unsignedInteger()),\n                            dq: (0, base64url_js_1.encode)(dec.unsignedInteger()),\n                            qi: (0, base64url_js_1.encode)(dec.unsignedInteger()),\n                        };\n                    }\n                    dec.end();\n                    return { kty: 'RSA', n, e, ...jwk };\n                }\n                case 'ec': {\n                    const crv = (0, get_named_curve_js_1.default)(keyObject);\n                    let len;\n                    let offset;\n                    let correction;\n                    switch (crv) {\n                        case 'secp256k1':\n                            len = 64;\n                            offset = 31 + 2;\n                            correction = -1;\n                            break;\n                        case 'P-256':\n                            len = 64;\n                            offset = 34 + 2;\n                            correction = -1;\n                            break;\n                        case 'P-384':\n                            len = 96;\n                            offset = 33 + 2;\n                            correction = -3;\n                            break;\n                        case 'P-521':\n                            len = 132;\n                            offset = 33 + 2;\n                            correction = -3;\n                            break;\n                        default:\n                            throw new errors_js_1.JOSENotSupported('Unsupported curve');\n                    }\n                    if (keyObject.type === 'public') {\n                        const der = keyObject.export({ type: 'spki', format: 'der' });\n                        return {\n                            kty: 'EC',\n                            crv,\n                            x: (0, base64url_js_1.encode)(der.subarray(-len, -len / 2)),\n                            y: (0, base64url_js_1.encode)(der.subarray(-len / 2)),\n                        };\n                    }\n                    const der = keyObject.export({ type: 'pkcs8', format: 'der' });\n                    if (der.length < 100) {\n                        offset += correction;\n                    }\n                    return {\n                        ...keyToJWK((0, crypto_1.createPublicKey)(keyObject)),\n                        d: (0, base64url_js_1.encode)(der.subarray(offset, offset + len / 2)),\n                    };\n                }\n                case 'ed25519':\n                case 'x25519': {\n                    const crv = (0, get_named_curve_js_1.default)(keyObject);\n                    if (keyObject.type === 'public') {\n                        const der = keyObject.export({ type: 'spki', format: 'der' });\n                        return {\n                            kty: 'OKP',\n                            crv,\n                            x: (0, base64url_js_1.encode)(der.subarray(-32)),\n                        };\n                    }\n                    const der = keyObject.export({ type: 'pkcs8', format: 'der' });\n                    return {\n                        ...keyToJWK((0, crypto_1.createPublicKey)(keyObject)),\n                        d: (0, base64url_js_1.encode)(der.subarray(-32)),\n                    };\n                }\n                case 'ed448':\n                case 'x448': {\n                    const crv = (0, get_named_curve_js_1.default)(keyObject);\n                    if (keyObject.type === 'public') {\n                        const der = keyObject.export({ type: 'spki', format: 'der' });\n                        return {\n                            kty: 'OKP',\n                            crv,\n                            x: (0, base64url_js_1.encode)(der.subarray(crv === 'Ed448' ? -57 : -56)),\n                        };\n                    }\n                    const der = keyObject.export({ type: 'pkcs8', format: 'der' });\n                    return {\n                        ...keyToJWK((0, crypto_1.createPublicKey)(keyObject)),\n                        d: (0, base64url_js_1.encode)(der.subarray(crv === 'Ed448' ? -57 : -56)),\n                    };\n                }\n                default:\n                    throw new errors_js_1.JOSENotSupported('Unsupported key asymmetricKeyType');\n            }\n        }\n        default:\n            throw new errors_js_1.JOSENotSupported('Unsupported key type');\n    }\n};\nexports.default = keyToJWK;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,WAAW,CAAC;IACd,IAAI;IACJ,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,MAAM;QACtC,IAAI,CAAC,IAAI,WAAW,EAAE;YAClB,MAAM,IAAI,UAAU;QACxB;QACA,YAAY,SAAS,SAAS,CAAC,IAAI,CAAC;IACxC,OACK,IAAI,CAAC,GAAG,mBAAmB,OAAO,EAAE,MAAM;QAC3C,YAAY;IAChB,OACK,IAAI,eAAe,YAAY;QAChC,OAAO;YACH,KAAK;YACL,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE;QAClC;IACJ,OACK;QACD,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK,EAAE;IAC5F;IACA,IAAI,WAAW,SAAS,EAAE;QACtB,IAAI,UAAU,IAAI,KAAK,YACnB,CAAC;YAAC;YAAO;YAAM;YAAW;YAAU;YAAS;SAAO,CAAC,QAAQ,CAAC,UAAU,iBAAiB,GAAG;YAC5F,MAAM,IAAI,YAAY,gBAAgB,CAAC;QAC3C;QACA,OAAO,UAAU,MAAM,CAAC;YAAE,QAAQ;QAAM;IAC5C;IACA,OAAQ,UAAU,IAAI;QAClB,KAAK;YACD,OAAO;gBACH,KAAK;gBACL,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,UAAU,MAAM;YAClD;QACJ,KAAK;QACL,KAAK;YAAU;gBACX,OAAQ,UAAU,iBAAiB;oBAC/B,KAAK;wBAAO;4BACR,MAAM,MAAM,UAAU,MAAM,CAAC;gCAAE,QAAQ;gCAAO,MAAM;4BAAQ;4BAC5D,MAAM,MAAM,IAAI,2BAA2B,OAAO,CAAC;4BACnD,IAAI,UAAU,IAAI,KAAK,WAAW;gCAC9B,IAAI,eAAe;4BACvB;4BACA,MAAM,IAAI,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,eAAe;4BACxD,MAAM,IAAI,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,eAAe;4BACxD,IAAI;4BACJ,IAAI,UAAU,IAAI,KAAK,WAAW;gCAC9B,MAAM;oCACF,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,eAAe;oCACjD,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,eAAe;oCACjD,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,eAAe;oCACjD,IAAI,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,eAAe;oCAClD,IAAI,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,eAAe;oCAClD,IAAI,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,eAAe;gCACtD;4BACJ;4BACA,IAAI,GAAG;4BACP,OAAO;gCAAE,KAAK;gCAAO;gCAAG;gCAAG,GAAG,GAAG;4BAAC;wBACtC;oBACA,KAAK;wBAAM;4BACP,MAAM,MAAM,CAAC,GAAG,qBAAqB,OAAO,EAAE;4BAC9C,IAAI;4BACJ,IAAI;4BACJ,IAAI;4BACJ,OAAQ;gCACJ,KAAK;oCACD,MAAM;oCACN,SAAS,KAAK;oCACd,aAAa,CAAC;oCACd;gCACJ,KAAK;oCACD,MAAM;oCACN,SAAS,KAAK;oCACd,aAAa,CAAC;oCACd;gCACJ,KAAK;oCACD,MAAM;oCACN,SAAS,KAAK;oCACd,aAAa,CAAC;oCACd;gCACJ,KAAK;oCACD,MAAM;oCACN,SAAS,KAAK;oCACd,aAAa,CAAC;oCACd;gCACJ;oCACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;4BAC/C;4BACA,IAAI,UAAU,IAAI,KAAK,UAAU;gCAC7B,MAAM,MAAM,UAAU,MAAM,CAAC;oCAAE,MAAM;oCAAQ,QAAQ;gCAAM;gCAC3D,OAAO;oCACH,KAAK;oCACL;oCACA,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,QAAQ,CAAC,CAAC,KAAK,CAAC,MAAM;oCACxD,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,QAAQ,CAAC,CAAC,MAAM;gCACtD;4BACJ;4BACA,MAAM,MAAM,UAAU,MAAM,CAAC;gCAAE,MAAM;gCAAS,QAAQ;4BAAM;4BAC5D,IAAI,IAAI,MAAM,GAAG,KAAK;gCAClB,UAAU;4BACd;4BACA,OAAO;gCACH,GAAG,SAAS,CAAC,GAAG,SAAS,eAAe,EAAE,WAAW;gCACrD,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,QAAQ,CAAC,QAAQ,SAAS,MAAM;4BACtE;wBACJ;oBACA,KAAK;oBACL,KAAK;wBAAU;4BACX,MAAM,MAAM,CAAC,GAAG,qBAAqB,OAAO,EAAE;4BAC9C,IAAI,UAAU,IAAI,KAAK,UAAU;gCAC7B,MAAM,MAAM,UAAU,MAAM,CAAC;oCAAE,MAAM;oCAAQ,QAAQ;gCAAM;gCAC3D,OAAO;oCACH,KAAK;oCACL;oCACA,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,QAAQ,CAAC,CAAC;gCAChD;4BACJ;4BACA,MAAM,MAAM,UAAU,MAAM,CAAC;gCAAE,MAAM;gCAAS,QAAQ;4BAAM;4BAC5D,OAAO;gCACH,GAAG,SAAS,CAAC,GAAG,SAAS,eAAe,EAAE,WAAW;gCACrD,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,QAAQ,CAAC,CAAC;4BAChD;wBACJ;oBACA,KAAK;oBACL,KAAK;wBAAQ;4BACT,MAAM,MAAM,CAAC,GAAG,qBAAqB,OAAO,EAAE;4BAC9C,IAAI,UAAU,IAAI,KAAK,UAAU;gCAC7B,MAAM,MAAM,UAAU,MAAM,CAAC;oCAAE,MAAM;oCAAQ,QAAQ;gCAAM;gCAC3D,OAAO;oCACH,KAAK;oCACL;oCACA,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,QAAQ,CAAC,QAAQ,UAAU,CAAC,KAAK,CAAC;gCACxE;4BACJ;4BACA,MAAM,MAAM,UAAU,MAAM,CAAC;gCAAE,MAAM;gCAAS,QAAQ;4BAAM;4BAC5D,OAAO;gCACH,GAAG,SAAS,CAAC,GAAG,SAAS,eAAe,EAAE,WAAW;gCACrD,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,QAAQ,CAAC,QAAQ,UAAU,CAAC,KAAK,CAAC;4BACxE;wBACJ;oBACA;wBACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;gBAC/C;YACJ;QACA;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/key/export.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.exportJWK = exports.exportPKCS8 = exports.exportSPKI = void 0;\nconst asn1_js_1 = require(\"../runtime/asn1.js\");\nconst asn1_js_2 = require(\"../runtime/asn1.js\");\nconst key_to_jwk_js_1 = require(\"../runtime/key_to_jwk.js\");\nasync function exportSPKI(key) {\n    return (0, asn1_js_1.toSPKI)(key);\n}\nexports.exportSPKI = exportSPKI;\nasync function exportPKCS8(key) {\n    return (0, asn1_js_2.toPKCS8)(key);\n}\nexports.exportPKCS8 = exportPKCS8;\nasync function exportJWK(key) {\n    return (0, key_to_jwk_js_1.default)(key);\n}\nexports.exportJWK = exportJWK;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,QAAQ,WAAW,GAAG,QAAQ,UAAU,GAAG,KAAK;AACpE,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,WAAW,GAAG;IACzB,OAAO,CAAC,GAAG,UAAU,MAAM,EAAE;AACjC;AACA,QAAQ,UAAU,GAAG;AACrB,eAAe,YAAY,GAAG;IAC1B,OAAO,CAAC,GAAG,UAAU,OAAO,EAAE;AAClC;AACA,QAAQ,WAAW,GAAG;AACtB,eAAe,UAAU,GAAG;IACxB,OAAO,CAAC,GAAG,gBAAgB,OAAO,EAAE;AACxC;AACA,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2736, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/encrypt_key_management.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst aeskw_js_1 = require(\"../runtime/aeskw.js\");\nconst ECDH = require(\"../runtime/ecdhes.js\");\nconst pbes2kw_js_1 = require(\"../runtime/pbes2kw.js\");\nconst rsaes_js_1 = require(\"../runtime/rsaes.js\");\nconst base64url_js_1 = require(\"../runtime/base64url.js\");\nconst cek_js_1 = require(\"../lib/cek.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst export_js_1 = require(\"../key/export.js\");\nconst check_key_type_js_1 = require(\"./check_key_type.js\");\nconst aesgcmkw_js_1 = require(\"./aesgcmkw.js\");\nasync function encryptKeyManagement(alg, enc, key, providedCek, providedParameters = {}) {\n    let encryptedKey;\n    let parameters;\n    let cek;\n    (0, check_key_type_js_1.default)(alg, key, 'encrypt');\n    switch (alg) {\n        case 'dir': {\n            cek = key;\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!ECDH.ecdhAllowed(key)) {\n                throw new errors_js_1.JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            }\n            const { apu, apv } = providedParameters;\n            let { epk: ephemeralKey } = providedParameters;\n            ephemeralKey || (ephemeralKey = (await ECDH.generateEpk(key)).privateKey);\n            const { x, y, crv, kty } = await (0, export_js_1.exportJWK)(ephemeralKey);\n            const sharedSecret = await ECDH.deriveKey(key, ephemeralKey, alg === 'ECDH-ES' ? enc : alg, alg === 'ECDH-ES' ? (0, cek_js_1.bitLength)(enc) : parseInt(alg.slice(-5, -2), 10), apu, apv);\n            parameters = { epk: { x, crv, kty } };\n            if (kty === 'EC')\n                parameters.epk.y = y;\n            if (apu)\n                parameters.apu = (0, base64url_js_1.encode)(apu);\n            if (apv)\n                parameters.apv = (0, base64url_js_1.encode)(apv);\n            if (alg === 'ECDH-ES') {\n                cek = sharedSecret;\n                break;\n            }\n            cek = providedCek || (0, cek_js_1.default)(enc);\n            const kwAlg = alg.slice(-6);\n            encryptedKey = await (0, aeskw_js_1.wrap)(kwAlg, sharedSecret, cek);\n            break;\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            cek = providedCek || (0, cek_js_1.default)(enc);\n            encryptedKey = await (0, rsaes_js_1.encrypt)(alg, key, cek);\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            cek = providedCek || (0, cek_js_1.default)(enc);\n            const { p2c, p2s } = providedParameters;\n            ({ encryptedKey, ...parameters } = await (0, pbes2kw_js_1.encrypt)(alg, key, cek, p2c, p2s));\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            cek = providedCek || (0, cek_js_1.default)(enc);\n            encryptedKey = await (0, aeskw_js_1.wrap)(alg, key, cek);\n            break;\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            cek = providedCek || (0, cek_js_1.default)(enc);\n            const { iv } = providedParameters;\n            ({ encryptedKey, ...parameters } = await (0, aesgcmkw_js_1.wrap)(alg, key, cek, iv));\n            break;\n        }\n        default: {\n            throw new errors_js_1.JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n    return { cek, encryptedKey, parameters };\n}\nexports.default = encryptKeyManagement;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,qBAAqB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,WAAW,EAAE,qBAAqB,CAAC,CAAC;IACnF,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,CAAC,GAAG,oBAAoB,OAAO,EAAE,KAAK,KAAK;IAC3C,OAAQ;QACJ,KAAK;YAAO;gBACR,MAAM;gBACN;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAkB;gBACnB,IAAI,CAAC,KAAK,WAAW,CAAC,MAAM;oBACxB,MAAM,IAAI,YAAY,gBAAgB,CAAC;gBAC3C;gBACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;gBACrB,IAAI,EAAE,KAAK,YAAY,EAAE,GAAG;gBAC5B,gBAAgB,CAAC,eAAe,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE,UAAU;gBACxE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,YAAY,SAAS,EAAE;gBAC5D,MAAM,eAAe,MAAM,KAAK,SAAS,CAAC,KAAK,cAAc,QAAQ,YAAY,MAAM,KAAK,QAAQ,YAAY,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,SAAS,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK;gBACrL,aAAa;oBAAE,KAAK;wBAAE;wBAAG;wBAAK;oBAAI;gBAAE;gBACpC,IAAI,QAAQ,MACR,WAAW,GAAG,CAAC,CAAC,GAAG;gBACvB,IAAI,KACA,WAAW,GAAG,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE;gBAChD,IAAI,KACA,WAAW,GAAG,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE;gBAChD,IAAI,QAAQ,WAAW;oBACnB,MAAM;oBACN;gBACJ;gBACA,MAAM,eAAe,CAAC,GAAG,SAAS,OAAO,EAAE;gBAC3C,MAAM,QAAQ,IAAI,KAAK,CAAC,CAAC;gBACzB,eAAe,MAAM,CAAC,GAAG,WAAW,IAAI,EAAE,OAAO,cAAc;gBAC/D;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACjB,MAAM,eAAe,CAAC,GAAG,SAAS,OAAO,EAAE;gBAC3C,eAAe,MAAM,CAAC,GAAG,WAAW,OAAO,EAAE,KAAK,KAAK;gBACvD;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAsB;gBACvB,MAAM,eAAe,CAAC,GAAG,SAAS,OAAO,EAAE;gBAC3C,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;gBACrB,CAAC,EAAE,YAAY,EAAE,GAAG,YAAY,GAAG,MAAM,CAAC,GAAG,aAAa,OAAO,EAAE,KAAK,KAAK,KAAK,KAAK,IAAI;gBAC3F;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,MAAM,eAAe,CAAC,GAAG,SAAS,OAAO,EAAE;gBAC3C,eAAe,MAAM,CAAC,GAAG,WAAW,IAAI,EAAE,KAAK,KAAK;gBACpD;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAa;gBACd,MAAM,eAAe,CAAC,GAAG,SAAS,OAAO,EAAE;gBAC3C,MAAM,EAAE,EAAE,EAAE,GAAG;gBACf,CAAC,EAAE,YAAY,EAAE,GAAG,YAAY,GAAG,MAAM,CAAC,GAAG,cAAc,IAAI,EAAE,KAAK,KAAK,KAAK,GAAG;gBACnF;YACJ;QACA;YAAS;gBACL,MAAM,IAAI,YAAY,gBAAgB,CAAC;YAC3C;IACJ;IACA,OAAO;QAAE;QAAK;QAAc;IAAW;AAC3C;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2846, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwe/flattened/encrypt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FlattenedEncrypt = exports.unprotected = void 0;\nconst base64url_js_1 = require(\"../../runtime/base64url.js\");\nconst encrypt_js_1 = require(\"../../runtime/encrypt.js\");\nconst zlib_js_1 = require(\"../../runtime/zlib.js\");\nconst iv_js_1 = require(\"../../lib/iv.js\");\nconst encrypt_key_management_js_1 = require(\"../../lib/encrypt_key_management.js\");\nconst errors_js_1 = require(\"../../util/errors.js\");\nconst is_disjoint_js_1 = require(\"../../lib/is_disjoint.js\");\nconst buffer_utils_js_1 = require(\"../../lib/buffer_utils.js\");\nconst validate_crit_js_1 = require(\"../../lib/validate_crit.js\");\nexports.unprotected = Symbol();\nclass FlattenedEncrypt {\n    constructor(plaintext) {\n        if (!(plaintext instanceof Uint8Array)) {\n            throw new TypeError('plaintext must be an instance of Uint8Array');\n        }\n        this._plaintext = plaintext;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this._sharedUnprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this._sharedUnprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this._aad = aad;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    async encrypt(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader && !this._sharedUnprotectedHeader) {\n            throw new errors_js_1.JWEInvalid('either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()');\n        }\n        if (!(0, is_disjoint_js_1.default)(this._protectedHeader, this._unprotectedHeader, this._sharedUnprotectedHeader)) {\n            throw new errors_js_1.JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n            ...this._sharedUnprotectedHeader,\n        };\n        (0, validate_crit_js_1.default)(errors_js_1.JWEInvalid, new Map(), options === null || options === void 0 ? void 0 : options.crit, this._protectedHeader, joseHeader);\n        if (joseHeader.zip !== undefined) {\n            if (!this._protectedHeader || !this._protectedHeader.zip) {\n                throw new errors_js_1.JWEInvalid('JWE \"zip\" (Compression Algorithm) Header MUST be integrity protected');\n            }\n            if (joseHeader.zip !== 'DEF') {\n                throw new errors_js_1.JOSENotSupported('Unsupported JWE \"zip\" (Compression Algorithm) Header Parameter value');\n            }\n        }\n        const { alg, enc } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new errors_js_1.JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        if (typeof enc !== 'string' || !enc) {\n            throw new errors_js_1.JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n        }\n        let encryptedKey;\n        if (alg === 'dir') {\n            if (this._cek) {\n                throw new TypeError('setContentEncryptionKey cannot be called when using Direct Encryption');\n            }\n        }\n        else if (alg === 'ECDH-ES') {\n            if (this._cek) {\n                throw new TypeError('setContentEncryptionKey cannot be called when using Direct Key Agreement');\n            }\n        }\n        let cek;\n        {\n            let parameters;\n            ({ cek, encryptedKey, parameters } = await (0, encrypt_key_management_js_1.default)(alg, enc, key, this._cek, this._keyManagementParameters));\n            if (parameters) {\n                if (options && exports.unprotected in options) {\n                    if (!this._unprotectedHeader) {\n                        this.setUnprotectedHeader(parameters);\n                    }\n                    else {\n                        this._unprotectedHeader = { ...this._unprotectedHeader, ...parameters };\n                    }\n                }\n                else {\n                    if (!this._protectedHeader) {\n                        this.setProtectedHeader(parameters);\n                    }\n                    else {\n                        this._protectedHeader = { ...this._protectedHeader, ...parameters };\n                    }\n                }\n            }\n        }\n        this._iv || (this._iv = (0, iv_js_1.default)(enc));\n        let additionalData;\n        let protectedHeader;\n        let aadMember;\n        if (this._protectedHeader) {\n            protectedHeader = buffer_utils_js_1.encoder.encode((0, base64url_js_1.encode)(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = buffer_utils_js_1.encoder.encode('');\n        }\n        if (this._aad) {\n            aadMember = (0, base64url_js_1.encode)(this._aad);\n            additionalData = (0, buffer_utils_js_1.concat)(protectedHeader, buffer_utils_js_1.encoder.encode('.'), buffer_utils_js_1.encoder.encode(aadMember));\n        }\n        else {\n            additionalData = protectedHeader;\n        }\n        let ciphertext;\n        let tag;\n        if (joseHeader.zip === 'DEF') {\n            const deflated = await ((options === null || options === void 0 ? void 0 : options.deflateRaw) || zlib_js_1.deflate)(this._plaintext);\n            ({ ciphertext, tag } = await (0, encrypt_js_1.default)(enc, deflated, cek, this._iv, additionalData));\n        }\n        else {\n            ;\n            ({ ciphertext, tag } = await (0, encrypt_js_1.default)(enc, this._plaintext, cek, this._iv, additionalData));\n        }\n        const jwe = {\n            ciphertext: (0, base64url_js_1.encode)(ciphertext),\n            iv: (0, base64url_js_1.encode)(this._iv),\n            tag: (0, base64url_js_1.encode)(tag),\n        };\n        if (encryptedKey) {\n            jwe.encrypted_key = (0, base64url_js_1.encode)(encryptedKey);\n        }\n        if (aadMember) {\n            jwe.aad = aadMember;\n        }\n        if (this._protectedHeader) {\n            jwe.protected = buffer_utils_js_1.decoder.decode(protectedHeader);\n        }\n        if (this._sharedUnprotectedHeader) {\n            jwe.unprotected = this._sharedUnprotectedHeader;\n        }\n        if (this._unprotectedHeader) {\n            jwe.header = this._unprotectedHeader;\n        }\n        return jwe;\n    }\n}\nexports.FlattenedEncrypt = FlattenedEncrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,QAAQ,WAAW,GAAG,KAAK;AACtD,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,QAAQ,WAAW,GAAG;AACtB,MAAM;IACF,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,CAAC,qBAAqB,UAAU,GAAG;YACpC,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,UAAU,GAAG;IACtB;IACA,2BAA2B,UAAU,EAAE;QACnC,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,wBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,2BAA2B,uBAAuB,EAAE;QAChD,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,wBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,qBAAqB,iBAAiB,EAAE;QACpC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,kBAAkB,GAAG;QAC1B,OAAO,IAAI;IACf;IACA,+BAA+B,GAAG,EAAE;QAChC,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,IAAI;IACf;IACA,wBAAwB,GAAG,EAAE;QACzB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,IAAI;IACf;IACA,wBAAwB,EAAE,EAAE;QACxB,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,GAAG,GAAG;QACX,OAAO,IAAI;IACf;IACA,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE;QACxB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;YACtF,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,IAAI,CAAC,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,wBAAwB,GAAG;YAC/G,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,MAAM,aAAa;YACf,GAAG,IAAI,CAAC,gBAAgB;YACxB,GAAG,IAAI,CAAC,kBAAkB;YAC1B,GAAG,IAAI,CAAC,wBAAwB;QACpC;QACA,CAAC,GAAG,mBAAmB,OAAO,EAAE,YAAY,UAAU,EAAE,IAAI,OAAO,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE;QAC1J,IAAI,WAAW,GAAG,KAAK,WAAW;YAC9B,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACtD,MAAM,IAAI,YAAY,UAAU,CAAC;YACrC;YACA,IAAI,WAAW,GAAG,KAAK,OAAO;gBAC1B,MAAM,IAAI,YAAY,gBAAgB,CAAC;YAC3C;QACJ;QACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;QACrB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;YACjC,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;YACjC,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,IAAI;QACJ,IAAI,QAAQ,OAAO;YACf,IAAI,IAAI,CAAC,IAAI,EAAE;gBACX,MAAM,IAAI,UAAU;YACxB;QACJ,OACK,IAAI,QAAQ,WAAW;YACxB,IAAI,IAAI,CAAC,IAAI,EAAE;gBACX,MAAM,IAAI,UAAU;YACxB;QACJ;QACA,IAAI;QACJ;YACI,IAAI;YACJ,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC,GAAG,4BAA4B,OAAO,EAAE,KAAK,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,wBAAwB,CAAC;YAC5I,IAAI,YAAY;gBACZ,IAAI,WAAW,QAAQ,WAAW,IAAI,SAAS;oBAC3C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;wBAC1B,IAAI,CAAC,oBAAoB,CAAC;oBAC9B,OACK;wBACD,IAAI,CAAC,kBAAkB,GAAG;4BAAE,GAAG,IAAI,CAAC,kBAAkB;4BAAE,GAAG,UAAU;wBAAC;oBAC1E;gBACJ,OACK;oBACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;wBACxB,IAAI,CAAC,kBAAkB,CAAC;oBAC5B,OACK;wBACD,IAAI,CAAC,gBAAgB,GAAG;4BAAE,GAAG,IAAI,CAAC,gBAAgB;4BAAE,GAAG,UAAU;wBAAC;oBACtE;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ,OAAO,EAAE,IAAI;QACjD,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,kBAAkB,kBAAkB,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,eAAe,MAAM,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,gBAAgB;QACtH,OACK;YACD,kBAAkB,kBAAkB,OAAO,CAAC,MAAM,CAAC;QACvD;QACA,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,YAAY,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,CAAC,IAAI;YAChD,iBAAiB,CAAC,GAAG,kBAAkB,MAAM,EAAE,iBAAiB,kBAAkB,OAAO,CAAC,MAAM,CAAC,MAAM,kBAAkB,OAAO,CAAC,MAAM,CAAC;QAC5I,OACK;YACD,iBAAiB;QACrB;QACA,IAAI;QACJ,IAAI;QACJ,IAAI,WAAW,GAAG,KAAK,OAAO;YAC1B,MAAM,WAAW,MAAM,CAAC,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,UAAU;YACpI,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,aAAa,OAAO,EAAE,KAAK,UAAU,KAAK,IAAI,CAAC,GAAG,EAAE,eAAe;QACxG,OACK;;YAED,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,aAAa,OAAO,EAAE,KAAK,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,eAAe;QAC/G;QACA,MAAM,MAAM;YACR,YAAY,CAAC,GAAG,eAAe,MAAM,EAAE;YACvC,IAAI,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,CAAC,GAAG;YACvC,KAAK,CAAC,GAAG,eAAe,MAAM,EAAE;QACpC;QACA,IAAI,cAAc;YACd,IAAI,aAAa,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE;QACnD;QACA,IAAI,WAAW;YACX,IAAI,GAAG,GAAG;QACd;QACA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,SAAS,GAAG,kBAAkB,OAAO,CAAC,MAAM,CAAC;QACrD;QACA,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,WAAW,GAAG,IAAI,CAAC,wBAAwB;QACnD;QACA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB;QACxC;QACA,OAAO;IACX;AACJ;AACA,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3031, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwe/general/encrypt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.GeneralEncrypt = void 0;\nconst encrypt_js_1 = require(\"../flattened/encrypt.js\");\nconst errors_js_1 = require(\"../../util/errors.js\");\nconst cek_js_1 = require(\"../../lib/cek.js\");\nconst is_disjoint_js_1 = require(\"../../lib/is_disjoint.js\");\nconst encrypt_key_management_js_1 = require(\"../../lib/encrypt_key_management.js\");\nconst base64url_js_1 = require(\"../../runtime/base64url.js\");\nconst validate_crit_js_1 = require(\"../../lib/validate_crit.js\");\nclass IndividualRecipient {\n    constructor(enc, key, options) {\n        this.parent = enc;\n        this.key = key;\n        this.options = options;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    addRecipient(...args) {\n        return this.parent.addRecipient(...args);\n    }\n    encrypt(...args) {\n        return this.parent.encrypt(...args);\n    }\n    done() {\n        return this.parent;\n    }\n}\nclass GeneralEncrypt {\n    constructor(plaintext) {\n        this._recipients = [];\n        this._plaintext = plaintext;\n    }\n    addRecipient(key, options) {\n        const recipient = new IndividualRecipient(this, key, { crit: options === null || options === void 0 ? void 0 : options.crit });\n        this._recipients.push(recipient);\n        return recipient;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this._aad = aad;\n        return this;\n    }\n    async encrypt(options) {\n        var _a, _b, _c;\n        if (!this._recipients.length) {\n            throw new errors_js_1.JWEInvalid('at least one recipient must be added');\n        }\n        options = { deflateRaw: options === null || options === void 0 ? void 0 : options.deflateRaw };\n        if (this._recipients.length === 1) {\n            const [recipient] = this._recipients;\n            const flattened = await new encrypt_js_1.FlattenedEncrypt(this._plaintext)\n                .setAdditionalAuthenticatedData(this._aad)\n                .setProtectedHeader(this._protectedHeader)\n                .setSharedUnprotectedHeader(this._unprotectedHeader)\n                .setUnprotectedHeader(recipient.unprotectedHeader)\n                .encrypt(recipient.key, { ...recipient.options, ...options });\n            let jwe = {\n                ciphertext: flattened.ciphertext,\n                iv: flattened.iv,\n                recipients: [{}],\n                tag: flattened.tag,\n            };\n            if (flattened.aad)\n                jwe.aad = flattened.aad;\n            if (flattened.protected)\n                jwe.protected = flattened.protected;\n            if (flattened.unprotected)\n                jwe.unprotected = flattened.unprotected;\n            if (flattened.encrypted_key)\n                jwe.recipients[0].encrypted_key = flattened.encrypted_key;\n            if (flattened.header)\n                jwe.recipients[0].header = flattened.header;\n            return jwe;\n        }\n        let enc;\n        for (let i = 0; i < this._recipients.length; i++) {\n            const recipient = this._recipients[i];\n            if (!(0, is_disjoint_js_1.default)(this._protectedHeader, this._unprotectedHeader, recipient.unprotectedHeader)) {\n                throw new errors_js_1.JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n            }\n            const joseHeader = {\n                ...this._protectedHeader,\n                ...this._unprotectedHeader,\n                ...recipient.unprotectedHeader,\n            };\n            const { alg } = joseHeader;\n            if (typeof alg !== 'string' || !alg) {\n                throw new errors_js_1.JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n            }\n            if (alg === 'dir' || alg === 'ECDH-ES') {\n                throw new errors_js_1.JWEInvalid('\"dir\" and \"ECDH-ES\" alg may only be used with a single recipient');\n            }\n            if (typeof joseHeader.enc !== 'string' || !joseHeader.enc) {\n                throw new errors_js_1.JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n            }\n            if (!enc) {\n                enc = joseHeader.enc;\n            }\n            else if (enc !== joseHeader.enc) {\n                throw new errors_js_1.JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter must be the same for all recipients');\n            }\n            (0, validate_crit_js_1.default)(errors_js_1.JWEInvalid, new Map(), recipient.options.crit, this._protectedHeader, joseHeader);\n            if (joseHeader.zip !== undefined) {\n                if (!this._protectedHeader || !this._protectedHeader.zip) {\n                    throw new errors_js_1.JWEInvalid('JWE \"zip\" (Compression Algorithm) Header MUST be integrity protected');\n                }\n            }\n        }\n        const cek = (0, cek_js_1.default)(enc);\n        let jwe = {\n            ciphertext: '',\n            iv: '',\n            recipients: [],\n            tag: '',\n        };\n        for (let i = 0; i < this._recipients.length; i++) {\n            const recipient = this._recipients[i];\n            const target = {};\n            jwe.recipients.push(target);\n            const joseHeader = {\n                ...this._protectedHeader,\n                ...this._unprotectedHeader,\n                ...recipient.unprotectedHeader,\n            };\n            const p2c = joseHeader.alg.startsWith('PBES2') ? 2048 + i : undefined;\n            if (i === 0) {\n                const flattened = await new encrypt_js_1.FlattenedEncrypt(this._plaintext)\n                    .setAdditionalAuthenticatedData(this._aad)\n                    .setContentEncryptionKey(cek)\n                    .setProtectedHeader(this._protectedHeader)\n                    .setSharedUnprotectedHeader(this._unprotectedHeader)\n                    .setUnprotectedHeader(recipient.unprotectedHeader)\n                    .setKeyManagementParameters({ p2c })\n                    .encrypt(recipient.key, {\n                    ...recipient.options,\n                    ...options,\n                    [encrypt_js_1.unprotected]: true,\n                });\n                jwe.ciphertext = flattened.ciphertext;\n                jwe.iv = flattened.iv;\n                jwe.tag = flattened.tag;\n                if (flattened.aad)\n                    jwe.aad = flattened.aad;\n                if (flattened.protected)\n                    jwe.protected = flattened.protected;\n                if (flattened.unprotected)\n                    jwe.unprotected = flattened.unprotected;\n                target.encrypted_key = flattened.encrypted_key;\n                if (flattened.header)\n                    target.header = flattened.header;\n                continue;\n            }\n            const { encryptedKey, parameters } = await (0, encrypt_key_management_js_1.default)(((_a = recipient.unprotectedHeader) === null || _a === void 0 ? void 0 : _a.alg) ||\n                ((_b = this._protectedHeader) === null || _b === void 0 ? void 0 : _b.alg) ||\n                ((_c = this._unprotectedHeader) === null || _c === void 0 ? void 0 : _c.alg), enc, recipient.key, cek, { p2c });\n            target.encrypted_key = (0, base64url_js_1.encode)(encryptedKey);\n            if (recipient.unprotectedHeader || parameters)\n                target.header = { ...recipient.unprotectedHeader, ...parameters };\n        }\n        return jwe;\n    }\n}\nexports.GeneralEncrypt = GeneralEncrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;IACF,YAAY,GAAG,EAAE,GAAG,EAAE,OAAO,CAAE;QAC3B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,qBAAqB,iBAAiB,EAAE;QACpC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,iBAAiB,GAAG;QACzB,OAAO,IAAI;IACf;IACA,aAAa,GAAG,IAAI,EAAE;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI;IACvC;IACA,QAAQ,GAAG,IAAI,EAAE;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI;IAClC;IACA,OAAO;QACH,OAAO,IAAI,CAAC,MAAM;IACtB;AACJ;AACA,MAAM;IACF,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,UAAU,GAAG;IACtB;IACA,aAAa,GAAG,EAAE,OAAO,EAAE;QACvB,MAAM,YAAY,IAAI,oBAAoB,IAAI,EAAE,KAAK;YAAE,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI;QAAC;QAC5H,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACtB,OAAO;IACX;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,2BAA2B,uBAAuB,EAAE;QAChD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,kBAAkB,GAAG;QAC1B,OAAO,IAAI;IACf;IACA,+BAA+B,GAAG,EAAE;QAChC,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,IAAI;IACf;IACA,MAAM,QAAQ,OAAO,EAAE;QACnB,IAAI,IAAI,IAAI;QACZ,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YAC1B,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,UAAU;YAAE,YAAY,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU;QAAC;QAC7F,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,GAAG;YAC/B,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW;YACpC,MAAM,YAAY,MAAM,IAAI,aAAa,gBAAgB,CAAC,IAAI,CAAC,UAAU,EACpE,8BAA8B,CAAC,IAAI,CAAC,IAAI,EACxC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EACxC,0BAA0B,CAAC,IAAI,CAAC,kBAAkB,EAClD,oBAAoB,CAAC,UAAU,iBAAiB,EAChD,OAAO,CAAC,UAAU,GAAG,EAAE;gBAAE,GAAG,UAAU,OAAO;gBAAE,GAAG,OAAO;YAAC;YAC/D,IAAI,MAAM;gBACN,YAAY,UAAU,UAAU;gBAChC,IAAI,UAAU,EAAE;gBAChB,YAAY;oBAAC,CAAC;iBAAE;gBAChB,KAAK,UAAU,GAAG;YACtB;YACA,IAAI,UAAU,GAAG,EACb,IAAI,GAAG,GAAG,UAAU,GAAG;YAC3B,IAAI,UAAU,SAAS,EACnB,IAAI,SAAS,GAAG,UAAU,SAAS;YACvC,IAAI,UAAU,WAAW,EACrB,IAAI,WAAW,GAAG,UAAU,WAAW;YAC3C,IAAI,UAAU,aAAa,EACvB,IAAI,UAAU,CAAC,EAAE,CAAC,aAAa,GAAG,UAAU,aAAa;YAC7D,IAAI,UAAU,MAAM,EAChB,IAAI,UAAU,CAAC,EAAE,CAAC,MAAM,GAAG,UAAU,MAAM;YAC/C,OAAO;QACX;QACA,IAAI;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAK;YAC9C,MAAM,YAAY,IAAI,CAAC,WAAW,CAAC,EAAE;YACrC,IAAI,CAAC,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,UAAU,iBAAiB,GAAG;gBAC7G,MAAM,IAAI,YAAY,UAAU,CAAC;YACrC;YACA,MAAM,aAAa;gBACf,GAAG,IAAI,CAAC,gBAAgB;gBACxB,GAAG,IAAI,CAAC,kBAAkB;gBAC1B,GAAG,UAAU,iBAAiB;YAClC;YACA,MAAM,EAAE,GAAG,EAAE,GAAG;YAChB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;gBACjC,MAAM,IAAI,YAAY,UAAU,CAAC;YACrC;YACA,IAAI,QAAQ,SAAS,QAAQ,WAAW;gBACpC,MAAM,IAAI,YAAY,UAAU,CAAC;YACrC;YACA,IAAI,OAAO,WAAW,GAAG,KAAK,YAAY,CAAC,WAAW,GAAG,EAAE;gBACvD,MAAM,IAAI,YAAY,UAAU,CAAC;YACrC;YACA,IAAI,CAAC,KAAK;gBACN,MAAM,WAAW,GAAG;YACxB,OACK,IAAI,QAAQ,WAAW,GAAG,EAAE;gBAC7B,MAAM,IAAI,YAAY,UAAU,CAAC;YACrC;YACA,CAAC,GAAG,mBAAmB,OAAO,EAAE,YAAY,UAAU,EAAE,IAAI,OAAO,UAAU,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAClH,IAAI,WAAW,GAAG,KAAK,WAAW;gBAC9B,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;oBACtD,MAAM,IAAI,YAAY,UAAU,CAAC;gBACrC;YACJ;QACJ;QACA,MAAM,MAAM,CAAC,GAAG,SAAS,OAAO,EAAE;QAClC,IAAI,MAAM;YACN,YAAY;YACZ,IAAI;YACJ,YAAY,EAAE;YACd,KAAK;QACT;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAK;YAC9C,MAAM,YAAY,IAAI,CAAC,WAAW,CAAC,EAAE;YACrC,MAAM,SAAS,CAAC;YAChB,IAAI,UAAU,CAAC,IAAI,CAAC;YACpB,MAAM,aAAa;gBACf,GAAG,IAAI,CAAC,gBAAgB;gBACxB,GAAG,IAAI,CAAC,kBAAkB;gBAC1B,GAAG,UAAU,iBAAiB;YAClC;YACA,MAAM,MAAM,WAAW,GAAG,CAAC,UAAU,CAAC,WAAW,OAAO,IAAI;YAC5D,IAAI,MAAM,GAAG;gBACT,MAAM,YAAY,MAAM,IAAI,aAAa,gBAAgB,CAAC,IAAI,CAAC,UAAU,EACpE,8BAA8B,CAAC,IAAI,CAAC,IAAI,EACxC,uBAAuB,CAAC,KACxB,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,EACxC,0BAA0B,CAAC,IAAI,CAAC,kBAAkB,EAClD,oBAAoB,CAAC,UAAU,iBAAiB,EAChD,0BAA0B,CAAC;oBAAE;gBAAI,GACjC,OAAO,CAAC,UAAU,GAAG,EAAE;oBACxB,GAAG,UAAU,OAAO;oBACpB,GAAG,OAAO;oBACV,CAAC,aAAa,WAAW,CAAC,EAAE;gBAChC;gBACA,IAAI,UAAU,GAAG,UAAU,UAAU;gBACrC,IAAI,EAAE,GAAG,UAAU,EAAE;gBACrB,IAAI,GAAG,GAAG,UAAU,GAAG;gBACvB,IAAI,UAAU,GAAG,EACb,IAAI,GAAG,GAAG,UAAU,GAAG;gBAC3B,IAAI,UAAU,SAAS,EACnB,IAAI,SAAS,GAAG,UAAU,SAAS;gBACvC,IAAI,UAAU,WAAW,EACrB,IAAI,WAAW,GAAG,UAAU,WAAW;gBAC3C,OAAO,aAAa,GAAG,UAAU,aAAa;gBAC9C,IAAI,UAAU,MAAM,EAChB,OAAO,MAAM,GAAG,UAAU,MAAM;gBACpC;YACJ;YACA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC,GAAG,4BAA4B,OAAO,EAAE,CAAC,CAAC,KAAK,UAAU,iBAAiB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,KAC/J,CAAC,CAAC,KAAK,IAAI,CAAC,gBAAgB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,KACzE,CAAC,CAAC,KAAK,IAAI,CAAC,kBAAkB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,GAAG,KAAK,UAAU,GAAG,EAAE,KAAK;gBAAE;YAAI;YACjH,OAAO,aAAa,GAAG,CAAC,GAAG,eAAe,MAAM,EAAE;YAClD,IAAI,UAAU,iBAAiB,IAAI,YAC/B,OAAO,MAAM,GAAG;gBAAE,GAAG,UAAU,iBAAiB;gBAAE,GAAG,UAAU;YAAC;QACxE;QACA,OAAO;IACX;AACJ;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/dsa_digest.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst errors_js_1 = require(\"../util/errors.js\");\nfunction dsaDigest(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'RS256':\n        case 'ES256':\n        case 'ES256K':\n            return 'sha256';\n        case 'PS384':\n        case 'RS384':\n        case 'ES384':\n            return 'sha384';\n        case 'PS512':\n        case 'RS512':\n        case 'ES512':\n            return 'sha512';\n        case 'EdDSA':\n            return undefined;\n        default:\n            throw new errors_js_1.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\nexports.default = dsaDigest;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,SAAS,UAAU,GAAG;IAClB,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IACtH;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/node_key.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nconst get_named_curve_js_1 = require(\"./get_named_curve.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst check_modulus_length_js_1 = require(\"./check_modulus_length.js\");\nconst flags_js_1 = require(\"./flags.js\");\nconst PSS = {\n    padding: crypto_1.constants.RSA_PKCS1_PSS_PADDING,\n    saltLength: crypto_1.constants.RSA_PSS_SALTLEN_DIGEST,\n};\nconst ecCurveAlgMap = new Map([\n    ['ES256', 'P-256'],\n    ['ES256K', 'secp256k1'],\n    ['ES384', 'P-384'],\n    ['ES512', 'P-521'],\n]);\nfunction keyForCrypto(alg, key) {\n    switch (alg) {\n        case 'EdDSA':\n            if (!['ed25519', 'ed448'].includes(key.asymmetricKeyType)) {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448');\n            }\n            return key;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            if (key.asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n            }\n            (0, check_modulus_length_js_1.default)(key, alg);\n            return key;\n        case flags_js_1.rsaPssParams && 'PS256':\n        case flags_js_1.rsaPssParams && 'PS384':\n        case flags_js_1.rsaPssParams && 'PS512':\n            if (key.asymmetricKeyType === 'rsa-pss') {\n                const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n                const length = parseInt(alg.slice(-3), 10);\n                if (hashAlgorithm !== undefined &&\n                    (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm)) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${alg}`);\n                }\n                if (saltLength !== undefined && saltLength > length >> 3) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${alg}`);\n                }\n            }\n            else if (key.asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss');\n            }\n            (0, check_modulus_length_js_1.default)(key, alg);\n            return { key, ...PSS };\n        case !flags_js_1.rsaPssParams && 'PS256':\n        case !flags_js_1.rsaPssParams && 'PS384':\n        case !flags_js_1.rsaPssParams && 'PS512':\n            if (key.asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n            }\n            (0, check_modulus_length_js_1.default)(key, alg);\n            return { key, ...PSS };\n        case 'ES256':\n        case 'ES256K':\n        case 'ES384':\n        case 'ES512': {\n            if (key.asymmetricKeyType !== 'ec') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ec');\n            }\n            const actual = (0, get_named_curve_js_1.default)(key);\n            const expected = ecCurveAlgMap.get(alg);\n            if (actual !== expected) {\n                throw new TypeError(`Invalid key curve for the algorithm, its curve must be ${expected}, got ${actual}`);\n            }\n            return { dsaEncoding: 'ieee-p1363', key };\n        }\n        default:\n            throw new errors_js_1.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\nexports.default = keyForCrypto;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,MAAM;IACR,SAAS,SAAS,SAAS,CAAC,qBAAqB;IACjD,YAAY,SAAS,SAAS,CAAC,sBAAsB;AACzD;AACA,MAAM,gBAAgB,IAAI,IAAI;IAC1B;QAAC;QAAS;KAAQ;IAClB;QAAC;QAAU;KAAY;IACvB;QAAC;QAAS;KAAQ;IAClB;QAAC;QAAS;KAAQ;CACrB;AACD,SAAS,aAAa,GAAG,EAAE,GAAG;IAC1B,OAAQ;QACJ,KAAK;YACD,IAAI,CAAC;gBAAC;gBAAW;aAAQ,CAAC,QAAQ,CAAC,IAAI,iBAAiB,GAAG;gBACvD,MAAM,IAAI,UAAU;YACxB;YACA,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,IAAI,iBAAiB,KAAK,OAAO;gBACjC,MAAM,IAAI,UAAU;YACxB;YACA,CAAC,GAAG,0BAA0B,OAAO,EAAE,KAAK;YAC5C,OAAO;QACX,KAAK,WAAW,YAAY,IAAI;QAChC,KAAK,WAAW,YAAY,IAAI;QAChC,KAAK,WAAW,YAAY,IAAI;YAC5B,IAAI,IAAI,iBAAiB,KAAK,WAAW;gBACrC,MAAM,EAAE,aAAa,EAAE,iBAAiB,EAAE,UAAU,EAAE,GAAG,IAAI,oBAAoB;gBACjF,MAAM,SAAS,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;gBACvC,IAAI,kBAAkB,aAClB,CAAC,kBAAkB,CAAC,GAAG,EAAE,QAAQ,IAAI,sBAAsB,aAAa,GAAG;oBAC3E,MAAM,IAAI,UAAU,CAAC,6FAA6F,EAAE,KAAK;gBAC7H;gBACA,IAAI,eAAe,aAAa,aAAa,UAAU,GAAG;oBACtD,MAAM,IAAI,UAAU,CAAC,yGAAyG,EAAE,KAAK;gBACzI;YACJ,OACK,IAAI,IAAI,iBAAiB,KAAK,OAAO;gBACtC,MAAM,IAAI,UAAU;YACxB;YACA,CAAC,GAAG,0BAA0B,OAAO,EAAE,KAAK;YAC5C,OAAO;gBAAE;gBAAK,GAAG,GAAG;YAAC;QACzB,KAAK,CAAC,WAAW,YAAY,IAAI;QACjC,KAAK,CAAC,WAAW,YAAY,IAAI;QACjC,KAAK,CAAC,WAAW,YAAY,IAAI;YAC7B,IAAI,IAAI,iBAAiB,KAAK,OAAO;gBACjC,MAAM,IAAI,UAAU;YACxB;YACA,CAAC,GAAG,0BAA0B,OAAO,EAAE,KAAK;YAC5C,OAAO;gBAAE;gBAAK,GAAG,GAAG;YAAC;QACzB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,IAAI,iBAAiB,KAAK,MAAM;oBAChC,MAAM,IAAI,UAAU;gBACxB;gBACA,MAAM,SAAS,CAAC,GAAG,qBAAqB,OAAO,EAAE;gBACjD,MAAM,WAAW,cAAc,GAAG,CAAC;gBACnC,IAAI,WAAW,UAAU;oBACrB,MAAM,IAAI,UAAU,CAAC,uDAAuD,EAAE,SAAS,MAAM,EAAE,QAAQ;gBAC3G;gBACA,OAAO;oBAAE,aAAa;oBAAc;gBAAI;YAC5C;QACA;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IACtH;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/hmac_digest.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst errors_js_1 = require(\"../util/errors.js\");\nfunction hmacDigest(alg) {\n    switch (alg) {\n        case 'HS256':\n            return 'sha256';\n        case 'HS384':\n            return 'sha384';\n        case 'HS512':\n            return 'sha512';\n        default:\n            throw new errors_js_1.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\nexports.default = hmacDigest;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,SAAS,WAAW,GAAG;IACnB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IACtH;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/get_sign_verify_key.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nconst webcrypto_js_1 = require(\"./webcrypto.js\");\nconst crypto_key_js_1 = require(\"../lib/crypto_key.js\");\nconst invalid_key_input_js_1 = require(\"../lib/invalid_key_input.js\");\nconst is_key_like_js_1 = require(\"./is_key_like.js\");\nfunction getSignVerifyKey(alg, key, usage) {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError((0, invalid_key_input_js_1.default)(key, ...is_key_like_js_1.types));\n        }\n        return (0, crypto_1.createSecretKey)(key);\n    }\n    if (key instanceof crypto_1.KeyObject) {\n        return key;\n    }\n    if ((0, webcrypto_js_1.isCryptoKey)(key)) {\n        (0, crypto_key_js_1.checkSigCryptoKey)(key, alg, usage);\n        return crypto_1.KeyObject.from(key);\n    }\n    throw new TypeError((0, invalid_key_input_js_1.default)(key, ...is_key_like_js_1.types, 'Uint8Array'));\n}\nexports.default = getSignVerifyKey;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,iBAAiB,GAAG,EAAE,GAAG,EAAE,KAAK;IACrC,IAAI,eAAe,YAAY;QAC3B,IAAI,CAAC,IAAI,UAAU,CAAC,OAAO;YACvB,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK;QAC1F;QACA,OAAO,CAAC,GAAG,SAAS,eAAe,EAAE;IACzC;IACA,IAAI,eAAe,SAAS,SAAS,EAAE;QACnC,OAAO;IACX;IACA,IAAI,CAAC,GAAG,eAAe,WAAW,EAAE,MAAM;QACtC,CAAC,GAAG,gBAAgB,iBAAiB,EAAE,KAAK,KAAK;QACjD,OAAO,SAAS,SAAS,CAAC,IAAI,CAAC;IACnC;IACA,MAAM,IAAI,UAAU,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,iBAAiB,KAAK,EAAE;AAC5F;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/sign.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto = require(\"crypto\");\nconst util_1 = require(\"util\");\nconst dsa_digest_js_1 = require(\"./dsa_digest.js\");\nconst hmac_digest_js_1 = require(\"./hmac_digest.js\");\nconst node_key_js_1 = require(\"./node_key.js\");\nconst get_sign_verify_key_js_1 = require(\"./get_sign_verify_key.js\");\nlet oneShotSign;\nif (crypto.sign.length > 3) {\n    oneShotSign = (0, util_1.promisify)(crypto.sign);\n}\nelse {\n    oneShotSign = crypto.sign;\n}\nconst sign = async (alg, key, data) => {\n    const keyObject = (0, get_sign_verify_key_js_1.default)(alg, key, 'sign');\n    if (alg.startsWith('HS')) {\n        const hmac = crypto.createHmac((0, hmac_digest_js_1.default)(alg), keyObject);\n        hmac.update(data);\n        return hmac.digest();\n    }\n    return oneShotSign((0, dsa_digest_js_1.default)(alg), data, (0, node_key_js_1.default)(alg, keyObject));\n};\nexports.default = sign;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,IAAI;AACJ,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;IACxB,cAAc,CAAC,GAAG,OAAO,SAAS,EAAE,OAAO,IAAI;AACnD,OACK;IACD,cAAc,OAAO,IAAI;AAC7B;AACA,MAAM,OAAO,OAAO,KAAK,KAAK;IAC1B,MAAM,YAAY,CAAC,GAAG,yBAAyB,OAAO,EAAE,KAAK,KAAK;IAClE,IAAI,IAAI,UAAU,CAAC,OAAO;QACtB,MAAM,OAAO,OAAO,UAAU,CAAC,CAAC,GAAG,iBAAiB,OAAO,EAAE,MAAM;QACnE,KAAK,MAAM,CAAC;QACZ,OAAO,KAAK,MAAM;IACtB;IACA,OAAO,YAAY,CAAC,GAAG,gBAAgB,OAAO,EAAE,MAAM,MAAM,CAAC,GAAG,cAAc,OAAO,EAAE,KAAK;AAChG;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/verify.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto = require(\"crypto\");\nconst util_1 = require(\"util\");\nconst dsa_digest_js_1 = require(\"./dsa_digest.js\");\nconst node_key_js_1 = require(\"./node_key.js\");\nconst sign_js_1 = require(\"./sign.js\");\nconst get_sign_verify_key_js_1 = require(\"./get_sign_verify_key.js\");\nconst flags_js_1 = require(\"./flags.js\");\nlet oneShotVerify;\nif (crypto.verify.length > 4 && flags_js_1.oneShotCallback) {\n    oneShotVerify = (0, util_1.promisify)(crypto.verify);\n}\nelse {\n    oneShotVerify = crypto.verify;\n}\nconst verify = async (alg, key, signature, data) => {\n    const keyObject = (0, get_sign_verify_key_js_1.default)(alg, key, 'verify');\n    if (alg.startsWith('HS')) {\n        const expected = await (0, sign_js_1.default)(alg, keyObject, data);\n        const actual = signature;\n        try {\n            return crypto.timingSafeEqual(actual, expected);\n        }\n        catch {\n            return false;\n        }\n    }\n    const algorithm = (0, dsa_digest_js_1.default)(alg);\n    const keyInput = (0, node_key_js_1.default)(alg, keyObject);\n    try {\n        return await oneShotVerify(algorithm, data, keyInput, signature);\n    }\n    catch {\n        return false;\n    }\n};\nexports.default = verify;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,IAAI;AACJ,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,KAAK,WAAW,eAAe,EAAE;IACxD,gBAAgB,CAAC,GAAG,OAAO,SAAS,EAAE,OAAO,MAAM;AACvD,OACK;IACD,gBAAgB,OAAO,MAAM;AACjC;AACA,MAAM,SAAS,OAAO,KAAK,KAAK,WAAW;IACvC,MAAM,YAAY,CAAC,GAAG,yBAAyB,OAAO,EAAE,KAAK,KAAK;IAClE,IAAI,IAAI,UAAU,CAAC,OAAO;QACtB,MAAM,WAAW,MAAM,CAAC,GAAG,UAAU,OAAO,EAAE,KAAK,WAAW;QAC9D,MAAM,SAAS;QACf,IAAI;YACA,OAAO,OAAO,eAAe,CAAC,QAAQ;QAC1C,EACA,OAAM;YACF,OAAO;QACX;IACJ;IACA,MAAM,YAAY,CAAC,GAAG,gBAAgB,OAAO,EAAE;IAC/C,MAAM,WAAW,CAAC,GAAG,cAAc,OAAO,EAAE,KAAK;IACjD,IAAI;QACA,OAAO,MAAM,cAAc,WAAW,MAAM,UAAU;IAC1D,EACA,OAAM;QACF,OAAO;IACX;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jws/flattened/verify.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.flattenedVerify = void 0;\nconst base64url_js_1 = require(\"../../runtime/base64url.js\");\nconst verify_js_1 = require(\"../../runtime/verify.js\");\nconst errors_js_1 = require(\"../../util/errors.js\");\nconst buffer_utils_js_1 = require(\"../../lib/buffer_utils.js\");\nconst is_disjoint_js_1 = require(\"../../lib/is_disjoint.js\");\nconst is_object_js_1 = require(\"../../lib/is_object.js\");\nconst check_key_type_js_1 = require(\"../../lib/check_key_type.js\");\nconst validate_crit_js_1 = require(\"../../lib/validate_crit.js\");\nconst validate_algorithms_js_1 = require(\"../../lib/validate_algorithms.js\");\nasync function flattenedVerify(jws, key, options) {\n    var _a;\n    if (!(0, is_object_js_1.default)(jws)) {\n        throw new errors_js_1.JWSInvalid('Flattened JWS must be an object');\n    }\n    if (jws.protected === undefined && jws.header === undefined) {\n        throw new errors_js_1.JWSInvalid('Flattened JWS must have either of the \"protected\" or \"header\" members');\n    }\n    if (jws.protected !== undefined && typeof jws.protected !== 'string') {\n        throw new errors_js_1.JWSInvalid('JWS Protected Header incorrect type');\n    }\n    if (jws.payload === undefined) {\n        throw new errors_js_1.JWSInvalid('JWS Payload missing');\n    }\n    if (typeof jws.signature !== 'string') {\n        throw new errors_js_1.JWSInvalid('JWS Signature missing or incorrect type');\n    }\n    if (jws.header !== undefined && !(0, is_object_js_1.default)(jws.header)) {\n        throw new errors_js_1.JWSInvalid('JWS Unprotected Header incorrect type');\n    }\n    let parsedProt = {};\n    if (jws.protected) {\n        try {\n            const protectedHeader = (0, base64url_js_1.decode)(jws.protected);\n            parsedProt = JSON.parse(buffer_utils_js_1.decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new errors_js_1.JWSInvalid('JWS Protected Header is invalid');\n        }\n    }\n    if (!(0, is_disjoint_js_1.default)(parsedProt, jws.header)) {\n        throw new errors_js_1.JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jws.header,\n    };\n    const extensions = (0, validate_crit_js_1.default)(errors_js_1.JWSInvalid, new Map([['b64', true]]), options === null || options === void 0 ? void 0 : options.crit, parsedProt, joseHeader);\n    let b64 = true;\n    if (extensions.has('b64')) {\n        b64 = parsedProt.b64;\n        if (typeof b64 !== 'boolean') {\n            throw new errors_js_1.JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n        }\n    }\n    const { alg } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new errors_js_1.JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n    }\n    const algorithms = options && (0, validate_algorithms_js_1.default)('algorithms', options.algorithms);\n    if (algorithms && !algorithms.has(alg)) {\n        throw new errors_js_1.JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter not allowed');\n    }\n    if (b64) {\n        if (typeof jws.payload !== 'string') {\n            throw new errors_js_1.JWSInvalid('JWS Payload must be a string');\n        }\n    }\n    else if (typeof jws.payload !== 'string' && !(jws.payload instanceof Uint8Array)) {\n        throw new errors_js_1.JWSInvalid('JWS Payload must be a string or an Uint8Array instance');\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jws);\n        resolvedKey = true;\n    }\n    (0, check_key_type_js_1.default)(alg, key, 'verify');\n    const data = (0, buffer_utils_js_1.concat)(buffer_utils_js_1.encoder.encode((_a = jws.protected) !== null && _a !== void 0 ? _a : ''), buffer_utils_js_1.encoder.encode('.'), typeof jws.payload === 'string' ? buffer_utils_js_1.encoder.encode(jws.payload) : jws.payload);\n    let signature;\n    try {\n        signature = (0, base64url_js_1.decode)(jws.signature);\n    }\n    catch {\n        throw new errors_js_1.JWSInvalid('Failed to base64url decode the signature');\n    }\n    const verified = await (0, verify_js_1.default)(alg, key, signature, data);\n    if (!verified) {\n        throw new errors_js_1.JWSSignatureVerificationFailed();\n    }\n    let payload;\n    if (b64) {\n        try {\n            payload = (0, base64url_js_1.decode)(jws.payload);\n        }\n        catch {\n            throw new errors_js_1.JWSInvalid('Failed to base64url decode the payload');\n        }\n    }\n    else if (typeof jws.payload === 'string') {\n        payload = buffer_utils_js_1.encoder.encode(jws.payload);\n    }\n    else {\n        payload = jws.payload;\n    }\n    const result = { payload };\n    if (jws.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jws.header !== undefined) {\n        result.unprotectedHeader = jws.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\nexports.flattenedVerify = flattenedVerify;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,gBAAgB,GAAG,EAAE,GAAG,EAAE,OAAO;IAC5C,IAAI;IACJ,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,MAAM;QACnC,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,IAAI,MAAM,KAAK,WAAW;QACzD,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,UAAU;QAClE,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,IAAI,OAAO,KAAK,WAAW;QAC3B,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,OAAO,IAAI,SAAS,KAAK,UAAU;QACnC,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,IAAI,MAAM,KAAK,aAAa,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,IAAI,MAAM,GAAG;QACtE,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,aAAa,CAAC;IAClB,IAAI,IAAI,SAAS,EAAE;QACf,IAAI;YACA,MAAM,kBAAkB,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,SAAS;YAChE,aAAa,KAAK,KAAK,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC;QAC7D,EACA,OAAM;YACF,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;IACJ;IACA,IAAI,CAAC,CAAC,GAAG,iBAAiB,OAAO,EAAE,YAAY,IAAI,MAAM,GAAG;QACxD,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,aAAa;QACf,GAAG,UAAU;QACb,GAAG,IAAI,MAAM;IACjB;IACA,MAAM,aAAa,CAAC,GAAG,mBAAmB,OAAO,EAAE,YAAY,UAAU,EAAE,IAAI,IAAI;QAAC;YAAC;YAAO;SAAK;KAAC,GAAG,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI,EAAE,YAAY;IACjL,IAAI,MAAM;IACV,IAAI,WAAW,GAAG,CAAC,QAAQ;QACvB,MAAM,WAAW,GAAG;QACpB,IAAI,OAAO,QAAQ,WAAW;YAC1B,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;IACJ;IACA,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,aAAa,WAAW,CAAC,GAAG,yBAAyB,OAAO,EAAE,cAAc,QAAQ,UAAU;IACpG,IAAI,cAAc,CAAC,WAAW,GAAG,CAAC,MAAM;QACpC,MAAM,IAAI,YAAY,iBAAiB,CAAC;IAC5C;IACA,IAAI,KAAK;QACL,IAAI,OAAO,IAAI,OAAO,KAAK,UAAU;YACjC,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;IACJ,OACK,IAAI,OAAO,IAAI,OAAO,KAAK,YAAY,CAAC,CAAC,IAAI,OAAO,YAAY,UAAU,GAAG;QAC9E,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,cAAc;IAClB,IAAI,OAAO,QAAQ,YAAY;QAC3B,MAAM,MAAM,IAAI,YAAY;QAC5B,cAAc;IAClB;IACA,CAAC,GAAG,oBAAoB,OAAO,EAAE,KAAK,KAAK;IAC3C,MAAM,OAAO,CAAC,GAAG,kBAAkB,MAAM,EAAE,kBAAkB,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,IAAI,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,kBAAkB,OAAO,CAAC,MAAM,CAAC,MAAM,OAAO,IAAI,OAAO,KAAK,WAAW,kBAAkB,OAAO,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,OAAO;IAC3Q,IAAI;IACJ,IAAI;QACA,YAAY,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,SAAS;IACxD,EACA,OAAM;QACF,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,WAAW,MAAM,CAAC,GAAG,YAAY,OAAO,EAAE,KAAK,KAAK,WAAW;IACrE,IAAI,CAAC,UAAU;QACX,MAAM,IAAI,YAAY,8BAA8B;IACxD;IACA,IAAI;IACJ,IAAI,KAAK;QACL,IAAI;YACA,UAAU,CAAC,GAAG,eAAe,MAAM,EAAE,IAAI,OAAO;QACpD,EACA,OAAM;YACF,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;IACJ,OACK,IAAI,OAAO,IAAI,OAAO,KAAK,UAAU;QACtC,UAAU,kBAAkB,OAAO,CAAC,MAAM,CAAC,IAAI,OAAO;IAC1D,OACK;QACD,UAAU,IAAI,OAAO;IACzB;IACA,MAAM,SAAS;QAAE;IAAQ;IACzB,IAAI,IAAI,SAAS,KAAK,WAAW;QAC7B,OAAO,eAAe,GAAG;IAC7B;IACA,IAAI,IAAI,MAAM,KAAK,WAAW;QAC1B,OAAO,iBAAiB,GAAG,IAAI,MAAM;IACzC;IACA,IAAI,aAAa;QACb,OAAO;YAAE,GAAG,MAAM;YAAE;QAAI;IAC5B;IACA,OAAO;AACX;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jws/compact/verify.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.compactVerify = void 0;\nconst verify_js_1 = require(\"../flattened/verify.js\");\nconst errors_js_1 = require(\"../../util/errors.js\");\nconst buffer_utils_js_1 = require(\"../../lib/buffer_utils.js\");\nasync function compactVerify(jws, key, options) {\n    if (jws instanceof Uint8Array) {\n        jws = buffer_utils_js_1.decoder.decode(jws);\n    }\n    if (typeof jws !== 'string') {\n        throw new errors_js_1.JWSInvalid('Compact JWS must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: payload, 2: signature, length } = jws.split('.');\n    if (length !== 3) {\n        throw new errors_js_1.JWSInvalid('Invalid Compact JWS');\n    }\n    const verified = await (0, verify_js_1.flattenedVerify)({ payload, protected: protectedHeader, signature }, key, options);\n    const result = { payload: verified.payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\nexports.compactVerify = compactVerify;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,cAAc,GAAG,EAAE,GAAG,EAAE,OAAO;IAC1C,IAAI,eAAe,YAAY;QAC3B,MAAM,kBAAkB,OAAO,CAAC,MAAM,CAAC;IAC3C;IACA,IAAI,OAAO,QAAQ,UAAU;QACzB,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,EAAE,GAAG,eAAe,EAAE,GAAG,OAAO,EAAE,GAAG,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK,CAAC;IAC3E,IAAI,WAAW,GAAG;QACd,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,WAAW,MAAM,CAAC,GAAG,YAAY,eAAe,EAAE;QAAE;QAAS,WAAW;QAAiB;IAAU,GAAG,KAAK;IACjH,MAAM,SAAS;QAAE,SAAS,SAAS,OAAO;QAAE,iBAAiB,SAAS,eAAe;IAAC;IACtF,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,SAAS,GAAG;QAAC;IAC1C;IACA,OAAO;AACX;AACA,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jws/general/verify.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.generalVerify = void 0;\nconst verify_js_1 = require(\"../flattened/verify.js\");\nconst errors_js_1 = require(\"../../util/errors.js\");\nconst is_object_js_1 = require(\"../../lib/is_object.js\");\nasync function generalVerify(jws, key, options) {\n    if (!(0, is_object_js_1.default)(jws)) {\n        throw new errors_js_1.JWSInvalid('General JWS must be an object');\n    }\n    if (!Array.isArray(jws.signatures) || !jws.signatures.every(is_object_js_1.default)) {\n        throw new errors_js_1.JWSInvalid('JWS Signatures missing or incorrect type');\n    }\n    for (const signature of jws.signatures) {\n        try {\n            return await (0, verify_js_1.flattenedVerify)({\n                header: signature.header,\n                payload: jws.payload,\n                protected: signature.protected,\n                signature: signature.signature,\n            }, key, options);\n        }\n        catch {\n        }\n    }\n    throw new errors_js_1.JWSSignatureVerificationFailed();\n}\nexports.generalVerify = generalVerify;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,cAAc,GAAG,EAAE,GAAG,EAAE,OAAO;IAC1C,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,MAAM;QACnC,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,eAAe,OAAO,GAAG;QACjF,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,KAAK,MAAM,aAAa,IAAI,UAAU,CAAE;QACpC,IAAI;YACA,OAAO,MAAM,CAAC,GAAG,YAAY,eAAe,EAAE;gBAC1C,QAAQ,UAAU,MAAM;gBACxB,SAAS,IAAI,OAAO;gBACpB,WAAW,UAAU,SAAS;gBAC9B,WAAW,UAAU,SAAS;YAClC,GAAG,KAAK;QACZ,EACA,OAAM,CACN;IACJ;IACA,MAAM,IAAI,YAAY,8BAA8B;AACxD;AACA,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/epoch.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = (date) => Math.floor(date.getTime() / 1000);\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,CAAC,OAAS,KAAK,KAAK,CAAC,KAAK,OAAO,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3693, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/secs.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)$/i;\nexports.default = (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[1]);\n    const unit = matched[2].toLowerCase();\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            return Math.round(value);\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            return Math.round(value * minute);\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            return Math.round(value * hour);\n        case 'day':\n        case 'days':\n        case 'd':\n            return Math.round(value * day);\n        case 'week':\n        case 'weeks':\n        case 'w':\n            return Math.round(value * week);\n        default:\n            return Math.round(value * year);\n    }\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM,SAAS;AACf,MAAM,OAAO,SAAS;AACtB,MAAM,MAAM,OAAO;AACnB,MAAM,OAAO,MAAM;AACnB,MAAM,OAAO,MAAM;AACnB,MAAM,QAAQ;AACd,QAAQ,OAAO,GAAG,CAAC;IACf,MAAM,UAAU,MAAM,IAAI,CAAC;IAC3B,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,QAAQ,WAAW,OAAO,CAAC,EAAE;IACnC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,WAAW;IACnC,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,KAAK,KAAK,CAAC;QACtB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,KAAK,KAAK,CAAC,QAAQ;QAC9B,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,KAAK,KAAK,CAAC,QAAQ;QAC9B,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,KAAK,KAAK,CAAC,QAAQ;QAC9B,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,KAAK,KAAK,CAAC,QAAQ;QAC9B;YACI,OAAO,KAAK,KAAK,CAAC,QAAQ;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/lib/jwt_claims_set.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst errors_js_1 = require(\"../util/errors.js\");\nconst buffer_utils_js_1 = require(\"./buffer_utils.js\");\nconst epoch_js_1 = require(\"./epoch.js\");\nconst secs_js_1 = require(\"./secs.js\");\nconst is_object_js_1 = require(\"./is_object.js\");\nconst normalizeTyp = (value) => value.toLowerCase().replace(/^application\\//, '');\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\nexports.default = (protectedHeader, encodedPayload, options = {}) => {\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new errors_js_1.JWTClaimValidationFailed('unexpected \"typ\" JWT header value', 'typ', 'check_failed');\n    }\n    let payload;\n    try {\n        payload = JSON.parse(buffer_utils_js_1.decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!(0, is_object_js_1.default)(payload)) {\n        throw new errors_js_1.JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    if (maxTokenAge !== undefined)\n        requiredClaims.push('iat');\n    if (audience !== undefined)\n        requiredClaims.push('aud');\n    if (subject !== undefined)\n        requiredClaims.push('sub');\n    if (issuer !== undefined)\n        requiredClaims.push('iss');\n    for (const claim of new Set(requiredClaims.reverse())) {\n        if (!(claim in payload)) {\n            throw new errors_js_1.JWTClaimValidationFailed(`missing required \"${claim}\" claim`, claim, 'missing');\n        }\n    }\n    if (issuer && !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new errors_js_1.JWTClaimValidationFailed('unexpected \"iss\" claim value', 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new errors_js_1.JWTClaimValidationFailed('unexpected \"sub\" claim value', 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new errors_js_1.JWTClaimValidationFailed('unexpected \"aud\" claim value', 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = (0, secs_js_1.default)(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = (0, epoch_js_1.default)(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new errors_js_1.JWTClaimValidationFailed('\"iat\" claim must be a number', 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new errors_js_1.JWTClaimValidationFailed('\"nbf\" claim must be a number', 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new errors_js_1.JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new errors_js_1.JWTClaimValidationFailed('\"exp\" claim must be a number', 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new errors_js_1.JWTExpired('\"exp\" claim timestamp check failed', 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : (0, secs_js_1.default)(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new errors_js_1.JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new errors_js_1.JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', 'iat', 'check_failed');\n        }\n    }\n    return payload;\n};\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,eAAe,CAAC,QAAU,MAAM,WAAW,GAAG,OAAO,CAAC,kBAAkB;AAC9E,MAAM,wBAAwB,CAAC,YAAY;IACvC,IAAI,OAAO,eAAe,UAAU;QAChC,OAAO,UAAU,QAAQ,CAAC;IAC9B;IACA,IAAI,MAAM,OAAO,CAAC,aAAa;QAC3B,OAAO,UAAU,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI;IACzD;IACA,OAAO;AACX;AACA,QAAQ,OAAO,GAAG,CAAC,iBAAiB,gBAAgB,UAAU,CAAC,CAAC;IAC5D,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,IAAI,OACA,CAAC,OAAO,gBAAgB,GAAG,KAAK,YAC5B,aAAa,gBAAgB,GAAG,MAAM,aAAa,IAAI,GAAG;QAC9D,MAAM,IAAI,YAAY,wBAAwB,CAAC,qCAAqC,OAAO;IAC/F;IACA,IAAI;IACJ,IAAI;QACA,UAAU,KAAK,KAAK,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC;IAC1D,EACA,OAAM,CACN;IACA,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,UAAU;QACvC,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,EAAE,iBAAiB,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IACxE,IAAI,gBAAgB,WAChB,eAAe,IAAI,CAAC;IACxB,IAAI,aAAa,WACb,eAAe,IAAI,CAAC;IACxB,IAAI,YAAY,WACZ,eAAe,IAAI,CAAC;IACxB,IAAI,WAAW,WACX,eAAe,IAAI,CAAC;IACxB,KAAK,MAAM,SAAS,IAAI,IAAI,eAAe,OAAO,IAAK;QACnD,IAAI,CAAC,CAAC,SAAS,OAAO,GAAG;YACrB,MAAM,IAAI,YAAY,wBAAwB,CAAC,CAAC,kBAAkB,EAAE,MAAM,OAAO,CAAC,EAAE,OAAO;QAC/F;IACJ;IACA,IAAI,UAAU,CAAC,CAAC,MAAM,OAAO,CAAC,UAAU,SAAS;QAAC;KAAO,EAAE,QAAQ,CAAC,QAAQ,GAAG,GAAG;QAC9E,MAAM,IAAI,YAAY,wBAAwB,CAAC,gCAAgC,OAAO;IAC1F;IACA,IAAI,WAAW,QAAQ,GAAG,KAAK,SAAS;QACpC,MAAM,IAAI,YAAY,wBAAwB,CAAC,gCAAgC,OAAO;IAC1F;IACA,IAAI,YACA,CAAC,sBAAsB,QAAQ,GAAG,EAAE,OAAO,aAAa,WAAW;QAAC;KAAS,GAAG,WAAW;QAC3F,MAAM,IAAI,YAAY,wBAAwB,CAAC,gCAAgC,OAAO;IAC1F;IACA,IAAI;IACJ,OAAQ,OAAO,QAAQ,cAAc;QACjC,KAAK;YACD,YAAY,CAAC,GAAG,UAAU,OAAO,EAAE,QAAQ,cAAc;YACzD;QACJ,KAAK;YACD,YAAY,QAAQ,cAAc;YAClC;QACJ,KAAK;YACD,YAAY;YACZ;QACJ;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,MAAM,MAAM,CAAC,GAAG,WAAW,OAAO,EAAE,eAAe,IAAI;IACvD,IAAI,CAAC,QAAQ,GAAG,KAAK,aAAa,WAAW,KAAK,OAAO,QAAQ,GAAG,KAAK,UAAU;QAC/E,MAAM,IAAI,YAAY,wBAAwB,CAAC,gCAAgC,OAAO;IAC1F;IACA,IAAI,QAAQ,GAAG,KAAK,WAAW;QAC3B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;YACjC,MAAM,IAAI,YAAY,wBAAwB,CAAC,gCAAgC,OAAO;QAC1F;QACA,IAAI,QAAQ,GAAG,GAAG,MAAM,WAAW;YAC/B,MAAM,IAAI,YAAY,wBAAwB,CAAC,sCAAsC,OAAO;QAChG;IACJ;IACA,IAAI,QAAQ,GAAG,KAAK,WAAW;QAC3B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;YACjC,MAAM,IAAI,YAAY,wBAAwB,CAAC,gCAAgC,OAAO;QAC1F;QACA,IAAI,QAAQ,GAAG,IAAI,MAAM,WAAW;YAChC,MAAM,IAAI,YAAY,UAAU,CAAC,sCAAsC,OAAO;QAClF;IACJ;IACA,IAAI,aAAa;QACb,MAAM,MAAM,MAAM,QAAQ,GAAG;QAC7B,MAAM,MAAM,OAAO,gBAAgB,WAAW,cAAc,CAAC,GAAG,UAAU,OAAO,EAAE;QACnF,IAAI,MAAM,YAAY,KAAK;YACvB,MAAM,IAAI,YAAY,UAAU,CAAC,4DAA4D,OAAO;QACxG;QACA,IAAI,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,YAAY,wBAAwB,CAAC,iEAAiE,OAAO;QAC3H;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwt/verify.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.jwtVerify = void 0;\nconst verify_js_1 = require(\"../jws/compact/verify.js\");\nconst jwt_claims_set_js_1 = require(\"../lib/jwt_claims_set.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nasync function jwtVerify(jwt, key, options) {\n    var _a;\n    const verified = await (0, verify_js_1.compactVerify)(jwt, key, options);\n    if (((_a = verified.protectedHeader.crit) === null || _a === void 0 ? void 0 : _a.includes('b64')) && verified.protectedHeader.b64 === false) {\n        throw new errors_js_1.JWTInvalid('JWTs MUST NOT use unencoded payload');\n    }\n    const payload = (0, jwt_claims_set_js_1.default)(verified.protectedHeader, verified.payload, options);\n    const result = { payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\nexports.jwtVerify = jwtVerify;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,UAAU,GAAG,EAAE,GAAG,EAAE,OAAO;IACtC,IAAI;IACJ,MAAM,WAAW,MAAM,CAAC,GAAG,YAAY,aAAa,EAAE,KAAK,KAAK;IAChE,IAAI,CAAC,CAAC,KAAK,SAAS,eAAe,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,MAAM,KAAK,SAAS,eAAe,CAAC,GAAG,KAAK,OAAO;QAC1I,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,UAAU,CAAC,GAAG,oBAAoB,OAAO,EAAE,SAAS,eAAe,EAAE,SAAS,OAAO,EAAE;IAC7F,MAAM,SAAS;QAAE;QAAS,iBAAiB,SAAS,eAAe;IAAC;IACpE,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,SAAS,GAAG;QAAC;IAC1C;IACA,OAAO;AACX;AACA,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3885, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwt/decrypt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.jwtDecrypt = void 0;\nconst decrypt_js_1 = require(\"../jwe/compact/decrypt.js\");\nconst jwt_claims_set_js_1 = require(\"../lib/jwt_claims_set.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nasync function jwtDecrypt(jwt, key, options) {\n    const decrypted = await (0, decrypt_js_1.compactDecrypt)(jwt, key, options);\n    const payload = (0, jwt_claims_set_js_1.default)(decrypted.protectedHeader, decrypted.plaintext, options);\n    const { protectedHeader } = decrypted;\n    if (protectedHeader.iss !== undefined && protectedHeader.iss !== payload.iss) {\n        throw new errors_js_1.JWTClaimValidationFailed('replicated \"iss\" claim header parameter mismatch', 'iss', 'mismatch');\n    }\n    if (protectedHeader.sub !== undefined && protectedHeader.sub !== payload.sub) {\n        throw new errors_js_1.JWTClaimValidationFailed('replicated \"sub\" claim header parameter mismatch', 'sub', 'mismatch');\n    }\n    if (protectedHeader.aud !== undefined &&\n        JSON.stringify(protectedHeader.aud) !== JSON.stringify(payload.aud)) {\n        throw new errors_js_1.JWTClaimValidationFailed('replicated \"aud\" claim header parameter mismatch', 'aud', 'mismatch');\n    }\n    const result = { payload, protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\nexports.jwtDecrypt = jwtDecrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,KAAK;AAC1B,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,WAAW,GAAG,EAAE,GAAG,EAAE,OAAO;IACvC,MAAM,YAAY,MAAM,CAAC,GAAG,aAAa,cAAc,EAAE,KAAK,KAAK;IACnE,MAAM,UAAU,CAAC,GAAG,oBAAoB,OAAO,EAAE,UAAU,eAAe,EAAE,UAAU,SAAS,EAAE;IACjG,MAAM,EAAE,eAAe,EAAE,GAAG;IAC5B,IAAI,gBAAgB,GAAG,KAAK,aAAa,gBAAgB,GAAG,KAAK,QAAQ,GAAG,EAAE;QAC1E,MAAM,IAAI,YAAY,wBAAwB,CAAC,oDAAoD,OAAO;IAC9G;IACA,IAAI,gBAAgB,GAAG,KAAK,aAAa,gBAAgB,GAAG,KAAK,QAAQ,GAAG,EAAE;QAC1E,MAAM,IAAI,YAAY,wBAAwB,CAAC,oDAAoD,OAAO;IAC9G;IACA,IAAI,gBAAgB,GAAG,KAAK,aACxB,KAAK,SAAS,CAAC,gBAAgB,GAAG,MAAM,KAAK,SAAS,CAAC,QAAQ,GAAG,GAAG;QACrE,MAAM,IAAI,YAAY,wBAAwB,CAAC,oDAAoD,OAAO;IAC9G;IACA,MAAM,SAAS;QAAE;QAAS;IAAgB;IAC1C,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,UAAU,GAAG;QAAC;IAC3C;IACA,OAAO;AACX;AACA,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwe/compact/encrypt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CompactEncrypt = void 0;\nconst encrypt_js_1 = require(\"../flattened/encrypt.js\");\nclass CompactEncrypt {\n    constructor(plaintext) {\n        this._flattened = new encrypt_js_1.FlattenedEncrypt(plaintext);\n    }\n    setContentEncryptionKey(cek) {\n        this._flattened.setContentEncryptionKey(cek);\n        return this;\n    }\n    setInitializationVector(iv) {\n        this._flattened.setInitializationVector(iv);\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        this._flattened.setKeyManagementParameters(parameters);\n        return this;\n    }\n    async encrypt(key, options) {\n        const jwe = await this._flattened.encrypt(key, options);\n        return [jwe.protected, jwe.encrypted_key, jwe.iv, jwe.ciphertext, jwe.tag].join('.');\n    }\n}\nexports.CompactEncrypt = CompactEncrypt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,MAAM;IACF,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,UAAU,GAAG,IAAI,aAAa,gBAAgB,CAAC;IACxD;IACA,wBAAwB,GAAG,EAAE;QACzB,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC;QACxC,OAAO,IAAI;IACf;IACA,wBAAwB,EAAE,EAAE;QACxB,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC;QACxC,OAAO,IAAI;IACf;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;QACnC,OAAO,IAAI;IACf;IACA,2BAA2B,UAAU,EAAE;QACnC,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC;QAC3C,OAAO,IAAI;IACf;IACA,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE;QACxB,MAAM,MAAM,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK;QAC/C,OAAO;YAAC,IAAI,SAAS;YAAE,IAAI,aAAa;YAAE,IAAI,EAAE;YAAE,IAAI,UAAU;YAAE,IAAI,GAAG;SAAC,CAAC,IAAI,CAAC;IACpF;AACJ;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3967, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jws/flattened/sign.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FlattenedSign = void 0;\nconst base64url_js_1 = require(\"../../runtime/base64url.js\");\nconst sign_js_1 = require(\"../../runtime/sign.js\");\nconst is_disjoint_js_1 = require(\"../../lib/is_disjoint.js\");\nconst errors_js_1 = require(\"../../util/errors.js\");\nconst buffer_utils_js_1 = require(\"../../lib/buffer_utils.js\");\nconst check_key_type_js_1 = require(\"../../lib/check_key_type.js\");\nconst validate_crit_js_1 = require(\"../../lib/validate_crit.js\");\nclass FlattenedSign {\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this._payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader) {\n            throw new errors_js_1.JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!(0, is_disjoint_js_1.default)(this._protectedHeader, this._unprotectedHeader)) {\n            throw new errors_js_1.JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n        };\n        const extensions = (0, validate_crit_js_1.default)(errors_js_1.JWSInvalid, new Map([['b64', true]]), options === null || options === void 0 ? void 0 : options.crit, this._protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this._protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new errors_js_1.JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new errors_js_1.JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        (0, check_key_type_js_1.default)(alg, key, 'sign');\n        let payload = this._payload;\n        if (b64) {\n            payload = buffer_utils_js_1.encoder.encode((0, base64url_js_1.encode)(payload));\n        }\n        let protectedHeader;\n        if (this._protectedHeader) {\n            protectedHeader = buffer_utils_js_1.encoder.encode((0, base64url_js_1.encode)(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = buffer_utils_js_1.encoder.encode('');\n        }\n        const data = (0, buffer_utils_js_1.concat)(protectedHeader, buffer_utils_js_1.encoder.encode('.'), payload);\n        const signature = await (0, sign_js_1.default)(alg, key, data);\n        const jws = {\n            signature: (0, base64url_js_1.encode)(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = buffer_utils_js_1.decoder.decode(payload);\n        }\n        if (this._unprotectedHeader) {\n            jws.header = this._unprotectedHeader;\n        }\n        if (this._protectedHeader) {\n            jws.protected = buffer_utils_js_1.decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\nexports.FlattenedSign = FlattenedSign;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,KAAK;AAC7B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;IACF,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,CAAC,mBAAmB,UAAU,GAAG;YAClC,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,qBAAqB,iBAAiB,EAAE;QACpC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,kBAAkB,GAAG;QAC1B,OAAO,IAAI;IACf;IACA,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACpD,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,IAAI,CAAC,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,GAAG;YAChF,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,MAAM,aAAa;YACf,GAAG,IAAI,CAAC,gBAAgB;YACxB,GAAG,IAAI,CAAC,kBAAkB;QAC9B;QACA,MAAM,aAAa,CAAC,GAAG,mBAAmB,OAAO,EAAE,YAAY,UAAU,EAAE,IAAI,IAAI;YAAC;gBAAC;gBAAO;aAAK;SAAC,GAAG,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE;QAC5L,IAAI,MAAM;QACV,IAAI,WAAW,GAAG,CAAC,QAAQ;YACvB,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG;YAC/B,IAAI,OAAO,QAAQ,WAAW;gBAC1B,MAAM,IAAI,YAAY,UAAU,CAAC;YACrC;QACJ;QACA,MAAM,EAAE,GAAG,EAAE,GAAG;QAChB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;YACjC,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,CAAC,GAAG,oBAAoB,OAAO,EAAE,KAAK,KAAK;QAC3C,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,KAAK;YACL,UAAU,kBAAkB,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,eAAe,MAAM,EAAE;QAC1E;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,kBAAkB,kBAAkB,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,eAAe,MAAM,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,gBAAgB;QACtH,OACK;YACD,kBAAkB,kBAAkB,OAAO,CAAC,MAAM,CAAC;QACvD;QACA,MAAM,OAAO,CAAC,GAAG,kBAAkB,MAAM,EAAE,iBAAiB,kBAAkB,OAAO,CAAC,MAAM,CAAC,MAAM;QACnG,MAAM,YAAY,MAAM,CAAC,GAAG,UAAU,OAAO,EAAE,KAAK,KAAK;QACzD,MAAM,MAAM;YACR,WAAW,CAAC,GAAG,eAAe,MAAM,EAAE;YACtC,SAAS;QACb;QACA,IAAI,KAAK;YACL,IAAI,OAAO,GAAG,kBAAkB,OAAO,CAAC,MAAM,CAAC;QACnD;QACA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB;QACxC;QACA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,SAAS,GAAG,kBAAkB,OAAO,CAAC,MAAM,CAAC;QACrD;QACA,OAAO;IACX;AACJ;AACA,QAAQ,aAAa,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jws/compact/sign.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CompactSign = void 0;\nconst sign_js_1 = require(\"../flattened/sign.js\");\nclass CompactSign {\n    constructor(payload) {\n        this._flattened = new sign_js_1.FlattenedSign(payload);\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    async sign(key, options) {\n        const jws = await this._flattened.sign(key, options);\n        if (jws.payload === undefined) {\n            throw new TypeError('use the flattened module for creating JWS with b64: false');\n        }\n        return `${jws.protected}.${jws.payload}.${jws.signature}`;\n    }\n}\nexports.CompactSign = CompactSign;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,MAAM;AACN,MAAM;IACF,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,aAAa,CAAC;IAClD;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;QACnC,OAAO,IAAI;IACf;IACA,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE;QACrB,MAAM,MAAM,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK;QAC5C,IAAI,IAAI,OAAO,KAAK,WAAW;YAC3B,MAAM,IAAI,UAAU;QACxB;QACA,OAAO,GAAG,IAAI,SAAS,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,SAAS,EAAE;IAC7D;AACJ;AACA,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4091, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jws/general/sign.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.GeneralSign = void 0;\nconst sign_js_1 = require(\"../flattened/sign.js\");\nconst errors_js_1 = require(\"../../util/errors.js\");\nclass IndividualSignature {\n    constructor(sig, key, options) {\n        this.parent = sig;\n        this.key = key;\n        this.options = options;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    addSignature(...args) {\n        return this.parent.addSignature(...args);\n    }\n    sign(...args) {\n        return this.parent.sign(...args);\n    }\n    done() {\n        return this.parent;\n    }\n}\nclass GeneralSign {\n    constructor(payload) {\n        this._signatures = [];\n        this._payload = payload;\n    }\n    addSignature(key, options) {\n        const signature = new IndividualSignature(this, key, options);\n        this._signatures.push(signature);\n        return signature;\n    }\n    async sign() {\n        if (!this._signatures.length) {\n            throw new errors_js_1.JWSInvalid('at least one signature must be added');\n        }\n        const jws = {\n            signatures: [],\n            payload: '',\n        };\n        for (let i = 0; i < this._signatures.length; i++) {\n            const signature = this._signatures[i];\n            const flattened = new sign_js_1.FlattenedSign(this._payload);\n            flattened.setProtectedHeader(signature.protectedHeader);\n            flattened.setUnprotectedHeader(signature.unprotectedHeader);\n            const { payload, ...rest } = await flattened.sign(signature.key, signature.options);\n            if (i === 0) {\n                jws.payload = payload;\n            }\n            else if (jws.payload !== payload) {\n                throw new errors_js_1.JWSInvalid('inconsistent use of JWS Unencoded Payload (RFC7797)');\n            }\n            jws.signatures.push(rest);\n        }\n        return jws;\n    }\n}\nexports.GeneralSign = GeneralSign;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,MAAM;AACN,MAAM;AACN,MAAM;IACF,YAAY,GAAG,EAAE,GAAG,EAAE,OAAO,CAAE;QAC3B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,eAAe,GAAG;QACvB,OAAO,IAAI;IACf;IACA,qBAAqB,iBAAiB,EAAE;QACpC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,iBAAiB,GAAG;QACzB,OAAO,IAAI;IACf;IACA,aAAa,GAAG,IAAI,EAAE;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI;IACvC;IACA,KAAK,GAAG,IAAI,EAAE;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI;IAC/B;IACA,OAAO;QACH,OAAO,IAAI,CAAC,MAAM;IACtB;AACJ;AACA,MAAM;IACF,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,aAAa,GAAG,EAAE,OAAO,EAAE;QACvB,MAAM,YAAY,IAAI,oBAAoB,IAAI,EAAE,KAAK;QACrD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACtB,OAAO;IACX;IACA,MAAM,OAAO;QACT,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YAC1B,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,MAAM,MAAM;YACR,YAAY,EAAE;YACd,SAAS;QACb;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAK;YAC9C,MAAM,YAAY,IAAI,CAAC,WAAW,CAAC,EAAE;YACrC,MAAM,YAAY,IAAI,UAAU,aAAa,CAAC,IAAI,CAAC,QAAQ;YAC3D,UAAU,kBAAkB,CAAC,UAAU,eAAe;YACtD,UAAU,oBAAoB,CAAC,UAAU,iBAAiB;YAC1D,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,GAAG,MAAM,UAAU,IAAI,CAAC,UAAU,GAAG,EAAE,UAAU,OAAO;YAClF,IAAI,MAAM,GAAG;gBACT,IAAI,OAAO,GAAG;YAClB,OACK,IAAI,IAAI,OAAO,KAAK,SAAS;gBAC9B,MAAM,IAAI,YAAY,UAAU,CAAC;YACrC;YACA,IAAI,UAAU,CAAC,IAAI,CAAC;QACxB;QACA,OAAO;IACX;AACJ;AACA,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwt/produce.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ProduceJWT = void 0;\nconst epoch_js_1 = require(\"../lib/epoch.js\");\nconst is_object_js_1 = require(\"../lib/is_object.js\");\nconst secs_js_1 = require(\"../lib/secs.js\");\nclass ProduceJWT {\n    constructor(payload) {\n        if (!(0, is_object_js_1.default)(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this._payload = payload;\n    }\n    setIssuer(issuer) {\n        this._payload = { ...this._payload, iss: issuer };\n        return this;\n    }\n    setSubject(subject) {\n        this._payload = { ...this._payload, sub: subject };\n        return this;\n    }\n    setAudience(audience) {\n        this._payload = { ...this._payload, aud: audience };\n        return this;\n    }\n    setJti(jwtId) {\n        this._payload = { ...this._payload, jti: jwtId };\n        return this;\n    }\n    setNotBefore(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, nbf: input };\n        }\n        else {\n            this._payload = { ...this._payload, nbf: (0, epoch_js_1.default)(new Date()) + (0, secs_js_1.default)(input) };\n        }\n        return this;\n    }\n    setExpirationTime(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, exp: input };\n        }\n        else {\n            this._payload = { ...this._payload, exp: (0, epoch_js_1.default)(new Date()) + (0, secs_js_1.default)(input) };\n        }\n        return this;\n    }\n    setIssuedAt(input) {\n        if (typeof input === 'undefined') {\n            this._payload = { ...this._payload, iat: (0, epoch_js_1.default)(new Date()) };\n        }\n        else {\n            this._payload = { ...this._payload, iat: input };\n        }\n        return this;\n    }\n}\nexports.ProduceJWT = ProduceJWT;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,KAAK;AAC1B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;IACF,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,UAAU;YACvC,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,UAAU,MAAM,EAAE;QACd,IAAI,CAAC,QAAQ,GAAG;YAAE,GAAG,IAAI,CAAC,QAAQ;YAAE,KAAK;QAAO;QAChD,OAAO,IAAI;IACf;IACA,WAAW,OAAO,EAAE;QAChB,IAAI,CAAC,QAAQ,GAAG;YAAE,GAAG,IAAI,CAAC,QAAQ;YAAE,KAAK;QAAQ;QACjD,OAAO,IAAI;IACf;IACA,YAAY,QAAQ,EAAE;QAClB,IAAI,CAAC,QAAQ,GAAG;YAAE,GAAG,IAAI,CAAC,QAAQ;YAAE,KAAK;QAAS;QAClD,OAAO,IAAI;IACf;IACA,OAAO,KAAK,EAAE;QACV,IAAI,CAAC,QAAQ,GAAG;YAAE,GAAG,IAAI,CAAC,QAAQ;YAAE,KAAK;QAAM;QAC/C,OAAO,IAAI;IACf;IACA,aAAa,KAAK,EAAE;QAChB,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK;YAAM;QACnD,OACK;YACD,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,CAAC,GAAG,WAAW,OAAO,EAAE,IAAI,UAAU,CAAC,GAAG,UAAU,OAAO,EAAE;YAAO;QACjH;QACA,OAAO,IAAI;IACf;IACA,kBAAkB,KAAK,EAAE;QACrB,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK;YAAM;QACnD,OACK;YACD,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,CAAC,GAAG,WAAW,OAAO,EAAE,IAAI,UAAU,CAAC,GAAG,UAAU,OAAO,EAAE;YAAO;QACjH;QACA,OAAO,IAAI;IACf;IACA,YAAY,KAAK,EAAE;QACf,IAAI,OAAO,UAAU,aAAa;YAC9B,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK,CAAC,GAAG,WAAW,OAAO,EAAE,IAAI;YAAQ;QACjF,OACK;YACD,IAAI,CAAC,QAAQ,GAAG;gBAAE,GAAG,IAAI,CAAC,QAAQ;gBAAE,KAAK;YAAM;QACnD;QACA,OAAO,IAAI;IACf;AACJ;AACA,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwt/sign.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SignJWT = void 0;\nconst sign_js_1 = require(\"../jws/compact/sign.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst produce_js_1 = require(\"./produce.js\");\nclass SignJWT extends produce_js_1.ProduceJWT {\n    setProtectedHeader(protectedHeader) {\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        var _a;\n        const sig = new sign_js_1.CompactSign(buffer_utils_js_1.encoder.encode(JSON.stringify(this._payload)));\n        sig.setProtectedHeader(this._protectedHeader);\n        if (Array.isArray((_a = this._protectedHeader) === null || _a === void 0 ? void 0 : _a.crit) &&\n            this._protectedHeader.crit.includes('b64') &&\n            this._protectedHeader.b64 === false) {\n            throw new errors_js_1.JWTInvalid('JWTs MUST NOT use unencoded payload');\n        }\n        return sig.sign(key, options);\n    }\n}\nexports.SignJWT = SignJWT;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG,KAAK;AACvB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,gBAAgB,aAAa,UAAU;IACzC,mBAAmB,eAAe,EAAE;QAChC,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE;QACrB,IAAI;QACJ,MAAM,MAAM,IAAI,UAAU,WAAW,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ;QACnG,IAAI,kBAAkB,CAAC,IAAI,CAAC,gBAAgB;QAC5C,IAAI,MAAM,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,gBAAgB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,KACvF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,UACpC,IAAI,CAAC,gBAAgB,CAAC,GAAG,KAAK,OAAO;YACrC,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,OAAO,IAAI,IAAI,CAAC,KAAK;IACzB;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwt/encrypt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EncryptJWT = void 0;\nconst encrypt_js_1 = require(\"../jwe/compact/encrypt.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst produce_js_1 = require(\"./produce.js\");\nclass EncryptJWT extends produce_js_1.ProduceJWT {\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    replicateIssuerAsHeader() {\n        this._replicateIssuerAsHeader = true;\n        return this;\n    }\n    replicateSubjectAsHeader() {\n        this._replicateSubjectAsHeader = true;\n        return this;\n    }\n    replicateAudienceAsHeader() {\n        this._replicateAudienceAsHeader = true;\n        return this;\n    }\n    async encrypt(key, options) {\n        const enc = new encrypt_js_1.CompactEncrypt(buffer_utils_js_1.encoder.encode(JSON.stringify(this._payload)));\n        if (this._replicateIssuerAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, iss: this._payload.iss };\n        }\n        if (this._replicateSubjectAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, sub: this._payload.sub };\n        }\n        if (this._replicateAudienceAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, aud: this._payload.aud };\n        }\n        enc.setProtectedHeader(this._protectedHeader);\n        if (this._iv) {\n            enc.setInitializationVector(this._iv);\n        }\n        if (this._cek) {\n            enc.setContentEncryptionKey(this._cek);\n        }\n        if (this._keyManagementParameters) {\n            enc.setKeyManagementParameters(this._keyManagementParameters);\n        }\n        return enc.encrypt(key, options);\n    }\n}\nexports.EncryptJWT = EncryptJWT;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,KAAK;AAC1B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,mBAAmB,aAAa,UAAU;IAC5C,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,2BAA2B,UAAU,EAAE;QACnC,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,wBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,wBAAwB,GAAG,EAAE;QACzB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,IAAI;IACf;IACA,wBAAwB,EAAE,EAAE;QACxB,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,GAAG,GAAG;QACX,OAAO,IAAI;IACf;IACA,0BAA0B;QACtB,IAAI,CAAC,wBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,2BAA2B;QACvB,IAAI,CAAC,yBAAyB,GAAG;QACjC,OAAO,IAAI;IACf;IACA,4BAA4B;QACxB,IAAI,CAAC,0BAA0B,GAAG;QAClC,OAAO,IAAI;IACf;IACA,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE;QACxB,MAAM,MAAM,IAAI,aAAa,cAAc,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ;QACzG,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,gBAAgB,GAAG;gBAAE,GAAG,IAAI,CAAC,gBAAgB;gBAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG;YAAC;QAC/E;QACA,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,gBAAgB,GAAG;gBAAE,GAAG,IAAI,CAAC,gBAAgB;gBAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG;YAAC;QAC/E;QACA,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,gBAAgB,GAAG;gBAAE,GAAG,IAAI,CAAC,gBAAgB;gBAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG;YAAC;QAC/E;QACA,IAAI,kBAAkB,CAAC,IAAI,CAAC,gBAAgB;QAC5C,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,IAAI,uBAAuB,CAAC,IAAI,CAAC,GAAG;QACxC;QACA,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,uBAAuB,CAAC,IAAI,CAAC,IAAI;QACzC;QACA,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,0BAA0B,CAAC,IAAI,CAAC,wBAAwB;QAChE;QACA,OAAO,IAAI,OAAO,CAAC,KAAK;IAC5B;AACJ;AACA,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwk/thumbprint.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.calculateJwkThumbprintUri = exports.calculateJwkThumbprint = void 0;\nconst digest_js_1 = require(\"../runtime/digest.js\");\nconst base64url_js_1 = require(\"../runtime/base64url.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst is_object_js_1 = require(\"../lib/is_object.js\");\nconst check = (value, description) => {\n    if (typeof value !== 'string' || !value) {\n        throw new errors_js_1.JWKInvalid(`${description} missing or invalid`);\n    }\n};\nasync function calculateJwkThumbprint(jwk, digestAlgorithm) {\n    if (!(0, is_object_js_1.default)(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    digestAlgorithm !== null && digestAlgorithm !== void 0 ? digestAlgorithm : (digestAlgorithm = 'sha256');\n    if (digestAlgorithm !== 'sha256' &&\n        digestAlgorithm !== 'sha384' &&\n        digestAlgorithm !== 'sha512') {\n        throw new TypeError('digestAlgorithm must one of \"sha256\", \"sha384\", or \"sha512\"');\n    }\n    let components;\n    switch (jwk.kty) {\n        case 'EC':\n            check(jwk.crv, '\"crv\" (Curve) Parameter');\n            check(jwk.x, '\"x\" (X Coordinate) Parameter');\n            check(jwk.y, '\"y\" (Y Coordinate) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x, y: jwk.y };\n            break;\n        case 'OKP':\n            check(jwk.crv, '\"crv\" (Subtype of Key Pair) Parameter');\n            check(jwk.x, '\"x\" (Public Key) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x };\n            break;\n        case 'RSA':\n            check(jwk.e, '\"e\" (Exponent) Parameter');\n            check(jwk.n, '\"n\" (Modulus) Parameter');\n            components = { e: jwk.e, kty: jwk.kty, n: jwk.n };\n            break;\n        case 'oct':\n            check(jwk.k, '\"k\" (Key Value) Parameter');\n            components = { k: jwk.k, kty: jwk.kty };\n            break;\n        default:\n            throw new errors_js_1.JOSENotSupported('\"kty\" (Key Type) Parameter missing or unsupported');\n    }\n    const data = buffer_utils_js_1.encoder.encode(JSON.stringify(components));\n    return (0, base64url_js_1.encode)(await (0, digest_js_1.default)(digestAlgorithm, data));\n}\nexports.calculateJwkThumbprint = calculateJwkThumbprint;\nasync function calculateJwkThumbprintUri(jwk, digestAlgorithm) {\n    digestAlgorithm !== null && digestAlgorithm !== void 0 ? digestAlgorithm : (digestAlgorithm = 'sha256');\n    const thumbprint = await calculateJwkThumbprint(jwk, digestAlgorithm);\n    return `urn:ietf:params:oauth:jwk-thumbprint:sha-${digestAlgorithm.slice(-3)}:${thumbprint}`;\n}\nexports.calculateJwkThumbprintUri = calculateJwkThumbprintUri;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,yBAAyB,GAAG,QAAQ,sBAAsB,GAAG,KAAK;AAC1E,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC,OAAO;IAClB,IAAI,OAAO,UAAU,YAAY,CAAC,OAAO;QACrC,MAAM,IAAI,YAAY,UAAU,CAAC,GAAG,YAAY,mBAAmB,CAAC;IACxE;AACJ;AACA,eAAe,uBAAuB,GAAG,EAAE,eAAe;IACtD,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,MAAM;QACnC,MAAM,IAAI,UAAU;IACxB;IACA,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,kBAAmB,kBAAkB;IAC9F,IAAI,oBAAoB,YACpB,oBAAoB,YACpB,oBAAoB,UAAU;QAC9B,MAAM,IAAI,UAAU;IACxB;IACA,IAAI;IACJ,OAAQ,IAAI,GAAG;QACX,KAAK;YACD,MAAM,IAAI,GAAG,EAAE;YACf,MAAM,IAAI,CAAC,EAAE;YACb,MAAM,IAAI,CAAC,EAAE;YACb,aAAa;gBAAE,KAAK,IAAI,GAAG;gBAAE,KAAK,IAAI,GAAG;gBAAE,GAAG,IAAI,CAAC;gBAAE,GAAG,IAAI,CAAC;YAAC;YAC9D;QACJ,KAAK;YACD,MAAM,IAAI,GAAG,EAAE;YACf,MAAM,IAAI,CAAC,EAAE;YACb,aAAa;gBAAE,KAAK,IAAI,GAAG;gBAAE,KAAK,IAAI,GAAG;gBAAE,GAAG,IAAI,CAAC;YAAC;YACpD;QACJ,KAAK;YACD,MAAM,IAAI,CAAC,EAAE;YACb,MAAM,IAAI,CAAC,EAAE;YACb,aAAa;gBAAE,GAAG,IAAI,CAAC;gBAAE,KAAK,IAAI,GAAG;gBAAE,GAAG,IAAI,CAAC;YAAC;YAChD;QACJ,KAAK;YACD,MAAM,IAAI,CAAC,EAAE;YACb,aAAa;gBAAE,GAAG,IAAI,CAAC;gBAAE,KAAK,IAAI,GAAG;YAAC;YACtC;QACJ;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;IACA,MAAM,OAAO,kBAAkB,OAAO,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC;IAC7D,OAAO,CAAC,GAAG,eAAe,MAAM,EAAE,MAAM,CAAC,GAAG,YAAY,OAAO,EAAE,iBAAiB;AACtF;AACA,QAAQ,sBAAsB,GAAG;AACjC,eAAe,0BAA0B,GAAG,EAAE,eAAe;IACzD,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,kBAAmB,kBAAkB;IAC9F,MAAM,aAAa,MAAM,uBAAuB,KAAK;IACrD,OAAO,CAAC,yCAAyC,EAAE,gBAAgB,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,YAAY;AAChG;AACA,QAAQ,yBAAyB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwk/embedded.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EmbeddedJWK = void 0;\nconst import_js_1 = require(\"../key/import.js\");\nconst is_object_js_1 = require(\"../lib/is_object.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nasync function EmbeddedJWK(protectedHeader, token) {\n    const joseHeader = {\n        ...protectedHeader,\n        ...token === null || token === void 0 ? void 0 : token.header,\n    };\n    if (!(0, is_object_js_1.default)(joseHeader.jwk)) {\n        throw new errors_js_1.JWSInvalid('\"jwk\" (JSON Web Key) Header Parameter must be a JSON object');\n    }\n    const key = await (0, import_js_1.importJWK)({ ...joseHeader.jwk, ext: true }, joseHeader.alg, true);\n    if (key instanceof Uint8Array || key.type !== 'public') {\n        throw new errors_js_1.JWSInvalid('\"jwk\" (JSON Web Key) Header Parameter must be a public key');\n    }\n    return key;\n}\nexports.EmbeddedJWK = EmbeddedJWK;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,MAAM;AACN,MAAM;AACN,MAAM;AACN,eAAe,YAAY,eAAe,EAAE,KAAK;IAC7C,MAAM,aAAa;QACf,GAAG,eAAe;QAClB,GAAG,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM;IACjE;IACA,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,WAAW,GAAG,GAAG;QAC9C,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,MAAM,MAAM,MAAM,CAAC,GAAG,YAAY,SAAS,EAAE;QAAE,GAAG,WAAW,GAAG;QAAE,KAAK;IAAK,GAAG,WAAW,GAAG,EAAE;IAC/F,IAAI,eAAe,cAAc,IAAI,IAAI,KAAK,UAAU;QACpD,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,OAAO;AACX;AACA,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwks/local.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createLocalJWKSet = exports.LocalJWKSet = exports.isJWKSLike = void 0;\nconst import_js_1 = require(\"../key/import.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst is_object_js_1 = require(\"../lib/is_object.js\");\nfunction getKtyFromAlg(alg) {\n    switch (typeof alg === 'string' && alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            return 'RSA';\n        case 'ES':\n            return 'EC';\n        case 'Ed':\n            return 'OKP';\n        default:\n            throw new errors_js_1.JOSENotSupported('Unsupported \"alg\" value for a JSON Web Key Set');\n    }\n}\nfunction isJWKSLike(jwks) {\n    return (jwks &&\n        typeof jwks === 'object' &&\n        Array.isArray(jwks.keys) &&\n        jwks.keys.every(isJWKLike));\n}\nexports.isJWKSLike = isJWKSLike;\nfunction isJWKLike(key) {\n    return (0, is_object_js_1.default)(key);\n}\nfunction clone(obj) {\n    if (typeof structuredClone === 'function') {\n        return structuredClone(obj);\n    }\n    return JSON.parse(JSON.stringify(obj));\n}\nclass LocalJWKSet {\n    constructor(jwks) {\n        this._cached = new WeakMap();\n        if (!isJWKSLike(jwks)) {\n            throw new errors_js_1.JWKSInvalid('JSON Web Key Set malformed');\n        }\n        this._jwks = clone(jwks);\n    }\n    async getKey(protectedHeader, token) {\n        const { alg, kid } = { ...protectedHeader, ...token === null || token === void 0 ? void 0 : token.header };\n        const kty = getKtyFromAlg(alg);\n        const candidates = this._jwks.keys.filter((jwk) => {\n            let candidate = kty === jwk.kty;\n            if (candidate && typeof kid === 'string') {\n                candidate = kid === jwk.kid;\n            }\n            if (candidate && typeof jwk.alg === 'string') {\n                candidate = alg === jwk.alg;\n            }\n            if (candidate && typeof jwk.use === 'string') {\n                candidate = jwk.use === 'sig';\n            }\n            if (candidate && Array.isArray(jwk.key_ops)) {\n                candidate = jwk.key_ops.includes('verify');\n            }\n            if (candidate && alg === 'EdDSA') {\n                candidate = jwk.crv === 'Ed25519' || jwk.crv === 'Ed448';\n            }\n            if (candidate) {\n                switch (alg) {\n                    case 'ES256':\n                        candidate = jwk.crv === 'P-256';\n                        break;\n                    case 'ES256K':\n                        candidate = jwk.crv === 'secp256k1';\n                        break;\n                    case 'ES384':\n                        candidate = jwk.crv === 'P-384';\n                        break;\n                    case 'ES512':\n                        candidate = jwk.crv === 'P-521';\n                        break;\n                }\n            }\n            return candidate;\n        });\n        const { 0: jwk, length } = candidates;\n        if (length === 0) {\n            throw new errors_js_1.JWKSNoMatchingKey();\n        }\n        else if (length !== 1) {\n            const error = new errors_js_1.JWKSMultipleMatchingKeys();\n            const { _cached } = this;\n            error[Symbol.asyncIterator] = async function* () {\n                for (const jwk of candidates) {\n                    try {\n                        yield await importWithAlgCache(_cached, jwk, alg);\n                    }\n                    catch {\n                        continue;\n                    }\n                }\n            };\n            throw error;\n        }\n        return importWithAlgCache(this._cached, jwk, alg);\n    }\n}\nexports.LocalJWKSet = LocalJWKSet;\nasync function importWithAlgCache(cache, jwk, alg) {\n    const cached = cache.get(jwk) || cache.set(jwk, {}).get(jwk);\n    if (cached[alg] === undefined) {\n        const key = await (0, import_js_1.importJWK)({ ...jwk, ext: true }, alg);\n        if (key instanceof Uint8Array || key.type !== 'public') {\n            throw new errors_js_1.JWKSInvalid('JSON Web Key Set members must be public keys');\n        }\n        cached[alg] = key;\n    }\n    return cached[alg];\n}\nfunction createLocalJWKSet(jwks) {\n    const set = new LocalJWKSet(jwks);\n    return async function (protectedHeader, token) {\n        return set.getKey(protectedHeader, token);\n    };\n}\nexports.createLocalJWKSet = createLocalJWKSet;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,QAAQ,WAAW,GAAG,QAAQ,UAAU,GAAG,KAAK;AAC5E,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,cAAc,GAAG;IACtB,OAAQ,OAAO,QAAQ,YAAY,IAAI,KAAK,CAAC,GAAG;QAC5C,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;AACJ;AACA,SAAS,WAAW,IAAI;IACpB,OAAQ,QACJ,OAAO,SAAS,YAChB,MAAM,OAAO,CAAC,KAAK,IAAI,KACvB,KAAK,IAAI,CAAC,KAAK,CAAC;AACxB;AACA,QAAQ,UAAU,GAAG;AACrB,SAAS,UAAU,GAAG;IAClB,OAAO,CAAC,GAAG,eAAe,OAAO,EAAE;AACvC;AACA,SAAS,MAAM,GAAG;IACd,IAAI,OAAO,oBAAoB,YAAY;QACvC,OAAO,gBAAgB;IAC3B;IACA,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;AACrC;AACA,MAAM;IACF,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC,WAAW,OAAO;YACnB,MAAM,IAAI,YAAY,WAAW,CAAC;QACtC;QACA,IAAI,CAAC,KAAK,GAAG,MAAM;IACvB;IACA,MAAM,OAAO,eAAe,EAAE,KAAK,EAAE;QACjC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;YAAE,GAAG,eAAe;YAAE,GAAG,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM;QAAC;QACzG,MAAM,MAAM,cAAc;QAC1B,MAAM,aAAa,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,IAAI,YAAY,QAAQ,IAAI,GAAG;YAC/B,IAAI,aAAa,OAAO,QAAQ,UAAU;gBACtC,YAAY,QAAQ,IAAI,GAAG;YAC/B;YACA,IAAI,aAAa,OAAO,IAAI,GAAG,KAAK,UAAU;gBAC1C,YAAY,QAAQ,IAAI,GAAG;YAC/B;YACA,IAAI,aAAa,OAAO,IAAI,GAAG,KAAK,UAAU;gBAC1C,YAAY,IAAI,GAAG,KAAK;YAC5B;YACA,IAAI,aAAa,MAAM,OAAO,CAAC,IAAI,OAAO,GAAG;gBACzC,YAAY,IAAI,OAAO,CAAC,QAAQ,CAAC;YACrC;YACA,IAAI,aAAa,QAAQ,SAAS;gBAC9B,YAAY,IAAI,GAAG,KAAK,aAAa,IAAI,GAAG,KAAK;YACrD;YACA,IAAI,WAAW;gBACX,OAAQ;oBACJ,KAAK;wBACD,YAAY,IAAI,GAAG,KAAK;wBACxB;oBACJ,KAAK;wBACD,YAAY,IAAI,GAAG,KAAK;wBACxB;oBACJ,KAAK;wBACD,YAAY,IAAI,GAAG,KAAK;wBACxB;oBACJ,KAAK;wBACD,YAAY,IAAI,GAAG,KAAK;wBACxB;gBACR;YACJ;YACA,OAAO;QACX;QACA,MAAM,EAAE,GAAG,GAAG,EAAE,MAAM,EAAE,GAAG;QAC3B,IAAI,WAAW,GAAG;YACd,MAAM,IAAI,YAAY,iBAAiB;QAC3C,OACK,IAAI,WAAW,GAAG;YACnB,MAAM,QAAQ,IAAI,YAAY,wBAAwB;YACtD,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI;YACxB,KAAK,CAAC,OAAO,aAAa,CAAC,GAAG;gBAC1B,KAAK,MAAM,OAAO,WAAY;oBAC1B,IAAI;wBACA,MAAM,MAAM,mBAAmB,SAAS,KAAK;oBACjD,EACA,OAAM;wBACF;oBACJ;gBACJ;YACJ;YACA,MAAM;QACV;QACA,OAAO,mBAAmB,IAAI,CAAC,OAAO,EAAE,KAAK;IACjD;AACJ;AACA,QAAQ,WAAW,GAAG;AACtB,eAAe,mBAAmB,KAAK,EAAE,GAAG,EAAE,GAAG;IAC7C,MAAM,SAAS,MAAM,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;IACxD,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;QAC3B,MAAM,MAAM,MAAM,CAAC,GAAG,YAAY,SAAS,EAAE;YAAE,GAAG,GAAG;YAAE,KAAK;QAAK,GAAG;QACpE,IAAI,eAAe,cAAc,IAAI,IAAI,KAAK,UAAU;YACpD,MAAM,IAAI,YAAY,WAAW,CAAC;QACtC;QACA,MAAM,CAAC,IAAI,GAAG;IAClB;IACA,OAAO,MAAM,CAAC,IAAI;AACtB;AACA,SAAS,kBAAkB,IAAI;IAC3B,MAAM,MAAM,IAAI,YAAY;IAC5B,OAAO,eAAgB,eAAe,EAAE,KAAK;QACzC,OAAO,IAAI,MAAM,CAAC,iBAAiB;IACvC;AACJ;AACA,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4618, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/fetch_jwks.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst http = require(\"http\");\nconst https = require(\"https\");\nconst events_1 = require(\"events\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst fetchJwks = async (url, timeout, options) => {\n    let get;\n    switch (url.protocol) {\n        case 'https:':\n            get = https.get;\n            break;\n        case 'http:':\n            get = http.get;\n            break;\n        default:\n            throw new TypeError('Unsupported URL protocol.');\n    }\n    const { agent, headers } = options;\n    const req = get(url.href, {\n        agent,\n        timeout,\n        headers,\n    });\n    const [response] = (await Promise.race([(0, events_1.once)(req, 'response'), (0, events_1.once)(req, 'timeout')]));\n    if (!response) {\n        req.destroy();\n        throw new errors_js_1.JWKSTimeout();\n    }\n    if (response.statusCode !== 200) {\n        throw new errors_js_1.JOSEError('Expected 200 OK from the JSON Web Key Set HTTP response');\n    }\n    const parts = [];\n    for await (const part of response) {\n        parts.push(part);\n    }\n    try {\n        return JSON.parse(buffer_utils_js_1.decoder.decode((0, buffer_utils_js_1.concat)(...parts)));\n    }\n    catch {\n        throw new errors_js_1.JOSEError('Failed to parse the JSON Web Key Set HTTP response as JSON');\n    }\n};\nexports.default = fetchJwks;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,YAAY,OAAO,KAAK,SAAS;IACnC,IAAI;IACJ,OAAQ,IAAI,QAAQ;QAChB,KAAK;YACD,MAAM,MAAM,GAAG;YACf;QACJ,KAAK;YACD,MAAM,KAAK,GAAG;YACd;QACJ;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG;IAC3B,MAAM,MAAM,IAAI,IAAI,IAAI,EAAE;QACtB;QACA;QACA;IACJ;IACA,MAAM,CAAC,SAAS,GAAI,MAAM,QAAQ,IAAI,CAAC;QAAC,CAAC,GAAG,SAAS,IAAI,EAAE,KAAK;QAAa,CAAC,GAAG,SAAS,IAAI,EAAE,KAAK;KAAW;IAChH,IAAI,CAAC,UAAU;QACX,IAAI,OAAO;QACX,MAAM,IAAI,YAAY,WAAW;IACrC;IACA,IAAI,SAAS,UAAU,KAAK,KAAK;QAC7B,MAAM,IAAI,YAAY,SAAS,CAAC;IACpC;IACA,MAAM,QAAQ,EAAE;IAChB,WAAW,MAAM,QAAQ,SAAU;QAC/B,MAAM,IAAI,CAAC;IACf;IACA,IAAI;QACA,OAAO,KAAK,KAAK,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,kBAAkB,MAAM,KAAK;IACxF,EACA,OAAM;QACF,MAAM,IAAI,YAAY,SAAS,CAAC;IACpC;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4672, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwks/remote.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createRemoteJWKSet = void 0;\nconst fetch_jwks_js_1 = require(\"../runtime/fetch_jwks.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst local_js_1 = require(\"./local.js\");\nfunction isCloudflareWorkers() {\n    return (typeof WebSocketPair !== 'undefined' ||\n        (typeof navigator !== 'undefined' && navigator.userAgent === 'Cloudflare-Workers') ||\n        (typeof EdgeRuntime !== 'undefined' && EdgeRuntime === 'vercel'));\n}\nclass RemoteJWKSet extends local_js_1.LocalJWKSet {\n    constructor(url, options) {\n        super({ keys: [] });\n        this._jwks = undefined;\n        if (!(url instanceof URL)) {\n            throw new TypeError('url must be an instance of URL');\n        }\n        this._url = new URL(url.href);\n        this._options = { agent: options === null || options === void 0 ? void 0 : options.agent, headers: options === null || options === void 0 ? void 0 : options.headers };\n        this._timeoutDuration =\n            typeof (options === null || options === void 0 ? void 0 : options.timeoutDuration) === 'number' ? options === null || options === void 0 ? void 0 : options.timeoutDuration : 5000;\n        this._cooldownDuration =\n            typeof (options === null || options === void 0 ? void 0 : options.cooldownDuration) === 'number' ? options === null || options === void 0 ? void 0 : options.cooldownDuration : 30000;\n        this._cacheMaxAge = typeof (options === null || options === void 0 ? void 0 : options.cacheMaxAge) === 'number' ? options === null || options === void 0 ? void 0 : options.cacheMaxAge : 600000;\n    }\n    coolingDown() {\n        return typeof this._jwksTimestamp === 'number'\n            ? Date.now() < this._jwksTimestamp + this._cooldownDuration\n            : false;\n    }\n    fresh() {\n        return typeof this._jwksTimestamp === 'number'\n            ? Date.now() < this._jwksTimestamp + this._cacheMaxAge\n            : false;\n    }\n    async getKey(protectedHeader, token) {\n        if (!this._jwks || !this.fresh()) {\n            await this.reload();\n        }\n        try {\n            return await super.getKey(protectedHeader, token);\n        }\n        catch (err) {\n            if (err instanceof errors_js_1.JWKSNoMatchingKey) {\n                if (this.coolingDown() === false) {\n                    await this.reload();\n                    return super.getKey(protectedHeader, token);\n                }\n            }\n            throw err;\n        }\n    }\n    async reload() {\n        if (this._pendingFetch && isCloudflareWorkers()) {\n            this._pendingFetch = undefined;\n        }\n        this._pendingFetch || (this._pendingFetch = (0, fetch_jwks_js_1.default)(this._url, this._timeoutDuration, this._options)\n            .then((json) => {\n            if (!(0, local_js_1.isJWKSLike)(json)) {\n                throw new errors_js_1.JWKSInvalid('JSON Web Key Set malformed');\n            }\n            this._jwks = { keys: json.keys };\n            this._jwksTimestamp = Date.now();\n            this._pendingFetch = undefined;\n        })\n            .catch((err) => {\n            this._pendingFetch = undefined;\n            throw err;\n        }));\n        await this._pendingFetch;\n    }\n}\nfunction createRemoteJWKSet(url, options) {\n    const set = new RemoteJWKSet(url, options);\n    return async function (protectedHeader, token) {\n        return set.getKey(protectedHeader, token);\n    };\n}\nexports.createRemoteJWKSet = createRemoteJWKSet;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,kBAAkB,GAAG,KAAK;AAClC,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS;IACL,OAAQ,OAAO,kBAAkB,eAC5B,OAAO,cAAc,eAAe,UAAU,SAAS,KAAK,wBAC5D,OAAO,gBAAgB,eAAe,gBAAgB;AAC/D;AACA,MAAM,qBAAqB,WAAW,WAAW;IAC7C,YAAY,GAAG,EAAE,OAAO,CAAE;QACtB,KAAK,CAAC;YAAE,MAAM,EAAE;QAAC;QACjB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,CAAC,eAAe,GAAG,GAAG;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI;QAC5B,IAAI,CAAC,QAAQ,GAAG;YAAE,OAAO,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK;YAAE,SAAS,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,OAAO;QAAC;QACrK,IAAI,CAAC,gBAAgB,GACjB,OAAO,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,eAAe,MAAM,WAAW,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,eAAe,GAAG;QAClL,IAAI,CAAC,iBAAiB,GAClB,OAAO,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,gBAAgB,MAAM,WAAW,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,gBAAgB,GAAG;QACpL,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,WAAW,MAAM,WAAW,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,WAAW,GAAG;IAC9L;IACA,cAAc;QACV,OAAO,OAAO,IAAI,CAAC,cAAc,KAAK,WAChC,KAAK,GAAG,KAAK,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,GACzD;IACV;IACA,QAAQ;QACJ,OAAO,OAAO,IAAI,CAAC,cAAc,KAAK,WAChC,KAAK,GAAG,KAAK,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,GACpD;IACV;IACA,MAAM,OAAO,eAAe,EAAE,KAAK,EAAE;QACjC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;YAC9B,MAAM,IAAI,CAAC,MAAM;QACrB;QACA,IAAI;YACA,OAAO,MAAM,KAAK,CAAC,OAAO,iBAAiB;QAC/C,EACA,OAAO,KAAK;YACR,IAAI,eAAe,YAAY,iBAAiB,EAAE;gBAC9C,IAAI,IAAI,CAAC,WAAW,OAAO,OAAO;oBAC9B,MAAM,IAAI,CAAC,MAAM;oBACjB,OAAO,KAAK,CAAC,OAAO,iBAAiB;gBACzC;YACJ;YACA,MAAM;QACV;IACJ;IACA,MAAM,SAAS;QACX,IAAI,IAAI,CAAC,aAAa,IAAI,uBAAuB;YAC7C,IAAI,CAAC,aAAa,GAAG;QACzB;QACA,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,gBAAgB,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,EACnH,IAAI,CAAC,CAAC;YACP,IAAI,CAAC,CAAC,GAAG,WAAW,UAAU,EAAE,OAAO;gBACnC,MAAM,IAAI,YAAY,WAAW,CAAC;YACtC;YACA,IAAI,CAAC,KAAK,GAAG;gBAAE,MAAM,KAAK,IAAI;YAAC;YAC/B,IAAI,CAAC,cAAc,GAAG,KAAK,GAAG;YAC9B,IAAI,CAAC,aAAa,GAAG;QACzB,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,aAAa,GAAG;YACrB,MAAM;QACV,EAAE;QACF,MAAM,IAAI,CAAC,aAAa;IAC5B;AACJ;AACA,SAAS,mBAAmB,GAAG,EAAE,OAAO;IACpC,MAAM,MAAM,IAAI,aAAa,KAAK;IAClC,OAAO,eAAgB,eAAe,EAAE,KAAK;QACzC,OAAO,IAAI,MAAM,CAAC,iBAAiB;IACvC;AACJ;AACA,QAAQ,kBAAkB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/jwt/unsecured.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UnsecuredJWT = void 0;\nconst base64url = require(\"../runtime/base64url.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst jwt_claims_set_js_1 = require(\"../lib/jwt_claims_set.js\");\nconst produce_js_1 = require(\"./produce.js\");\nclass UnsecuredJWT extends produce_js_1.ProduceJWT {\n    encode() {\n        const header = base64url.encode(JSON.stringify({ alg: 'none' }));\n        const payload = base64url.encode(JSON.stringify(this._payload));\n        return `${header}.${payload}.`;\n    }\n    static decode(jwt, options) {\n        if (typeof jwt !== 'string') {\n            throw new errors_js_1.JWTInvalid('Unsecured JWT must be a string');\n        }\n        const { 0: encodedHeader, 1: encodedPayload, 2: signature, length } = jwt.split('.');\n        if (length !== 3 || signature !== '') {\n            throw new errors_js_1.JWTInvalid('Invalid Unsecured JWT');\n        }\n        let header;\n        try {\n            header = JSON.parse(buffer_utils_js_1.decoder.decode(base64url.decode(encodedHeader)));\n            if (header.alg !== 'none')\n                throw new Error();\n        }\n        catch {\n            throw new errors_js_1.JWTInvalid('Invalid Unsecured JWT');\n        }\n        const payload = (0, jwt_claims_set_js_1.default)(header, base64url.decode(encodedPayload), options);\n        return { payload, header };\n    }\n}\nexports.UnsecuredJWT = UnsecuredJWT;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG,KAAK;AAC5B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,qBAAqB,aAAa,UAAU;IAC9C,SAAS;QACL,MAAM,SAAS,UAAU,MAAM,CAAC,KAAK,SAAS,CAAC;YAAE,KAAK;QAAO;QAC7D,MAAM,UAAU,UAAU,MAAM,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ;QAC7D,OAAO,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC;IAClC;IACA,OAAO,OAAO,GAAG,EAAE,OAAO,EAAE;QACxB,IAAI,OAAO,QAAQ,UAAU;YACzB,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,MAAM,EAAE,GAAG,aAAa,EAAE,GAAG,cAAc,EAAE,GAAG,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK,CAAC;QAChF,IAAI,WAAW,KAAK,cAAc,IAAI;YAClC,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,IAAI;QACJ,IAAI;YACA,SAAS,KAAK,KAAK,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC,UAAU,MAAM,CAAC;YACtE,IAAI,OAAO,GAAG,KAAK,QACf,MAAM,IAAI;QAClB,EACA,OAAM;YACF,MAAM,IAAI,YAAY,UAAU,CAAC;QACrC;QACA,MAAM,UAAU,CAAC,GAAG,oBAAoB,OAAO,EAAE,QAAQ,UAAU,MAAM,CAAC,iBAAiB;QAC3F,OAAO;YAAE;YAAS;QAAO;IAC7B;AACJ;AACA,QAAQ,YAAY,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/util/base64url.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decode = exports.encode = void 0;\nconst base64url = require(\"../runtime/base64url.js\");\nexports.encode = base64url.encode;\nexports.decode = base64url.decode;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAG,KAAK;AACvC,MAAM;AACN,QAAQ,MAAM,GAAG,UAAU,MAAM;AACjC,QAAQ,MAAM,GAAG,UAAU,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/util/decode_protected_header.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decodeProtectedHeader = void 0;\nconst base64url_js_1 = require(\"./base64url.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst is_object_js_1 = require(\"../lib/is_object.js\");\nfunction decodeProtectedHeader(token) {\n    let protectedB64u;\n    if (typeof token === 'string') {\n        const parts = token.split('.');\n        if (parts.length === 3 || parts.length === 5) {\n            ;\n            [protectedB64u] = parts;\n        }\n    }\n    else if (typeof token === 'object' && token) {\n        if ('protected' in token) {\n            protectedB64u = token.protected;\n        }\n        else {\n            throw new TypeError('Token does not contain a Protected Header');\n        }\n    }\n    try {\n        if (typeof protectedB64u !== 'string' || !protectedB64u) {\n            throw new Error();\n        }\n        const result = JSON.parse(buffer_utils_js_1.decoder.decode((0, base64url_js_1.decode)(protectedB64u)));\n        if (!(0, is_object_js_1.default)(result)) {\n            throw new Error();\n        }\n        return result;\n    }\n    catch {\n        throw new TypeError('Invalid Token or Protected Header formatting');\n    }\n}\nexports.decodeProtectedHeader = decodeProtectedHeader;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,qBAAqB,GAAG,KAAK;AACrC,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,sBAAsB,KAAK;IAChC,IAAI;IACJ,IAAI,OAAO,UAAU,UAAU;QAC3B,MAAM,QAAQ,MAAM,KAAK,CAAC;QAC1B,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,GAAG;;YAE1C,CAAC,cAAc,GAAG;QACtB;IACJ,OACK,IAAI,OAAO,UAAU,YAAY,OAAO;QACzC,IAAI,eAAe,OAAO;YACtB,gBAAgB,MAAM,SAAS;QACnC,OACK;YACD,MAAM,IAAI,UAAU;QACxB;IACJ;IACA,IAAI;QACA,IAAI,OAAO,kBAAkB,YAAY,CAAC,eAAe;YACrD,MAAM,IAAI;QACd;QACA,MAAM,SAAS,KAAK,KAAK,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,eAAe,MAAM,EAAE;QACtF,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,SAAS;YACtC,MAAM,IAAI;QACd;QACA,OAAO;IACX,EACA,OAAM;QACF,MAAM,IAAI,UAAU;IACxB;AACJ;AACA,QAAQ,qBAAqB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/util/decode_jwt.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decodeJwt = void 0;\nconst base64url_js_1 = require(\"./base64url.js\");\nconst buffer_utils_js_1 = require(\"../lib/buffer_utils.js\");\nconst is_object_js_1 = require(\"../lib/is_object.js\");\nconst errors_js_1 = require(\"./errors.js\");\nfunction decodeJwt(jwt) {\n    if (typeof jwt !== 'string')\n        throw new errors_js_1.JWTInvalid('JWTs must use Compact JWS serialization, JWT must be a string');\n    const { 1: payload, length } = jwt.split('.');\n    if (length === 5)\n        throw new errors_js_1.JWTInvalid('Only JWTs using Compact JWS serialization can be decoded');\n    if (length !== 3)\n        throw new errors_js_1.JWTInvalid('Invalid JWT');\n    if (!payload)\n        throw new errors_js_1.JWTInvalid('JWTs must contain a payload');\n    let decoded;\n    try {\n        decoded = (0, base64url_js_1.decode)(payload);\n    }\n    catch {\n        throw new errors_js_1.JWTInvalid('Failed to base64url decode the payload');\n    }\n    let result;\n    try {\n        result = JSON.parse(buffer_utils_js_1.decoder.decode(decoded));\n    }\n    catch {\n        throw new errors_js_1.JWTInvalid('Failed to parse the decoded payload as JSON');\n    }\n    if (!(0, is_object_js_1.default)(result))\n        throw new errors_js_1.JWTInvalid('Invalid JWT Claims Set');\n    return result;\n}\nexports.decodeJwt = decodeJwt;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,UAAU,GAAG;IAClB,IAAI,OAAO,QAAQ,UACf,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC,MAAM,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK,CAAC;IACzC,IAAI,WAAW,GACX,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC,IAAI,WAAW,GACX,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC,IAAI,CAAC,SACD,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC,IAAI;IACJ,IAAI;QACA,UAAU,CAAC,GAAG,eAAe,MAAM,EAAE;IACzC,EACA,OAAM;QACF,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI;IACJ,IAAI;QACA,SAAS,KAAK,KAAK,CAAC,kBAAkB,OAAO,CAAC,MAAM,CAAC;IACzD,EACA,OAAM;QACF,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC;IACA,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,SAC7B,MAAM,IAAI,YAAY,UAAU,CAAC;IACrC,OAAO;AACX;AACA,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/generate.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.generateKeyPair = exports.generateSecret = void 0;\nconst crypto_1 = require(\"crypto\");\nconst util_1 = require(\"util\");\nconst random_js_1 = require(\"./random.js\");\nconst check_modulus_length_js_1 = require(\"./check_modulus_length.js\");\nconst errors_js_1 = require(\"../util/errors.js\");\nconst generate = (0, util_1.promisify)(crypto_1.generateKeyPair);\nasync function generateSecret(alg, options) {\n    let length;\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512':\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            length = parseInt(alg.slice(-3), 10);\n            break;\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW':\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW':\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            length = parseInt(alg.slice(1, 4), 10);\n            break;\n        default:\n            throw new errors_js_1.JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n    }\n    return (0, crypto_1.createSecretKey)((0, random_js_1.default)(new Uint8Array(length >> 3)));\n}\nexports.generateSecret = generateSecret;\nasync function generateKeyPair(alg, options) {\n    var _a, _b;\n    switch (alg) {\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n        case 'RSA1_5': {\n            const modulusLength = (_a = options === null || options === void 0 ? void 0 : options.modulusLength) !== null && _a !== void 0 ? _a : 2048;\n            if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n                throw new errors_js_1.JOSENotSupported('Invalid or unsupported modulusLength option provided, 2048 bits or larger keys must be used');\n            }\n            const keypair = await generate('rsa', {\n                modulusLength,\n                publicExponent: 0x10001,\n            });\n            (0, check_modulus_length_js_1.setModulusLength)(keypair.privateKey, modulusLength);\n            (0, check_modulus_length_js_1.setModulusLength)(keypair.publicKey, modulusLength);\n            return keypair;\n        }\n        case 'ES256':\n            return generate('ec', { namedCurve: 'P-256' });\n        case 'ES256K':\n            return generate('ec', { namedCurve: 'secp256k1' });\n        case 'ES384':\n            return generate('ec', { namedCurve: 'P-384' });\n        case 'ES512':\n            return generate('ec', { namedCurve: 'P-521' });\n        case 'EdDSA': {\n            switch (options === null || options === void 0 ? void 0 : options.crv) {\n                case undefined:\n                case 'Ed25519':\n                    return generate('ed25519');\n                case 'Ed448':\n                    return generate('ed448');\n                default:\n                    throw new errors_js_1.JOSENotSupported('Invalid or unsupported crv option provided, supported values are Ed25519 and Ed448');\n            }\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW':\n            const crv = (_b = options === null || options === void 0 ? void 0 : options.crv) !== null && _b !== void 0 ? _b : 'P-256';\n            switch (crv) {\n                case undefined:\n                case 'P-256':\n                case 'P-384':\n                case 'P-521':\n                    return generate('ec', { namedCurve: crv });\n                case 'X25519':\n                    return generate('x25519');\n                case 'X448':\n                    return generate('x448');\n                default:\n                    throw new errors_js_1.JOSENotSupported('Invalid or unsupported crv option provided, supported values are P-256, P-384, P-521, X25519, and X448');\n            }\n        default:\n            throw new errors_js_1.JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n    }\n}\nexports.generateKeyPair = generateKeyPair;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,QAAQ,cAAc,GAAG,KAAK;AACxD,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,WAAW,CAAC,GAAG,OAAO,SAAS,EAAE,SAAS,eAAe;AAC/D,eAAe,eAAe,GAAG,EAAE,OAAO;IACtC,IAAI;IACJ,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,SAAS,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;YACjC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,SAAS,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YACnC;QACJ;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;IACA,OAAO,CAAC,GAAG,SAAS,eAAe,EAAE,CAAC,GAAG,YAAY,OAAO,EAAE,IAAI,WAAW,UAAU;AAC3F;AACA,QAAQ,cAAc,GAAG;AACzB,eAAe,gBAAgB,GAAG,EAAE,OAAO;IACvC,IAAI,IAAI;IACR,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,MAAM,gBAAgB,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;gBACtI,IAAI,OAAO,kBAAkB,YAAY,gBAAgB,MAAM;oBAC3D,MAAM,IAAI,YAAY,gBAAgB,CAAC;gBAC3C;gBACA,MAAM,UAAU,MAAM,SAAS,OAAO;oBAClC;oBACA,gBAAgB;gBACpB;gBACA,CAAC,GAAG,0BAA0B,gBAAgB,EAAE,QAAQ,UAAU,EAAE;gBACpE,CAAC,GAAG,0BAA0B,gBAAgB,EAAE,QAAQ,SAAS,EAAE;gBACnE,OAAO;YACX;QACA,KAAK;YACD,OAAO,SAAS,MAAM;gBAAE,YAAY;YAAQ;QAChD,KAAK;YACD,OAAO,SAAS,MAAM;gBAAE,YAAY;YAAY;QACpD,KAAK;YACD,OAAO,SAAS,MAAM;gBAAE,YAAY;YAAQ;QAChD,KAAK;YACD,OAAO,SAAS,MAAM;gBAAE,YAAY;YAAQ;QAChD,KAAK;YAAS;gBACV,OAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG;oBACjE,KAAK;oBACL,KAAK;wBACD,OAAO,SAAS;oBACpB,KAAK;wBACD,OAAO,SAAS;oBACpB;wBACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;gBAC/C;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,MAAM,MAAM,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YAClH,OAAQ;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACD,OAAO,SAAS,MAAM;wBAAE,YAAY;oBAAI;gBAC5C,KAAK;oBACD,OAAO,SAAS;gBACpB,KAAK;oBACD,OAAO,SAAS;gBACpB;oBACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;YAC/C;QACJ;YACI,MAAM,IAAI,YAAY,gBAAgB,CAAC;IAC/C;AACJ;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5016, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/key/generate_key_pair.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.generateKeyPair = void 0;\nconst generate_js_1 = require(\"../runtime/generate.js\");\nasync function generateKeyPair(alg, options) {\n    return (0, generate_js_1.generateKeyPair)(alg, options);\n}\nexports.generateKeyPair = generateKeyPair;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,MAAM;AACN,eAAe,gBAAgB,GAAG,EAAE,OAAO;IACvC,OAAO,CAAC,GAAG,cAAc,eAAe,EAAE,KAAK;AACnD;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5031, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/key/generate_secret.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.generateSecret = void 0;\nconst generate_js_1 = require(\"../runtime/generate.js\");\nasync function generateSecret(alg, options) {\n    return (0, generate_js_1.generateSecret)(alg, options);\n}\nexports.generateSecret = generateSecret;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,eAAe,eAAe,GAAG,EAAE,OAAO;IACtC,OAAO,CAAC,GAAG,cAAc,cAAc,EAAE,KAAK;AAClD;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/runtime/runtime.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = 'node:crypto';\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5056, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/util/runtime.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst runtime_js_1 = require(\"../runtime/runtime.js\");\nexports.default = runtime_js_1.default;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,QAAQ,OAAO,GAAG,aAAa,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5067, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/jose/dist/node/cjs/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cryptoRuntime = exports.base64url = exports.generateSecret = exports.generateKeyPair = exports.errors = exports.decodeJwt = exports.decodeProtectedHeader = exports.importJWK = exports.importX509 = exports.importPKCS8 = exports.importSPKI = exports.exportJWK = exports.exportSPKI = exports.exportPKCS8 = exports.UnsecuredJWT = exports.createRemoteJWKSet = exports.createLocalJWKSet = exports.EmbeddedJWK = exports.calculateJwkThumbprintUri = exports.calculateJwkThumbprint = exports.EncryptJWT = exports.SignJWT = exports.GeneralSign = exports.FlattenedSign = exports.CompactSign = exports.FlattenedEncrypt = exports.CompactEncrypt = exports.jwtDecrypt = exports.jwtVerify = exports.generalVerify = exports.flattenedVerify = exports.compactVerify = exports.GeneralEncrypt = exports.generalDecrypt = exports.flattenedDecrypt = exports.compactDecrypt = void 0;\nvar decrypt_js_1 = require(\"./jwe/compact/decrypt.js\");\nObject.defineProperty(exports, \"compactDecrypt\", { enumerable: true, get: function () { return decrypt_js_1.compactDecrypt; } });\nvar decrypt_js_2 = require(\"./jwe/flattened/decrypt.js\");\nObject.defineProperty(exports, \"flattenedDecrypt\", { enumerable: true, get: function () { return decrypt_js_2.flattenedDecrypt; } });\nvar decrypt_js_3 = require(\"./jwe/general/decrypt.js\");\nObject.defineProperty(exports, \"generalDecrypt\", { enumerable: true, get: function () { return decrypt_js_3.generalDecrypt; } });\nvar encrypt_js_1 = require(\"./jwe/general/encrypt.js\");\nObject.defineProperty(exports, \"GeneralEncrypt\", { enumerable: true, get: function () { return encrypt_js_1.GeneralEncrypt; } });\nvar verify_js_1 = require(\"./jws/compact/verify.js\");\nObject.defineProperty(exports, \"compactVerify\", { enumerable: true, get: function () { return verify_js_1.compactVerify; } });\nvar verify_js_2 = require(\"./jws/flattened/verify.js\");\nObject.defineProperty(exports, \"flattenedVerify\", { enumerable: true, get: function () { return verify_js_2.flattenedVerify; } });\nvar verify_js_3 = require(\"./jws/general/verify.js\");\nObject.defineProperty(exports, \"generalVerify\", { enumerable: true, get: function () { return verify_js_3.generalVerify; } });\nvar verify_js_4 = require(\"./jwt/verify.js\");\nObject.defineProperty(exports, \"jwtVerify\", { enumerable: true, get: function () { return verify_js_4.jwtVerify; } });\nvar decrypt_js_4 = require(\"./jwt/decrypt.js\");\nObject.defineProperty(exports, \"jwtDecrypt\", { enumerable: true, get: function () { return decrypt_js_4.jwtDecrypt; } });\nvar encrypt_js_2 = require(\"./jwe/compact/encrypt.js\");\nObject.defineProperty(exports, \"CompactEncrypt\", { enumerable: true, get: function () { return encrypt_js_2.CompactEncrypt; } });\nvar encrypt_js_3 = require(\"./jwe/flattened/encrypt.js\");\nObject.defineProperty(exports, \"FlattenedEncrypt\", { enumerable: true, get: function () { return encrypt_js_3.FlattenedEncrypt; } });\nvar sign_js_1 = require(\"./jws/compact/sign.js\");\nObject.defineProperty(exports, \"CompactSign\", { enumerable: true, get: function () { return sign_js_1.CompactSign; } });\nvar sign_js_2 = require(\"./jws/flattened/sign.js\");\nObject.defineProperty(exports, \"FlattenedSign\", { enumerable: true, get: function () { return sign_js_2.FlattenedSign; } });\nvar sign_js_3 = require(\"./jws/general/sign.js\");\nObject.defineProperty(exports, \"GeneralSign\", { enumerable: true, get: function () { return sign_js_3.GeneralSign; } });\nvar sign_js_4 = require(\"./jwt/sign.js\");\nObject.defineProperty(exports, \"SignJWT\", { enumerable: true, get: function () { return sign_js_4.SignJWT; } });\nvar encrypt_js_4 = require(\"./jwt/encrypt.js\");\nObject.defineProperty(exports, \"EncryptJWT\", { enumerable: true, get: function () { return encrypt_js_4.EncryptJWT; } });\nvar thumbprint_js_1 = require(\"./jwk/thumbprint.js\");\nObject.defineProperty(exports, \"calculateJwkThumbprint\", { enumerable: true, get: function () { return thumbprint_js_1.calculateJwkThumbprint; } });\nObject.defineProperty(exports, \"calculateJwkThumbprintUri\", { enumerable: true, get: function () { return thumbprint_js_1.calculateJwkThumbprintUri; } });\nvar embedded_js_1 = require(\"./jwk/embedded.js\");\nObject.defineProperty(exports, \"EmbeddedJWK\", { enumerable: true, get: function () { return embedded_js_1.EmbeddedJWK; } });\nvar local_js_1 = require(\"./jwks/local.js\");\nObject.defineProperty(exports, \"createLocalJWKSet\", { enumerable: true, get: function () { return local_js_1.createLocalJWKSet; } });\nvar remote_js_1 = require(\"./jwks/remote.js\");\nObject.defineProperty(exports, \"createRemoteJWKSet\", { enumerable: true, get: function () { return remote_js_1.createRemoteJWKSet; } });\nvar unsecured_js_1 = require(\"./jwt/unsecured.js\");\nObject.defineProperty(exports, \"UnsecuredJWT\", { enumerable: true, get: function () { return unsecured_js_1.UnsecuredJWT; } });\nvar export_js_1 = require(\"./key/export.js\");\nObject.defineProperty(exports, \"exportPKCS8\", { enumerable: true, get: function () { return export_js_1.exportPKCS8; } });\nObject.defineProperty(exports, \"exportSPKI\", { enumerable: true, get: function () { return export_js_1.exportSPKI; } });\nObject.defineProperty(exports, \"exportJWK\", { enumerable: true, get: function () { return export_js_1.exportJWK; } });\nvar import_js_1 = require(\"./key/import.js\");\nObject.defineProperty(exports, \"importSPKI\", { enumerable: true, get: function () { return import_js_1.importSPKI; } });\nObject.defineProperty(exports, \"importPKCS8\", { enumerable: true, get: function () { return import_js_1.importPKCS8; } });\nObject.defineProperty(exports, \"importX509\", { enumerable: true, get: function () { return import_js_1.importX509; } });\nObject.defineProperty(exports, \"importJWK\", { enumerable: true, get: function () { return import_js_1.importJWK; } });\nvar decode_protected_header_js_1 = require(\"./util/decode_protected_header.js\");\nObject.defineProperty(exports, \"decodeProtectedHeader\", { enumerable: true, get: function () { return decode_protected_header_js_1.decodeProtectedHeader; } });\nvar decode_jwt_js_1 = require(\"./util/decode_jwt.js\");\nObject.defineProperty(exports, \"decodeJwt\", { enumerable: true, get: function () { return decode_jwt_js_1.decodeJwt; } });\nexports.errors = require(\"./util/errors.js\");\nvar generate_key_pair_js_1 = require(\"./key/generate_key_pair.js\");\nObject.defineProperty(exports, \"generateKeyPair\", { enumerable: true, get: function () { return generate_key_pair_js_1.generateKeyPair; } });\nvar generate_secret_js_1 = require(\"./key/generate_secret.js\");\nObject.defineProperty(exports, \"generateSecret\", { enumerable: true, get: function () { return generate_secret_js_1.generateSecret; } });\nexports.base64url = require(\"./util/base64url.js\");\nvar runtime_js_1 = require(\"./util/runtime.js\");\nObject.defineProperty(exports, \"cryptoRuntime\", { enumerable: true, get: function () { return runtime_js_1.default; } });\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,aAAa,GAAG,QAAQ,SAAS,GAAG,QAAQ,cAAc,GAAG,QAAQ,eAAe,GAAG,QAAQ,MAAM,GAAG,QAAQ,SAAS,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,SAAS,GAAG,QAAQ,UAAU,GAAG,QAAQ,WAAW,GAAG,QAAQ,UAAU,GAAG,QAAQ,SAAS,GAAG,QAAQ,UAAU,GAAG,QAAQ,WAAW,GAAG,QAAQ,YAAY,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,WAAW,GAAG,QAAQ,yBAAyB,GAAG,QAAQ,sBAAsB,GAAG,QAAQ,UAAU,GAAG,QAAQ,OAAO,GAAG,QAAQ,WAAW,GAAG,QAAQ,aAAa,GAAG,QAAQ,WAAW,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,cAAc,GAAG,QAAQ,UAAU,GAAG,QAAQ,SAAS,GAAG,QAAQ,aAAa,GAAG,QAAQ,eAAe,GAAG,QAAQ,aAAa,GAAG,QAAQ,cAAc,GAAG,QAAQ,cAAc,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,cAAc,GAAG,KAAK;AAC/1B,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,kBAAkB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,cAAc;IAAE;AAAE;AAC9H,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,oBAAoB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,gBAAgB;IAAE;AAAE;AAClI,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,kBAAkB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,cAAc;IAAE;AAAE;AAC9H,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,kBAAkB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,cAAc;IAAE;AAAE;AAC9H,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,iBAAiB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,aAAa;IAAE;AAAE;AAC3H,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,mBAAmB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,eAAe;IAAE;AAAE;AAC/H,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,iBAAiB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,aAAa;IAAE;AAAE;AAC3H,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,aAAa;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,SAAS;IAAE;AAAE;AACnH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,UAAU;IAAE;AAAE;AACtH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,kBAAkB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,cAAc;IAAE;AAAE;AAC9H,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,oBAAoB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,gBAAgB;IAAE;AAAE;AAClI,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,eAAe;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,WAAW;IAAE;AAAE;AACrH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,iBAAiB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,aAAa;IAAE;AAAE;AACzH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,eAAe;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,WAAW;IAAE;AAAE;AACrH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,WAAW;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,OAAO;IAAE;AAAE;AAC7G,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,UAAU;IAAE;AAAE;AACtH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,0BAA0B;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,gBAAgB,sBAAsB;IAAE;AAAE;AACjJ,OAAO,cAAc,CAAC,SAAS,6BAA6B;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,gBAAgB,yBAAyB;IAAE;AAAE;AACvJ,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,eAAe;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,cAAc,WAAW;IAAE;AAAE;AACzH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,qBAAqB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,WAAW,iBAAiB;IAAE;AAAE;AAClI,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,sBAAsB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,kBAAkB;IAAE;AAAE;AACrI,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,gBAAgB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,eAAe,YAAY;IAAE;AAAE;AAC5H,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,eAAe;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,WAAW;IAAE;AAAE;AACvH,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,UAAU;IAAE;AAAE;AACrH,OAAO,cAAc,CAAC,SAAS,aAAa;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,SAAS;IAAE;AAAE;AACnH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,UAAU;IAAE;AAAE;AACrH,OAAO,cAAc,CAAC,SAAS,eAAe;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,WAAW;IAAE;AAAE;AACvH,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,UAAU;IAAE;AAAE;AACrH,OAAO,cAAc,CAAC,SAAS,aAAa;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,YAAY,SAAS;IAAE;AAAE;AACnH,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,yBAAyB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,6BAA6B,qBAAqB;IAAE;AAAE;AAC5J,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,aAAa;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,gBAAgB,SAAS;IAAE;AAAE;AACvH,QAAQ,MAAM;AACd,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,mBAAmB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,uBAAuB,eAAe;IAAE;AAAE;AAC1I,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,kBAAkB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,qBAAqB,cAAc;IAAE;AAAE;AACtI,QAAQ,SAAS;AACjB,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,iBAAiB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,OAAO;IAAE;AAAE", "ignoreList": [0], "debugId": null}}]}