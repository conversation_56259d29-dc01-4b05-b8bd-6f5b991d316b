{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/NavigationButton.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface NavigationButtonProps {\r\n  text: string;\r\n  href?: string;\r\n  onClick?: () => void;\r\n  className?: string;\r\n  style?: React.CSSProperties;\r\n}\r\n\r\nexport function NavigationButton({ text, href, onClick, className = \"\", style }: NavigationButtonProps) {\r\n  const content = (\r\n    <>\r\n      {text}\r\n      <svg\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        width=\"20\"\r\n        height=\"20\"\r\n        fill=\"none\"\r\n        viewBox=\"0 0 24 24\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"2.5\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n        className=\"ml-2 group-hover:translate-x-1 transition-transform duration-150\"\r\n      >\r\n        <path d=\"M5 12h14\" />\r\n        <path d=\"M12 5l7 7-7 7\" />\r\n      </svg>\r\n    </>\r\n  );\r\n\r\n  const buttonClass =\r\n    \"rounded-full border-2 border-gray-800 dark:border-gray-200 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer \" +\r\n    (className || \"\");\r\n\r\n  return (\r\n    <div className=\"flex justify-center w-full my-8\">\r\n      {href ? (\r\n        <Button asChild variant=\"outline\" className={buttonClass} style={style} onClick={onClick}>\r\n          <Link href={href}>{content}</Link>\r\n        </Button>\r\n      ) : (\r\n        <Button\r\n          variant=\"outline\"\r\n          className={buttonClass}\r\n          style={style}\r\n          onClick={onClick}\r\n        >\r\n          {content}\r\n        </Button>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default NavigationButton;"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAaO,SAAS,iBAAiB,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,KAAK,EAAyB;IACpG,MAAM,wBACJ;;YACG;0BACD,8OAAC;gBACC,OAAM;gBACN,OAAM;gBACN,QAAO;gBACP,MAAK;gBACL,SAAQ;gBACR,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;gBACf,WAAU;;kCAEV,8OAAC;wBAAK,GAAE;;;;;;kCACR,8OAAC;wBAAK,GAAE;;;;;;;;;;;;;;IAKd,MAAM,cACJ,wPACA,CAAC,aAAa,EAAE;IAElB,qBACE,8OAAC;QAAI,WAAU;kBACZ,qBACC,8OAAC,2HAAA,CAAA,SAAM;YAAC,OAAO;YAAC,SAAQ;YAAU,WAAW;YAAa,OAAO;YAAO,SAAS;sBAC/E,cAAA,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM;0BAAO;;;;;;;;;;iCAGrB,8OAAC,2HAAA,CAAA,SAAM;YACL,SAAQ;YACR,WAAW;YACX,OAAO;YACP,SAAS;sBAER;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/Footer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport default function Footer() {\r\n  return (\r\n    <div className={cn(\r\n      \"w-full relative overflow-hidden text-black\",\r\n      \"py-4 px-8\",\r\n      \"text-xs leading-relaxed\",\r\n      \"rounded-t-[40px]\",\r\n      \"mt-10\"\r\n    )}>\r\n      {/* Gradient background */}\r\n      <div\r\n        style={{\r\n          position: 'absolute',\r\n          inset: 0,\r\n          zIndex: 0,\r\n          pointerEvents: 'none',\r\n          background: 'linear-gradient(135deg, rgba(75,159,239,0.9) 0%, rgba(75,159,239,0.3) 100%)',\r\n        }}\r\n      />\r\n      <div className=\"max-w-[1200px] mx-auto flex flex-col md:flex-row items-start md:items-center gap-2 md:gap-0 relative z-10\">\r\n        <div className=\"flex-1\">\r\n          Disclaimer – The report's findings are based on the specific test administered and the responses given. The report is designed to help people identify their natural abilities, strengths, limitations and career interests, and should be used only as a tool for self-discovery\r\n        </div>\r\n        <div className=\"whitespace-nowrap md:ml-4\">\r\n          |   Copyright – TalentMetrix 2025\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,8CACA,aACA,2BACA,oBACA;;0BAGA,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,YAAY;gBACd;;;;;;0BAEF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAS;;;;;;kCAGxB,8OAAC;wBAAI,WAAU;kCAA4B;;;;;;;;;;;;;;;;;;AAMnD", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/GradientCard.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface GradientCardProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  style?: React.CSSProperties;\r\n  infoIcon?: boolean;\r\n  infoIconContent?: React.ReactNode;\r\n  color?: \"blue\" | \"orange\";\r\n}\r\n\r\n/**\r\n * GradientCard - Standardized card with gradient background and info icon (optional).\r\n * Usage: Wrap dynamic content inside <GradientCard>...</GradientCard>\r\n */\r\nconst colorMap = {\r\n  blue: {\r\n    gradient: \"linear-gradient(135deg, rgba(55,147,247,0.5) 0%, rgba(55,147,247,0.05) 30%, transparent 100%)\",\r\n    iconBg: \"#3793F7\",\r\n  },\r\n  orange: {\r\n    gradient: \"linear-gradient(135deg, rgba(255,168,0,0.5) 0%, rgba(255,168,0,0.05) 30%, transparent 100%)\",\r\n    iconBg: \"#FFA800\",\r\n  },\r\n};\r\n\r\nconst GradientCard: React.FC<GradientCardProps> = ({\r\n  children,\r\n  className = \"\",\r\n  style = {},\r\n  infoIcon = false,\r\n  infoIconContent = \"i\",\r\n  color = \"blue\",\r\n}) => {\r\n  const theme = colorMap[color] || colorMap.blue;\r\n  \r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"relative rounded-2xl p-6 mb-6 shadow-md bg-white dark:bg-gray-800 overflow-hidden max-w-[980px] mx-auto text-gray-900 dark:text-gray-100\",\r\n        className\r\n      )}\r\n      style={style}\r\n    >\r\n      {/* Gradient Background */}\r\n      <div\r\n        style={{\r\n          position: 'absolute',\r\n          inset: 0,\r\n          zIndex: 0,\r\n          pointerEvents: 'none',\r\n          borderRadius: '1rem',\r\n          background: theme.gradient,\r\n        }}\r\n      />\r\n      {infoIcon && (\r\n        <div\r\n          className=\"absolute top-5 right-5 w-6 h-6 text-white rounded-full flex items-center justify-center italic font-bold z-10\"\r\n          style={{ background: theme.iconBg }}\r\n        >\r\n          {infoIconContent}\r\n        </div>\r\n      )}\r\n      <div className=\"relative z-[1]\">{children}</div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GradientCard;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA;;;CAGC,GACD,MAAM,WAAW;IACf,MAAM;QACJ,UAAU;QACV,QAAQ;IACV;IACA,QAAQ;QACN,UAAU;QACV,QAAQ;IACV;AACF;AAEA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,CAAC,CAAC,EACV,WAAW,KAAK,EAChB,kBAAkB,GAAG,EACrB,QAAQ,MAAM,EACf;IACC,MAAM,QAAQ,QAAQ,CAAC,MAAM,IAAI,SAAS,IAAI;IAE9C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4IACA;QAEF,OAAO;;0BAGP,8OAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,cAAc;oBACd,YAAY,MAAM,QAAQ;gBAC5B;;;;;;YAED,0BACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,YAAY,MAAM,MAAM;gBAAC;0BAEjC;;;;;;0BAGL,8OAAC;gBAAI,WAAU;0BAAkB;;;;;;;;;;;;AAGvC;uCAEe", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/app/%28with-sidebar%29/1_1_understand_yourself/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport Image from 'next/image';\r\nimport { useAssessment } from '@/context/AssessmentContext';\r\nimport NavigationButton from '@/components/NavigationButton';\r\nimport Footer from '@/components/Footer';\r\nimport { cn } from '@/lib/utils';\r\nimport GradientCard from '@/components/GradientCard';\r\n\r\nexport default function UnderstandYourselfPage() {\r\n  const { assessmentData, loading, error } = useAssessment();\r\n\r\n  const description = assessmentData?.assessment?.section_i?.general_description?.description ||\r\n    \"Loading your personalized assessment data...\";\r\n\r\n  return (\r\n    <div className=\"w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6\">\r\n      {/* Main Content */}\r\n      <div className=\"w-full max-w-[1100px] mx-auto px-5 pt-8 relative\">\r\n        {/* Header Section - Image on left, title on right */}\r\n        <div className=\"flex flex-col md:flex-row relative mb-10 mt-5\">\r\n          <div className=\"flex-none w-full md:w-[400px] lg:w-[400px] relative\">\r\n            <div className=\"relative w-full h-[320px]\">\r\n              <Image\r\n                src=\"/1.1_Understand_yourselfTM.svg\"\r\n                alt=\"Understand Yourself Illustration\"\r\n                fill\r\n                style={{ objectFit: \"contain\" }}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex-1 md:pl-10 mt-4 md:mt-0\">\r\n            <h1 className=\"text-[3rem] font-light text-[#3793F7] dark:text-blue-400 mb-8 md:text-4xl lg:text-[3rem]\">\r\n              1.1 Understand Yourself\r\n            </h1>\r\n\r\n            <p className=\"mb-6 leading-relaxed text-gray-900 dark:text-gray-200\">\r\n              This segment provides insights into your natural behaviours,\r\n              strengths, and limitations, helping you recognize how you\r\n              interact with others and respond to different situations. This\r\n              segment also explores your communication style, emotional\r\n              patterns, and how you may be perceived under stress, offering\r\n              a deeper understanding of your personal dynamics.\r\n              <br /> <br />\r\n              Based on your responses, the report has selected general statements to provide a broad\r\n              understanding of your behavioural style. These statements identify your basic natural behaviour.\r\n              That is, if left on your own, these statements identify HOW YOU WOULD CHOOSE TO DO\r\n              SOMETHING. Use the general characteristics to gain a better understanding of your natural\r\n              behaviour.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Card with Assessment Data */}\r\n        <GradientCard color=\"blue\"  >\r\n          {loading ? (\r\n            <div className=\"text-center p-8 text-[#3793F7] dark:text-blue-400 flex flex-col items-center justify-center\">\r\n              <div className=\"border-4 border-[rgba(0,0,0,0.1)] dark:border-[rgba(255,255,255,0.1)] border-l-[#3793F7] dark:border-l-blue-400 w-9 h-9 rounded-full animate-spin mb-4\"></div>\r\n              <p>Loading your personalized assessment...</p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex flex-col gap-4\">\r\n              {description.split(/(?<=\\.)(?=\\s)/).map((sentence: string, index: number) => (\r\n                sentence.trim() && (\r\n                  <div className=\"flex items-start leading-relaxed\" key={index}>\r\n                    <span className=\"text-[#3793F7] dark:text-blue-400 text-lg mr-2.5 flex-shrink-0\">•</span>\r\n                    <span className=\"text-gray-900 dark:text-gray-200\">{sentence.trim()}</span>\r\n                  </div>\r\n                )\r\n              ))}\r\n            </div>\r\n          )}\r\n        </GradientCard>\r\n\r\n        {/* Footer Text */}\r\n        <p className=\"my-8 leading-relaxed max-w-[980px] mx-auto text-gray-900 dark:text-gray-200\">\r\n          Understanding your natural behaviour is the first step toward recognizing how it influences your\r\n          strengths and areas for growth. Building on this foundation, the next section explores how these\r\n          traits shape your approach to academics, work, and relationships, highlighting both your\r\n          advantages and areas for improvement.\r\n        </p>\r\n\r\n        {/* Continue Button */}\r\n        <NavigationButton\r\n          text=\"CONTINUE\"\r\n          href=\"/1_2_strengths_limitations\"\r\n        />\r\n\r\n        {/* Footer */}\r\n        <Footer />\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAEA;AARA;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAEvD,MAAM,cAAc,gBAAgB,YAAY,WAAW,qBAAqB,eAC9E;IAEF,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,OAAO;wCAAE,WAAW;oCAAU;oCAC9B,QAAQ;;;;;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2F;;;;;;8CAIzG,8OAAC;oCAAE,WAAU;;wCAAwD;sDAOnE,8OAAC;;;;;wCAAK;sDAAC,8OAAC;;;;;wCAAK;;;;;;;;;;;;;;;;;;;8BAWnB,8OAAC,2HAAA,CAAA,UAAY;oBAAC,OAAM;8BACjB,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAE;;;;;;;;;;;6CAGL,8OAAC;wBAAI,WAAU;kCACZ,YAAY,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,UAAkB,QACzD,SAAS,IAAI,oBACX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiE;;;;;;kDACjF,8OAAC;wCAAK,WAAU;kDAAoC,SAAS,IAAI;;;;;;;+BAFZ;;;;;;;;;;;;;;;8BAWjE,8OAAC;oBAAE,WAAU;8BAA8E;;;;;;8BAQ3F,8OAAC,+HAAA,CAAA,UAAgB;oBACf,MAAK;oBACL,MAAK;;;;;;8BAIP,8OAAC,qHAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}