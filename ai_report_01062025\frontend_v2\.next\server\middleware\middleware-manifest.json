{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0deeb1a4._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_b1a685ee.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "lSJO1icyWqEuJOy1SQsceATrGXEMVPGsiCVLARBJsw0=", "__NEXT_PREVIEW_MODE_ID": "95aa12e4181620ddc70dafc5aaf882bc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "333936053b7296952864c018f82961132e07c738ce886915b8fac94bd9fc57fc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b2afd69dbdf94636688087b116233cda5f415d50cf2d685cf7d0be49364dd26a"}}}, "instrumentation": null, "functions": {}}