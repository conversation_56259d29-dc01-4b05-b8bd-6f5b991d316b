'use client';
import React, { useState, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { 
  Bar<PERSON>hart3, CheckCircle, AlertCircle, Lightbulb, FileText, 
  TrendingUp, Target, Award, RefreshCw, Download, Upload, X,
  Briefcase
} from 'lucide-react';
import { sampleJobDescriptions, sampleResumes } from './sampleData';

interface AnalysisResults {
  score: number;
  wordCount: number;
  sections: string[];
  keywords: string[];
  issues: { type: string; text: string }[];
  suggestions: string[];
}

interface JobMatchResults {
  matchScore: number;
  missingKeywords: string[];
  matchingKeywords: string[];
}

export default function ATSScoreAnalyzer() {
  const [resumeText, setResumeText] = useState('');
  const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [jobDescription, setJobDescription] = useState('');
  const [showJobDescription, setShowJobDescription] = useState(false);
  const [jobMatchResults, setJobMatchResults] = useState<JobMatchResults | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const onDrop = (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        setResumeText(text);
        setUploadedFile(file);
      };
      reader.readAsText(file);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt', '.docx', '.pdf'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    multiple: false
  });

  const analyzeResume = () => {
    if (!resumeText.trim()) return;
    
    setIsAnalyzing(true);
    
    // Simulate API call
    setTimeout(() => {
      const sections = detectSections(resumeText);
      const keywords = extractKeywords(resumeText);
      const wordCount = resumeText.split(/\s+/).length;
      
      // Generate a score based on various factors
      let score = 70; // Base score
      if (wordCount > 300) score += 10;
      if (sections.length >= 4) score += 10;
      if (keywords.length > 15) score += 10;
      
      // Cap the score at 100
      score = Math.min(score, 100);
      
      const issues = [];
      if (wordCount < 200) issues.push({
        type: 'warning',
        text: 'Your resume is quite short. Consider adding more details about your experience and skills.'
      });
      
      if (sections.length < 3) issues.push({
        type: 'warning',
        text: 'Your resume is missing common sections like Experience, Education, or Skills.'
      });
      
      const suggestions = [
        'Use bullet points to highlight your achievements',
        'Include relevant keywords from the job description',
        'Quantify your achievements with numbers and metrics'
      ];
      
      setAnalysisResults({
        score,
        wordCount,
        sections,
        keywords: keywords.slice(0, 20), // Limit to top 20 keywords
        issues,
        suggestions
      });
      
      setIsAnalyzing(false);
    }, 1500);
  };

  const detectSections = (text: string) => {
    const sections: string[] = [];
    const commonSections = ['experience', 'education', 'skills', 'projects', 'summary'];
    
    commonSections.forEach(section => {
      if (text.toLowerCase().includes(section)) {
        sections.push(section.charAt(0).toUpperCase() + section.slice(1));
      }
    });
    
    return sections;
  };

  const extractKeywords = (text: string) => {
    // This is a simplified keyword extraction
    // In a real app, you'd want a more sophisticated approach
    const commonWords = new Set(['the', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'with', 'a', 'an', 'of']);
    const words = text.toLowerCase().split(/\s+/);
    const wordCount: Record<string, number> = {};
    
    words.forEach(word => {
      // Remove non-alphanumeric characters
      const cleanWord = word.replace(/[^\w\s]/g, '');
      if (cleanWord.length > 3 && !commonWords.has(cleanWord)) {
        wordCount[cleanWord] = (wordCount[cleanWord] || 0) + 1;
      }
    });
    
    // Sort by frequency and return top 20
    return Object.entries(wordCount)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([word]) => word);
  };

  const compareWithJobDescription = () => {
    if (!jobDescription.trim() || !resumeText) return;
    
    // Simple keyword matching (in a real app, you'd want something more sophisticated)
    const jobKeywords = jobDescription.toLowerCase().split(/\s+/);
    const resumeKeywords = new Set(resumeText.toLowerCase().split(/\s+/));
    
    const matchingKeywords = [];
    const missingKeywords = [];
    
    for (const keyword of jobKeywords) {
      if (resumeKeywords.has(keyword)) {
        matchingKeywords.push(keyword);
      } else if (keyword.length > 4) { // Only consider longer keywords
        missingKeywords.push(keyword);
      }
    }
    
    const matchScore = Math.min(100, Math.floor((matchingKeywords.length / jobKeywords.length) * 100));
    
    setJobMatchResults({
      matchScore,
      matchingKeywords: [...new Set(matchingKeywords)].slice(0, 20), // Remove duplicates and limit
      missingKeywords: [...new Set(missingKeywords)].slice(0, 20)
    });
  };

  const loadSampleResume = (index: number) => {
    setResumeText(sampleResumes[index]);
    setUploadedFile(new File([sampleResumes[index]], 'sample-resume.txt', { type: 'text/plain' }));
    setAnalysisResults(null);
    setJobMatchResults(null);
  };

  const loadSampleJobDescription = (index: number) => {
    setJobDescription(sampleJobDescriptions[index].description);
    setJobMatchResults(null);
  };

  const resetAll = () => {
    setResumeText('');
    setUploadedFile(null);
    setJobDescription('');
    setAnalysisResults(null);
    setJobMatchResults(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">ATS Score Analyzer</h1>
          <p className="text-xl text-gray-600">
            Upload your resume and job description to analyze your ATS compatibility score
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Resume Upload Section */}
          <div className="bg-white p-6 rounded-xl shadow-md">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">Your Resume</h2>
              {uploadedFile && (
                <button
                  onClick={resetAll}
                  className="text-sm text-red-600 hover:text-red-800 flex items-center"
                >
                  <X className="w-4 h-4 mr-1" /> Reset All
                </button>
              )}
            </div>
            
            {!uploadedFile ? (
              <div 
                {...getRootProps()} 
                className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                  isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className="w-10 h-10 mx-auto text-gray-400 mb-3" />
                <p className="text-gray-600 mb-2">
                  {isDragActive ? 'Drop your resume here' : 'Drag & drop your resume here, or click to select'}
                </p>
                <p className="text-sm text-gray-500">Supports: .pdf, .doc, .docx, .txt (max 5MB)</p>
              </div>
            ) : (
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <div className="flex items-center">
                    <FileText className="w-5 h-5 text-blue-500 mr-2" />
                    <span className="font-medium text-gray-700">{uploadedFile.name}</span>
                  </div>
                  <span className="text-sm text-green-600 bg-green-50 px-2 py-1 rounded">
                    {Math.round(uploadedFile.size / 1024)} KB
                  </span>
                </div>
                <div className="flex space-x-3 mt-4">
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md text-sm font-medium transition-colors"
                  >
                    Change File
                  </button>
                  <button
                    onClick={() => analyzeResume()}
                    disabled={isAnalyzing}
                    className={`flex-1 flex items-center justify-center ${
                      isAnalyzing
                        ? 'bg-blue-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    } text-white py-2 px-4 rounded-md text-sm font-medium transition-colors`}
                  >
                    {isAnalyzing ? (
                      <>
                        <RefreshCw className="animate-spin w-4 h-4 mr-2" />
                        Analyzing...
                      </>
                    ) : (
                      'Analyze Resume'
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* Sample Resumes */}
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Or try a sample resume:</h3>
              <div className="grid grid-cols-2 gap-2">
                {sampleResumes.slice(0, 2).map((resume, index) => (
                  <button
                    key={index}
                    onClick={() => loadSampleResume(index)}
                    className="text-left text-sm text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 px-3 py-2 rounded-md transition-colors"
                  >
                    Sample Resume {index + 1}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Job Description Section */}
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Job Description</h2>
            
            <div className="mb-4">
              <label htmlFor="jobDescription" className="block text-sm font-medium text-gray-700 mb-1">
                Paste the job description here
              </label>
              <textarea
                id="jobDescription"
                rows={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="Paste the job description text here..."
                value={jobDescription}
                onChange={(e) => setJobDescription(e.target.value)}
              />
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={compareWithJobDescription}
                disabled={!jobDescription || !resumeText}
                className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium text-white ${
                  !jobDescription || !resumeText
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                <Target className="w-4 h-4 mr-2" />
                Compare with Resume
              </button>
              
              <div className="relative">
                <button
                  onClick={() => setShowJobDescription(!showJobDescription)}
                  className="w-full sm:w-auto flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Briefcase className="w-4 h-4 mr-2" />
                  Sample Job Descriptions
                </button>
                
                {showJobDescription && (
                  <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg">
                    <div className="p-2">
                      {sampleJobDescriptions.map((job, index) => (
                        <button
                          key={job.id}
                          onClick={() => {
                            loadSampleJobDescription(index);
                            setShowJobDescription(false);
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded"
                        >
                          {job.title}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Results Section */}
        {(analysisResults || jobMatchResults) && (
          <div className="bg-white p-6 rounded-xl shadow-md mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">Analysis Results</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* ATS Score */}
              {analysisResults && (
                <div className="bg-blue-50 p-6 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-800">ATS Score</h3>
                    <BarChart3 className="w-6 h-6 text-blue-500" />
                  </div>
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-32 h-32 rounded-full border-8 border-blue-200 mb-4">
                      <span className="text-3xl font-bold text-blue-600">
                        {analysisResults.score}
                      </span>
                      <span className="text-lg text-blue-400">/100</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      {analysisResults.score >= 80
                        ? 'Excellent! Your resume is well-optimized for ATS.'
                        : analysisResults.score >= 60
                        ? 'Good, but there is room for improvement.'
                        : 'Needs work. Consider optimizing your resume further.'}
                    </p>
                  </div>
                </div>
              )}

              {/* Key Metrics */}
              {analysisResults && (
                <div className="bg-white border border-gray-200 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-800 mb-4">Key Metrics</h3>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Word Count</span>
                        <span className="font-medium">{analysisResults.wordCount}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            analysisResults.wordCount > 400 
                              ? 'bg-red-500' 
                              : analysisResults.wordCount > 300 
                                ? 'bg-yellow-500' 
                                : 'bg-green-500'
                          }`}
                          style={{ width: `${Math.min(100, (analysisResults.wordCount / 5))}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {analysisResults.wordCount > 400 
                          ? 'Consider reducing the length for better readability'
                          : analysisResults.wordCount > 300 
                            ? 'Good length'
                            : 'Could add more details'}
                      </p>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Sections Found</span>
                        <span className="font-medium">{analysisResults.sections.length}/5</span>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {analysisResults.sections.map((section, i) => (
                          <span key={i} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {section}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Job Match Score */}
              {jobMatchResults && (
                <div className="bg-green-50 p-6 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-800">Job Match Score</h3>
                    <TrendingUp className="w-6 h-6 text-green-500" />
                  </div>
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-32 h-32 rounded-full border-8 border-green-200 mb-4">
                      <span className="text-3xl font-bold text-green-600">
                        {jobMatchResults.matchScore}
                      </span>
                      <span className="text-lg text-green-400">/100</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      {jobMatchResults.matchScore >= 70
                        ? 'Great match! Your resume aligns well with the job description.'
                        : jobMatchResults.matchScore >= 40
                        ? 'Moderate match. Consider adding more relevant keywords.'
                        : 'Low match. Review the missing keywords below.'}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Issues & Suggestions */}
            {analysisResults && (
              <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                {analysisResults.issues.length > 0 && (
                  <div className="bg-red-50 p-5 rounded-lg">
                    <h3 className="text-lg font-medium text-red-800 mb-3 flex items-center">
                      <AlertCircle className="w-5 h-5 mr-2" />
                      Issues to Address
                    </h3>
                    <ul className="space-y-2">
                      {analysisResults.issues.map((issue, i) => (
                        <li key={i} className="flex items-start">
                          <span className="text-red-500 mr-2">•</span>
                          <span className="text-red-700">{issue.text}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="bg-yellow-50 p-5 rounded-lg">
                  <h3 className="text-lg font-medium text-yellow-800 mb-3 flex items-center">
                    <Lightbulb className="w-5 h-5 mr-2" />
                    Suggestions for Improvement
                  </h3>
                  <ul className="space-y-2">
                    {analysisResults.suggestions.map((suggestion, i) => (
                      <li key={i} className="flex items-start">
                        <span className="text-yellow-600 mr-2">•</span>
                        <span className="text-yellow-800">{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {/* Job Match Details */}
            {jobMatchResults && (
              <div className="mt-8">
                <h3 className="text-lg font-medium text-gray-800 mb-3">Keyword Analysis</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-green-50 p-5 rounded-lg">
                    <h4 className="font-medium text-green-800 mb-3 flex items-center">
                      <CheckCircle className="w-5 h-5 mr-2 text-green-500" />
                      Matching Keywords ({jobMatchResults.matchingKeywords.length})
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {jobMatchResults.matchingKeywords.map((keyword, i) => (
                        <span key={i} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {keyword}
                        </span>
                      ))}
                      {jobMatchResults.matchingKeywords.length === 0 && (
                        <p className="text-sm text-gray-500">No matching keywords found.</p>
                      )}
                    </div>
                  </div>

                  <div className="bg-red-50 p-5 rounded-lg">
                    <h4 className="font-medium text-red-800 mb-3 flex items-center">
                      <AlertCircle className="w-5 h-5 mr-2 text-red-500" />
                      Missing Keywords ({jobMatchResults.missingKeywords.length})
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {jobMatchResults.missingKeywords.map((keyword, i) => (
                        <span key={i} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          {keyword}
                        </span>
                      ))}
                      {jobMatchResults.missingKeywords.length === 0 && (
                        <p className="text-sm text-gray-500">No missing keywords found.</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Tips Section */}
        <div className="bg-white p-6 rounded-xl shadow-md">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Tips to Improve Your ATS Score</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                <Award className="w-5 h-5 text-blue-600" />
              </div>
              <h3 className="font-medium text-gray-800 mb-2">Use Relevant Keywords</h3>
              <p className="text-sm text-gray-600">Incorporate keywords from the job description naturally throughout your resume.</p>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mb-3">
                <FileText className="w-5 h-5 text-purple-600" />
              </div>
              <h3 className="font-medium text-gray-800 mb-2">Standard Formatting</h3>
              <p className="text-sm text-gray-600">Use standard fonts (Arial, Calibri, Times New Roman) and avoid complex layouts.</p>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mb-3">
                <Target className="w-5 h-5 text-green-600" />
              </div>
              <h3 className="font-medium text-gray-800 mb-2">Quantify Achievements</h3>
              <p className="text-sm text-gray-600">Use numbers and metrics to demonstrate your impact in previous roles.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
