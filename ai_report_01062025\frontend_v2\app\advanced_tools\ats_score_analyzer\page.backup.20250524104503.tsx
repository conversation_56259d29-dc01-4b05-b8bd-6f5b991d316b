'use client';
import React, { useState, useCallback, useRef, ChangeEvent, MouseEvent } from 'react';
import { useDropzone } from 'react-dropzone';
import { 
  BarChart3, CheckCircle, AlertCircle, Lightbulb, FileText, 
  TrendingUp, Target, Award, RefreshCw, Download, Share2, Upload, X,
  Briefcase, FileText as FileTextIcon
} from 'lucide-react';
import { sampleJobDescriptions, sampleResumes } from './sampleData';

interface AnalysisResults {
  score: number;
  wordCount: number;
  sections: string[];
  keywords: string[];
  issues: { type: string; text: string }[];
  suggestions: string[];
}

interface JobMatchResults {
  matchScore: number;
  missingKeywords: string[];
  matchingKeywords: string[];
}

export default function ATSScoreAnalyzer() {
  const [resumeText, setResumeText] = useState('');
  const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [jobDescription, setJobDescription] = useState('');
  const [showJobDescription, setShowJobDescription] = useState(false);
  const [jobMatchResults, setJobMatchResults] = useState<JobMatchResults | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const detectSections = (text: string) => {
    const sections: string[] = [];
    const commonSections = ['experience', 'education', 'skills', 'projects', 'summary'];
    
    commonSections.forEach(section => {
      if (text.toLowerCase().includes(section)) {
        sections.push(section.charAt(0).toUpperCase() + section.slice(1));
      }
    });
    
    return sections;
  };

  const extractKeywords = (text: string) => {
    const techKeywords = [
      'JavaScript', 'Python', 'React', 'Node.js', 'SQL', 'Git', 'AWS', 
      'Docker', 'MongoDB', 'API', 'HTML', 'CSS', 'Java', 'C++', 'Agile',
      'TypeScript', 'Redux', 'GraphQL', 'REST', 'Express', 'Django', 'Flask',
      'PostgreSQL', 'MySQL', 'Firebase', 'Azure', 'GCP', 'Kubernetes', 'CI/CD'
    ];
    
    const found = techKeywords.filter(keyword => 
      text.toLowerCase().includes(keyword.toLowerCase())
    );
    
    return found.slice(0, 8); // Limit to first 8 found
  };

  const analyzeResume = () => {
    if (!resumeText.trim()) return;
    
    setIsAnalyzing(true);
    
    // Simulate analysis delay
    setTimeout(() => {
      const score = Math.floor(Math.random() * 40) + 60;
      const wordCount = resumeText.split(/\s+/).filter(word => word.length > 0).length;
      const hasEmail = resumeText.toLowerCase().includes('@');
      const hasPhone = /\d{3}[-.]?\d{3}[-.]?\d{4}/.test(resumeText);
      const hasQuantified = /\d+%|\d+\$|\d+ hours|\d+ users/.test(resumeText);
      
      // More sophisticated mock analysis based on actual content
      const issues = [];
      const suggestions = [
        'Use standard section headers like "Experience", "Education", "Skills"',
        'Include specific technical keywords relevant to your target role',
        'Ensure all dates are formatted consistently (MM/YYYY)',
        'Use action verbs to start each bullet point'
      ];
      
      // Check for common issues
      if (!hasEmail) {
        issues.push({ type: 'error', text: 'Missing email address in contact information' });
      } else {
        issues.push({ type: 'success', text: 'Contact information found' });
      }
      
      if (!hasPhone) {
        issues.push({ type: 'warning', text: 'Phone number not detected or improperly formatted' });
      }
      
      if (wordCount < 250) {
        issues.push({ type: 'warning', text: 'Resume may be too short - consider adding more details' });
      } else if (wordCount > 700) {
        issues.push({ type: 'warning', text: 'Resume may be too long - consider condensing content' });
      } else {
        issues.push({ type: 'success', text: 'Good resume length' });
      }
      
      if (hasQuantified) {
        issues.push({ type: 'success', text: 'Great use of quantified achievements' });
      } else {
        suggestions.push('Add numbers to quantify your achievements (e.g., \"Increased sales by 20%\")');
      }
      
      // Check for common sections and content
      const sections = detectSections(resumeText);
      const keywords = extractKeywords(resumeText);
      
      if (sections.length < 3) {
        suggestions.push('Consider adding more sections like Skills, Projects, or Certifications');
      }
      
      if (keywords.length < 3) {
        suggestions.push('Include more technical keywords relevant to your target job');
      }
      
      if (score < 70) {
        suggestions.push('Consider using a simpler, more ATS-friendly resume template');
      }
      
      setAnalysisResults({
        score,
        wordCount,
        sections,
        keywords,
        issues,
        suggestions: suggestions.slice(0, 5) // Limit to top 5 suggestions
      });
      
      // If job description is provided, run comparison
      if (jobDescription.trim()) {
        compareWithJobDescription();
      } else if (jobMatchResults) {
        // Clear previous job match results if no job description is provided
        setJobMatchResults(null);
      }
      
      setIsAnalyzing(false);
    }, 1500);
  };

  const clearAnalysis = () => {
    setResumeText('');
    setAnalysisResults(null);
    setUploadedFile(null);
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setUploadedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        setResumeText(text || 'Could not read file content');
      };
      reader.readAsText(file);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt']
    },
    maxFiles: 1,
    onDragEnter: () => setIsDragging(true),
    onDragLeave: () => setIsDragging(false),
  });

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onDrop([file]);
    }
  };

  const removeFile = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setUploadedFile(null);
    setResumeText('');
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-600';
    if (score >= 60) return 'bg-yellow-600';
    return 'bg-red-600';
  };
  
  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Needs Work';
  };

  const compareWithJobDescription = () => {
    if (!jobDescription.trim() || !resumeText.trim()) return;
    
    // Simple keyword matching (can be enhanced with NLP)
    const jobKeywords = jobDescription
      .toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 3); // Filter out small words
    
    const resumeTextLower = resumeText.toLowerCase();
    const matching = jobKeywords.filter(keyword => 
      resumeTextLower.includes(keyword)
    );
    
    const missing = jobKeywords.filter(keyword => 
      !resumeTextLower.includes(keyword)
    ).slice(0, 10); // Show top 10 missing keywords
    
    const matchScore = Math.min(
      100,
      Math.round((matching.length / jobKeywords.length) * 100)
    );
    
    setJobMatchResults({
      matchScore,
      missingKeywords: [...new Set(missing)], // Remove duplicates
      matchingKeywords: [...new Set(matching)].slice(0, 20) // Show top 20 matches
    });
  };

  const sampleResume = `John Doe
Software Engineer
Email: <EMAIL> | Phone: (*************

EXPERIENCE
Software Developer Intern at Tech Corp (June 2024 - August 2024)
• Developed React components for customer dashboard, improving user engagement by 25%
• Collaborated with team of 5 developers using Agile methodology
• Implemented REST APIs using Node.js and MongoDB

EDUCATION
Bachelor of Science in Computer Science
University of Technology (2022-2026)
GPA: 3.7/4.0

PROJECTS
E-commerce Web App
• Built full-stack application using React, Node.js, and PostgreSQL
• Integrated payment processing and user authentication
• Deployed on AWS with Docker containerization

SKILLS
Programming: JavaScript, Python, Java, SQL
Frameworks: React, Express.js, Django
Tools: Git, Docker, AWS, MongoDB`;

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-2">
            <BarChart3 className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">ATS Score Analyzer</h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Get instant feedback on how well your resume will perform in an Applicant Tracking System
          </p>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
          <div className="p-6">
            {/* Input Section */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Upload or Paste Your Resume</h2>
                <div className="flex gap-2">
                  <button
                    onClick={() => setResumeText(sampleResume)}
                    className="text-sm bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded"
                  {!showJobDescription ? (
                    <button 
                      onClick={() => setShowJobDescription(true)}
                      className="text-sm text-blue-600 hover:underline"
                    >
                      + Add Job Description
                    </button>
                  ) : (
                    <button 
                      onClick={() => {
                        setShowJobDescription(false);
                        setJobDescription('');
                        setJobMatchResults(null);
                      }}
                      className="text-sm text-gray-500 hover:text-gray-700"
                    >
                      Remove
                    </button>
                  )}
                </div>
                
                {showJobDescription && (
                  <div className="space-y-2">
                    <textarea
                      className="w-full h-32 p-3 border rounded-lg text-sm"
                      placeholder="Paste the job description here to compare with your resume..."
                      value={jobDescription}
                      onChange={(e) => setJobDescription(e.target.value)}
                    />
                    <button
                      onClick={compareWithJobDescription}
                      disabled={!jobDescription.trim() || isAnalyzing}
                      className={`px-4 py-2 text-sm rounded-lg ${
                        !jobDescription.trim() || isAnalyzing
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                      }`}
                    >
                      Compare with Job Description
                  </div>
                  <span className="text-sm text-gray-500">
                    {Math.round(uploadedFile.size / 1024)} KB • Click to change file
                  </span>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="mx-auto w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Upload className="w-5 h-5 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-700">
                    {isDragActive ? 'Drop your resume here' : 'Drag & drop your resume here'}
                  </h3>
                  <p className="text-sm text-gray-500">
                    or <span className="text-blue-600 font-medium">browse files</span> (PDF, DOC, DOCX, TXT)
                  </p>
                </div>
              )}
            </div>
            
            <div className="relative my-4">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center">
                <span className="px-2 bg-white text-sm text-gray-500">or paste your text below</span>
              </div>
            </div>
            
            {/* Job Description Section */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Briefcase className="w-4 h-4 text-gray-500" />
                  <h3 className="font-medium">Job Description (Optional)</h3>
                </div>
                <div className="flex items-center gap-2">
                  <select 
                    className="text-xs border rounded px-2 py-1 bg-white"
                    onChange={(e) => {
                      const selectedJob = sampleJobDescriptions.find(job => job.id === e.target.value);
                      if (selectedJob) {
                        setJobDescription(selectedJob.description);
                        setShowJobDescription(true);
                      }
                    }}
                    value=""
                  >
                    <option value="">Load sample job</option>
                    {sampleJobDescriptions.map(job => (
                      <option key={job.id} value={job.id}>
                        {job.title} - {job.company}
                      </option>
                    ))}
                  </select>
                {!showJobDescription ? (
                  <button 
                    onClick={() => setShowJobDescription(true)}
                    className="text-sm text-blue-600 hover:underline"
                  >
                    + Add Job Description
                  </button>
                ) : (
                  <button 
                    onClick={() => {
                      setShowJobDescription(false);
                      setJobDescription('');
                      setJobMatchResults(null);
                    }}
                    className="text-sm text-gray-500 hover:text-gray-700"
                  >
                    Remove
                  </button>
                )}
              </div>
              
              {showJobDescription && (
                <div className="space-y-2">
                  <textarea
                    className="w-full h-32 p-3 border rounded-lg text-sm"
                    placeholder="Paste the job description here to compare with your resume..."
                    value={jobDescription}
                    onChange={(e) => setJobDescription(e.target.value)}
                  />
                  <button
                    onClick={compareWithJobDescription}
                    disabled={!jobDescription.trim() || isAnalyzing}
                    className={`px-4 py-2 text-sm rounded-lg ${
                      !jobDescription.trim() || isAnalyzing
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                    }`}
                  >
                    Compare with Job Description
                  </button>
                </div>
              )}
            </div>
            
            <div className="mb-3">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-gray-700">Load Sample Resume:</label>
                <select 
                  className="text-sm border rounded px-2 py-1 bg-white"
                  onChange={(e) => {
                    const selectedResume = sampleResumes.find(r => r.id === e.target.value);
                    if (selectedResume) {
                      setResumeText(selectedResume.content);
                      setUploadedFile(null);
                    }
                  }}
                  value=""
                >
                  <option value="">Select a sample resume</option>
                  {sampleResumes.map(resume => (
                    <option key={resume.id} value={resume.id}>
                      {resume.title}
                    </option>
                  ))}
                </select>
              </div>
              <textarea
                className="w-full h-48 p-4 border-2 rounded-lg resize-none focus:border-blue-500 focus:outline-none"
                placeholder="Paste your resume text here. Include all sections: contact info, experience, education, skills, etc."
                value={resumeText}
                onChange={(e) => setResumeText(e.target.value)}
              />
                <button
                  onClick={analyzeResume}
                  disabled={!resumeText.trim() || isAnalyzing}
                  className={`px-6 py-2 rounded-lg font-medium text-white ${
                    !resumeText.trim() || isAnalyzing
                      ? 'bg-blue-300 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                >
                  {isAnalyzing ? (
                    <span className="flex items-center gap-2">
                      <RefreshCw className="w-4 h-4 animate-spin" />
                      Analyzing...
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      <BarChart3 className="w-4 h-4" />
                      Analyze My Resume
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Results Section */}
        {analysisResults ? (
          <div className="space-y-6">
            {/* Score Card */}
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200">
              <div className="flex flex-col md:flex-row items-center justify-between">
                <div className="mb-4 md:mb-0">
                  <h3 className="text-xl font-semibold mb-1">ATS Compatibility Score</h3>
                  <p className="text-gray-600">How well your resume works with Applicant Tracking Systems</p>
                </div>
                <div className="text-center">
                  <div className={`text-5xl font-bold ${getScoreColor(analysisResults.score)}`}>
                    {analysisResults.score}
                  </div>
                  <div className="text-lg text-gray-500">/ 100 • {getScoreLabel(analysisResults.score)}</div>
                </div>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-3 mt-6 mb-2">
                <div 
                  className={`h-3 rounded-full transition-all duration-1000 ${getScoreBgColor(analysisResults.score)}`}
                  style={{width: `${analysisResults.score}%`}}
                ></div>
              </div>
              
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>0</span>
                <span>25</span>
                <span>50</span>
                <span>75</span>
                <span>100</span>
              </div>
              
              <div className="flex items-center justify-between text-xs mt-1">
                <span className="text-red-500">Needs Work</span>
                <span className="text-yellow-500">Fair</span>
                <span className="text-green-500">Excellent</span>
              </div>
            </div>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white border rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="w-5 h-5 text-blue-600" />
                  <span className="font-medium">Word Count</span>
                </div>
                <div className="text-2xl font-bold text-blue-600">{analysisResults.wordCount}</div>
                <div className="text-sm text-gray-600 mt-1">
                  {analysisResults.wordCount < 300 ? 'Consider adding more detail' : 
                   analysisResults.wordCount > 700 ? 'May be too lengthy' : 'Good length'}
                </div>
              </div>
              
              <div className="bg-white border rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="w-5 h-5 text-green-600" />
                  <span className="font-medium">Sections Found</span>
                </div>
                <div className="text-2xl font-bold text-green-600">{analysisResults.sections.length}</div>
                <div className="text-sm text-gray-600 mt-1">
                  {analysisResults.sections.join(', ') || 'None detected'}
                </div>
              </div>
              
              <div className="bg-white border rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Award className="w-5 h-5 text-purple-600" />
                  <span className="font-medium">Keywords Found</span>
                </div>
                <div className="text-2xl font-bold text-purple-600">{analysisResults.keywords.length}</div>
                <div className="text-sm text-gray-600 mt-1">
                  {analysisResults.keywords.slice(0, 3).join(', ')}
                  {analysisResults.keywords.length > 3 ? '...' : ''}
                </div>
              </div>
            </div>

            {/* Analysis Results */}
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <AlertCircle className="w-5 h-5 text-orange-500" />
                Analysis Results
              </h3>
              
              <div className="space-y-3">
                {analysisResults.issues.map((issue, idx) => (
                  <div key={idx} className={`flex items-start gap-3 p-3 rounded-lg ${
                    issue.type === 'error' ? 'bg-red-50 border border-red-200' : 
                    issue.type === 'warning' ? 'bg-yellow-50 border border-yellow-200' : 
                    'bg-green-50 border border-green-200'
                  }`}>
                    {issue.type === 'error' ? (
                      <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
                    ) : issue.type === 'warning' ? (
                      <AlertCircle className="w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5" />
                    ) : (
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                    )}
                    <div>
                      <div className={`font-medium ${
                        issue.type === 'error' ? 'text-red-800' : 
                        issue.type === 'warning' ? 'text-yellow-800' : 
                        'text-green-800'
                      }`}>
                        {issue.type === 'error' ? 'Critical Issue' : 
                         issue.type === 'warning' ? 'Improvement Needed' : 
                         'Strength Identified'}
                      </div>
                      <div className="text-sm text-gray-700">{issue.text}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Improvement Suggestions */}
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Lightbulb className="w-5 h-5 text-blue-500" />
                Improvement Suggestions
              </h3>
              
              <div className="grid gap-3">
                {analysisResults.suggestions.map((suggestion, idx) => (
                  <div key={idx} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-bold text-blue-600">{idx + 1}</span>
                    </div>
                    <div className="text-sm text-gray-700">{suggestion}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Job Match Results */}
            {jobMatchResults && (
              <div className="bg-white border rounded-lg p-6 mt-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <BarChart3 className="w-5 h-5 text-purple-600" />
                  Job Description Match
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="text-center mb-4">
                      <div className={`text-4xl font-bold ${
                        jobMatchResults.matchScore > 70 ? 'text-green-600' :
                        jobMatchResults.matchScore > 40 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {jobMatchResults.matchScore}%
                      </div>
                      <div className="text-gray-600">Keyword Match Score</div>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-700">Matching Keywords:</h4>
                      <div className="flex flex-wrap gap-2">
                        {jobMatchResults.matchingKeywords.map((keyword, i) => (
                          <span key={i} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                            {keyword}
                          </span>
                        ))}
                        {jobMatchResults.matchingKeywords.length === 0 && (
                          <span className="text-sm text-gray-500">No matching keywords found</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-700 mb-2">Missing Keywords:</h4>
                    {jobMatchResults.missingKeywords.length > 0 ? (
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600">
                          Consider adding these keywords to better match the job description:
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {jobMatchResults.missingKeywords.map((keyword, i) => (
                            <span key={i} className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
                              {keyword}
                            </span>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="text-green-600 text-sm">
                        Great job! Your resume includes most keywords from the job description.
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3 pt-4 border-t">
              <button 
                onClick={analyzeResume}
                disabled={isAnalyzing}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${isAnalyzing ? 'animate-spin' : ''}`} />
                {isAnalyzing ? 'Analyzing...' : 'Re-analyze'}
              </button>
              <button className="px-6 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center gap-2">
                <Download className="w-4 h-4" />
                Export Report
              </button>
              <button className="px-6 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center gap-2">
                <Share2 className="w-4 h-4" />
                Share Results
              </button>
            </div>
          </div>
        ) : (
          /* Empty State with Instructions */
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="font-semibold text-blue-900 mb-3 flex items-center">
              <Lightbulb className="w-5 h-5 mr-2" />
              How to Use This Tool:
            </h3>
            <ul className="space-y-2 text-sm text-blue-800">
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <div>
                  <strong>Paste your resume</strong> in the text area above or upload a file
                </div>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <div>
                  <strong>Click "Analyze My Resume"</strong> to check ATS compatibility
                </div>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <div>
                  <strong>Review the analysis</strong> and implement the suggested improvements
                </div>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <div>
                  <strong>Save or share</strong> your results for future reference
                </div>
              </li>
            </ul>
            
            <div className="mt-4 pt-4 border-t border-blue-200">
              <h4 className="font-medium text-blue-900 mb-2">What We Check:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                  <span>Contact Information</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                  <span>Section Headers</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                  <span>Keyword Optimization</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                  <span>Length & Readability</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
