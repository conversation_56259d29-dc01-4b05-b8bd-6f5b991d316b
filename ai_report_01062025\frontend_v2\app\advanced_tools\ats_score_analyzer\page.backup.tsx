'use client';

import { useState, useCallback, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { sampleJobDescriptions, sampleResumes } from './sampleData';

// Import components
import { FileUpload } from './components/FileUpload';
import { JobDescription } from './components/JobDescription';
import { AnalysisResults } from './components/AnalysisResults';
import { JobMatchResults } from './components/JobMatchResults';
import { ResumeInput } from './components/ResumeInput';
import { SampleSelector } from './components/SampleSelector';
import { ActionButtons } from './components/ActionButtons';
import { InstructionsPanel } from './components/InstructionsPanel';
import { Header } from './components/Header';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Enhanced interfaces to match backend response
export interface ATSIssue {
  type: 'error' | 'warning' | 'success' | 'info';
  category: 'formatting' | 'content' | 'keywords' | 'structure' | 'contact_info';
  text: string;
  severity: number; // 1-5
  fix_suggestion: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
}

export interface ATSKeywordAnalysis {
  found_keywords: string[];
  missing_keywords: string[];
  keyword_density: number;
  relevance_score: number;
  technical_skills: string[];
  soft_skills: string[];
  industry_terms: string[];
}

export interface ATSContactInfo {
  has_email: boolean;
  has_phone: boolean;
  has_linkedin: boolean;
  has_portfolio: boolean;
  email_format_valid: boolean;
  phone_format_valid: boolean;
  missing_elements: string[];
}

export interface ATSStructureAnalysis {
  sections_found: string[];
  sections_missing: string[];
  section_order_score: number;
  formatting_consistency: number;
  readability_score: number;
}

export interface ATSJobMatch {
  overall_match_score: number;
  keyword_match_score: number;
  skills_match_score: number;
  experience_match_score: number;
  matching_keywords: string[];
  missing_critical_keywords: string[];
  missing_preferred_keywords: string[];
  recommendations: string[];
  competitive_edge: string[];
}

export interface ATSScoreBreakdown {
  overall_score: number;
  formatting_score: number;
  content_score: number;
  keyword_score: number;
  contact_score: number;
  experience_score: number;
  education_score: number;
  skills_score: number;
}

export interface AnalysisResultsType {
  // Core scores
  scores: ATSScoreBreakdown;
  overall_rating: string;
  
  // Detailed analysis
  word_count: number;
  character_count: number;
  structure: ATSStructureAnalysis;
  keywords: ATSKeywordAnalysis;
  contact_info: ATSContactInfo;
  issues: ATSIssue[];
  suggestions: string[];
  
  // Job matching (if job description provided)
  job_match?: ATSJobMatch;
  
  // Metadata
  processing_time: number;
  model_used: string;
  analysis_depth: string;
  timestamp: string;
}

export interface QuickScoreResponse {
  score: number;
  rating: string;
  top_strength: string;
  top_weakness: string;
  quick_win: string;
  processing_time: number;
  timestamp: string;
}

// Maintain backward compatibility for existing components
export interface Issue {
  type: 'error' | 'warning' | 'success';
  text: string;
}

export interface JobMatchResultsType {
  matchScore: number;
  matchingKeywords: string[];
  missingKeywords: string[];
  experienceMatch?: string;
  educationMatch?: string;
}

// API Functions
class ATSApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  async quickScore(resumeText: string): Promise<QuickScoreResponse> {
    const response = await fetch(`${this.baseUrl}/api/ats/quick-score`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ resume_text: resumeText }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Quick score analysis failed');
    }

    return response.json();
  }

  async comprehensiveAnalysis(
    resumeText: string,
    jobDescription?: string,
    targetRole?: string,
    industry?: string
  ): Promise<AnalysisResultsType> {
    const response = await fetch(`${this.baseUrl}/api/ats/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        resume_text: resumeText,
        job_description: jobDescription || undefined,
        target_role: targetRole || undefined,
        analysis_depth: 'comprehensive',
        industry: industry || undefined,
        experience_level: 'mid' // Could be made dynamic
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Comprehensive analysis failed');
    }

    return response.json();
  }

  async keywordOptimization(
    resumeText: string,
    jobDescription: string,
    targetRole?: string
  ): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/ats/keyword-optimize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        resume_text: resumeText,
        job_description: jobDescription,
        target_role: targetRole || 'Software Engineer'
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Keyword optimization failed');
    }

    return response.json();
  }

  async healthCheck(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/ats/health`);
    return response.json();
  }
}

const atsApi = new ATSApiClient();

export default function ATSScoreAnalyzer() {
  // State management
  const [resumeText, setResumeText] = useState('');
  const [analysisResults, setAnalysisResults] = useState<AnalysisResultsType | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [jobDescription, setJobDescription] = useState('');
  const [jobMatchResults, setJobMatchResults] = useState<JobMatchResultsType | null>(null);
  const [showJobDescription, setShowJobDescription] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [quickScoreData, setQuickScoreData] = useState<QuickScoreResponse | null>(null);
  
  // File handling
  const handleFileDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setUploadedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        setResumeText(text || 'Could not read file content');
      };
      reader.readAsText(file);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: handleFileDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt']
    },
    maxFiles: 1,
    onDragEnter: () => setIsDragging(true),
    onDragLeave: () => setIsDragging(false),
  });

  const removeFile = useCallback(() => {
    setUploadedFile(null);
    setResumeText('');
    setAnalysisResults(null);
    setJobMatchResults(null);
    setQuickScoreData(null);
    setAnalysisError(null);
  }, []);

  // Load sample job description
  const loadSampleJobDescription = useCallback((index: number) => {
    setJobDescription(sampleJobDescriptions[index].description);
    setShowJobDescription(true);
    setJobMatchResults(null);
  }, []);

  // Load sample resume
  const loadSampleResume = useCallback((index: number) => {
    const sampleResume = sampleResumes[index];
    if (sampleResume) {
      setResumeText(sampleResume.content);
      setUploadedFile(new File([sampleResume.content], `sample-resume-${index}.txt`, { type: 'text/plain' }));
      setAnalysisResults(null);
      setJobMatchResults(null);
      setQuickScoreData(null);
      setAnalysisError(null);
    }
  }, []);

  // Reset all state
  const resetAll = useCallback(() => {
    setResumeText('');
    setAnalysisResults(null);
    setUploadedFile(null);
    setJobDescription('');
    setJobMatchResults(null);
    setShowJobDescription(false);
    setQuickScoreData(null);
    setAnalysisError(null);
  }, []);

  // Quick score analysis (for instant feedback)
  const handleQuickScore = useCallback(async () => {
    if (!resumeText.trim() || resumeText.length < 50) return;
    
    try {
      setAnalysisError(null);
      const quickResult = await atsApi.quickScore(resumeText);
      setQuickScoreData(quickResult);
    } catch (error) {
      console.error('Quick score failed:', error);
      setAnalysisError(error instanceof Error ? error.message : 'Quick score failed');
    }
  }, [resumeText]);

  // Convert backend job match to legacy format for existing components
  const convertJobMatchFormat = (backendJobMatch: ATSJobMatch): JobMatchResultsType => {
    return {
      matchScore: backendJobMatch.overall_match_score,
      matchingKeywords: backendJobMatch.matching_keywords,
      missingKeywords: [
        ...backendJobMatch.missing_critical_keywords,
        ...backendJobMatch.missing_preferred_keywords
      ],
      experienceMatch: `Experience Match: ${backendJobMatch.experience_match_score}%`,
      educationMatch: `Skills Match: ${backendJobMatch.skills_match_score}%`
    };
  };

  // Main analysis function using backend API
  const analyzeResume = useCallback(async () => {
    if (!resumeText.trim() || resumeText.length < 50) {
      setAnalysisError('Resume text must be at least 50 characters long');
      return;
    }
    
    setIsAnalyzing(true);
    setAnalysisError(null);
    
    try {
      // Run comprehensive analysis
      const results = await atsApi.comprehensiveAnalysis(
        resumeText,
        jobDescription.trim() || undefined,
        undefined, // target_role - could be made dynamic
        'technology' // industry - could be made dynamic
      );
      
      setAnalysisResults(results);
      
      // Convert job match results for existing components
      if (results.job_match) {
        const legacyJobMatch = convertJobMatchFormat(results.job_match);
        setJobMatchResults(legacyJobMatch);
      } else {
        setJobMatchResults(null);
      }
      
      // Clear quick score when full analysis is done
      setQuickScoreData(null);
      
    } catch (error) {
      console.error('Analysis failed:', error);
      setAnalysisError(error instanceof Error ? error.message : 'Analysis failed');
      setAnalysisResults(null);
      setJobMatchResults(null);
    } finally {
      setIsAnalyzing(false);
    }
  }, [resumeText, jobDescription]);

  // Handle job description comparison (legacy support)
  const handleCompareWithJobDescription = useCallback(async () => {
    if (!jobDescription.trim() || !resumeText.trim()) return;
    
    try {
      setAnalysisError(null);
      const keywordResults = await atsApi.keywordOptimization(
        resumeText,
        jobDescription,
        'Software Engineer'
      );
      
      // Convert to legacy format for existing components
      const legacyResults: JobMatchResultsType = {
        matchScore: 75, // Could be calculated from keyword results
        matchingKeywords: [], // Would need to be extracted from results
        missingKeywords: [
          ...keywordResults.critical_missing,
          ...keywordResults.important_missing
        ]
      };
      
      setJobMatchResults(legacyResults);
    } catch (error) {
      console.error('Job comparison failed:', error);
      setAnalysisError(error instanceof Error ? error.message : 'Job comparison failed');
    }
  }, [jobDescription, resumeText]);

  // Auto-trigger quick score when resume text changes (debounced)
  const [quickScoreTimeout, setQuickScoreTimeout] = useState<NodeJS.Timeout | null>(null);
  
  const handleResumeTextChange = useCallback((text: string) => {
    setResumeText(text);
    
    // Clear previous timeout
    if (quickScoreTimeout) {
      clearTimeout(quickScoreTimeout);
    }
    
    // Set new timeout for quick score
    if (text.length >= 50) {
      const timeout = setTimeout(() => {
        handleQuickScore();
      }, 1000); // 1 second delay
      
      setQuickScoreTimeout(timeout);
    }
  }, [quickScoreTimeout, handleQuickScore]);

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <Header resetAll={resetAll} />

        {/* API Status Indicator */}
        <div className="mb-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-blue-800">
                Powered by Gemini AI • Advanced ATS Analysis
              </span>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {analysisError && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <div className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5">⚠️</div>
              <div>
                <div className="font-medium text-red-800">Analysis Error</div>
                <div className="text-red-700 text-sm mt-1">{analysisError}</div>
                <button 
                  onClick={() => setAnalysisError(null)}
                  className="text-red-600 hover:text-red-800 text-sm mt-2 underline"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Quick Score Display */}
        {quickScoreData && !analysisResults && (
          <div className="mb-6 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-semibold text-green-800">Quick ATS Score</div>
                <div className="text-green-700 text-sm">{quickScoreData.top_strength}</div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-green-600">{quickScoreData.score}</div>
                <div className="text-green-600 text-sm">{quickScoreData.rating}</div>
              </div>
            </div>
            <div className="mt-3 text-sm text-blue-700">
              <strong>Quick Win:</strong> {quickScoreData.quick_win}
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
          <div className="p-6">
            {/* File Upload Section */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Upload or Paste Your Resume</h2>
              </div>
              
              <FileUpload
                onFileUpload={handleFileDrop}
                isDragActive={isDragActive}
                getRootProps={getRootProps}
                getInputProps={getInputProps}
                removeFile={removeFile}
                hasFile={!!uploadedFile}
                fileName={uploadedFile?.name}
                fileSize={uploadedFile?.size}
              />
              
              <div className="relative my-4">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center">
                  <span className="px-2 bg-white text-sm text-gray-500">or paste your text below</span>
                </div>
              </div>
              
              <ResumeInput
                resumeText={resumeText}
                setResumeText={handleResumeTextChange} // Use enhanced handler
                analyzeResume={analyzeResume}
                isAnalyzing={isAnalyzing}
              />
              
              <div className="mt-4 flex justify-between items-center">
                <SampleSelector
                  samples={sampleResumes}
                  onSelect={loadSampleResume}
                  placeholder="Select a sample resume"
                  label="Load Sample Resume"
                />
                
                {/* Analysis Mode Selector */}
                <div className="flex gap-2">
                  <button
                    onClick={handleQuickScore}
                    disabled={!resumeText.trim() || resumeText.length < 50}
                    className="px-4 py-2 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Quick Score
                  </button>
                  <button
                    onClick={analyzeResume}
                    disabled={!resumeText.trim() || resumeText.length < 50 || isAnalyzing}
                    className="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isAnalyzing ? 'Analyzing...' : 'Full Analysis'}
                  </button>
                </div>
              </div>
            </div>
            
            {/* Job Description Section */}
            <JobDescription
              jobDescription={jobDescription}
              setJobDescription={setJobDescription}
              sampleJobDescriptions={sampleJobDescriptions}
              compareWithJobDescription={handleCompareWithJobDescription}
              isAnalyzing={isAnalyzing}
              showJobDescription={showJobDescription}
              setShowJobDescription={setShowJobDescription}
              setJobMatchResults={setJobMatchResults}
            />
          </div>
        </div>

        {/* Results Section */}
        {analysisResults ? (
          <div className="space-y-6">
            {/* Analysis Results */}
            <AnalysisResults results={analysisResults} />
            
            {/* Job Match Results */}
            {jobMatchResults && (
              <JobMatchResults results={jobMatchResults} />
            )}
            
            {/* Action Buttons */}
            <ActionButtons
              onReanalyze={analyzeResume}
              isAnalyzing={isAnalyzing}
            />
          </div>
        ) : (
          /* Empty State with Instructions */
          <InstructionsPanel />
        )}
      </div>
    </div>
  );
}