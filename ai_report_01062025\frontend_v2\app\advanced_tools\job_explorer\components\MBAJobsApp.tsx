'use client';

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { getAllJobs, type Job } from '../data';
import { useAppState } from './hooks/useAppState';
import { Header } from './layout/Header';
import { Footer } from './layout/Footer';
import { HomeView } from './views/HomeView';
import { JobsView } from './views/JobsView';
import { JobDetailView } from './views/JobDetailView';
import { QuestionsView } from './views/QuestionsView';
import { QuestionDetailView } from './views/QuestionDetailView';

const MBAJobsApp = () => {
  // Static metadata
  const metadata = {
    "created_date": "2025-05-27",
    "version": "1.0",
    "description": "Comprehensive database of entry-level MBA job descriptions and interview questions for Indian market with interactive learning features",
    "total_jobs": 3,
    "specializations_covered": ["Marketing", "Finance"]
  };

  const specializations = [
    {
      "id": "MKT",
      "name": "Marketing",
      "description": "Brand management, digital marketing, market research, and consumer engagement",
      "popular_industries": ["FMCG", "E-commerce", "Consumer Goods", "Retail", "Media"],
      "avg_starting_package": "6-12 LPA",
      "growth_trajectory": "High demand with digital transformation focus"
    },
    {
      "id": "FIN",
      "name": "Finance", 
      "description": "Corporate finance, investment banking, financial analysis, and risk management",
      "popular_industries": ["Banking", "Financial Services", "Investment Banking", "Corporate", "Consulting"],
      "avg_starting_package": "7-15 LPA",
      "growth_trajectory": "Highest paying specialization with diverse career paths"
    }
  ];

  // Get jobs from data structure
  const jobs = getAllJobs();
  console.log("46:",jobs);

  // Custom hook for state management
  const {
    currentView,
    setCurrentView,
    selectedJob,
    setSelectedJob,
    selectedQuestion,
    setSelectedQuestion,
    searchTerm,
    setSearchTerm,
    filters,
    setFilters,
    questionFilters,
    setQuestionFilters,
    showFilters,
    setShowFilters,
    selectedQuestionModal,
    setSelectedQuestionModal,
    bookmarkedJobs,
    setBookmarkedJobs,
    bookmarkedQuestions,
    setBookmarkedQuestions,
    userNotes,
    setUserNotes,
    confidenceRatings,
    setConfidenceRatings
  } = useAppState();

  // Console log user interactions for persistence
  useEffect(() => {
    console.log('User State Update:', {
      bookmarkedJobs: Array.from(bookmarkedJobs),
      bookmarkedQuestions: Array.from(bookmarkedQuestions),
      userNotes,
      confidenceRatings,
      timestamp: new Date().toISOString()
    });
  }, [bookmarkedJobs, bookmarkedQuestions, userNotes, confidenceRatings]);

  const renderCurrentView = () => {
    const commonProps = {
      jobs,
      specializations,
      metadata,
      searchTerm,
      setSearchTerm,
      filters,
      setFilters,
      questionFilters,
      setQuestionFilters,
      showFilters,
      setShowFilters,
      selectedQuestionModal,
      setSelectedQuestionModal,
      bookmarkedJobs,
      setBookmarkedJobs,
      bookmarkedQuestions,
      setBookmarkedQuestions,
      userNotes,
      setUserNotes,
      confidenceRatings,
      setConfidenceRatings,
      setCurrentView,
      setSelectedJob,
      setSelectedQuestion
    };

    switch (currentView) {
      case 'jobs':
        return <JobsView {...commonProps} />;
      case 'job-detail':
        return <JobDetailView {...commonProps} selectedJob={selectedJob} />;
      case 'questions':
        return <QuestionsView {...commonProps} selectedJob={selectedJob} />;
      case 'question-detail':
        return <QuestionDetailView {...commonProps} selectedQuestion={selectedQuestion} />;
      default:
        return <HomeView {...commonProps} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        currentView={currentView}
        setCurrentView={setCurrentView}
        metadata={metadata}
      />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderCurrentView()}
      </main>

      <Footer metadata={metadata} />
    </div>
  );
};

export default MBAJobsApp;