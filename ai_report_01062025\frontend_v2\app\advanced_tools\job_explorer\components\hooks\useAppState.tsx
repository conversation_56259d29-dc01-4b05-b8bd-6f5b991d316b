import { useState } from 'react';
import { type Job } from '../../data';

export const useAppState = () => {
  const [currentView, setCurrentView] = useState('home');
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [selectedQuestion, setSelectedQuestion] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    specialization: '',
    industry: '',
    difficulty: ''
  });
  const [questionFilters, setQuestionFilters] = useState({
    category: '',
    difficulty: '',
    importance: '',
    frequency: '',
    skills: ''
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedQuestionModal, setSelectedQuestionModal] = useState(null);
  const [bookmarkedJobs, setBookmarkedJobs] = useState(new Set());
  const [bookmarkedQuestions, setBookmarkedQuestions] = useState(new Set());
  const [userNotes, setUserNotes] = useState<Record<string, string>>({});
  const [confidenceRatings, setConfidenceRatings] = useState<Record<string, number>>({});

  return {
    currentView,
    setCurrentView,
    selectedJob,
    setSelectedJob,
    selectedQuestion,
    setSelectedQuestion,
    searchTerm,
    setSearchTerm,
    filters,
    setFilters,
    questionFilters,
    setQuestionFilters,
    showFilters,
    setShowFilters,
    selectedQuestionModal,
    setSelectedQuestionModal,
    bookmarkedJobs,
    setBookmarkedJobs,
    bookmarkedQuestions,
    setBookmarkedQuestions,
    userNotes,
    setUserNotes,
    confidenceRatings,
    setConfidenceRatings
  };
};