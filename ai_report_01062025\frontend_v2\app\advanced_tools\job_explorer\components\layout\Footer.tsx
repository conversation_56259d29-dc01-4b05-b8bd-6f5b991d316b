import React from 'react';

interface FooterProps {
  metadata: {
    created_date: string;
    description: string;
  };
}

export const Footer: React.FC<FooterProps> = ({ metadata }) => {
  return (
    <footer className="bg-white border-t mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center text-gray-600">
          <p>MBA Interview Preparation Database • Last Updated: {metadata.created_date}</p>
          <p className="text-sm mt-2">{metadata.description}</p>
        </div>
      </div>
    </footer>
  );
};