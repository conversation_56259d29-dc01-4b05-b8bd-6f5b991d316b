import React from 'react';
import { Badge } from '@/components/ui/badge';

interface HeaderProps {
  currentView: string;
  setCurrentView: (view: string) => void;
  metadata: {
    version: string;
  };
}

export const Header: React.FC<HeaderProps> = ({ currentView, setCurrentView, metadata }) => {
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center gap-8">
            <button 
              onClick={() => setCurrentView('home')}
              className="text-xl font-bold text-blue-600 hover:text-blue-700"
            >
              MBA Prep Hub
            </button>
            <nav className="hidden md:flex space-x-6">
              <button 
                onClick={() => setCurrentView('home')}
                className={`${currentView === 'home' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'} pb-1`}
              >
                Home
              </button>
              <button 
                onClick={() => setCurrentView('jobs')}
                className={`${currentView === 'jobs' || currentView === 'job-detail' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'} pb-1`}
              >
                Jobs
              </button>
              <button 
                onClick={() => setCurrentView('questions')}
                className={`${currentView === 'questions' || currentView === 'question-detail' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'} pb-1`}
              >
                Questions
              </button>
            </nav>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="hidden sm:flex">
              v{metadata.version}
            </Badge>
          </div>
        </div>
      </div>
    </header>
  );
};