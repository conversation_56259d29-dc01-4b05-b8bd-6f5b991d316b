import React from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BookmarkPlus, 
  Bookmark, 
  ChevronRight, 
  Award,
  Target,
  X,
  Clock
} from 'lucide-react';
import { ConfidenceRating } from '../shared';

interface QuestionModalProps {
  question: any;
  isOpen: boolean;
  onClose: () => void;
  bookmarkedQuestions: Set<string>;
  toggleBookmarkQuestion: (questionId: string) => void;
  userNotes: Record<string, string>;
  updateUserNote: (id: string, note: string) => void;
  confidenceRatings: Record<string, number>;
  updateConfidenceRating: (questionId: string, rating: number) => void;
}

export const QuestionModal: React.FC<QuestionModalProps> = ({
  question,
  isOpen,
  onClose,
  bookmarkedQuestions,
  toggleBookmarkQuestion,
  userNotes,
  updateUserNote,
  confidenceRatings,
  updateConfidenceRating
}) => {
  if (!question) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        className="max-h-[95vh] overflow-y-auto bg-white border border-gray-200 shadow-2xl"
        style={{ 
          maxWidth: '95vw', 
          width: '95vw',
          minWidth: '90vw'
        }}
      >
        <DialogHeader className="border-b border-gray-200 pb-6 bg-white">
          <div className="flex items-start justify-between">
            <div className="flex-1 pr-6">
              <DialogTitle className="text-2xl font-bold text-gray-900 leading-tight mb-4">
                {question.question}
              </DialogTitle>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="bg-blue-50 border-blue-200 text-blue-700 font-medium">
                  {question.category}
                </Badge>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => toggleBookmarkQuestion(question.question_id)}
                className="hover:bg-blue-100 transition-colors bg-white"
              >
                {bookmarkedQuestions.has(question.question_id) ? 
                  <Bookmark className="h-5 w-5 fill-blue-600 text-blue-600" /> : 
                  <BookmarkPlus className="h-5 w-5 text-gray-500 hover:text-blue-600" />
                }
              </Button>
            </div>
          </div>
          {question.interviewer_intent && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-blue-900">
                <strong className="text-blue-800">💡 Interviewer Intent:</strong> {question.interviewer_intent}
              </p>
            </div>
          )}
        </DialogHeader>

        <div className="bg-white p-6">
          <Tabs defaultValue="answer-points" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-gradient-to-r from-gray-100 to-blue-100 p-1.5 rounded-xl shadow-inner h-14 border border-gray-200">
              <TabsTrigger 
                value="answer-points" 
                className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-200 rounded-lg font-semibold text-sm data-[state=active]:text-blue-700 data-[state=active]:border data-[state=active]:border-blue-200"
              >
                🎯 Answer Points
              </TabsTrigger>
              <TabsTrigger 
                value="examples" 
                className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-200 rounded-lg font-semibold text-sm data-[state=active]:text-green-700 data-[state=active]:border data-[state=active]:border-green-200"
              >
                ✨ Examples
              </TabsTrigger>
              <TabsTrigger 
                value="tips" 
                className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-200 rounded-lg font-semibold text-sm data-[state=active]:text-purple-700 data-[state=active]:border data-[state=active]:border-purple-200"
              >
                💡 Tips
              </TabsTrigger>
              <TabsTrigger 
                value="follow-up" 
                className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-200 rounded-lg font-semibold text-sm data-[state=active]:text-orange-700 data-[state=active]:border data-[state=active]:border-orange-200"
              >
                🔄 Follow-up
              </TabsTrigger>
            </TabsList>

            <TabsContent value="answer-points" className="mt-6">
              <Card className="border-2 border-blue-100 shadow-sm bg-white">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50">
                  <CardTitle className="flex items-center gap-2 text-blue-900">
                    <Target className="h-5 w-5" />
                    Key Answer Points
                  </CardTitle>
                  <CardDescription className="text-blue-700">
                    Essential components of a strong answer
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6 bg-white">
                  <div className="grid gap-4">
                    {question.answer_points?.map((point, index) => (
                      <div key={index} className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-blue-200 transition-colors">
                        <span className="bg-blue-600 text-white text-sm font-bold px-3 py-1.5 rounded-full min-w-[2rem] text-center">
                          {index + 1}
                        </span>
                        <span className="text-gray-700 leading-relaxed flex-1">{point}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="examples" className="mt-6">
              <div className="space-y-6">
                {question.example_answers && Object.entries(question.example_answers).map(([level, answer]: [string, any]) => (
                  <Card key={level} className={`bg-white border-2 ${
                    level === 'excellent' ? 'border-green-200 bg-green-50' :
                    level === 'good' ? 'border-yellow-200 bg-yellow-50' :
                    'border-red-200 bg-red-50'
                  }`}>
                    <CardHeader className="bg-white">
                      <CardTitle className={`text-xl font-bold ${
                        level === 'excellent' ? 'text-green-800' :
                        level === 'good' ? 'text-yellow-800' :
                        'text-red-800'
                      }`}>
                        {answer.level} Answer
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="bg-white p-6">
                      <div className="mb-6">
                        <h4 className="text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">Sample Response:</h4>
                        <div className="bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500">
                          <div className="text-gray-800 leading-relaxed text-lg font-medium space-y-4">
                            {answer.answer.split('. ').map((sentence, idx, array) => {
                              // Group sentences into paragraphs (2-3 sentences each)
                              if (idx === 0 || idx % 3 === 0) {
                                const paragraphSentences = array.slice(idx, idx + 3);
                                return (
                                  <p key={idx} className="leading-relaxed">
                                    {paragraphSentences.join('. ')}
                                    {paragraphSentences.length > 1 && paragraphSentences[paragraphSentences.length - 1] !== array[array.length - 1] ? '.' : ''}
                                  </p>
                                );
                              }
                              return null;
                            })}
                          </div>
                        </div>
                      </div>
                      <div className={`p-4 rounded-lg border-l-4 ${
                        level === 'excellent' ? 'bg-green-100 text-green-900 border-green-500' :
                        level === 'good' ? 'bg-yellow-100 text-yellow-900 border-yellow-500' :
                        'bg-red-100 text-red-900 border-red-500'
                      }`}>
                        <h5 className="font-bold text-sm uppercase tracking-wide mb-2">
                          Why this is {level}:
                        </h5>
                        <p className="leading-relaxed">
                          {answer[`why_${level}`] || answer.why_excellent || answer.why_good || answer.why_poor}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="tips" className="mt-6">
              <Card className="border-2 border-purple-100 shadow-sm bg-white">
                <CardHeader className="bg-gradient-to-r from-purple-50 to-blue-50">
                  <CardTitle className="flex items-center gap-2 text-purple-900">
                    <Target className="h-5 w-5" />
                    Preparation Tips
                  </CardTitle>
                  <CardDescription className="text-purple-700">
                    How to prepare for this type of question
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6 bg-white">
                  <ul className="space-y-3">
                    {question.preparation_tips?.map((tip, index) => (
                      <li key={index} className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
                        <Target className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{tip}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="follow-up" className="mt-6">
              <Card className="border-2 border-orange-100 shadow-sm bg-white">
                <CardHeader className="bg-gradient-to-r from-orange-50 to-red-50">
                  <CardTitle className="flex items-center gap-2 text-orange-900">
                    <ChevronRight className="h-5 w-5" />
                    Possible Follow-up Questions
                  </CardTitle>
                  <CardDescription className="text-orange-700">
                    Be prepared for these additional questions
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6 bg-white">
                  <ul className="space-y-3">
                    {question.follow_up_questions_possible?.map((followUp, index) => (
                      <li key={index} className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                        <ChevronRight className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{followUp}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Enhanced Self Assessment */}
        <div className="bg-white p-6">
          <Card className="border-2 border-green-200 bg-white shadow-sm">
            <CardHeader className="bg-gradient-to-r from-green-100 to-blue-100">
              <CardTitle className="flex items-center gap-2 text-green-900">
                <Award className="h-5 w-5" />
                Self Assessment & Learning
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6 bg-white">
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <label className="block text-sm font-semibold mb-3 text-green-800">
                  Rate your confidence on this question:
                </label>
                <ConfidenceRating 
                  questionId={question.question_id}
                  currentRating={confidenceRatings[question.question_id] || 0}
                  onRatingChange={updateConfidenceRating}
                />
              </div>
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <label className="block text-sm font-semibold mb-3 text-blue-800">
                  Personal Notes & Practice Answers:
                </label>
                <Textarea
                  placeholder="✍️ Add your notes, practice answers, key insights, or areas to improve..."
                  value={userNotes[question.question_id] || ''}
                  onChange={(e) => updateUserNote(question.question_id, e.target.value)}
                  className="min-h-[120px] border-blue-200 focus:border-blue-400 bg-white"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};