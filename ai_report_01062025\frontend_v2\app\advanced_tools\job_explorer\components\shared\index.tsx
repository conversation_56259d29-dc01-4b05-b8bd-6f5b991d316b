import React from 'react';
import { Star } from 'lucide-react';

export interface ConfidenceRatingProps {
  questionId: string;
  currentRating: number;
  onRatingChange: (questionId: string, rating: number) => void;
}

export const ConfidenceRating: React.FC<ConfidenceRatingProps> = ({ 
  questionId, 
  currentRating, 
  onRatingChange 
}) => {
  return (
    <div className="flex items-center gap-3">
      <span className="text-sm font-medium text-gray-700">Confidence:</span>
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-5 w-5 cursor-pointer transition-all duration-200 hover:scale-110 ${
              star <= currentRating 
                ? 'fill-yellow-400 text-yellow-400 drop-shadow-sm' 
                : 'text-gray-300 hover:text-yellow-300'
            }`}
            onClick={() => onRatingChange(questionId, star)}
          />
        ))}
      </div>
      {currentRating > 0 && (
        <span className="text-xs text-gray-500 ml-2">
          {currentRating === 1 ? 'Need practice' :
           currentRating === 2 ? 'Some confidence' :
           currentRating === 3 ? 'Moderately confident' :
           currentRating === 4 ? 'Very confident' :
           'Fully confident'}
        </span>
      )}
    </div>
  );
};