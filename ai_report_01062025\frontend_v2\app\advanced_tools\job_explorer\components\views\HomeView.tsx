import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Building2,
  GraduationCap,
  BrainCircuit,
  Heart,
  BookmarkPlus,
  Bookmark
} from 'lucide-react';
import { type Job } from '../../data';

interface HomeViewProps {
  jobs: Job[];
  specializations: any[];
  metadata: any;
  bookmarkedJobs: Set<string>;
  bookmarkedQuestions: Set<string>;
  setCurrentView: (view: string) => void;
  setSelectedJob: (job: Job) => void;
  setFilters: (filters: any) => void;
  setBookmarkedJobs: (bookmarks: Set<string>) => void;
}

export const HomeView: React.FC<HomeViewProps> = ({
  jobs,
  specializations,
  metadata,
  bookmarkedJobs,
  bookmarkedQuestions,
  setCurrentView,
  setSelectedJob,
  setFilters,
  setBookmarkedJobs
}) => {
  const toggleBookmarkJob = (jobId: string) => {
    const newBookmarks = new Set(bookmarkedJobs);
    if (newBookmarks.has(jobId)) {
      newBookmarks.delete(jobId);
    } else {
      newBookmarks.add(jobId);
    }
    setBookmarkedJobs(newBookmarks);
  };

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg">
        <h1 className="text-3xl font-bold mb-2">MBA Job Interview Preparation</h1>
        <p className="text-blue-100 mb-4">Master your MBA interviews with curated job descriptions and expert-level questions</p>
        <div className="flex gap-4">
          <Button onClick={() => setCurrentView('jobs')} className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold">
            Explore Jobs
          </Button>
          <Button onClick={() => setCurrentView('questions')} className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold">
            View All Questions
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Building2 className="h-8 w-8 mx-auto mb-2 text-blue-600" />
            <div className="text-2xl font-bold">{metadata.total_jobs}</div>
            <div className="text-sm text-gray-600">Job Roles</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <GraduationCap className="h-8 w-8 mx-auto mb-2 text-green-600" />
            <div className="text-2xl font-bold">{specializations?.length || 0}</div>
            <div className="text-sm text-gray-600">Specializations</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <BrainCircuit className="h-8 w-8 mx-auto mb-2 text-purple-600" />
            <div className="text-2xl font-bold">
              {jobs.reduce((total, job) => 
                total + job.interview_questions.reduce((qtotal, category) => qtotal + category.questions.length, 0), 0
              )}
            </div>
            <div className="text-sm text-gray-600">Questions</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Heart className="h-8 w-8 mx-auto mb-2 text-red-600" />
            <div className="text-2xl font-bold">{bookmarkedJobs.size + bookmarkedQuestions.size}</div>
            <div className="text-sm text-gray-600">Bookmarked</div>
          </CardContent>
        </Card>
      </div>

      {/* Specializations Overview */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Specializations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {specializations.map((spec) => (
            <Card key={spec.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                  onClick={() => {
                    setFilters(prev => ({ ...prev, specialization: spec.name }));
                    setCurrentView('jobs');
                  }}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {spec.name}
                  <Badge variant="secondary">{spec.avg_starting_package}</Badge>
                </CardTitle>
                <CardDescription>{spec.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium">Industries: </span>
                    <span className="text-sm text-gray-600">{spec.popular_industries.join(', ')}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Growth: </span>
                    <span className="text-sm text-gray-600">{spec.growth_trajectory}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Featured Jobs */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Featured Jobs</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {jobs.slice(0, 3).map((job) => (
            <Card key={job.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                  onClick={() => {
                    setSelectedJob(job);
                    setCurrentView('job-detail');
                  }}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {job.job_title}
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleBookmarkJob(job.id);
                    }}
                  >
                    {bookmarkedJobs.has(job.id) ? 
                      <Bookmark className="h-4 w-4 fill-current" /> : 
                      <BookmarkPlus className="h-4 w-4" />
                    }
                  </Button>
                </CardTitle>
                <CardDescription>{job.industry}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2 mb-3">
                  <Badge variant="outline">{job.specialization}</Badge>
                  <Badge variant="outline">{job.difficulty_level}</Badge>
                  <Badge variant="outline">★ {job.popularity_score}/10</Badge>
                </div>
                <p className="text-sm text-gray-600 line-clamp-3">{job.job_description.overview}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};