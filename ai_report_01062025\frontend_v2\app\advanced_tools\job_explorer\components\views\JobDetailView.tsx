import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft,
  BookmarkPlus,
  Bookmark,
  Target,
  GraduationCap,
  TrendingUp,
  BrainCircuit,
  FileText,
  ChevronRight
} from 'lucide-react';
import { type Job } from '../../data';
import { QuestionModal } from '../modals/QuestionModal';
import { ConfidenceRating } from '../shared';

interface JobDetailViewProps {
  selectedJob: Job | null;
  selectedQuestionModal: any;
  setSelectedQuestionModal: (question: any) => void;
  bookmarkedJobs: Set<string>;
  setBookmarkedJobs: (bookmarks: Set<string>) => void;
  bookmarkedQuestions: Set<string>;
  setBookmarkedQuestions: (bookmarks: Set<string>) => void;
  userNotes: Record<string, string>;
  setUserNotes: (notes: Record<string, string>) => void;
  confidenceRatings: Record<string, number>;
  setConfidenceRatings: (ratings: Record<string, number>) => void;
  setCurrentView: (view: string) => void;
  setSelectedQuestion: (question: any) => void;
}

export const JobDetailView: React.FC<JobDetailViewProps> = ({
  selectedJob,
  selectedQuestionModal,
  setSelectedQuestionModal,
  bookmarkedJobs,
  setBookmarkedJobs,
  bookmarkedQuestions,
  setBookmarkedQuestions,
  userNotes,
  setUserNotes,
  confidenceRatings,
  setConfidenceRatings,
  setCurrentView,
  setSelectedQuestion
}) => {
  if (!selectedJob) return null;

  const toggleBookmarkJob = (jobId: string) => {
    const newBookmarks = new Set(bookmarkedJobs);
    if (newBookmarks.has(jobId)) {
      newBookmarks.delete(jobId);
    } else {
      newBookmarks.add(jobId);
    }
    setBookmarkedJobs(newBookmarks);
  };

  const toggleBookmarkQuestion = (questionId: string) => {
    const newBookmarks = new Set(bookmarkedQuestions);
    if (newBookmarks.has(questionId)) {
      newBookmarks.delete(questionId);
    } else {
      newBookmarks.add(questionId);
    }
    setBookmarkedQuestions(newBookmarks);
  };

  const updateUserNote = (id: string, note: string) => {
    setUserNotes(prev => ({ ...prev, [id]: note }));
  };

  const updateConfidenceRating = (questionId: string, rating: number) => {
    setConfidenceRatings(prev => ({ ...prev, [questionId]: rating }));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => setCurrentView('jobs')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Jobs
        </Button>
        <Button 
          variant="outline"
          onClick={() => toggleBookmarkJob(selectedJob.id)}
        >
          {bookmarkedJobs.has(selectedJob.id) ? 
            <Bookmark className="h-4 w-4 mr-2 fill-current" /> : 
            <BookmarkPlus className="h-4 w-4 mr-2" />
          }
          {bookmarkedJobs.has(selectedJob.id) ? 'Bookmarked' : 'Bookmark'}
        </Button>
      </div>

      <Card className="relative overflow-hidden bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 border-2 border-gray-100 hover:border-blue-200 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-50" />
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-transparent rounded-full -translate-y-16 translate-x-16" />
        
        <CardHeader className="relative z-10 pb-4">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-900 bg-clip-text text-transparent mb-2">
                {selectedJob.job_title}
              </CardTitle>
              <CardDescription className="text-xl text-gray-600 font-medium">
                {selectedJob.industry}
              </CardDescription>
            </div>
            <div className="flex items-center gap-3">
              <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-3 py-1.5 rounded-full text-sm font-bold shadow-md">
                ★ {selectedJob.popularity_score}/10
              </div>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-3">
            <Badge className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 text-sm font-semibold shadow-md hover:shadow-lg transition-shadow">
              {selectedJob.specialization}
            </Badge>
            <Badge variant="outline" className="bg-white/80 backdrop-blur-sm border-2 border-blue-200 text-blue-800 px-4 py-2 font-medium hover:bg-blue-50 transition-colors">
              {selectedJob.company_type}
            </Badge>
            <Badge variant="outline" className="bg-white/80 backdrop-blur-sm border-2 border-purple-200 text-purple-800 px-4 py-2 font-medium hover:bg-purple-50 transition-colors">
              {selectedJob.difficulty_level}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="relative z-10">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-gradient-to-r from-gray-100 to-blue-100 p-1.5 rounded-xl shadow-inner h-14 border border-gray-200">
              <TabsTrigger 
                value="overview" 
                className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-200 rounded-lg font-semibold text-sm data-[state=active]:text-blue-700 data-[state=active]:border data-[state=active]:border-blue-200"
              >
                📋 Overview
              </TabsTrigger>
              <TabsTrigger 
                value="responsibilities" 
                className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-200 rounded-lg font-semibold text-sm data-[state=active]:text-green-700 data-[state=active]:border data-[state=active]:border-green-200"
              >
                🎯 Responsibilities
              </TabsTrigger>
              <TabsTrigger 
                value="qualifications" 
                className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-200 rounded-lg font-semibold text-sm data-[state=active]:text-purple-700 data-[state=active]:border data-[state=active]:border-purple-200"
              >
                🎓 Qualifications
              </TabsTrigger>
              <TabsTrigger 
                value="skills" 
                className="data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-200 rounded-lg font-semibold text-sm data-[state=active]:text-orange-700 data-[state=active]:border data-[state=active]:border-orange-200"
              >
                📈 Skills
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="mt-8 focus:outline-none">
              <div className="space-y-6 p-6 bg-gradient-to-br from-blue-50/50 to-transparent rounded-xl border border-blue-100">
                <p className="text-gray-700 leading-relaxed text-lg">{selectedJob.job_description.overview}</p>
                <div className="bg-white/80 p-5 rounded-lg border border-gray-200 shadow-sm">
                  <h4 className="font-bold mb-3 text-gray-800 flex items-center gap-2">
                    🏷️ Key Tags:
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedJob.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 px-3 py-1.5 font-medium hover:scale-105 transition-transform">
                        {tag.replace('_', ' ')}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="responsibilities" className="mt-8 focus:outline-none">
              <div className="p-6 bg-gradient-to-br from-green-50/50 to-transparent rounded-xl border border-green-100">
                <ul className="space-y-4">
                  {selectedJob.job_description.key_responsibilities.map((resp, index) => (
                    <li key={index} className="flex items-start gap-4 p-4 bg-white/80 rounded-lg border border-green-100 hover:border-green-200 transition-colors shadow-sm">
                      <Target className="h-6 w-6 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 leading-relaxed">{resp}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </TabsContent>
            
            <TabsContent value="qualifications" className="mt-8 focus:outline-none">
              <div className="p-6 bg-gradient-to-br from-purple-50/50 to-transparent rounded-xl border border-purple-100">
                <ul className="space-y-4">
                  {selectedJob.job_description.required_qualifications.map((qual, index) => (
                    <li key={index} className="flex items-start gap-4 p-4 bg-white/80 rounded-lg border border-purple-100 hover:border-purple-200 transition-colors shadow-sm">
                      <GraduationCap className="h-6 w-6 text-purple-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 leading-relaxed">{qual}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </TabsContent>
            
            <TabsContent value="skills" className="mt-8 focus:outline-none">
              <div className="p-6 bg-gradient-to-br from-orange-50/50 to-transparent rounded-xl border border-orange-100">
                <ul className="space-y-4">
                  {selectedJob.job_description.preferred_skills.map((skill, index) => (
                    <li key={index} className="flex items-start gap-4 p-4 bg-white/80 rounded-lg border border-orange-100 hover:border-orange-200 transition-colors shadow-sm">
                      <TrendingUp className="h-6 w-6 text-orange-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 leading-relaxed">{skill}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Interview Questions Section - Modern Card Grid */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BrainCircuit className="h-6 w-6" />
            Interview Questions
            <Badge variant="secondary" className="ml-2">
              {selectedJob.interview_questions.reduce((total, cat) => total + cat.questions.length, 0)} questions
            </Badge>
          </CardTitle>
          <CardDescription>
            Prepare for your interview with these curated questions and expert answers
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Questions Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            {selectedJob.interview_questions.flatMap(category => 
              category.questions.map((question, index) => (
                <Card 
                  key={`${category.category}-${index}`}
                  className="group hover:shadow-2xl transition-all duration-300 cursor-pointer border-2 border-gray-100 hover:border-blue-300 bg-white hover:bg-gradient-to-br hover:from-blue-50 hover:to-white transform hover:-translate-y-2 hover:scale-[1.02] min-h-[320px] flex flex-col relative overflow-hidden"
                  onClick={() => {
                    const questionWithCategory = {
                      ...question,
                      category: category.category,
                      categoryDescription: category.category_description
                    };
                    setSelectedQuestionModal(questionWithCategory);
                  }}
                >
                  {/* Subtle background pattern */}
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-50/30 to-transparent opacity-50 group-hover:opacity-100 transition-opacity" />
                  
                  <CardHeader className="pb-4 flex-shrink-0 relative z-10">
                    <div className="flex items-start justify-between mb-3">
                      <Badge 
                        variant="outline" 
                        className="text-xs bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 text-blue-800 font-semibold px-3 py-1.5 rounded-full shadow-sm"
                      >
                        {category.category}
                      </Badge>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleBookmarkQuestion(question.question_id);
                        }}
                        className="h-8 w-8 p-0 hover:bg-blue-100 transition-all duration-200 rounded-full flex-shrink-0 hover:scale-110"
                      >
                        {bookmarkedQuestions.has(question.question_id) ? 
                          <Bookmark className="h-4 w-4 fill-blue-600 text-blue-600 drop-shadow-sm" /> : 
                          <BookmarkPlus className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
                        }
                      </Button>
                    </div>
                    
                    <CardTitle className="text-base line-clamp-4 group-hover:text-blue-900 transition-colors font-bold leading-tight text-gray-800">
                      {question.question}
                    </CardTitle>
                  </CardHeader>
                  
                  <CardContent className="pt-0 space-y-4 flex-grow flex flex-col relative z-10">
                    {/* Enhanced Key Points Preview */}
                    <div className="space-y-3 flex-grow">
                      <div className="flex items-center gap-2 mb-2">
                        <Target className="h-4 w-4 text-blue-600" />
                        <p className="text-sm font-bold text-gray-800">Key Answer Points</p>
                      </div>
                      <div className="space-y-2">
                        {question.answer_points?.slice(0, 2).map((point, idx) => (
                          <div key={idx} className="flex items-start gap-3 p-3 bg-gradient-to-r from-gray-50 to-blue-50/30 rounded-lg border border-gray-100 group-hover:border-blue-200 transition-colors">
                            <span className="bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full min-w-[1.5rem] text-center shadow-sm">
                              {idx + 1}
                            </span>
                            <span className="text-sm text-gray-700 line-clamp-2 flex-1 leading-relaxed">{point}</span>
                          </div>
                        ))}
                      </div>
                      {question.answer_points?.length > 2 && (
                        <div className="flex items-center gap-2 mt-3 p-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                          <div className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                            +{question.answer_points.length - 2}
                          </div>
                          <span className="text-sm text-blue-700 font-medium">more detailed insights</span>
                          <ChevronRight className="h-4 w-4 text-blue-600" />
                        </div>
                      )}
                    </div>
                    
                    {/* Enhanced Footer with Actions */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-200 mt-auto flex-shrink-0 bg-white/80 backdrop-blur-sm rounded-lg p-3 -mx-3">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        className="text-sm font-bold bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex-shrink-0 px-4 py-2 rounded-full shadow-sm hover:shadow-md hover:scale-105"
                        onClick={(e) => {
                          e.stopPropagation();
                          const questionWithCategory = {
                            ...question,
                            category: category.category,
                            categoryDescription: category.category_description
                          };
                          setSelectedQuestionModal(questionWithCategory);
                        }}
                      >
                        View Details
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                      <div className="flex-shrink-0">
                        <ConfidenceRating 
                          questionId={question.question_id}
                          currentRating={confidenceRatings[question.question_id] || 0}
                          onRatingChange={updateConfidenceRating}
                        />
                      </div>
                    </div>
                  </CardContent>

                  {/* Subtle hover effect overlay */}
                  <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                </Card>
              ))
            )}
          </div>
          
          <div className="text-center">
            <Button 
              onClick={() => {
                setCurrentView('questions');
              }}
              className="bg-blue-600 hover:bg-blue-700"
            >
              View All Questions for This Job
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Personal Notes Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Personal Notes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Add your personal notes about this job role..."
            value={userNotes[selectedJob.id] || ''}
            onChange={(e) => updateUserNote(selectedJob.id, e.target.value)}
            className="min-h-[100px]"
          />
        </CardContent>
      </Card>

      {/* Question Detail Modal */}
      <QuestionModal 
        question={selectedQuestionModal}
        isOpen={!!selectedQuestionModal}
        onClose={() => setSelectedQuestionModal(null)}
        bookmarkedQuestions={bookmarkedQuestions}
        toggleBookmarkQuestion={toggleBookmarkQuestion}
        userNotes={userNotes}
        updateUserNote={updateUserNote}
        confidenceRatings={confidenceRatings}
        updateConfidenceRating={updateConfidenceRating}
      />
    </div>
  );
};