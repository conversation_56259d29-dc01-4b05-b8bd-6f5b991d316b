import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, ArrowLeft, BookmarkPlus, Bookmark } from 'lucide-react';
import { type Job } from '../../data';

interface JobsViewProps {
  jobs: Job[];
  specializations: any[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filters: any;
  setFilters: (filters: any) => void;
  bookmarkedJobs: Set<string>;
  setBookmarkedJobs: (bookmarks: Set<string>) => void;
  setCurrentView: (view: string) => void;
  setSelectedJob: (job: Job) => void;
}

export const JobsView: React.FC<JobsViewProps> = ({
  jobs,
  specializations,
  searchTerm,
  setSearchTerm,
  filters,
  setFilters,
  bookmarkedJobs,
  setBookmarkedJobs,
  setCurrentView,
  setSelectedJob
}) => {
  const toggleBookmarkJob = (jobId: string) => {
    const newBookmarks = new Set(bookmarkedJobs);
    if (newBookmarks.has(jobId)) {
      newBookmarks.delete(jobId);
    } else {
      newBookmarks.add(jobId);
    }
    setBookmarkedJobs(newBookmarks);
  };

  const filteredJobs = jobs.filter(job => {
    const matchesSearch = !searchTerm || 
      job.job_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.job_description.overview.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesSpecialization = !filters.specialization || job.specialization === filters.specialization;
    const matchesIndustry = !filters.industry || job.industry.includes(filters.industry);
    const matchesDifficulty = !filters.difficulty || job.difficulty_level === filters.difficulty;
    
    return matchesSearch && matchesSpecialization && matchesIndustry && matchesDifficulty;
  });
  console.log("57:",filteredJobs);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Job Explorer</h1>
        <Button variant="outline" onClick={() => setCurrentView('home')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Home
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search jobs, descriptions, or skills..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={filters.specialization} onValueChange={(value) => 
                setFilters(prev => ({ ...prev, specialization: value === "all" ? "" : value }))}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Specialization" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Specializations</SelectItem>
                  {specializations.map(spec => (
                    <SelectItem key={spec.id} value={spec.name}>{spec.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={filters.difficulty} onValueChange={(value) => 
                setFilters(prev => ({ ...prev, difficulty: value === "all" ? "" : value }))}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Difficulty" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="Entry">Entry</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="Advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Jobs List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredJobs.map((job) => (
          <Card key={job.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => {
                  setSelectedJob(job);
                  setCurrentView('job-detail');
                }}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {job.job_title}
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleBookmarkJob(job.id);
                  }}
                >
                  {bookmarkedJobs.has(job.id) ? 
                    <Bookmark className="h-4 w-4 fill-current" /> : 
                    <BookmarkPlus className="h-4 w-4" />
                  }
                </Button>
              </CardTitle>
              <CardDescription>{job.industry}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2 mb-3">
                <Badge variant="outline">{job.specialization}</Badge>
                <Badge variant="outline">{job.difficulty_level}</Badge>
                <Badge variant="outline">★ {job.popularity_score}/10</Badge>
              </div>
              <p className="text-sm text-gray-600 mb-4 line-clamp-3">{job.job_description.overview}</p>
              
              <div className="flex flex-wrap gap-1 mb-3">
                {job.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag.replace('_', ' ')}
                  </Badge>
                ))}
                {job.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs">+{job.tags.length - 3} more</Badge>
                )}
              </div>
              
              <div className="text-xs text-gray-500">
                {job.interview_questions.reduce((total, category) => total + category.questions.length, 0)} interview questions available
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};