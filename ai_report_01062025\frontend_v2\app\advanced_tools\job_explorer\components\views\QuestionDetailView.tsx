import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft,
  BookmarkPlus,
  Bookmark,
  Target,
  ChevronRight
} from 'lucide-react';
import { ConfidenceRating } from '../shared';

interface QuestionDetailViewProps {
  selectedQuestion: any;
  bookmarkedQuestions: Set<string>;
  setBookmarkedQuestions: (bookmarks: Set<string>) => void;
  userNotes: Record<string, string>;
  setUserNotes: (notes: Record<string, string>) => void;
  confidenceRatings: Record<string, number>;
  setConfidenceRatings: (ratings: Record<string, number>) => void;
  setCurrentView: (view: string) => void;
}

export const QuestionDetailView: React.FC<QuestionDetailViewProps> = ({
  selectedQuestion,
  bookmarkedQuestions,
  setBookmarkedQuestions,
  userNotes,
  setUserNotes,
  confidenceRatings,
  setConfidenceRatings,
  setCurrentView
}) => {
  if (!selectedQuestion) return null;

  const toggleBookmarkQuestion = (questionId: string) => {
    const newBookmarks = new Set(bookmarkedQuestions);
    if (newBookmarks.has(questionId)) {
      newBookmarks.delete(questionId);
    } else {
      newBookmarks.add(questionId);
    }
    setBookmarkedQuestions(newBookmarks);
  };

  const updateUserNote = (id: string, note: string) => {
    setUserNotes(prev => ({ ...prev, [id]: note }));
  };

  const updateConfidenceRating = (questionId: string, rating: number) => {
    setConfidenceRatings(prev => ({ ...prev, [questionId]: rating }));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => setCurrentView('questions')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Questions
        </Button>
        <Button 
          variant="outline"
          onClick={() => toggleBookmarkQuestion(selectedQuestion.question_id)}
        >
          {bookmarkedQuestions.has(selectedQuestion.question_id) ? 
            <Bookmark className="h-4 w-4 mr-2 fill-current" /> : 
            <BookmarkPlus className="h-4 w-4 mr-2" />
          }
          {bookmarkedQuestions.has(selectedQuestion.question_id) ? 'Bookmarked' : 'Bookmark'}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">{selectedQuestion.question}</CardTitle>
          <div className="flex flex-wrap gap-2 mt-2">
            <Badge variant="outline">{selectedQuestion.category}</Badge>
            <Badge variant={selectedQuestion.difficulty_level === 'Easy' ? 'secondary' : 
                         selectedQuestion.difficulty_level === 'Medium' ? 'default' : 'destructive'}>
              {selectedQuestion.difficulty_level}
            </Badge>
            <Badge variant={selectedQuestion.importance === 'High' ? 'destructive' : 'secondary'}>
              {selectedQuestion.importance}
            </Badge>
          </div>
          <CardDescription className="text-base mt-3">
            <strong>Interviewer Intent:</strong> {selectedQuestion.interviewer_intent}
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="answer-points" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="answer-points">Answer Points</TabsTrigger>
          <TabsTrigger value="examples">Example Answers</TabsTrigger>
          <TabsTrigger value="tips">Preparation Tips</TabsTrigger>
          <TabsTrigger value="follow-up">Follow-up</TabsTrigger>
        </TabsList>
        
        <TabsContent value="answer-points" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Key Answer Points</CardTitle>
              <CardDescription>Essential components of a strong answer</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {selectedQuestion.answer_points.map((point, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full mt-0.5">
                      {index + 1}
                    </span>
                    <span className="text-gray-700">{point}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="examples" className="mt-6">
          <div className="space-y-4">
            {selectedQuestion.example_answers && Object.entries(selectedQuestion.example_answers).map(([level, answer]: [string, any]) => (
              <Card key={level} className={`${
                level === 'excellent' ? 'border-green-200 bg-green-50' :
                level === 'good' ? 'border-yellow-200 bg-yellow-50' :
                'border-red-200 bg-red-50'
              }`}>
                <CardHeader>
                  <CardTitle className={`text-lg ${
                    level === 'excellent' ? 'text-green-800' :
                    level === 'good' ? 'text-yellow-800' :
                    'text-red-800'
                  }`}>
                    {answer.level} Answer
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-3 leading-relaxed">{answer.answer}</p>
                  <div className={`text-sm p-3 rounded-lg ${
                    level === 'excellent' ? 'bg-green-100 text-green-800' :
                    level === 'good' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    <strong>Why {level}:</strong> {answer[`why_${level}`] || answer.why_excellent || answer.why_good || answer.why_poor}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="tips" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Preparation Tips</CardTitle>
              <CardDescription>How to prepare for this type of question</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {selectedQuestion.preparation_tips && selectedQuestion.preparation_tips.map((tip, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <Target className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{tip}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="follow-up" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Possible Follow-up Questions</CardTitle>
              <CardDescription>Be prepared for these additional questions</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {selectedQuestion.follow_up_questions_possible && selectedQuestion.follow_up_questions_possible.map((question, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <ChevronRight className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{question}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Self Assessment */}
      <Card>
        <CardHeader>
          <CardTitle>Self Assessment</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Rate your confidence on this question:</label>
            <ConfidenceRating 
              questionId={selectedQuestion.question_id}
              currentRating={confidenceRatings[selectedQuestion.question_id] || 0}
              onRatingChange={updateConfidenceRating}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Personal Notes:</label>
            <Textarea
              placeholder="Add your notes, practice answers, or key insights..."
              value={userNotes[selectedQuestion.question_id] || ''}
              onChange={(e) => updateUserNote(selectedQuestion.question_id, e.target.value)}
              className="min-h-[100px]"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};