import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  ArrowLeft,
  Filter,
  BookmarkPlus,
  Bookmark,
  Award,
  Clock,
  BrainCircuit,
  X,
  Target,
  ChevronRight
} from 'lucide-react';
import { type Job } from '../../data';
import { ConfidenceRating } from '../shared';
import { QuestionModal } from '../modals/QuestionModal';

interface QuestionsViewProps {
  jobs: Job[];
  selectedJob: Job | null;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  questionFilters: any;
  setQuestionFilters: (filters: any) => void;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  selectedQuestionModal: any;
  setSelectedQuestionModal: (question: any) => void;
  bookmarkedQuestions: Set<string>;
  setBookmarkedQuestions: (bookmarks: Set<string>) => void;
  userNotes: Record<string, string>;
  setUserNotes: (notes: Record<string, string>) => void;
  confidenceRatings: Record<string, number>;
  setConfidenceRatings: (ratings: Record<string, number>) => void;
  setCurrentView: (view: string) => void;
}

export const QuestionsView: React.FC<QuestionsViewProps> = ({
  jobs,
  selectedJob,
  searchTerm,
  setSearchTerm,
  questionFilters,
  setQuestionFilters,
  showFilters,
  setShowFilters,
  selectedQuestionModal,
  setSelectedQuestionModal,
  bookmarkedQuestions,
  setBookmarkedQuestions,
  userNotes,
  setUserNotes,
  confidenceRatings,
  setConfidenceRatings,
  setCurrentView
}) => {
  const toggleBookmarkQuestion = (questionId: string) => {
    const newBookmarks = new Set(bookmarkedQuestions);
    if (newBookmarks.has(questionId)) {
      newBookmarks.delete(questionId);
    } else {
      newBookmarks.add(questionId);
    }
    setBookmarkedQuestions(newBookmarks);
  };

  const updateUserNote = (id: string, note: string) => {
    setUserNotes(prev => ({ ...prev, [id]: note }));
  };

  const updateConfidenceRating = (questionId: string, rating: number) => {
    setConfidenceRatings(prev => ({ ...prev, [questionId]: rating }));
  };

  // Enhanced question filtering
  const getFilteredQuestions = () => {
    const allQuestions = selectedJob ? 
      selectedJob.interview_questions.flatMap(category => 
        category.questions.map(q => ({ ...q, category: category.category, categoryDescription: category.category_description }))
      ) :
      jobs.flatMap(job => 
        job.interview_questions.flatMap(category => 
          category.questions.map(q => ({ ...q, category: category.category, jobTitle: job.job_title, categoryDescription: category.category_description }))
        )
      );

    return allQuestions.filter(question => {
      const matchesSearch = !searchTerm || 
        question.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        question.answer_points?.some(point => point.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = !questionFilters.category || question.category === questionFilters.category;
      const matchesDifficulty = !questionFilters.difficulty || question.difficulty_level === questionFilters.difficulty;
      const matchesImportance = !questionFilters.importance || question.importance === questionFilters.importance;
      const matchesFrequency = !questionFilters.frequency || question.frequency === questionFilters.frequency;
      const matchesSkills = !questionFilters.skills || 
        (question.related_skills && question.related_skills.some(skill => 
          skill.toLowerCase().includes(questionFilters.skills.toLowerCase())
        ));
      
      return matchesSearch && matchesCategory && matchesDifficulty && matchesImportance && matchesFrequency && matchesSkills;
    });
  };

  // Get unique values for filter options
  const getFilterOptions = () => {
    const allQuestions = jobs.flatMap(job => 
      job.interview_questions.flatMap(category => 
        category.questions.map(q => ({ ...q, category: category.category }))
      )
    );

    return {
      categories: [...new Set(allQuestions.map(q => q.category))],
      difficulties: [...new Set(allQuestions.map(q => q.difficulty_level))],
      importances: [...new Set(allQuestions.map(q => q.importance))],
      frequencies: [...new Set(allQuestions.map(q => q.frequency).filter(Boolean))],
      skills: [...new Set(allQuestions.flatMap(q => q.related_skills || []))]
    };
  };

  const filteredQuestions = getFilteredQuestions();
  const filterOptions = getFilterOptions();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">
          {selectedJob ? `Questions for ${selectedJob.job_title}` : 'All Interview Questions'}
        </h1>
        <Button variant="outline" onClick={() => setCurrentView(selectedJob ? 'job-detail' : 'home')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      </div>

      {/* Advanced Search and Filter Bar */}
      <Card className="border-2 border-gray-100 shadow-sm">
        <CardContent className="p-6">
          <div className="flex flex-col space-y-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <Input
                    placeholder="Search questions, answers, concepts, or skills..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 h-12 border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>
              </div>
              <Button 
                variant="outline" 
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center gap-2 h-12 px-6 transition-all duration-200 ${
                  showFilters ? 'bg-blue-50 border-blue-200 text-blue-700' : ''
                }`}
              >
                <Filter className="h-4 w-4" />
                Advanced Filters
                {Object.values(questionFilters).some(v => v) && (
                  <Badge variant="destructive" className="ml-1 h-5 w-5 p-0 text-xs animate-pulse">
                    {Object.values(questionFilters).filter(v => v).length}
                  </Badge>
                )}
              </Button>
            </div>

            {/* Advanced Filters Panel with Animation */}
            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl border border-gray-200 animate-in slide-in-from-top-2 duration-200">
                <Select value={questionFilters.category} onValueChange={(value) => 
                  setQuestionFilters(prev => ({ ...prev, category: value === "all" ? "" : value }))}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {filterOptions.categories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={questionFilters.difficulty} onValueChange={(value) => 
                  setQuestionFilters(prev => ({ ...prev, difficulty: value === "all" ? "" : value }))}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    {filterOptions.difficulties.map(diff => (
                      <SelectItem key={diff} value={diff}>{diff}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={questionFilters.importance} onValueChange={(value) => 
                  setQuestionFilters(prev => ({ ...prev, importance: value === "all" ? "" : value }))}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Importance" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Importance</SelectItem>
                    {filterOptions.importances.map(imp => (
                      <SelectItem key={imp} value={imp}>{imp}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={questionFilters.frequency} onValueChange={(value) => 
                  setQuestionFilters(prev => ({ ...prev, frequency: value === "all" ? "" : value }))}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Frequency</SelectItem>
                    {filterOptions.frequencies.map(freq => (
                      <SelectItem key={freq} value={freq}>{freq}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={questionFilters.skills} onValueChange={(value) => 
                  setQuestionFilters(prev => ({ ...prev, skills: value === "all" ? "" : value }))}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Skills" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Skills</SelectItem>
                    {filterOptions.skills.slice(0, 10).map(skill => (
                      <SelectItem key={skill} value={skill}>{skill}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button 
                  variant="outline" 
                  onClick={() => setQuestionFilters({
                    category: '', difficulty: '', importance: '', frequency: '', skills: ''
                  })}
                  className="flex items-center gap-2 h-10 bg-white hover:bg-red-50 hover:border-red-200 hover:text-red-600 transition-colors"
                >
                  <X className="h-4 w-4" />
                  Clear All
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Results Summary */}
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100">
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-2">
            <BrainCircuit className="h-5 w-5 text-blue-600" />
            <span className="font-semibold text-blue-900">
              {filteredQuestions.length} Questions Found
            </span>
          </div>
          {selectedJob && (
            <Badge variant="outline" className="bg-white">
              From {selectedJob.job_title}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Bookmark className="h-4 w-4 text-blue-600" />
            <span className="text-blue-700 font-medium">{bookmarkedQuestions.size} bookmarked</span>
          </div>
          <div className="flex items-center gap-2">
            <Award className="h-4 w-4 text-green-600" />
            <span className="text-green-700 font-medium">{Object.keys(confidenceRatings).length} rated</span>
          </div>
        </div>
      </div>

      {/* Modern Pinterest-Style Card Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredQuestions.map((question, index) => (
          <Card 
            key={`${question.question_id}-${index}`} 
            className="group hover:shadow-2xl transition-all duration-300 cursor-pointer border-2 border-gray-100 hover:border-blue-300 bg-white hover:bg-gradient-to-br hover:from-blue-50 hover:to-white transform hover:-translate-y-2 hover:scale-[1.02] min-h-[320px] flex flex-col relative overflow-hidden"
            onClick={() => setSelectedQuestionModal(question)}
          >
            {/* Subtle background pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-gray-50/30 to-transparent opacity-50 group-hover:opacity-100 transition-opacity" />
            
            <CardHeader className="pb-4 flex-shrink-0 relative z-10">
              <div className="flex items-start justify-between mb-3">
                <Badge 
                  variant="outline" 
                  className="text-xs bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 text-blue-800 font-semibold px-3 py-1.5 rounded-full shadow-sm"
                >
                  {question.category}
                </Badge>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleBookmarkQuestion(question.question_id);
                  }}
                  className="h-8 w-8 p-0 hover:bg-blue-100 transition-all duration-200 rounded-full flex-shrink-0 hover:scale-110"
                >
                  {bookmarkedQuestions.has(question.question_id) ? 
                    <Bookmark className="h-4 w-4 fill-blue-600 text-blue-600 drop-shadow-sm" /> : 
                    <BookmarkPlus className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
                  }
                </Button>
              </div>
              
              <CardTitle className="text-base line-clamp-4 group-hover:text-blue-900 transition-colors font-bold leading-tight text-gray-800">
                {question.question}
              </CardTitle>
            </CardHeader>
            
            <CardContent className="pt-0 space-y-4 flex-grow flex flex-col relative z-10">
              {/* Job Title for Cross-Job View */}
              {question.jobTitle && (
                <Badge variant="outline" className="text-xs bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 text-purple-800 font-medium w-fit px-3 py-1 rounded-full">
                  📋 {question.jobTitle}
                </Badge>
              )}

              {/* Enhanced Key Points Preview */}
              <div className="space-y-3 flex-grow">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  <p className="text-sm font-bold text-gray-800">Key Answer Points</p>
                </div>
                <div className="space-y-2">
                  {question.answer_points?.slice(0, 2).map((point, idx) => (
                    <div key={idx} className="flex items-start gap-3 p-3 bg-gradient-to-r from-gray-50 to-blue-50/30 rounded-lg border border-gray-100 group-hover:border-blue-200 transition-colors">
                      <span className="bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full min-w-[1.5rem] text-center shadow-sm">
                        {idx + 1}
                      </span>
                      <span className="text-sm text-gray-700 line-clamp-2 flex-1 leading-relaxed">{point}</span>
                    </div>
                  ))}
                </div>
                {question.answer_points?.length > 2 && (
                  <div className="flex items-center gap-2 mt-3 p-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                    <div className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                      +{question.answer_points.length - 2}
                    </div>
                    <span className="text-sm text-blue-700 font-medium">more detailed insights</span>
                    <ChevronRight className="h-4 w-4 text-blue-600" />
                  </div>
                )}
              </div>
              
              {/* Enhanced Footer with Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 mt-auto flex-shrink-0 bg-white/80 backdrop-blur-sm rounded-lg p-3 -mx-3">
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="text-sm font-bold bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex-shrink-0 px-4 py-2 rounded-full shadow-sm hover:shadow-md hover:scale-105"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedQuestionModal(question);
                  }}
                >
                  View Details
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
                <div className="flex-shrink-0">
                  <ConfidenceRating 
                    questionId={question.question_id}
                    currentRating={confidenceRatings[question.question_id] || 0}
                    onRatingChange={updateConfidenceRating}
                  />
                </div>
              </div>
            </CardContent>

            {/* Subtle hover effect overlay */}
            <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
          </Card>
        ))}
      </div>

      {/* Enhanced Empty State */}
      {filteredQuestions.length === 0 && (
        <Card className="p-12 text-center border-2 border-dashed border-gray-200">
          <div className="flex flex-col items-center space-y-4">
            <div className="p-4 bg-gray-100 rounded-full">
              <BrainCircuit className="h-12 w-12 text-gray-400" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No questions found</h3>
              <p className="text-gray-500 max-w-md">
                We couldn't find any questions matching your current filters. 
                Try adjusting your search terms or clearing some filters to discover more questions.
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button 
                variant="outline"
                onClick={() => {
                  setSearchTerm('');
                  setQuestionFilters({
                    category: '', difficulty: '', importance: '', frequency: '', skills: ''
                  });
                }}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Clear all filters
              </Button>
              <Button 
                onClick={() => setCurrentView('home')}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Explore All Content
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Enhanced Question Detail Modal */}
      <QuestionModal 
        question={selectedQuestionModal}
        isOpen={!!selectedQuestionModal}
        onClose={() => setSelectedQuestionModal(null)}
        bookmarkedQuestions={bookmarkedQuestions}
        toggleBookmarkQuestion={toggleBookmarkQuestion}
        userNotes={userNotes}
        updateUserNote={updateUserNote}
        confidenceRatings={confidenceRatings}
        updateConfidenceRating={updateConfidenceRating}
      />
    </div>
  );
};