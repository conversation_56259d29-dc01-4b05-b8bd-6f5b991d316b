
export const portfolioRiskAnalystCAPM = {
  "id": "MBA_INV_011",
  "job_title": "Portfolio Risk Analyst – CAPM & Asset Pricing",
  "specialization": "Finance",
  "specialization_id": "FIN",
  "industry": "Asset Management / Investment Banking / Consulting",
  "company_type": "Asset Manager / Hedge Fund / Investment Bank",
  "difficulty_level": "Entry",
  "popularity_score": 7,
  "tags": ["CAPM", "beta_estimation", "portfolio_optimization", "asset_pricing", "systematic_risk"],
  "job_description": {
    "overview": "Entry-level analyst role focused on applying Modern Portfolio Theory and the Capital Asset Pricing Model (CAPM) to support portfolio construction, performance attribution, and risk budgeting. You will estimate expected returns, variances, covariances, and betas, and translate these inputs into actionable insights for portfolio managers.",
    "key_responsibilities": [
      "Collect price, factor, and fundamental data; calculate daily, weekly, and monthly security returns",
      "Estimate variance–covariance matrices, systematic/unsystematic risk splits, and security betas using regression, shrinkage, and rolling-window techniques",
      "Maintain and calibrate CAPM and multi-factor models; update expected-return inputs for strategic and tactical asset-allocation processes",
      "Generate efficient frontiers, capital-allocation lines, and optimal risky portfolios for client mandates using solver-based optimization",
      "Monitor portfolio ex-ante and ex-post tracking error, active beta drift, and factor exposures; propose trades to maintain target risk budgets",
      "Assist in quarterly risk reviews, drafting commentary that links performance to CAPM factors, macro news, and idiosyncratic events",
      "Support cost-of-equity estimates for corporate-finance and valuation engagements (beta un-levering/re-levering, country-risk adjustments)",
      "Automate recurring analytics in Python/R and visualize outputs in PowerBI/Tableau dashboards",
      "Ensure model governance and documentation comply with internal risk-model validation standards and regulators’ expectations"
    ],
    "required_qualifications": [
      "MBA or Master’s in Finance/Economics/Statistics with coursework in investments and risk management",
      "Strong grasp of expected return, variance, covariance, diversification, systematic vs. unsystematic risk, beta, and CAPM equilibrium mechanics",
      "Proficiency in Excel (matrix operations, Solver); working knowledge of Python or R for statistical analysis",
      "Ability to interpret regression diagnostics and articulate model limitations to non-quant stakeholders",
      "Excellent analytical writing and presentation skills"
    ],
    "preferred_skills": [
      "Passed or pursuing CFA Level I/II",
      "Experience with Barra, Axioma, or Bloomberg PORT",
      "Familiarity with Fama–French–Carhart multi-factor models and downside-risk metrics",
      "Understanding of stress-testing frameworks and scenario analysis",
      "Basic SQL for data extraction; REST API usage"
    ]
  },
  "interview_questions": [
    /* ------------------------------------------------------------------ */
    {
      "category": "CAPM & Core Asset-Pricing Concepts",
      "category_description": "Fundamental theories linking risk and return for individual securities and portfolios",
      "questions": [
        /* -------------- INV_CAPM_001 ---------------------------------- */
        {
          "question_id": "INV_CAPM_001",
          "question": "Derive the CAPM expected-return equation from the assumptions of mean-variance efficiency and market equilibrium. Which risk is priced and why?",
          "difficulty_level": "Medium",
          "importance": "High",
          "frequency": "Very Common",
          "interviewer_intent": "Test conceptual mastery of CAPM foundations and the distinction between systematic vs. diversifiable risk",
          "prerequisite_knowledge": "Efficient frontier, two-fund separation, market portfolio, beta definition, risk-free borrowing/lending",
          "related_skills": ["Theoretical Reasoning", "Quantitative Finance"],
          "answer_points": [
            "Under mean-variance efficiency, all investors combine the risk-free asset with the tangent (market) portfolio.",
            "An asset’s marginal contribution to portfolio variance equals βᵢ σ_M, not its total volatility.",
            "In equilibrium prices adjust until excess return per unit of β is the same for all assets ⇒ E[Rᵢ] = R_f + βᵢ(E[R_M] − R_f).",
            "Only systematic (market) risk is priced because idiosyncratic risk can be diversified away at no cost."
          ],
          "example_answers": {
            "excellent": {
              "level": "Excellent",
              "answer": "Start with every investor holding combinations of the risk-free asset and the unique tangent portfolio (the market). The equilibrium risk premium on any asset must be proportional to how much it contributes to the variance of that market portfolio, which is βᵢ σ_M. Setting the reward-to-risk ratio equal across assets yields E[Rᵢ] = R_f + βᵢ(E[R_M] − R_f). Thus, only market (systematic) risk earns a premium, while diversifiable risk is ignored.",
              "why_excellent": "Links assumptions → derivation → formula; clearly states why only systematic risk matters."
            },
            "good": {
              "level": "Good",
              "answer": "Because everyone holds the market portfolio, the expected excess return of a security must depend on how it moves with the market, measured by beta. The CAPM formula is E[R]=R_f+β(E[R_M]−R_f). Idiosyncratic risk is not priced.",
              "why_good": "States key result and reasoning, but omits step-by-step derivation."
            },
            "not_so_good": {
              "level": "Not So Good",
              "answer": "CAPM just says higher risk gives higher return: R = Rf + beta × premium.",
              "why_poor": "No derivation, no mention of diversification or systematic risk."
            }
          },
          "follow_up_questions_possible": [
            "How does the CAPM change if investors cannot borrow at the risk-free rate?",
            "What empirical evidence challenges the CAPM’s predictions?"
          ],
          "preparation_tips": [
            "Re-derive CAPM from first principles and practice drawing the CML vs. SML.",
            "Study empirical tests (e.g., size/value anomalies) to understand limitations."
          ],
          "common_mistakes": [
            "Confusing beta with total volatility or correlation.",
            "Ignoring the role of the risk-free asset in the derivation."
          ]
        },
        /* -------------- INV_CAPM_002 ---------------------------------- */
        {
          "question_id": "INV_CAPM_002",
          "question": "When you regress a stock’s excess returns on the market’s excess returns, the R² is only 0.15. What does this tell you, and how would you improve the model’s explanatory power?",
          "difficulty_level": "Medium",
          "importance": "Medium",
          "frequency": "Common",
          "interviewer_intent": "Probe practical beta-estimation issues and multifactor extensions",
          "prerequisite_knowledge": "OLS regression, beta estimation error, factor models (Fama-French, momentum)",
          "related_skills": ["Statistical Analysis", "Critical Thinking"],
          "answer_points": [
            "R² = 0.15 → market factor explains just 15 % of return variance; 85 % is idiosyncratic or due to omitted factors.",
            "Low explanatory power may stem from short sample, high event risk, or the stock belonging to a different risk factor (e.g., small-cap, value, momentum).",
            "Improvements: lengthen estimation window, use higher-frequency data, apply regression shrinkage (Vasicek/Blume), or migrate to multifactor models (size, value, momentum) to capture additional systematic sources."
          ],
          "example_answers": {
            "excellent": {
              "level": "Excellent",
              "answer": "A 0.15 R² means CAPM leaves 85 % of the variance unexplained. That could be genuine firm-specific news or missing risk factors. I’d first assess estimation error by extending to at least five years of monthly data or two years of daily data, then apply a Blume shrinkage to stabilize beta. If low R² persists, I’d run a Fama-French-Carhart regression to incorporate size, value, and momentum factors – these typically raise R² for small or growth firms. Finally I’d evaluate whether the stock is driven by earnings-news events that no factor model captures.",
              "why_excellent": "Interprets statistic, names concrete remedies, shows awareness of empirical practice."
            },
            "good": {
              "level": "Good",
              "answer": "It shows the market alone doesn’t explain much. I’d take more data and maybe add factors like size or value to the regression.",
              "why_good": "Gets the gist but light on diagnostic detail."
            },
            "not_so_good": {
              "level": "Not So Good",
              "answer": "The model is wrong; we need a different one.",
              "why_poor": "Dismissive, no analysis or specific improvements."
            }
          },
          "follow_up_questions_possible": [
            "What are the pros and cons of using daily vs. monthly returns for beta estimation?",
            "Explain and calculate a Vasicek-adjusted beta."
          ],
          "preparation_tips": [
            "Practice running single-factor and multifactor regressions in Excel/Python.",
            "Understand shrinkage techniques and how sampling frequency affects beta noise."
          ],
          "common_mistakes": [
            "Equating low R² with ‘bad’ beta – betas can still be significant.",
            "Failing to test stability of beta across sub-samples."
          ]
        }
      ]
    },
    /* ------------------------------------------------------------------ */
    {
      "category": "Portfolio Theory & Diversification",
      "category_description": "Applying variance–covariance math to construct efficient portfolios and understand risk reduction",
      "questions": [
        /* -------------- INV_PORT_001 ---------------------------------- */
        {
          "question_id": "INV_PORT_001",
          "question": "Why is the minimum-variance frontier typically concave, and how does adding a risk-free asset transform it into a straight capital-market line (CML)?",
          "difficulty_level": "Easy",
          "importance": "Medium",
          "frequency": "Very Common",
          "interviewer_intent": "Assess intuitive grasp of efficient-set geometry and diversification benefits",
          "prerequisite_knowledge": "Variance, covariance, diversification, risk-free borrowing/lending",
          "related_skills": ["Visualization", "Conceptual Reasoning"],
          "answer_points": [
            "Diversification yields diminishing marginal variance reduction, producing a concave (bowed-out) frontier of risky portfolios.",
            "Introducing a risk-free asset allows linear combinations between R_f and any risky portfolio.",
            "The tangent from R_f to the frontier (market portfolio) dominates all interior points → straight CML with highest Sharpe ratio."
          ],
          "example_answers": {
            "excellent": {
              "level": "Excellent",
              "answer": "With only risky assets, the trade-off between σ and expected return is curved because adding a new asset improves risk less and less (diminishing covariance benefits). Once you allow borrowing/lending at R_f, any line from R_f to a risky point is feasible; the line tangent to the frontier (touching at the market) offers the best Sharpe ratio, so investors choose points on this straight Capital-Market Line.",
              "why_excellent": "Explains both curvature source and the linear transformation mechanism."
            },
            "good": {
              "level": "Good",
              "answer": "Risky portfolios form a curved frontier. Adding the risk-free asset lets you draw a straight line from R_f; the tangent is the best risk-return trade-off.",
              "why_good": "Correct but less detail on diversification mechanics."
            },
            "not_so_good": {
              "level": "Not So Good",
              "answer": "Because of math it becomes a line with risk-free asset.",
              "why_poor": "No explanation of diversification or geometry."
            }
          },
          "follow_up_questions_possible": [
            "What happens to the CML if the borrowing rate exceeds the lending rate?",
            "How would short-selling constraints change the efficient frontier shape?"
          ],
          "preparation_tips": [
            "Plot sample frontiers in Excel/Python using real return series.",
            "Experiment with adding R_f to visualize the CML."
          ],
          "common_mistakes": [
            "Confusing the CML (risk-free + market) with the SML (beta-expected return graph).",
            "Forgetting that the frontier curvature arises from covariance, not individual volatilities."
          ]
        }
      ]
    },
    /* ------------------------------------------------------------------ */
    {
      "category": "Role-Specific Scenarios",
      "category_description": "Practical problems mirroring on-the-desk tasks faced by a portfolio-risk analyst",
      "questions": [
        /* -------------- INV_SCEN_001 ---------------------------------- */
        {
          "question_id": "INV_SCEN_001",
          "question": "Your PM wants to tilt a $500 million equity fund by +0.2 beta without altering the cash position. Propose a futures overlay using S&P 500 e-mini contracts (notional value ≈ $200 000 each) and justify your sizing.",
          "difficulty_level": "Hard",
          "importance": "High",
          "frequency": "Common",
          "interviewer_intent": "Test ability to translate beta targets into trade implementation using derivatives",
          "prerequisite_knowledge": "Beta concept, futures overlay math, notional calculations, rounding to contract size",
          "related_skills": ["Implementation Skill", "Numeracy", "Derivative Knowledge"],
          "answer_points": [
            "Target beta change: Δβ = +0.2 on $500 M → desired market exposure change = $500 M × 0.2 = $100 M notional.",
            "Each e-mini ≈ $200 k notional → contracts needed ≈ $100 M / $0.2 M = 500 contracts.",
            "Go long 500 e-mini contracts; monitor basis risk and daily variation margin.",
            "Explain liquidity, margin requirements (~5 % notional) and rebalancing for beta drift."
          ],
          "example_answers": {
            "excellent": {
              "level": "Excellent",
              "answer": "We need an extra $100 million of market exposure (0.2 × $500 M). Each CME S&P 500 e-mini contract carries roughly $200 000 notional (index level × $50).  $100 M / $0.2 M ≈ 500 contracts. Therefore I’d go long 500 e-mini futures, review hedge ratios daily, and top-slice if the fund’s equity beta or index level moves materially. Initial margin is roughly 5–6 % so <$3 M cash is required, leaving the portfolio’s cash allocation unchanged.",
              "why_excellent": "Shows full calculation, rounding logic, and practical execution notes."
            },
            "good": {
              "level": "Good",
              "answer": "Add about $100 million of futures exposure, which is roughly 500 e-mini contracts, to raise beta by 0.2.",
              "why_good": "Gets sizing right but lacks discussion of margin or monitoring."
            },
            "not_so_good": {
              "level": "Not So Good",
              "answer": "Just buy some S&P futures to increase beta.",
              "why_poor": "No numbers or justification."
            }
          },
          "follow_up_questions_possible": [
            "How would you adjust if index level rises 10 % after execution?",
            "Discuss tracking-error sources between futures overlay and cash equities."
          ],
          "preparation_tips": [
            "Practice beta overlay math for different contract notionals.",
            "Understand exchange margin schedules and basis behavior."
          ],
          "common_mistakes": [
            "Using contract price instead of notional value (index × multiplier).",
            "Forgetting to consider maintenance margin and liquidity constraints."
          ]
        }
      ]
    },
    /* ------------------------------------------------------------------ */
    {
      "category": "Excel & Programming Skills",
      "category_description": "Hands-on questions to verify practical modeling and automation proficiency",
      "questions": [
        /* -------------- INV_TECH_001 ---------------------------------- */
        {
          "question_id": "INV_TECH_001",
          "question": "Describe how you would build an Excel template that ingests daily return data, computes a rolling 60-day beta, and flags beta drift beyond ±0.10 using conditional formatting.",
          "difficulty_level": "Medium",
          "importance": "Medium",
          "frequency": "Common",
          "interviewer_intent": "Gauge spreadsheet automation skills and understanding of model drivers",
          "prerequisite_knowledge": "Excel functions (LINEST, SLOPE, OFFSET, INDEX), dynamic named ranges, conditional formatting rules",
          "related_skills": ["Excel Proficiency", "Financial Modeling", "Automation"],
          "answer_points": [
            "Import price series with PowerQuery or direct CSV link; compute daily % returns.",
            "Use dynamic named range or OFFSET to capture last 60 observations for both stock and market returns.",
            "Apply SLOPE or LINEST to calculate β_t for each date; fill down to create a beta time series.",
            "Set conditional-format rule: if |β_t − β_avg| > 0.10, highlight cell red.",
            "Optional: create a Sparkline chart for visual drift monitoring."
          ],
          "example_answers": {
            "excellent": {
              "level": "Excellent",
              "answer": "Load prices via PowerQuery; add a column for daily returns =LN(P_t/P_{t-1}). Define dynamic ranges StockRet60 and MktRet60 with OFFSET($B2,0,0,60,1). In C61 enter =SLOPE(StockRet60,MktRet60) and copy downward — this produces a rolling 60-day beta. Add a conditional format to the beta column: Formula =ABS(C61 − AVERAGE($C$61:$C$500))>0.10, fill colour red. Finally add a sparkline in the header for quick visual insight.",
              "why_excellent": "Details every step, cites exact functions, includes visualization idea."
            },
            "good": {
              "level": "Good",
              "answer": "Calculate 60-day betas with SLOPE or LINEST on rolling windows, then use conditional formatting to flag when the beta moves ±0.1 from average.",
              "why_good": "Conceptually correct but lacks step-by-step implementation specifics."
            },
            "not_so_good": {
              "level": "Not So Good",
              "answer": "Just make a beta column and colour it when it changes too much.",
              "why_poor": "No explanation of rolling window or Excel mechanics."
            }
          },
          "follow_up_questions_possible": [
            "How would you convert the template into a VBA macro for multiple tickers?",
            "What limitations does Excel have versus Python for rolling-beta calculations?"
          ],
          "preparation_tips": [
            "Master dynamic ranges and the What-If Analysis/Data Table tools.",
            "Rehearse translating Excel logic into Python (pandas rolling regressions)."
          ],
          "common_mistakes": [
            "Forgetting to lock cell references when copying formulas.",
            "Using a static 60-row range instead of a dynamic OFFSET/INDEX approach."
          ]
        }
      ]
    },
    {
        "category": "Advanced CAPM Applications & Performance Attribution",
        "category_description": "Higher-order concepts that extend CAPM into real-world portfolio evaluation, multi-factor thinking, and performance diagnostics.",
        "questions": [
          /* -------------- INV_ADV_001 ---------------------------------- */
          {
            "question_id": "INV_ADV_001",
            "question": "Compare Jensen’s alpha, the Treynor ratio, and the Sharpe ratio. In what contexts would each be most appropriate?",
            "difficulty_level": "Medium",
            "importance": "High",
            "frequency": "Common",
            "interviewer_intent": "Verify understanding of risk-adjusted performance measures and when to apply them.",
            "prerequisite_knowledge": "CAPM, systematic vs. total risk, portfolio performance metrics",
            "related_skills": ["Performance Attribution", "Risk Measurement", "Analytical Thinking"],
            "answer_points": [
              "Jensen’s alpha measures excess return above CAPM benchmark (uses beta/systematic risk).",
              "Treynor ratio = (R_p − R_f)/β_p; risk unit is systematic risk—best for already well-diversified portfolios.",
              "Sharpe ratio = (R_p − R_f)/σ_p; denominator is total volatility—best when diversification level varies or benchmark unclear.",
              "Alpha indicates skill in stock-picking relative to market; ratios compare efficiency per unit of risk."
            ],
            "example_answers": {
              "excellent": {
                "level": "Excellent",
                "answer": "Sharpe uses total σ and is ideal for ranking stand-alone portfolios or hedge funds with non-benchmark mandates. Treynor scales excess return by beta, so it’s suited to pension funds that are already diversified but want to see reward per unit of market risk. Jensen’s alpha isolates manager skill relative to CAPM predictions; a positive alpha means the manager outperformed after accounting for beta exposure.",
                "why_excellent": "Clearly distinguishes denominators, use-cases, and links to diversification level."
              },
              "good": {
                "level": "Good",
                "answer": "Alpha is return above CAPM, Sharpe divides by volatility, Treynor divides by beta. Use Sharpe when you care about total risk and Treynor when investors are well diversified.",
                "why_good": "Covers essentials but missing nuanced context."
              },
              "not_so_good": {
                "level": "Not So Good",
                "answer": "All three show performance, but Sharpe is the best one.",
                "why_poor": "No explanation of differences or when to choose each."
              }
            },
            "follow_up_questions_possible": [
              "How would these metrics behave for a market-neutral long/short fund?",
              "Explain information ratio vs. alpha."
            ],
            "preparation_tips": [
              "Practice calculating all three metrics on sample portfolios.",
              "Understand how diversification affects choice of denominator."
            ],
            "common_mistakes": [
              "Confusing the Sharpe denominator (σ) with beta.",
              "Using Treynor for undiversified single-stock portfolios."
            ]
          },
          /* -------------- INV_ADV_002 ---------------------------------- */
          {
            "question_id": "INV_ADV_002",
            "question": "Outline the Brinson model of performance attribution and the data you need to implement it for an actively managed equity fund.",
            "difficulty_level": "Hard",
            "importance": "High",
            "frequency": "Common",
            "interviewer_intent": "Assess knowledge of return attribution frameworks and practical implementation details.",
            "prerequisite_knowledge": "Benchmark construction, active vs. passive returns, allocation & selection effects",
            "related_skills": ["Performance Attribution", "Portfolio Analytics", "Data Handling"],
            "answer_points": [
              "Brinson decomposes active return into allocation effect, selection effect, and interaction.",
              "Requires: benchmark weights & returns by sector/asset, portfolio weights & returns, total portfolio return.",
              "Allocation effect: (w_p − w_b) × r_b; Selection: w_b × (r_p − r_b); Interaction: (w_p − w_b) × (r_p − r_b).",
              "Need consistent sector classifications throughout measurement period."
            ],
            "example_answers": {
              "excellent": {
                "level": "Excellent",
                "answer": "You download benchmark GICS sector weights/returns and your fund’s sector weights/returns. For each sector, compute allocation (weight difference × bench return), selection (bench weight × excess sector return), and interaction (product of both deltas). Sum across sectors to reconcile to total active return. Clean mappings to avoid residuals.",
                "why_excellent": "Gives formulae, data items, and quality-control step."
              },
              "good": {
                "level": "Good",
                "answer": "Need portfolio and benchmark weights/returns by sector. Decompose active return into allocation, selection and interaction with Brinson formulas.",
                "why_good": "Correct but light on implementation detail."
              },
              "not_so_good": {
                "level": "Not So Good",
                "answer": "Just compare sector returns to benchmark.",
                "why_poor": "Misses decomposition logic."
              }
            },
            "follow_up_questions_possible": [
              "How would you extend Brinson attribution to multi-currency portfolios?",
              "Explain the interaction term’s economic meaning."
            ],
            "preparation_tips": [
              "Work through Brinson spreadsheets for sample data.",
              "Ensure benchmark classification is static to avoid drift."
            ],
            "common_mistakes": [
              "Ignoring interaction term, leading to unexplained residual.",
              "Mismatching benchmark vs. portfolio sector schema."
            ]
          },
          /* -------------- INV_ADV_003 ---------------------------------- */
          {
            "question_id": "INV_ADV_003",
            "question": "A portfolio shows a consistently negative Jensen’s alpha but a positive information ratio. How can this occur and what does it imply about the manager’s skill?",
            "difficulty_level": "Medium",
            "importance": "Medium",
            "frequency": "Common",
            "interviewer_intent": "Test nuanced interpretation of conflicting performance metrics.",
            "prerequisite_knowledge": "Alpha, tracking error, information ratio (IR = alpha / TE)",
            "related_skills": ["Critical Thinking", "Risk Diagnostics", "Quantitative Analysis"],
            "answer_points": [
              "Negative alpha ⇒ underperforms CAPM expectation on average.",
              "Positive IR ⇒ alpha is stable relative to tracking error; manager adds value per unit of active risk despite negative level.",
              "Possible when benchmark mismatch or systematic tilts dominate, but stock-picking skill generates consistent small excess vs. custom benchmark.",
              "Implies manager is efficient at taking low active-risk bets but baseline strategy lags CAPM."
            ],
            "example_answers": {
              "excellent": {
                "level": "Excellent",
                "answer": "Alpha < 0 says the portfolio underperforms CAPM, yet IR > 0 means the shortfall is very consistent with low tracking error. A closet-indexer charging high fees may drift slightly below the SML every period: poor absolute skill (negative alpha) but strong risk-adjusted consistency (positive IR). If fees were cut or benchmark better matched, IR suggests the process is reliable.",
                "why_excellent": "Explains metric mechanics and managerial inference."
              },
              "good": {
                "level": "Good",
                "answer": "It means the manager loses a little every period but does so very predictably; thus alpha negative, IR positive.",
                "why_good": "Captures key idea but lacks context."
              },
              "not_so_good": {
                "level": "Not So Good",
                "answer": "This cannot happen; alpha and information ratio always move together.",
                "why_poor": "Factually incorrect."
              }
            },
            "follow_up_questions_possible": [
              "How would you adjust fees to turn negative alpha into positive net alpha?",
              "Could a factor benchmark (e.g., size/value) resolve the conflict?"
            ],
            "preparation_tips": [
              "Review IR formula and cases with small but stable negative alpha.",
              "Understand role of benchmark choice in alpha measurement."
            ],
            "common_mistakes": [
              "Assuming positive IR implies positive alpha.",
              "Ignoring tracking-error magnitude when interpreting IR."
            ]
          },
          /* -------------- INV_ADV_004 ---------------------------------- */
          {
            "question_id": "INV_ADV_004",
            "question": "Design a multi-factor performance-attribution dashboard for an equity portfolio using Python. What key sections and visualizations would you include?",
            "difficulty_level": "Hard",
            "importance": "Medium",
            "frequency": "Less Common",
            "interviewer_intent": "Gauge end-to-end thinking from data ingestion to stakeholder-ready output.",
            "prerequisite_knowledge": "Python (pandas, matplotlib/plotly), factor regressions, API data pulls",
            "related_skills": ["Programming", "Data Visualization", "Factor Analysis"],
            "answer_points": [
              "Modules: data loader (price & factor API), preprocessing (returns calc, align calendars), regression engine (rolling OLS for betas).",
              "Visuals: cumulative active return vs. benchmark, rolling factor betas heatmap, contribution bar charts, distribution of residuals.",
              "Panels: summary KPIs (alpha, IR, TE), sector/factor attribution tables, drill-downs by period.",
              "Interactivity: date sliders, factor-on/off toggles; export to PowerBI or HTML."
            ],
            "example_answers": {
              "excellent": {
                "level": "Excellent",
                "answer": "I’d build a Streamlit app: (1) Ingest prices from Bloomberg API and Fama–French factors via pandas-datareader. (2) Calculate daily excess returns and run a 36-month rolling OLS to estimate betas/alphas. (3) Display summary tiles (alpha, IR, TE). (4) Heatmap of rolling betas per factor. (5) Stacked bar chart showing cumulative contribution of each factor to total active return. (6) Residual histogram to test normality. All plots via Plotly for hover detail; downloadable CSV and PDF report buttons.",
                "why_excellent": "Covers architecture, visuals, and stakeholder deliverables."
              },
              "good": {
                "level": "Good",
                "answer": "Load data, run regressions, show alpha and betas in charts; let user pick dates.",
                "why_good": "Outline but lacks specific tools or layout."
              },
              "not_so_good": {
                "level": "Not So Good",
                "answer": "Just print the results in Python.",
                "why_poor": "No dashboard concept."
              }
            },
            "follow_up_questions_possible": [
              "How would you automate nightly refresh of the dashboard?",
              "Describe how you’d version-control factor definitions."
            ],
            "preparation_tips": [
              "Prototype dashboards with Streamlit or Dash using public data.",
              "Learn to cache API calls for speed and cost control."
            ],
            "common_mistakes": [
              "Overloading the dashboard with metrics without clear hierarchy.",
              "Forgetting to align holidays/calendars across factor and price data."
            ]
          },
          /* -------------- INV_ADV_005 ---------------------------------- */
          {
            "question_id": "INV_ADV_005",
            "question": "Discuss three empirical limitations of the CAPM and explain how the Arbitrage Pricing Theory (APT) or multi-factor models address them.",
            "difficulty_level": "Medium",
            "importance": "High",
            "frequency": "Very Common",
            "interviewer_intent": "Check awareness of CAPM’s shortcomings and alternative frameworks.",
            "prerequisite_knowledge": "CAPM anomalies, APT basics, multi-factor evidence (size, value, momentum)",
            "related_skills": ["Theoretical Reasoning", "Empirical Finance", "Problem Diagnosis"],
            "answer_points": [
              "Size & value anomalies: small-cap and high-B/M stocks earn excess returns unexplained by CAPM beta.",
              "Low-beta anomaly: low-vol stocks often outperform on risk-adjusted basis.",
              "Time-varying betas & premiums: market risk premium not constant; CAPM assumes stability.",
              "APT/multi-factor: allows multiple priced factors (size, value, momentum) and avoids single-factor restriction, capturing systematic sources CAPM misses."
            ],
            "example_answers": {
              "excellent": {
                "level": "Excellent",
                "answer": "CAPM predicts excess returns depend solely on beta, yet data show persistent size and value premiums (Banz 1981, Fama-French 1993), a low-volatility anomaly, and that market premium itself varies across business cycles. The APT posits that returns are driven by a linear combination of several unknown but identifiable macro or style factors, so researchers build multi-factor models (e.g., SMB, HML, MOM) that significantly reduce pricing errors and capture those anomalies.",
                "why_excellent": "Names concrete anomalies, cites literature, links to APT remedy."
              },
              "good": {
                "level": "Good",
                "answer": "CAPM can’t explain size, value or momentum effects and assumes a constant market premium. Multi-factor models fix this by adding extra factors.",
                "why_good": "Mentions issues but without empirical colour."
              },
              "not_so_good": {
                "level": "Not So Good",
                "answer": "CAPM is old; modern models are better.",
                "why_poor": "No details or evidence."
              }
            },
            "follow_up_questions_possible": [
              "How would you empirically test whether momentum is a priced factor?",
              "What are the trade-offs between statistical and fundamental factor construction?"
            ],
            "preparation_tips": [
              "Read seminal anomaly papers; replicate regressions in Python/R.",
              "Understand factor-model specification and mis-pricing tests."
            ],
            "common_mistakes": [
              "Stating CAPM ‘fails’ without naming specific anomalies.",
              "Assuming multi-factor models eliminate all alpha—residuals still exist."
            ]
          }
        ]
      }
  ]
} as const;
 
