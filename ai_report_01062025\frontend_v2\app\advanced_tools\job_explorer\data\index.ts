// Import individual job files
import { marketingExecutiveBrandManagement } from './marketing/marketing_executive_brand_management';
import { financialAnalystCorporateFinance } from './finance/financial_analyst_corporate_finance';
import { portfolioRiskAnalystCAPM } from './finance/portfolio_risk_analyst';

// Interface definitions
export interface JobDescription {
  overview: string;
  key_responsibilities: string[];
  required_qualifications: string[];
  preferred_skills: string[];
}

export interface ExampleAnswer {
  level: string;
  answer: string;
  why_excellent?: string;
  why_good?: string;
  why_poor?: string;
}

export interface Question {
  question_id: string;
  question: string;
  difficulty_level: string;
  importance: string;
  interviewer_intent: string;
  answer_points: string[];
  example_answers: Record<string, ExampleAnswer>;
  follow_up_questions_possible: string[];
  preparation_tips: string[];
}

export interface QuestionCategory {
  category: string;
  category_description: string;
  questions: Question[];
}

export interface Job {
  id: string;
  job_title: string;
  specialization: string;
  specialization_id: string;
  industry: string;
  company_type: string;
  difficulty_level: string;
  popularity_score: number;
  tags: string[];
  job_description: JobDescription;
  interview_questions: QuestionCategory[];
}

// Data aggregator
const jobsData = {
  marketing: {
    marketingExecutiveBrandManagement
  },
  finance: {
    financialAnalystCorporateFinance,
    portfolioRiskAnalystCAPM
  }
} as const;

// Helper functions with proper typing
export const getAllJobs = (): Job[] => {
  const allJobs: Job[] = [];
  
  // Add marketing jobs
  Object.values(jobsData.marketing).forEach(job => {
    allJobs.push(job as Job);
  });
  
  // Add finance jobs
  Object.values(jobsData.finance).forEach(job => {
    allJobs.push(job as Job);
  });
  
  return allJobs;
};

export const getJobsBySpecialization = (specialization: string): Job[] => {
  const spec = specialization.toLowerCase();
  
  switch (spec) {
    case 'marketing':
      return Object.values(jobsData.marketing) as Job[];
    case 'finance':
      return Object.values(jobsData.finance) as Job[];
    default:
      return [];
  }
};

export const getJobById = (jobId: string): Job | undefined => {
  const allJobs = getAllJobs();
  return allJobs.find(job => job.id === jobId);
};