
'use client'
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Textarea } from '@/components/ui/textarea';
import { 
  Search, 
  BookmarkPlus, 
  Bookmark, 
  ChevronDown, 
  ChevronRight, 
  Star,
  ArrowLeft,
  Filter,
  Building2,
  GraduationCap,
  Target,
  TrendingUp,
  Users,
  BrainCircuit,
  FileText,
  Heart
} from 'lucide-react';

// Import data from separate files
import { getAllJobs, getJobsBySpecialization, getJobById } from './data';

const MBAJobsApp = () => {
  // Static metadata
  const metadata = {
    "created_date": "2025-05-27",
    "version": "1.0",
    "description": "Comprehensive database of entry-level MBA job descriptions and interview questions for Indian market with interactive learning features",
    "total_jobs": 2,
    "specializations_covered": ["Marketing", "Finance"]
  };

  const specializations = [
    {
      "id": "MKT",
      "name": "Marketing",
      "description": "Brand management, digital marketing, market research, and consumer engagement",
      "popular_industries": ["FMCG", "E-commerce", "Consumer Goods", "Retail", "Media"],
      "avg_starting_package": "6-12 LPA",
      "growth_trajectory": "High demand with digital transformation focus"
    },
    {
      "id": "FIN",
      "name": "Finance", 
      "description": "Corporate finance, investment banking, financial analysis, and risk management",
      "popular_industries": ["Banking", "Financial Services", "Investment Banking", "Corporate", "Consulting"],
      "avg_starting_package": "7-15 LPA",
      "growth_trajectory": "Highest paying specialization with diverse career paths"
    }
  ];

  // Get jobs from data structure
  const jobs = getAllJobs();

  const [currentView, setCurrentView] = useState('home');
  const [selectedJob, setSelectedJob] = useState(null);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    specialization: '',
    industry: '',
    difficulty: ''
  });
  const [bookmarkedJobs, setBookmarkedJobs] = useState(new Set());
  const [bookmarkedQuestions, setBookmarkedQuestions] = useState(new Set());
  const [userNotes, setUserNotes] = useState({});
  const [confidenceRatings, setConfidenceRatings] = useState({});

  // Console log user interactions for persistence
  useEffect(() => {
    console.log('User State Update:', {
      bookmarkedJobs: Array.from(bookmarkedJobs),
      bookmarkedQuestions: Array.from(bookmarkedQuestions),
      userNotes,
      confidenceRatings,
      timestamp: new Date().toISOString()
    });
  }, [bookmarkedJobs, bookmarkedQuestions, userNotes, confidenceRatings]);

  const filteredJobs = jobs.filter(job => {
    const matchesSearch = !searchTerm || 
      job.job_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.job_description.overview.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesSpecialization = !filters.specialization || job.specialization === filters.specialization;
    const matchesIndustry = !filters.industry || job.industry.includes(filters.industry);
    const matchesDifficulty = !filters.difficulty || job.difficulty_level === filters.difficulty;
    
    return matchesSearch && matchesSpecialization && matchesIndustry && matchesDifficulty;
  });

  const toggleBookmarkJob = (jobId) => {
    const newBookmarks = new Set(bookmarkedJobs);
    if (newBookmarks.has(jobId)) {
      newBookmarks.delete(jobId);
    } else {
      newBookmarks.add(jobId);
    }
    setBookmarkedJobs(newBookmarks);
  };

  const toggleBookmarkQuestion = (questionId) => {
    const newBookmarks = new Set(bookmarkedQuestions);
    if (newBookmarks.has(questionId)) {
      newBookmarks.delete(questionId);
    } else {
      newBookmarks.add(questionId);
    }
    setBookmarkedQuestions(newBookmarks);
  };

  const updateUserNote = (id, note) => {
    setUserNotes(prev => ({ ...prev, [id]: note }));
  };

  const updateConfidenceRating = (questionId, rating) => {
    setConfidenceRatings(prev => ({ ...prev, [questionId]: rating }));
  };

  const ConfidenceRating = ({ questionId, currentRating, onRatingChange }) => {
    return (
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-600">Confidence:</span>
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 cursor-pointer ${
              star <= currentRating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
            }`}
            onClick={() => onRatingChange(questionId, star)}
          />
        ))}
      </div>
    );
  };

  const renderHome = () => (
    <div className="space-y-6">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg">
        <h1 className="text-3xl font-bold mb-2">MBA Job Interview Preparation</h1>
        <p className="text-blue-100 mb-4">Master your MBA interviews with curated job descriptions and expert-level questions</p>
        <div className="flex gap-4">
          <Button onClick={() => setCurrentView('jobs')} className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold">
            Explore Jobs
          </Button>
          <Button onClick={() => setCurrentView('questions')} className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold">
            View All Questions
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Building2 className="h-8 w-8 mx-auto mb-2 text-blue-600" />
            <div className="text-2xl font-bold">{metadata.total_jobs}</div>
            <div className="text-sm text-gray-600">Job Roles</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <GraduationCap className="h-8 w-8 mx-auto mb-2 text-green-600" />
            <div className="text-2xl font-bold">{specializations?.length || 0}</div>
            <div className="text-sm text-gray-600">Specializations</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <BrainCircuit className="h-8 w-8 mx-auto mb-2 text-purple-600" />
            <div className="text-2xl font-bold">
              {jobs.reduce((total, job) => 
                total + job.interview_questions.reduce((qtotal, category) => qtotal + category.questions.length, 0), 0
              )}
            </div>
            <div className="text-sm text-gray-600">Questions</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Heart className="h-8 w-8 mx-auto mb-2 text-red-600" />
            <div className="text-2xl font-bold">{bookmarkedJobs.size + bookmarkedQuestions.size}</div>
            <div className="text-sm text-gray-600">Bookmarked</div>
          </CardContent>
        </Card>
      </div>

      {/* Specializations Overview */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Specializations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {specializations.map((spec) => (
            <Card key={spec.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                  onClick={() => {
                    setFilters(prev => ({ ...prev, specialization: spec.name }));
                    setCurrentView('jobs');
                  }}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {spec.name}
                  <Badge variant="secondary">{spec.avg_starting_package}</Badge>
                </CardTitle>
                <CardDescription>{spec.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium">Industries: </span>
                    <span className="text-sm text-gray-600">{spec.popular_industries.join(', ')}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Growth: </span>
                    <span className="text-sm text-gray-600">{spec.growth_trajectory}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Featured Jobs */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Featured Jobs</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {jobs.slice(0, 2).map((job) => (
            <Card key={job.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                  onClick={() => {
                    setSelectedJob(job);
                    setCurrentView('job-detail');
                  }}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {job.job_title}
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleBookmarkJob(job.id);
                    }}
                  >
                    {bookmarkedJobs.has(job.id) ? 
                      <Bookmark className="h-4 w-4 fill-current" /> : 
                      <BookmarkPlus className="h-4 w-4" />
                    }
                  </Button>
                </CardTitle>
                <CardDescription>{job.industry}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2 mb-3">
                  <Badge variant="outline">{job.specialization}</Badge>
                  <Badge variant="outline">{job.difficulty_level}</Badge>
                  <Badge variant="outline">★ {job.popularity_score}/10</Badge>
                </div>
                <p className="text-sm text-gray-600 line-clamp-3">{job.job_description.overview}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );

  const renderJobs = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Job Explorer</h1>
        <Button variant="outline" onClick={() => setCurrentView('home')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Home
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search jobs, descriptions, or skills..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={filters.specialization} onValueChange={(value) => 
                setFilters(prev => ({ ...prev, specialization: value === "all" ? "" : value }))}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Specialization" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Specializations</SelectItem>
                  {specializations.map(spec => (
                    <SelectItem key={spec.id} value={spec.name}>{spec.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={filters.difficulty} onValueChange={(value) => 
                setFilters(prev => ({ ...prev, difficulty: value === "all" ? "" : value }))}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Difficulty" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="Entry">Entry</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="Advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Jobs List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredJobs.map((job) => (
          <Card key={job.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => {
                  setSelectedJob(job);
                  setCurrentView('job-detail');
                }}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {job.job_title}
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleBookmarkJob(job.id);
                  }}
                >
                  {bookmarkedJobs.has(job.id) ? 
                    <Bookmark className="h-4 w-4 fill-current" /> : 
                    <BookmarkPlus className="h-4 w-4" />
                  }
                </Button>
              </CardTitle>
              <CardDescription>{job.industry}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2 mb-3">
                <Badge variant="outline">{job.specialization}</Badge>
                <Badge variant="outline">{job.difficulty_level}</Badge>
                <Badge variant="outline">★ {job.popularity_score}/10</Badge>
              </div>
              <p className="text-sm text-gray-600 mb-4 line-clamp-3">{job.job_description.overview}</p>
              
              <div className="flex flex-wrap gap-1 mb-3">
                {job.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag.replace('_', ' ')}
                  </Badge>
                ))}
                {job.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs">+{job.tags.length - 3} more</Badge>
                )}
              </div>
              
              <div className="text-xs text-gray-500">
                {job.interview_questions.reduce((total, category) => total + category.questions.length, 0)} interview questions available
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderJobDetail = () => {
    if (!selectedJob) return null;

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={() => setCurrentView('jobs')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Jobs
          </Button>
          <Button 
            variant="outline"
            onClick={() => toggleBookmarkJob(selectedJob.id)}
          >
            {bookmarkedJobs.has(selectedJob.id) ? 
              <Bookmark className="h-4 w-4 mr-2 fill-current" /> : 
              <BookmarkPlus className="h-4 w-4 mr-2" />
            }
            {bookmarkedJobs.has(selectedJob.id) ? 'Bookmarked' : 'Bookmark'}
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">{selectedJob.job_title}</CardTitle>
            <CardDescription className="text-lg">{selectedJob.industry}</CardDescription>
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge>{selectedJob.specialization}</Badge>
              <Badge variant="outline">{selectedJob.company_type}</Badge>
              <Badge variant="outline">{selectedJob.difficulty_level}</Badge>
              <Badge variant="outline">★ {selectedJob.popularity_score}/10</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="responsibilities">Responsibilities</TabsTrigger>
                <TabsTrigger value="qualifications">Qualifications</TabsTrigger>
                <TabsTrigger value="skills">Skills</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="mt-6">
                <div className="space-y-4">
                  <p className="text-gray-700 leading-relaxed">{selectedJob.job_description.overview}</p>
                  <div>
                    <h4 className="font-semibold mb-2">Key Tags:</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedJob.tags.map((tag) => (
                        <Badge key={tag} variant="secondary">
                          {tag.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="responsibilities" className="mt-6">
                <ul className="space-y-3">
                  {selectedJob.job_description.key_responsibilities.map((resp, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <Target className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{resp}</span>
                    </li>
                  ))}
                </ul>
              </TabsContent>
              
              <TabsContent value="qualifications" className="mt-6">
                <ul className="space-y-3">
                  {selectedJob.job_description.required_qualifications.map((qual, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <GraduationCap className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{qual}</span>
                    </li>
                  ))}
                </ul>
              </TabsContent>
              
              <TabsContent value="skills" className="mt-6">
                <ul className="space-y-3">
                  {selectedJob.job_description.preferred_skills.map((skill, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <TrendingUp className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{skill}</span>
                    </li>
                  ))}
                </ul>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Interview Questions Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BrainCircuit className="h-6 w-6" />
              Interview Questions
            </CardTitle>
            <CardDescription>
              Prepare for your interview with these curated questions and expert answers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {selectedJob.interview_questions.map((category, categoryIndex) => (
                <Collapsible key={categoryIndex}>
                  <CollapsibleTrigger className="flex w-full items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="text-left">
                      <h4 className="font-semibold">{category.category}</h4>
                      <p className="text-sm text-gray-600 mt-1">{category.category_description}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{category.questions.length} questions</Badge>
                      <ChevronDown className="h-4 w-4" />
                    </div>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="mt-2">
                    <div className="space-y-3 p-4 border rounded-lg">
                      {category.questions.map((question, questionIndex) => (
                        <div key={questionIndex} className="border-b last:border-b-0 pb-3 last:pb-0">
                          <div className="flex items-start justify-between mb-2">
                            <h5 className="font-medium text-gray-800 flex-1">{question.question}</h5>
                            <div className="flex items-center gap-2 ml-4">
                              <Badge variant={question.difficulty_level === 'Easy' ? 'secondary' : 
                                           question.difficulty_level === 'Medium' ? 'default' : 'destructive'} 
                                     className="text-xs">
                                {question.difficulty_level}
                              </Badge>
                              <Badge variant={question.importance === 'High' ? 'destructive' : 'secondary'} 
                                     className="text-xs">
                                {question.importance}
                              </Badge>
                            </div>
                          </div>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setSelectedQuestion(question);
                              setCurrentView('question-detail');
                            }}
                          >
                            View Detailed Answer
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </div>
            
            <div className="mt-6 text-center">
              <Button 
                onClick={() => {
                  setCurrentView('questions');
                }}
                className="bg-blue-600 hover:bg-blue-700"
              >
                View All Questions for This Job
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Personal Notes Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Personal Notes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Add your personal notes about this job role..."
              value={userNotes[selectedJob.id] || ''}
              onChange={(e) => updateUserNote(selectedJob.id, e.target.value)}
              className="min-h-[100px]"
            />
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderQuestions = () => {
    const allQuestions = selectedJob ? 
      selectedJob.interview_questions.flatMap(category => 
        category.questions.map(q => ({ ...q, category: category.category }))
      ) :
      jobs.flatMap(job => 
        job.interview_questions.flatMap(category => 
          category.questions.map(q => ({ ...q, category: category.category, jobTitle: job.job_title }))
        )
      );

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">
            {selectedJob ? `Questions for ${selectedJob.job_title}` : 'All Interview Questions'}
          </h1>
          <Button variant="outline" onClick={() => setCurrentView(selectedJob ? 'job-detail' : 'home')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>

        <div className="grid gap-4">
          {allQuestions.map((question, index) => (
            <Card key={`${question.question_id}-${index}`} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2">{question.question}</CardTitle>
                    <div className="flex flex-wrap gap-2 mb-2">
                      <Badge variant="outline">{question.category}</Badge>
                      {question.jobTitle && <Badge variant="secondary">{question.jobTitle}</Badge>}
                      <Badge variant={question.difficulty_level === 'Easy' ? 'secondary' : 
                                   question.difficulty_level === 'Medium' ? 'default' : 'destructive'}>
                        {question.difficulty_level}
                      </Badge>
                      <Badge variant={question.importance === 'High' ? 'destructive' : 'secondary'}>
                        {question.importance}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">{question.interviewer_intent}</p>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => toggleBookmarkQuestion(question.question_id)}
                  >
                    {bookmarkedQuestions.has(question.question_id) ? 
                      <Bookmark className="h-4 w-4 fill-current" /> : 
                      <BookmarkPlus className="h-4 w-4" />
                    }
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Key Answer Points:</h4>
                    <ul className="space-y-1 text-sm">
                      {question.answer_points.slice(0, 2).map((point, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span className="text-blue-600 mt-1">•</span>
                          <span className="text-gray-700">{point}</span>
                        </li>
                      ))}
                    </ul>
                    {question.answer_points.length > 2 && (
                      <p className="text-xs text-gray-500 mt-2">+{question.answer_points.length - 2} more points</p>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <ConfidenceRating 
                      questionId={question.question_id}
                      currentRating={confidenceRatings[question.question_id] || 0}
                      onRatingChange={updateConfidenceRating}
                    />
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        setSelectedQuestion(question);
                        setCurrentView('question-detail');
                      }}
                    >
                      View Full Answer
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderQuestionDetail = () => {
    if (!selectedQuestion) return null;

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={() => setCurrentView('questions')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Questions
          </Button>
          <Button 
            variant="outline"
            onClick={() => toggleBookmarkQuestion(selectedQuestion.question_id)}
          >
            {bookmarkedQuestions.has(selectedQuestion.question_id) ? 
              <Bookmark className="h-4 w-4 mr-2 fill-current" /> : 
              <BookmarkPlus className="h-4 w-4 mr-2" />
            }
            {bookmarkedQuestions.has(selectedQuestion.question_id) ? 'Bookmarked' : 'Bookmark'}
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">{selectedQuestion.question}</CardTitle>
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge variant="outline">{selectedQuestion.category}</Badge>
              <Badge variant={selectedQuestion.difficulty_level === 'Easy' ? 'secondary' : 
                           selectedQuestion.difficulty_level === 'Medium' ? 'default' : 'destructive'}>
                {selectedQuestion.difficulty_level}
              </Badge>
              <Badge variant={selectedQuestion.importance === 'High' ? 'destructive' : 'secondary'}>
                {selectedQuestion.importance}
              </Badge>
            </div>
            <CardDescription className="text-base mt-3">
              <strong>Interviewer Intent:</strong> {selectedQuestion.interviewer_intent}
            </CardDescription>
          </CardHeader>
        </Card>

        <Tabs defaultValue="answer-points" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="answer-points">Answer Points</TabsTrigger>
            <TabsTrigger value="examples">Example Answers</TabsTrigger>
            <TabsTrigger value="tips">Preparation Tips</TabsTrigger>
            <TabsTrigger value="follow-up">Follow-up</TabsTrigger>
          </TabsList>
          
          <TabsContent value="answer-points" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Key Answer Points</CardTitle>
                <CardDescription>Essential components of a strong answer</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {selectedQuestion.answer_points.map((point, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full mt-0.5">
                        {index + 1}
                      </span>
                      <span className="text-gray-700">{point}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="examples" className="mt-6">
            <div className="space-y-4">
              {selectedQuestion.example_answers && Object.entries(selectedQuestion.example_answers).map(([level, answer]) => (
                <Card key={level} className={`${
                  level === 'excellent' ? 'border-green-200 bg-green-50' :
                  level === 'good' ? 'border-yellow-200 bg-yellow-50' :
                  'border-red-200 bg-red-50'
                }`}>
                  <CardHeader>
                    <CardTitle className={`text-lg ${
                      level === 'excellent' ? 'text-green-800' :
                      level === 'good' ? 'text-yellow-800' :
                      'text-red-800'
                    }`}>
                      {answer.level} Answer
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-3 leading-relaxed">{answer.answer}</p>
                    <div className={`text-sm p-3 rounded-lg ${
                      level === 'excellent' ? 'bg-green-100 text-green-800' :
                      level === 'good' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      <strong>Why {level}:</strong> {answer[`why_${level}`] || answer.why_excellent || answer.why_good || answer.why_poor}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="tips" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Preparation Tips</CardTitle>
                <CardDescription>How to prepare for this type of question</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {selectedQuestion.preparation_tips && selectedQuestion.preparation_tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <Target className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{tip}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="follow-up" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Possible Follow-up Questions</CardTitle>
                <CardDescription>Be prepared for these additional questions</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {selectedQuestion.follow_up_questions_possible && selectedQuestion.follow_up_questions_possible.map((question, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <ChevronRight className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{question}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Self Assessment */}
        <Card>
          <CardHeader>
            <CardTitle>Self Assessment</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Rate your confidence on this question:</label>
              <ConfidenceRating 
                questionId={selectedQuestion.question_id}
                currentRating={confidenceRatings[selectedQuestion.question_id] || 0}
                onRatingChange={updateConfidenceRating}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Personal Notes:</label>
              <Textarea
                placeholder="Add your notes, practice answers, or key insights..."
                value={userNotes[selectedQuestion.question_id] || ''}
                onChange={(e) => updateUserNote(selectedQuestion.question_id, e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'jobs':
        return renderJobs();
      case 'job-detail':
        return renderJobDetail();
      case 'questions':
        return renderQuestions();
      case 'question-detail':
        return renderQuestionDetail();
      default:
        return renderHome();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-8">
              <button 
                onClick={() => setCurrentView('home')}
                className="text-xl font-bold text-blue-600 hover:text-blue-700"
              >
                MBA Prep Hub
              </button>
              <nav className="hidden md:flex space-x-6">
                <button 
                  onClick={() => setCurrentView('home')}
                  className={`${currentView === 'home' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'} pb-1`}
                >
                  Home
                </button>
                <button 
                  onClick={() => setCurrentView('jobs')}
                  className={`${currentView === 'jobs' || currentView === 'job-detail' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'} pb-1`}
                >
                  Jobs
                </button>
                <button 
                  onClick={() => setCurrentView('questions')}
                  className={`${currentView === 'questions' || currentView === 'question-detail' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-blue-600'} pb-1`}
                >
                  Questions
                </button>
              </nav>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="outline" className="hidden sm:flex">
                v{metadata.version}
              </Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderCurrentView()}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>MBA Interview Preparation Database • Last Updated: {metadata.created_date}</p>
            <p className="text-sm mt-2">{metadata.description}</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default MBAJobsApp;