import React from 'react';
import { <PERSON>, <PERSON><PERSON>dingUp, Target, Award, BarChart3, Arrow<PERSON><PERSON>, Truck, Megaphone } from 'lucide-react';
import { mcqQuestions, categoryInfo } from '../questions';

interface DashboardProps {
  quizHistory: any[];
  setSelectedCategory: (category: string) => void;
  setCurrentView: (view: string) => void;
  setCurrentQuestion: (question: any) => void;
  setQuestionIndex: (index: number) => void;
  setTimeLeft: (time: number) => void;
  setIsActive: (active: boolean) => void;
  setSelectedAnswer: (answer: any) => void;
  setShowResult: (show: boolean) => void;
  setQuizResults: (results: any[]) => void;
}

const Dashboard: React.FC<DashboardProps> = ({
  quizHistory,
  setSelectedCategory,
  setCurrentView,
  setCurrentQuestion,
  setQuestionIndex,
  setTimeLeft,
  setIsActive,
  setSelectedAnswer,
  setShowResult,
  setQuizResults,
}) => {
  const startQuiz = (category: string) => {
    const questions = mcqQuestions[category as keyof typeof mcqQuestions];
    setSelectedCategory(category);
    setCurrentQuestion(questions[0]);
    setQuestionIndex(0);
    setTimeLeft(questions[0].timeLimit);
    setIsActive(true);
    setSelectedAnswer(null);
    setShowResult(false);
    setQuizResults([]);
    setCurrentView('quiz');
  };

  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'BarChart3': return BarChart3;
      case 'TrendingUp': return TrendingUp;
      case 'Brain': return Brain;
      case 'Truck': return Truck;
      case 'Megaphone': return Megaphone;
      default: return BarChart3;
    }
  };

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue': return {
        border: 'hover:border-blue-500',
        icon: 'text-blue-600',
        text: 'text-blue-600'
      };
      case 'green': return {
        border: 'hover:border-green-500',
        icon: 'text-green-600',
        text: 'text-green-600'
      };
      case 'purple': return {
        border: 'hover:border-purple-500',
        icon: 'text-purple-600',
        text: 'text-purple-600'
      };
      case 'orange': return {
        border: 'hover:border-orange-500',
        icon: 'text-orange-600',
        text: 'text-orange-600'
      };
      case 'pink': return {
        border: 'hover:border-pink-500',
        icon: 'text-pink-600',
        text: 'text-pink-600'
      };
      default: return {
        border: 'hover:border-blue-500',
        icon: 'text-blue-600',
        text: 'text-blue-600'
      };
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-3">MBA MCQ Practice</h1>
        <p className="text-xl text-gray-600">Master business concepts with targeted multiple choice questions</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-blue-50 rounded-lg p-6 text-center">
          <Brain className="h-8 w-8 text-blue-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-blue-900">{quizHistory.length}</div>
          <div className="text-blue-600 text-sm">Quizzes Taken</div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-6 text-center">
          <Target className="h-8 w-8 text-green-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-green-900">
            {quizHistory.length > 0 
              ? Math.round(quizHistory.reduce((acc, q) => acc + q.percentage, 0) / quizHistory.length)
              : 0}%
          </div>
          <div className="text-green-600 text-sm">Average Score</div>
        </div>
        
        <div className="bg-purple-50 rounded-lg p-6 text-center">
          <Award className="h-8 w-8 text-purple-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-purple-900">
            {quizHistory.length > 0 
              ? Math.max(...quizHistory.map(q => q.percentage))
              : 0}%
          </div>
          <div className="text-purple-600 text-sm">Best Score</div>
        </div>
        
        <div className="bg-orange-50 rounded-lg p-6 text-center">
          <TrendingUp className="h-8 w-8 text-orange-600 mx-auto mb-2" />
          <div className="text-2xl font-bold text-orange-900">
            {Object.keys(mcqQuestions).reduce((acc, cat) => acc + mcqQuestions[cat as keyof typeof mcqQuestions].length, 0)}
          </div>
          <div className="text-orange-600 text-sm">Total Questions</div>
        </div>
      </div>

      {/* Quiz Categories */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        {Object.entries(categoryInfo).map(([key, info]) => {
          const IconComponent = getIconComponent(info.icon);
          const colors = getColorClasses(info.color);
          
          return (
            <div 
              key={key}
              className={`bg-white border-2 border-gray-200 rounded-xl p-6 ${colors.border} hover:shadow-lg cursor-pointer transition-all`}
              onClick={() => startQuiz(key)}
            >
              <div className="flex items-center mb-4">
                <IconComponent className={`h-10 w-10 ${colors.icon} mr-4`} />
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{info.title}</h3>
                  <p className="text-sm text-gray-600">{info.description}</p>
                </div>
              </div>
              <p className="text-gray-700 mb-4">
                {key === 'business-fundamentals' && 'Core business concepts every MBA should master'}
                {key === 'finance' && 'Financial analysis and valuation techniques'}
                {key === 'consulting' && 'McKinsey, Bain, BCG style questions'}
                {key === 'supply-chain' && 'End-to-end supply chain optimization'}
                {key === 'marketing' && 'Consumer behavior and brand strategy'}
              </p>
              <div className="flex justify-between items-center">
                <span className={`text-sm font-medium ${colors.text}`}>
                  {mcqQuestions[key as keyof typeof mcqQuestions].length} Questions
                </span>
                <ArrowRight className="h-5 w-5 text-gray-400" />
              </div>
            </div>
          );
        })}
      </div>

      {/* Recent History */}
      {quizHistory.length > 0 && (
        <div className="bg-white rounded-xl p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Recent Quiz Results</h2>
          <div className="space-y-3">
            {quizHistory.slice(-5).reverse().map((quiz) => (
              <div key={quiz.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium capitalize">{quiz.category.replace('-', ' ')}</p>
                  <p className="text-sm text-gray-600">
                    {new Date(quiz.date).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <p className={`text-lg font-bold ${quiz.percentage >= 70 ? 'text-green-600' : 'text-red-600'}`}>
                    {quiz.percentage}%
                  </p>
                  <p className="text-sm text-gray-600">{quiz.score}/{quiz.total}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;