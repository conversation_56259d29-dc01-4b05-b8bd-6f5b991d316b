import React from 'react';
import { Brain, CheckCircle, XCircle, Target, TrendingUp, X } from 'lucide-react';
import type { MCQQuestion } from '../questions';

interface DetailModalProps {
  showModal: boolean;
  question: MCQQuestion | null;
  onClose: () => void;
}

const DetailModal: React.FC<DetailModalProps> = ({ showModal, question, onClose }) => {
  if (!showModal || !question) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Detailed Explanation</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-6 w-6 text-gray-500" />
          </button>
        </div>

        {/* Modal Content - Scrollable */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {/* Question */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Question:</h3>
            <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">{question.question}</p>
          </div>

          {/* Concept Explanation */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <Brain className="h-5 w-5 mr-2 text-blue-600" />
              Core Concept
            </h3>
            <p className="text-gray-700 leading-relaxed">{question.detailedExplanation?.concept}</p>
          </div>

          {/* Why Correct Answer is Right */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-green-800 mb-3 flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
              Why Option {String.fromCharCode(65 + question.correct)} is Correct
            </h3>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-green-800">{question.detailedExplanation?.whyCorrect}</p>
            </div>
          </div>

          {/* Why Other Options are Wrong */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-red-800 mb-3 flex items-center">
              <XCircle className="h-5 w-5 mr-2 text-red-600" />
              Why Other Options are Incorrect
            </h3>
            <div className="space-y-3">
              {Object.entries(question.detailedExplanation?.whyOthersWrong || {}).map(([option, reason]) => (
                <div key={option} className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="font-medium text-red-800 mb-1">Option {option}:</p>
                  <p className="text-red-700 text-sm">{reason}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Real-world Examples */}
          {question.detailedExplanation?.examples && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-purple-800 mb-3 flex items-center">
                <Target className="h-5 w-5 mr-2 text-purple-600" />
                Real-world Examples
              </h3>
              <div className="space-y-3">
                {question.detailedExplanation.examples.map((example, index) => (
                  <div key={index} className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                    <p className="text-purple-800 text-sm">{example}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Practical Applications */}
          {question.detailedExplanation?.applications && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-indigo-800 mb-3 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-indigo-600" />
                Practical Applications
              </h3>
              <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                <p className="text-indigo-800">{question.detailedExplanation.applications}</p>
              </div>
            </div>
          )}
        </div>

        {/* Modal Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Got It!
          </button>
        </div>
      </div>
    </div>
  );
};

export default DetailModal;