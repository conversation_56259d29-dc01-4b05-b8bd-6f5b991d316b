import React, { useEffect, useRef } from 'react';
import { CheckCircle, XCircle, RotateCcw, Info } from 'lucide-react';
import { mcqQuestions } from '../questions';

interface QuizProps {
  selectedCategory: string;
  currentQuestion: any;
  questionIndex: number;
  selectedAnswer: number | null;
  showResult: boolean;
  timeLeft: number;
  isActive: boolean;
  quizResults: any[];
  quizHistory: any[];
  setQuestionIndex: (index: number) => void;
  setCurrentQuestion: (question: any) => void;
  setSelectedAnswer: (answer: number | null) => void;
  setShowResult: (show: boolean) => void;
  setTimeLeft: (time: number) => void;
  setIsActive: (active: boolean) => void;
  setQuizResults: (results: any[]) => void;
  setQuizHistory: (history: any[]) => void;
  setCurrentView: (view: string) => void;
  resetQuiz: () => void;
  openDetailModal: (question: any) => void;
}

const Quiz: React.FC<QuizProps> = ({
  selectedCategory,
  currentQuestion,
  questionIndex,
  selectedAnswer,
  showResult,
  timeLeft,
  isActive,
  quizResults,
  quizHistory,
  setQuestionIndex,
  setCurrentQuestion,
  setSelectedAnswer,
  setShowResult,
  setTimeLeft,
  setIsActive,
  setQuizResults,
  setQuizHistory,
  setCurrentView,
  resetQuiz,
  openDetailModal,
}) => {
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Timer effect
  useEffect(() => {
    if (isActive && timeLeft > 0) {
      timerRef.current = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
    } else if (timeLeft === 0 && isActive) {
      handleTimeUp();
    }
    
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [isActive, timeLeft]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (answerIndex: number) => {
    setSelectedAnswer(answerIndex);
    
    // If this question was previously answered, reset the result state to allow re-submission
    if (showResult) {
      setShowResult(false);
      // Only restart timer if it was at 0 (previously answered/timed out)
      if (timeLeft === 0) {
        setIsActive(true);
        setTimeLeft(currentQuestion.timeLimit);
      }
    }
  };

  const jumpToQuestion = (targetIndex: number) => {
    const questions = mcqQuestions[selectedCategory as keyof typeof mcqQuestions];
    
    // Simply jump to the target question without auto-submitting
    setQuestionIndex(targetIndex);
    setCurrentQuestion(questions[targetIndex]);
    
    // Check if this question was already answered
    const existingResult = quizResults.find(r => r.questionId === questions[targetIndex].id);
    
    if (existingResult) {
      // Question was already answered - show the previous result
      setSelectedAnswer(existingResult.selectedAnswer);
      setShowResult(true);
      setIsActive(false);
      setTimeLeft(0);
    } else {
      // Fresh question - reset everything
      setSelectedAnswer(null);
      setShowResult(false);
      setIsActive(true);
      setTimeLeft(questions[targetIndex].timeLimit);
    }
  };

  const getQuestionStatus = (index: number) => {
    const questions = mcqQuestions[selectedCategory as keyof typeof mcqQuestions];
    const questionId = questions[index].id;
    const result = quizResults.find(r => r.questionId === questionId);
    
    if (!result) return 'unanswered';
    if (result.isSkipped) return 'skipped';
    if (result.isCorrect) return 'correct';
    return 'incorrect';
  };

  const handleSkip = () => {
    setIsActive(false);
    setShowResult(true);
    
    const result = {
      questionId: currentQuestion.id,
      category: currentQuestion.category,
      topic: currentQuestion.topic,
      question: currentQuestion.question,
      selectedAnswer: null,
      correctAnswer: currentQuestion.correct,
      isCorrect: false,
      isSkipped: true,
      timeUsed: currentQuestion.timeLimit - timeLeft,
      timeLimit: currentQuestion.timeLimit
    };
    
    // Update or add result
    updateQuizResult(result);
  };

  const handleSubmit = () => {
    if (selectedAnswer === null) return;
    
    setIsActive(false);
    setShowResult(true);
    
    const isCorrect = selectedAnswer === currentQuestion.correct;
    const result = {
      questionId: currentQuestion.id,
      category: currentQuestion.category,
      topic: currentQuestion.topic,
      question: currentQuestion.question,
      selectedAnswer,
      correctAnswer: currentQuestion.correct,
      isCorrect,
      isSkipped: false,
      timeUsed: currentQuestion.timeLimit - timeLeft,
      timeLimit: currentQuestion.timeLimit
    };
    
    // Update or add result
    updateQuizResult(result);
  };

  const updateQuizResult = (newResult: any) => {
    // Find if this question already has a result
    const existingIndex = quizResults.findIndex(r => r.questionId === newResult.questionId);
    
    if (existingIndex !== -1) {
      // Update existing result
      const updatedResults = [...quizResults];
      updatedResults[existingIndex] = newResult;
      setQuizResults(updatedResults);
    } else {
      // Add new result
      setQuizResults([...quizResults, newResult]);
    }
  };

  const handleTimeUp = () => {
    setIsActive(false);
    setShowResult(true);
    
    const result = {
      questionId: currentQuestion.id,
      category: currentQuestion.category,
      topic: currentQuestion.topic,
      question: currentQuestion.question,
      selectedAnswer: null,
      correctAnswer: currentQuestion.correct,
      isCorrect: false,
      isSkipped: false,
      timeUsed: currentQuestion.timeLimit,
      timeLimit: currentQuestion.timeLimit
    };
    
    updateQuizResult(result);
  };

  const nextQuestion = () => {
    const questions = mcqQuestions[selectedCategory as keyof typeof mcqQuestions];
    const nextIndex = questionIndex + 1;
    
    if (nextIndex < questions.length) {
      // Check if next question was already answered
      const nextQuestion = questions[nextIndex];
      const existingResult = quizResults.find(r => r.questionId === nextQuestion.id);
      
      setQuestionIndex(nextIndex);
      setCurrentQuestion(nextQuestion);
      
      if (existingResult) {
        // Next question was already answered - show the previous result
        setSelectedAnswer(existingResult.selectedAnswer);
        setShowResult(true);
        setIsActive(false);
        setTimeLeft(0);
      } else {
        // Fresh question
        setSelectedAnswer(null);
        setShowResult(false);
        setIsActive(true);
        setTimeLeft(nextQuestion.timeLimit);
      }
    } else {
      finishQuiz();
    }
  };

  const finishQuiz = () => {
    // Use current quiz results as they are already deduplicated by updateQuizResult
    const uniqueResults = [...quizResults];
    
    const score = uniqueResults.filter(r => r.isCorrect).length;
    const total = uniqueResults.length;
    const percentage = total > 0 ? Math.round((score / total) * 100) : 0;
    
    const session = {
      id: Date.now(),
      category: selectedCategory,
      date: new Date().toISOString(),
      score,
      total,
      percentage,
      results: uniqueResults
    };
    
    const updatedHistory = [...quizHistory, session];
    setQuizHistory(updatedHistory);
    localStorage.setItem('mba-mcq-history', JSON.stringify(updatedHistory));
    
    setCurrentView('results');
  };

  const questions = mcqQuestions[selectedCategory as keyof typeof mcqQuestions];

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        
        {/* Question Navigator */}
        <div className="bg-gray-50 border-b border-gray-200 p-4">
          <h3 className="text-sm font-semibold text-gray-700 mb-3">Question Navigator</h3>
          <div className="flex flex-wrap gap-2">
            {questions.map((_, index) => {
              const status = getQuestionStatus(index);
              const isCurrent = index === questionIndex;
              
              return (
                <button
                  key={index}
                  onClick={() => jumpToQuestion(index)}
                  className={`w-10 h-10 rounded-lg font-semibold text-sm transition-all duration-200 flex items-center justify-center ${
                    isCurrent 
                      ? 'ring-2 ring-blue-500 ring-offset-2' 
                      : ''
                  } ${
                    status === 'correct' 
                      ? 'bg-green-500 text-white hover:bg-green-600' 
                      : status === 'incorrect'
                      ? 'bg-red-500 text-white hover:bg-red-600'
                      : status === 'skipped'
                      ? 'bg-yellow-500 text-white hover:bg-yellow-600'
                      : 'bg-white border-2 border-gray-300 text-gray-700 hover:border-blue-500 hover:text-blue-600'
                  }`}
                  title={`Question ${index + 1} - ${status.charAt(0).toUpperCase() + status.slice(1)}`}
                >
                  {index + 1}
                </button>
              );
            })}
          </div>
          <div className="flex items-center justify-between mt-3 text-xs text-gray-600">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded mr-1"></div>
                <span>Correct</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded mr-1"></div>
                <span>Incorrect</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded mr-1"></div>
                <span>Skipped</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-gray-300 border rounded mr-1"></div>
                <span>Unanswered</span>
              </div>
            </div>
            <span>Current: Q{questionIndex + 1}</span>
          </div>
        </div>

        <div className="p-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Question {questionIndex + 1} of {questions.length}
              </h1>
              <p className="text-gray-600 capitalize">{selectedCategory.replace('-', ' ')} • {currentQuestion?.category}</p>
              {getQuestionStatus(questionIndex) !== 'unanswered' && (
                <div className="mt-2">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    getQuestionStatus(questionIndex) === 'correct' ? 'bg-green-100 text-green-800' :
                    getQuestionStatus(questionIndex) === 'incorrect' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    Previously {getQuestionStatus(questionIndex)} 
                  </span>
                  <p className="text-sm text-blue-600 mt-1 font-medium">
                    💡 Click any answer option below to change your response
                  </p>
                </div>
              )}
            </div>
            <div className="text-right">
              <div className={`text-3xl font-bold ${timeLeft <= 30 ? 'text-red-600' : 'text-blue-600'}`}>
                {timeLeft > 0 ? formatTime(timeLeft) : '00:00'}
              </div>
              <p className="text-sm text-gray-600">
                {timeLeft > 0 ? 'Time Left' : 'No Time Limit'}
              </p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Progress</span>
              <span>{Math.round(((questionIndex + 1) / questions.length) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((questionIndex + 1) / questions.length) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Question */}
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-3">
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                currentQuestion?.difficulty === 'Easy' ? 'bg-green-100 text-green-800' :
                currentQuestion?.difficulty === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {currentQuestion?.difficulty}
              </span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">{currentQuestion?.question}</h2>
          </div>

          {/* Options */}
          <div className="space-y-3 mb-6">
            {currentQuestion?.options.map((option: string, index: number) => (
              <button
                key={index}
                onClick={() => handleAnswerSelect(index)}
                className={`w-full text-left p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  selectedAnswer === index 
                    ? showResult
                      ? index === currentQuestion.correct
                        ? 'border-green-500 bg-green-50'
                        : 'border-red-500 bg-red-50'
                      : 'border-blue-500 bg-blue-50'
                    : showResult && index === currentQuestion.correct
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center">
                  <span className="font-medium mr-3 text-gray-600">
                    {String.fromCharCode(65 + index)}.
                  </span>
                  <span className="flex-1">{option}</span>
                  {showResult && index === currentQuestion.correct && (
                    <CheckCircle className="h-5 w-5 text-green-600 ml-2" />
                  )}
                  {showResult && selectedAnswer === index && index !== currentQuestion.correct && (
                    <XCircle className="h-5 w-5 text-red-600 ml-2" />
                  )}
                </div>
              </button>
            ))}
          </div>

          {/* Result Explanation */}
          {showResult && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-blue-900">Explanation:</h3>
                <button
                  onClick={() => openDetailModal(currentQuestion)}
                  className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                  title="View detailed explanation"
                >
                  <Info className="h-5 w-5" />
                </button>
              </div>
              <p className="text-blue-800">{currentQuestion?.explanation}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between">
            <button
              onClick={resetQuiz}
              className="flex items-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Quit Quiz
            </button>

            <div className="space-x-3">
              {!showResult && (
                <>
                  <button
                    onClick={handleSkip}
                    className="px-6 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                  >
                    Skip Question
                  </button>
                  {selectedAnswer !== null && (
                    <button
                      onClick={handleSubmit}
                      className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      {getQuestionStatus(questionIndex) !== 'unanswered' ? 'Update Answer' : 'Submit Answer'}
                    </button>
                  )}
                </>
              )}

              {showResult && (
                <button
                  onClick={nextQuestion}
                  className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  {questionIndex + 1 < questions.length ? 'Next Question' : 'Finish Quiz'}
                </button>
              )}
            </div>
          </div>

          {/* Recording Status - Only for Audio Questions */}
          {currentQuestion?.responseType === 'audio' && isActive && (
            <div className="flex items-center justify-center mt-4">
              <div className="flex items-center text-red-600">
                <div className="animate-pulse bg-red-600 rounded-full h-3 w-3 mr-2"></div>
                <span className="font-medium">Recording in progress...</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Quiz;