import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react';
import { mcqQuestions } from '../questions';

interface ResultsProps {
  selectedCategory: string;
  quizHistory: any[];
  resetQuiz: () => void;
  setCurrentView: (view: string) => void;
  setCurrentQuestion: (question: any) => void;
  setQuestionIndex: (index: number) => void;
  setTimeLeft: (time: number) => void;
  setIsActive: (active: boolean) => void;
  setSelectedAnswer: (answer: any) => void;
  setShowResult: (show: boolean) => void;
  setQuizResults: (results: any[]) => void;
}

const Results: React.FC<ResultsProps> = ({
  selectedCategory,
  quizHistory,
  resetQuiz,
  setCurrentView,
  setCurrentQuestion,
  setQuestionIndex,
  setTimeLeft,
  setIsActive,
  setSelectedAnswer,
  setShowResult,
  setQuizResults,
}) => {
  const latestQuiz = quizHistory[quizHistory.length - 1];

  // Safety check - if no quiz history, redirect back to dashboard
  if (!latestQuiz || !latestQuiz.results) {
    console.error('No quiz results found, redirecting to dashboard');
    setCurrentView('dashboard');
    return null;
  }

  console.log('Latest quiz:', latestQuiz);

  const startQuiz = (category: string) => {
    const questions = mcqQuestions[category as keyof typeof mcqQuestions];
    setCurrentQuestion(questions[0]);
    setQuestionIndex(0);
    setTimeLeft(questions[0].timeLimit);
    setIsActive(true);
    setSelectedAnswer(null);
    setShowResult(false);
    setQuizResults([]);
    setCurrentView('quiz');
  };

  // Calculate statistics
  const totalQuestions = latestQuiz.results.length;
  const correctAnswers = latestQuiz.results.filter(r => r.isCorrect).length;
  const skippedQuestions = latestQuiz.results.filter(r => r.isSkipped).length;
  const incorrectAnswers = totalQuestions - correctAnswers - skippedQuestions;

  const getStatusColor = (result: any) => {
    if (result.isSkipped) return 'bg-gradient-to-br from-yellow-50 to-amber-50 border-yellow-300 hover:border-yellow-400';
    if (result.isCorrect) return 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-300 hover:border-green-400';
    return 'bg-gradient-to-br from-red-50 to-rose-50 border-red-300 hover:border-red-400';
  };

  const getStatusIcon = (result: any) => {
    if (result.isSkipped) return <SkipForward className="h-3 w-3 text-white" />;
    if (result.isCorrect) return <CheckCircle className="h-3 w-3 text-white" />;
    return <XCircle className="h-3 w-3 text-white" />;
  };

  const getStatusText = (result: any) => {
    if (result.isSkipped) return 'Skipped';
    if (result.isCorrect) return 'Correct';
    return 'Incorrect';
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
      <div className="max-w-7xl mx-auto px-6">
        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 mb-6 shadow-lg">
            <span className="text-3xl font-bold text-white">
              {latestQuiz.percentage}%
            </span>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-3">
            Quiz Complete!
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            You've completed the {selectedCategory.replace('-', ' ')} assessment. Here's your detailed performance breakdown.
          </p>
        </div>

        {/* Score Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          {/* Overall Score */}
          <div className="md:col-span-1 bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="text-center">
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${
                latestQuiz.percentage >= 80 ? 'bg-gradient-to-r from-green-400 to-green-600' : 
                latestQuiz.percentage >= 60 ? 'bg-gradient-to-r from-yellow-400 to-yellow-600' : 
                'bg-gradient-to-r from-red-400 to-red-600'
              }`}>
                <span className="text-2xl font-bold text-white">{latestQuiz.percentage}%</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-1">Overall Score</h3>
              <p className="text-sm text-gray-500">{latestQuiz.score} out of {latestQuiz.total}</p>
            </div>
          </div>

          {/* Correct Answers */}
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 shadow-lg border border-green-100">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-green-500 rounded-xl p-3">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-green-700">{correctAnswers}</p>
                <p className="text-sm text-green-600">Correct</p>
              </div>
            </div>
            <div className="w-full bg-green-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(correctAnswers / totalQuestions) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Incorrect Answers */}
          <div className="bg-gradient-to-br from-red-50 to-rose-50 rounded-2xl p-6 shadow-lg border border-red-100">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-red-500 rounded-xl p-3">
                <XCircle className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-red-700">{incorrectAnswers}</p>
                <p className="text-sm text-red-600">Incorrect</p>
              </div>
            </div>
            <div className="w-full bg-red-200 rounded-full h-2">
              <div 
                className="bg-red-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(incorrectAnswers / totalQuestions) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Skipped Questions */}
          <div className="bg-gradient-to-br from-yellow-50 to-amber-50 rounded-2xl p-6 shadow-lg border border-yellow-100">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-yellow-500 rounded-xl p-3">
                <SkipForward className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-yellow-700">{skippedQuestions}</p>
                <p className="text-sm text-yellow-600">Skipped</p>
              </div>
            </div>
            <div className="w-full bg-yellow-200 rounded-full h-2">
              <div 
                className="bg-yellow-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(skippedQuestions / totalQuestions) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Performance Message */}
        <div className="mb-12">
          {latestQuiz.percentage >= 80 && (
            <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl p-8 text-white shadow-lg">
              <div className="flex items-center">
                <div className="bg-white/20 rounded-full p-3 mr-4">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold mb-2">Outstanding Performance! 🎉</h3>
                  <p className="text-green-50">You have demonstrated excellent mastery of the material. Keep up the fantastic work!</p>
                </div>
              </div>
            </div>
          )}
          {latestQuiz.percentage >= 60 && latestQuiz.percentage < 80 && (
            <div className="bg-gradient-to-r from-yellow-500 to-amber-600 rounded-2xl p-8 text-white shadow-lg">
              <div className="flex items-center">
                <div className="bg-white/20 rounded-full p-3 mr-4">
                  <Target className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold mb-2">Good Progress! 👍</h3>
                  <p className="text-yellow-50">You're on the right track. Review the areas you missed to reach excellence.</p>
                </div>
              </div>
            </div>
          )}
          {latestQuiz.percentage < 60 && (
            <div className="bg-gradient-to-r from-red-500 to-rose-600 rounded-2xl p-8 text-white shadow-lg">
              <div className="flex items-center">
                <div className="bg-white/20 rounded-full p-3 mr-4">
                  <Brain className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold mb-2">Keep Learning! 📚</h3>
                  <p className="text-red-50">Great effort! Focus on understanding the concepts and try practicing more questions.</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Question Results Grid */}
        <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 mb-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Detailed Question Analysis</h3>
              <p className="text-gray-600">Review your performance on each question</p>
            </div>
            <div className="flex space-x-4 text-sm">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-500 rounded mr-2"></div>
                <span className="text-gray-600">Correct</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-red-500 rounded mr-2"></div>
                <span className="text-gray-600">Incorrect</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-yellow-500 rounded mr-2"></div>
                <span className="text-gray-600">Skipped</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
            {latestQuiz.results.map((result: any, index: number) => (
              <div
                key={`${result.questionId}-${index}`}
                className={`relative border-2 rounded-xl p-4 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer ${getStatusColor(result)}`}
              >
                {/* Status Icon */}
                <div className="absolute -top-2 -right-2">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    result.isSkipped ? 'bg-yellow-500' : result.isCorrect ? 'bg-green-500' : 'bg-red-500'
                  }`}>
                    {getStatusIcon(result)}
                  </div>
                </div>

                {/* Question Number */}
                <div className="text-center mb-3">
                  <div className="text-2xl font-bold text-gray-800 mb-1">Q{index + 1}</div>
                  <div className="text-xs font-medium text-gray-600">#{result.questionId.toString().slice(-4)}</div>
                </div>

                {/* Topic */}
                <div className="mb-3">
                  <p className="text-xs font-medium text-gray-700 truncate" title={result.topic}>
                    {result.topic}
                  </p>
                </div>

                {/* Status and Time */}
                <div className="space-y-2">
                  <div className="text-center">
                    <span className={`text-xs font-bold px-2 py-1 rounded-full ${
                      result.isSkipped ? 'bg-yellow-100 text-yellow-800' : 
                      result.isCorrect ? 'bg-green-100 text-green-800' : 
                      'bg-red-100 text-red-800'
                    }`}>
                      {getStatusText(result)}
                    </span>
                  </div>
                  <div className="flex items-center justify-center text-xs text-gray-600">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>{Math.round(result.timeUsed)}s</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
          <button
            onClick={() => startQuiz(selectedCategory)}
            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 font-semibold"
          >
            Retake Quiz
          </button>
          <button
            onClick={resetQuiz}
            className="px-8 py-4 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-xl hover:from-gray-700 hover:to-gray-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 font-semibold"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
};

export default Results;