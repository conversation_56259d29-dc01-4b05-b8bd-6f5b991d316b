'use client';

import React, { useState, useEffect } from 'react';
import Dashboard from './components/Dashboard';
import Quiz from './components/Quiz';
import Results from './components/Results';
import DetailModal from './components/DetailModal';

export default function MBAMCQApp() {
  const [currentView, setCurrentView] = useState('dashboard');
  const [selectedCategory, setSelectedCategory] = useState('business-fundamentals');
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [questionIndex, setQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [quizResults, setQuizResults] = useState([]);
  const [quizHistory, setQuizHistory] = useState([]);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [modalQuestion, setModalQuestion] = useState(null);

  // Load history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('mba-mcq-history');
    if (savedHistory) {
      setQuizHistory(JSON.parse(savedHistory));
    }
  }, []);

  const resetQuiz = () => {
    setCurrentView('dashboard');
    setCurrentQuestion(null);
    setQuestionIndex(0);
    setSelectedAnswer(null);
    setShowResult(false);
    setTimeLeft(0);
    setIsActive(false);
    setQuizResults([]);
    setShowDetailModal(false);
    setModalQuestion(null);
  };

  const openDetailModal = (question) => {
    setModalQuestion(question);
    setShowDetailModal(true);
  };

  const closeDetailModal = () => {
    setShowDetailModal(false);
    setModalQuestion(null);
  };

  // Shared props object to avoid prop drilling
  const sharedProps = {
    currentView,
    setCurrentView,
    selectedCategory,
    setSelectedCategory,
    currentQuestion,
    setCurrentQuestion,
    questionIndex,
    setQuestionIndex,
    selectedAnswer,
    setSelectedAnswer,
    showResult,
    setShowResult,
    timeLeft,
    setTimeLeft,
    isActive,
    setIsActive,
    quizResults,
    setQuizResults,
    quizHistory,
    setQuizHistory,
    resetQuiz,
    openDetailModal,
    closeDetailModal,
    showDetailModal,
    modalQuestion
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {currentView === 'dashboard' && <Dashboard {...sharedProps} />}
      {currentView === 'quiz' && <Quiz {...sharedProps} />}
      {currentView === 'results' && <Results {...sharedProps} />}
      <DetailModal 
        showModal={showDetailModal}
        question={modalQuestion}
        onClose={closeDetailModal}
      />
    </div>
  );
}