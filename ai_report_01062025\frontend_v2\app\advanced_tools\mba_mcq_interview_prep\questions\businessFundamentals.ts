export interface MCQQuestion {
    id: number;
    category: string;
    topic: string;  // Added topic field
    difficulty: 'Easy' | 'Medium' | 'Hard';
    question: string;
    options: string[];
    correct: number;
    explanation: string;
    detailedExplanation: {
      concept: string;
      whyCorrect: string;
      whyOthersWrong: Record<string, string>;
      examples: string[];
      applications: string;
    };
    timeLimit: number;
  }
  
  export const businessFundamentalsQuestions: MCQQuestion[] = [
    {
        id: 12834567,
        category: 'Strategy',
        topic: 'Strategic Analysis',
        difficulty: 'Easy',
        question: "What is the primary purpose of conducting a SWOT analysis?",
        options: [
          "To determine the company's stock price",
          "To identify internal strengths/weaknesses and external opportunities/threats",
          "To calculate return on investment",
          "To analyze competitor pricing strategies"
        ],
        correct: 1,
        explanation: "SWOT analysis helps organizations understand their internal capabilities (Strengths & Weaknesses) and external environment (Opportunities & Threats) to make strategic decisions.",
        detailedExplanation: {
          concept: "SWOT Analysis is a strategic planning framework that evaluates four key dimensions of a business or project. It's one of the most fundamental tools in strategic management and is widely used across industries for decision-making.",
          whyCorrect: "Option B is correct because SWOT specifically focuses on internal factors (Strengths and Weaknesses that the organization can control) and external factors (Opportunities and Threats from the environment). This dual perspective helps organizations understand their current position and develop strategies accordingly.",
          whyOthersWrong: {
            "A": "Stock price determination requires financial valuation methods like DCF, comparable analysis, or market multiples - not SWOT analysis.",
            "C": "ROI calculation is a financial metric computed using investment costs and returns, completely unrelated to SWOT framework.",
            "D": "Competitor pricing analysis is just one small component that might fall under 'Threats' in SWOT, but it's not the primary purpose."
          },
          examples: [
            "Tesla's SWOT: Strengths (innovation, brand), Weaknesses (production challenges), Opportunities (EV market growth), Threats (traditional automakers entering EV space)",
            "A startup might identify: Strengths (agile team), Weaknesses (limited funding), Opportunities (market gap), Threats (established competitors)",
            "Retail chain: Strengths (locations), Weaknesses (outdated systems), Opportunities (e-commerce), Threats (Amazon competition)"
          ],
          applications: "SWOT is used in business strategy, product launches, market entry decisions, competitive analysis, and personal career planning."
        },
        timeLimit: 90
      },
      {
        id: 45789123,
        category: 'Marketing',
        topic: 'Portfolio Management',
        difficulty: 'Medium',
        question: "In the BCG Growth-Share Matrix, what characterizes a 'Cash Cow'?",
        options: [
          "High market growth, low market share",
          "High market growth, high market share",
          "Low market growth, high market share",
          "Low market growth, low market share"
        ],
        correct: 2,
        explanation: "Cash Cows have high market share in low-growth markets. They generate substantial cash flow that can fund other business units.",
        detailedExplanation: {
          concept: "The BCG Growth-Share Matrix is a strategic planning tool developed by Boston Consulting Group that categorizes business units based on market growth rate (y-axis) and relative market share (x-axis). It helps companies allocate resources across their portfolio.",
          whyCorrect: "Cash Cows are characterized by high market share in low-growth markets. They generate more cash than they consume because of their strong position in mature markets. These business units typically have low costs due to economies of scale and experience curve effects.",
          whyOthersWrong: {
            "A": "This describes Question Marks (or Problem Children) - high growth, low share businesses that require significant investment.",
            "B": "This describes Stars - high growth, high share businesses that are future Cash Cows but currently require investment to maintain position.",
            "D": "This describes Dogs - low growth, low share businesses that typically generate little cash and may be candidates for divestiture."
          },
          examples: [
            "Coca-Cola's core soda business in mature markets - dominant share but low growth",
            "Microsoft's Windows OS in developed markets - high share in a mature, slow-growing market",
            "Apple's iPhone in saturated markets like US/Europe - generates massive cash flow despite market maturity"
          ],
          applications: "Companies use BCG Matrix for portfolio management, resource allocation decisions, and strategic planning. Cash from Cash Cows funds investment in Question Marks and Stars."
        },
        timeLimit: 120
      },
      {
        id: 78956234,
        category: 'Operations',
        topic: 'Supply Chain Management',
        difficulty: 'Hard',
        question: "What is the key principle behind Just-In-Time (JIT) inventory management?",
        options: [
          "Minimize inventory costs by reducing waste and storage",
          "Maximize inventory to prevent stockouts",
          "Balance inventory costs with customer demand",
          "Automate inventory tracking systems"
        ],
        correct: 0,
        explanation: "JIT focuses on producing and receiving goods only when needed, minimizing inventory holding costs and reducing waste.",
        detailedExplanation: {
          concept: "Just-In-Time (JIT) is a lean manufacturing philosophy that aims to improve efficiency by producing goods only when they are needed, in the quantity needed, and when needed. It originated from Toyota's Production System and focuses on eliminating waste (muda in Japanese).",
          whyCorrect: "Option A correctly captures JIT's core principle: minimize inventory costs by reducing waste and storage. JIT eliminates the waste of overproduction, waiting time, unnecessary inventory, and storage costs. It synchronizes production with demand.",
          whyOthersWrong: {
            "B": "This contradicts JIT philosophy - maximizing inventory increases holding costs and ties up working capital.",
            "C": "While JIT does balance supply with demand, the primary focus is waste elimination, not just balancing costs with demand.",
            "D": "Automation is a tool that can support JIT, but it's not the key principle - JIT is about timing and waste reduction."
          },
          examples: [
            "Toyota assembles cars based on actual orders, receiving parts from suppliers just hours before assembly",
            "Dell's build-to-order model where computers are assembled only after customer orders are received",
            "Grocery stores receiving fresh produce deliveries multiple times per day rather than storing large quantities"
          ],
          applications: "JIT requires strong supplier relationships, reliable transportation, quality processes, and flexible manufacturing systems. Benefits include reduced inventory costs, improved quality, and faster response to market changes."
        },
        timeLimit: 100
      },
      {
        id: 63847291,
        category: 'Leadership',
        topic: 'Competitive Analysis',
        difficulty: 'Medium',
        question: "According to Porter's Five Forces, which factor directly affects industry profitability?",
        options: [
          "Company's internal culture",
          "Bargaining power of suppliers",
          "Employee satisfaction levels",
          "Marketing budget allocation"
        ],
        correct: 1,
        explanation: "Porter's Five Forces analyzes external industry factors that affect profitability, including supplier bargaining power.",
        detailedExplanation: {
          concept: "Porter's Five Forces is a framework for analyzing the competitive environment and industry attractiveness. It examines five external forces that shape competition: supplier power, buyer power, competitive rivalry, threat of substitutes, and barriers to entry.",
          whyCorrect: "Bargaining power of suppliers directly affects industry profitability. When suppliers have high bargaining power, they can charge higher prices or provide lower quality, reducing industry margins. This is an external force that impacts all competitors in the industry.",
          whyOthersWrong: {
            "A": "Internal culture is important for company performance but is not part of Porter's Five Forces, which focuses on external industry factors.",
            "C": "Employee satisfaction is an internal factor affecting individual company performance, not an industry-wide force.",
            "D": "Marketing budget is a strategic choice made by individual companies, not an external industry force."
          },
          examples: [
            "Oil industry: OPEC countries have high supplier power, affecting profitability of oil companies globally",
            "Smartphone industry: Qualcomm's chip dominance gives it supplier power over phone manufacturers",
            "Airline industry: Boeing and Airbus duopoly gives them significant supplier power over airlines"
          ],
          applications: "Used for industry analysis, competitive strategy formulation, investment decisions, and market entry/exit decisions. Helps companies understand profit potential and competitive dynamics."
        },
        timeLimit: 110
      },
      {
        id: 11223344,
        category: 'Finance',
        topic: 'Break-even Analysis',
        difficulty: 'Easy',
        question: "In break-even analysis, the break-even point is defined as the level of sales at which:",
        options: [
          "Total revenue equals total costs",
          "Fixed costs are zero",
          "Variable costs equal fixed costs",
          "Profit margin is maximized"
        ],
        correct: 0,
        explanation: "The break-even point occurs when total revenues cover total fixed and variable costs, resulting in zero profit.",
        detailedExplanation: {
          concept: "Break-even Analysis calculates the sales volume at which revenues equal all costs, helping managers understand the minimum performance needed to avoid losses.",
          whyCorrect: "Option A is correct because break-even is precisely the point where total revenue equals total costs (fixed plus variable), leading to zero profit.",
          whyOthersWrong: {
            "B": "Fixed costs cannot be zero in break-even; they are part of the equation revenue must cover.",
            "C": "Variable costs equaling fixed costs is not the definition of break-even; it's about total revenue equaling total costs.",
            "D": "Profit margin maximization occurs beyond break-even, not at the break-even point."
          },
          examples: [
            "A bakery calculates how many loaves it must sell monthly to cover rent and ingredient costs",
            "A software firm determines subscriptions needed to offset development and hosting expenses",
            "A retail store computes units sold per quarter to pay for salaries and utilities"
          ],
          applications: "Used in budgeting, pricing decisions, cost-volume-profit analysis, and financial planning."
        },
        timeLimit: 90
      },
      {
        id: 22334455,
        category: 'Strategy',
        topic: 'Business Model Canvas',
        difficulty: 'Medium',
        question: "Which block of the Business Model Canvas describes the network of suppliers and partners that make the business model work?",
        options: [
          "Key Activities",
          "Key Partners",
          "Value Proposition",
          "Customer Relationships"
        ],
        correct: 1,
        explanation: "The Key Partners block details alliances, suppliers, and partnerships crucial for operations.",
        detailedExplanation: {
          concept: "The Business Model Canvas is a strategic management tool that visualizes nine building blocks of a business model, including customers, value propositions, channels, partners, and more.",
          whyCorrect: "Option B is correct because Key Partners identifies the external companies or organizations that help the business operate (suppliers, alliances, joint ventures).",
          whyOthersWrong: {
            "A": "Key Activities are the major actions the company must perform to deliver its value proposition, not the partner network.",
            "C": "Value Proposition focuses on the products and services that deliver value to customers, not partnerships.",
            "D": "Customer Relationships covers how a company interacts with its customers, not its supplier or partner network."
          },
          examples: [
            "Airbnb's partnerships with property owners and cleaning services",
            "A car manufacturer’s alliances with parts suppliers and logistics providers",
            "A smartphone company’s agreements with chipset and OS providers"
          ],
          applications: "Helps businesses identify critical partnerships to reduce risk, acquire resources, and optimize operations."
        },
        timeLimit: 120
      },
      {
        id: 33445566,
        category: 'Strategy',
        topic: 'Ansoff Matrix',
        difficulty: 'Easy',
        question: "In the Ansoff Matrix, which strategy focuses on introducing existing products into new markets?",
        options: [
          "Market Penetration",
          "Product Development",
          "Market Development",
          "Diversification"
        ],
        correct: 2,
        explanation: "Market Development involves taking existing products to new markets.",
        detailedExplanation: {
          concept: "The Ansoff Matrix is a strategic tool that presents four growth strategies based on products (existing/new) and markets (existing/new).",
          whyCorrect: "Option C is correct because Market Development means selling existing products in new geographic or segment markets.",
          whyOthersWrong: {
            "A": "Market Penetration targets increasing sales in existing markets with existing products.",
            "B": "Product Development focuses on creating new products for existing markets.",
            "D": "Diversification involves new products in new markets."
          },
          examples: [
            "A coffee brand launching in a new country",
            "A software firm selling its existing app to a different industry segment",
            "A fast-food chain opening outlets in untapped regions"
          ],
          applications: "Used for strategic planning to identify growth opportunities with minimal product change."
        },
        timeLimit: 90
      },
      {
        id: 44556677,
        category: 'Strategy',
        topic: 'Balanced Scorecard',
        difficulty: 'Medium',
        question: "Which perspective of the Balanced Scorecard focuses on customer satisfaction and market share?",
        options: [
          "Financial",
          "Internal Process",
          "Customer",
          "Learning and Growth"
        ],
        correct: 2,
        explanation: "The Customer perspective monitors metrics related to customer satisfaction, retention, and market share.",
        detailedExplanation: {
          concept: "The Balanced Scorecard is a performance management framework that examines four perspectives: Financial, Customer, Internal Process, and Learning & Growth.",
          whyCorrect: "Option C is correct because the Customer perspective tracks how well the company serves its target market.",
          whyOthersWrong: {
            "A": "Financial measures profitability, ROI, and revenue growth, not customer-centric metrics.",
            "B": "Internal Process focuses on operational efficiency and process improvements.",
            "D": "Learning and Growth covers employee skills, culture, and innovation capacity."
          },
          examples: [
            "Customer satisfaction scores and Net Promoter Scores (NPS)",
            "Market share percentage in key segments",
            "Customer retention and churn rates"
          ],
          applications: "Helps organizations align operations with customer needs and measure performance beyond just financials."
        },
        timeLimit: 120
      },
      {
        id: 55667788,
        category: 'Marketing',
        topic: 'Value Proposition',
        difficulty: 'Easy',
        question: "A value proposition primarily communicates:",
        options: [
          "How a product solves customer problems",
          "The company's mission statement",
          "Employee roles",
          "Supplier agreements"
        ],
        correct: 0,
        explanation: "A value proposition conveys how a product or service addresses customer needs and creates value.",
        detailedExplanation: {
          concept: "A value proposition is a clear statement that explains how a product solves customer problems, delivers specific benefits, and why customers should choose it over alternatives.",
          whyCorrect: "Option A is correct because the value proposition focuses on customer benefits and problem solving.",
          whyOthersWrong: {
            "B": "Mission statements express the organization's purpose, not detailed customer benefits.",
            "C": "Employee roles relate to internal HR practices, not the external customer promise.",
            "D": "Supplier agreements are part of operations, not the customer-facing value proposition."
          },
          examples: [
            "Slack’s proposition: ‘Be more productive at work with less effort.’",
            "Uber’s proposition: ‘Tap the app, get a ride.’",
            "Dropbox’s proposition: ‘Your files, anywhere.’"
          ],
          applications: "Used in marketing communications, pitch decks, website headlines, and sales collateral."
        },
        timeLimit: 90
      },
      {
        id: 66778899,
        category: 'Marketing',
        topic: 'Customer Segmentation',
        difficulty: 'Hard',
        question: "Which segmentation method divides the market based on customers’ beliefs, values, and lifestyles?",
        options: [
          "Demographic Segmentation",
          "Geographic Segmentation",
          "Psychographic Segmentation",
          "Behavioral Segmentation"
        ],
        correct: 2,
        explanation: "Psychographic segmentation groups customers by lifestyle, values, personality traits, and social class.",
        detailedExplanation: {
          concept: "Psychographic segmentation categorizes consumers based on psychological attributes like interests, attitudes, and values to tailor marketing strategies.",
          whyCorrect: "Option C is correct because psychographic variables include beliefs, values, and lifestyle factors.",
          whyOthersWrong: {
            "A": "Demographic segmentation uses age, gender, income, etc., not psychological traits.",
            "B": "Geographic segmentation divides markets by location, not by lifestyle or values.",
            "D": "Behavioral segmentation focuses on purchase behavior, usage rate, and loyalty."
          },
          examples: [
            "Fitness brand targeting health-conscious lifestyle enthusiasts",
            "Luxury car maker focusing on status-driven consumers",
            "Eco-friendly products aimed at environmentally conscious buyers"
          ],
          applications: "Helps companies create personalized marketing campaigns, product positioning, and customer journeys."
        },
        timeLimit: 150
      },

      {
        id: 77889900,
        category: 'Organization',
        topic: 'Organizational Structure',
        difficulty: 'Medium',
        question: "Which organizational structure groups employees by product lines, each with its own functional specialists?",
        options: [
          "Functional structure",
          "Divisional structure",
          "Matrix structure",
          "Network structure"
        ],
        correct: 1,
        explanation: "A divisional structure organizes the company into semi-autonomous units based on products, markets, or geographies, each with dedicated functions.",
        detailedExplanation: {
          concept: "Divisional structure (or product structure) creates units based on product lines or regions, allowing focused strategies and accountability for each division.",
          whyCorrect: "Option B is correct because divisional structures allocate full functional teams to each product line, enhancing responsiveness and specialization.",
          whyOthersWrong: {
            "A": "Functional structure groups by job functions (e.g., marketing, finance) across the organization, not by product lines.",
            "C": "Matrix structure blends functional and divisional, creating dual reporting lines rather than purely by product.",
            "D": "Network structure relies on partnerships and outsourcing, not organizing internal functional specialists by product."
          },
          examples: [
            "General Electric’s aviation, healthcare, and renewable energy divisions",
            "Sony’s electronics, entertainment, and financial services divisions",
            "PepsiCo’s beverage and snack food divisions"
          ],
          applications: "Useful for large, diversified firms needing clear accountability for distinct product lines or markets."
        },
        timeLimit: 100
      },
      {
        id: ********,
        category: 'HR',
        topic: 'Motivation Theories',
        difficulty: 'Easy',
        question: "According to Maslow's Hierarchy of Needs, which need must be satisfied before esteem needs?",
        options: [
          "Physiological needs",
          "Safety needs",
          "Belonging/social needs",
          "Self-actualization needs"
        ],
        correct: 2,
        explanation: "Belongingness and social needs (friendship, family, community) precede esteem needs in Maslow’s pyramid.",
        detailedExplanation: {
          concept: "Maslow’s model arranges human needs into five levels: physiological, safety, belonging, esteem, and self-actualization.",
          whyCorrect: "Option C is correct because social needs (love, belonging) come after basic and safety needs, before esteem.",
          whyOthersWrong: {
            "A": "Physiological needs (food, water) are the most basic and must be met first, two levels below esteem.",
            "B": "Safety needs (security, stability) follow physiological but still precede social needs.",
            "D": "Self-actualization is the highest level, coming after esteem needs."
          },
          examples: [
            "An employee seeks peer acceptance before seeking recognition and promotions.",
            "Team-building fosters social bonds, enabling motivation for esteem-based incentives."
          ],
          applications: "Managers use this hierarchy to design benefit packages and engagement programs that address foundational needs first."
        },
        timeLimit: 90
      },
      {
        id: 99001122,
        category: 'HR',
        topic: 'Herzberg’s Two-Factor Theory',
        difficulty: 'Medium',
        question: "Which of the following is considered a hygiene factor in Herzberg's Two-Factor Theory?",
        options: [
          "Challenging work",
          "Recognition",
          "Salary",
          "Achievement"
        ],
        correct: 2,
        explanation: "Salary is a hygiene factor; its absence causes dissatisfaction but presence alone doesn’t motivate.",
        detailedExplanation: {
          concept: "Herzberg divides job factors into hygiene (prevent dissatisfaction) and motivators (drive satisfaction).",
          whyCorrect: "Option C is correct because salary, benefits, and policies are hygiene factors that must be adequate to avoid employee dissatisfaction.",
          whyOthersWrong: {
            "A": "Challenging work is a motivator that enhances satisfaction and performance.",
            "B": "Recognition is a motivator factor linked to achievement and growth.",
            "D": "Achievement is a motivator that fulfills growth and advancement needs."
          },
          examples: [
            "Competitive pay packages reduce turnover but don’t boost engagement alone.",
            "Upgrading office facilities prevents complaints but doesn’t increase job enthusiasm."
          ],
          applications: "Organizations must ensure hygiene factors are met before implementing motivators like career development programs."
        },
        timeLimit: 100
      },
      {
        id: ********,
        category: 'Ethics & Governance',
        topic: 'Corporate Governance',
        difficulty: 'Medium',
        question: "Which principle of corporate governance ensures that board decisions reflect the interests of all stakeholders?",
        options: [
          "Transparency",
          "Accountability",
          "Fairness",
          "Responsibility"
        ],
        correct: 2,
        explanation: "Fairness requires impartial treatment of all stakeholders, ensuring equitable consideration in decisions.",
        detailedExplanation: {
          concept: "Corporate governance principles (transparency, accountability, fairness, responsibility) guide ethical and effective board conduct.",
          whyCorrect: "Option C is correct because fairness emphasizes equitable treatment of shareholders, employees, suppliers, and communities.",
          whyOthersWrong: {
            "A": "Transparency relates to openness in disclosures, not equitable treatment.",
            "B": "Accountability focuses on being answerable for actions, not on balancing stakeholder interests.",
            "D": "Responsibility deals with meeting obligations, distinct from fairness in stakeholder treatment."
          },
          examples: [
            "Including minority shareholders in major decision processes.",
            "Ensuring employee voices are heard in strategic planning."
          ],
          applications: "Boards adopt fairness policies to mitigate conflicts and uphold reputation, enhancing long-term value."
        },
        timeLimit: 120
      },
      {
        id: ********,
        category: 'Change Management',
        topic: 'Kotter’s 8-Step Model',
        difficulty: 'Easy',
        question: "What is the first step in Kotter’s 8-Step Change Model?",
        options: [
          "Create a sense of urgency",
          "Form a powerful coalition",
          "Develop a vision and strategy",
          "Communicate the vision"
        ],
        correct: 0,
        explanation: "Creating urgency mobilizes stakeholders and builds momentum for change efforts.",
        detailedExplanation: {
          concept: "Kotter’s model provides an eight-step roadmap for successful organizational change, starting with urgency.",
          whyCorrect: "Option A is correct because establishing urgency highlights the need for change and motivates action.",
          whyOthersWrong: {
            "B": "Forming a coalition is step two, after urgency is recognized.",
            "C": "Vision development is step three, guided by the coalition.",
            "D": "Communicating the vision is step four, ensuring buy-in."
          },
          examples: [
            "Highlighting declining sales to rally the team around a transformation initiative.",
            "Using market data to show risks of status quo."
          ],
          applications: "Leaders use urgency to overcome inertia and secure early stakeholder support."
        },
        timeLimit: 100
      },
      {
        id: 13141516,
        category: 'Entrepreneurship',
        topic: 'Lean Startup',
        difficulty: 'Medium',
        question: "In Lean Startup methodology, what is the primary goal of the 'Build-Measure-Learn' loop?",
        options: [
          "To develop marketing strategies",
          "To validate hypotheses with minimal waste",
          "To maximize features before launch",
          "To create a detailed business plan"
        ],
        correct: 1,
        explanation: "The loop aims to test assumptions quickly with minimal resources, learning to iterate effectively.",
        detailedExplanation: {
          concept: "Build-Measure-Learn is the core feedback loop in Lean Startup, driving iterative product development based on validated learning.",
          whyCorrect: "Option B is correct because the methodology prioritizes hypothesis testing and learning over extensive upfront investment. ",
          whyOthersWrong: {
            "A": "Marketing strategy is secondary; the focus is on product-market fit through experimentation.",
            "C": "Feature maximization contradicts Lean Startup, which favors minimal viable features.",
            "D": "Detailed plans emerge from learning but are not the loop’s primary goal."
          },
          examples: [
            "Launching an MVP app to test user engagement before full development.",
            "Running A/B tests on pricing to gauge willingness to pay."        
          ],
          applications: "Startups and intrapreneurs use this loop to reduce risk and optimize product-market fit."
        },
        timeLimit: 110
      },
      {
        id: 14151617,
        category: 'Marketing',
        topic: 'Pricing Strategy',
        difficulty: 'Easy',
        question: "Value-based pricing sets prices primarily based on:",
        options: [
          "Production costs",
          "Competitor prices",
          "Perceived customer value",
          "Distributor margins"
        ],
        correct: 2,
        explanation: "Value-based pricing aligns price with the value customers perceive and are willing to pay.",
        detailedExplanation: {
          concept: "Value-based pricing focuses on customer-perceived benefits rather than internal cost structures.",
          whyCorrect: "Option C is correct because prices reflect the value delivered to customers, maximizing willingness to pay.",
          whyOthersWrong: {
            "A": "Cost-based pricing uses production costs plus markup, not customer value.",
            "B": "Competition-based pricing benchmarks against rivals’ prices.",
            "D": "Distributor margin considerations are part of channel strategy, not primary pricing driver."
          },
          examples: [
            "Luxury brands charging premiums for status and quality.",
            "SaaS platforms tiered by perceived feature value."
          ],
          applications: "Used for premium products, innovative offerings, and segmented pricing models."
        },
        timeLimit: 90
      },
      {
        id: 15161718,
        category: 'Marketing',
        topic: 'Distribution Channels',
        difficulty: 'Easy',
        question: "Which channel involves selling directly from manufacturer to end customers without intermediaries?",
        options: [
          "Direct channel",
          "Retail channel",
          "Wholesale channel",
          "Hybrid channel"
        ],
        correct: 0,
        explanation: "Direct channel cuts out intermediaries, enabling manufacturers to sell straight to consumers.",
        detailedExplanation: {
          concept: "Distribution channels define the path products take from producer to consumer; direct channels have no middlemen.",
          whyCorrect: "Option A is correct because direct selling bypasses retailers and wholesalers, such as D2C e-commerce models.",
          whyOthersWrong: {
            "B": "Retail channels use stores as intermediaries.",
            "C": "Wholesale channels distribute through bulk resellers and distributors.",
            "D": "Hybrid channels combine direct and indirect paths."
          },
          examples: [
            "Apple’s online store selling iPhones directly to customers.",
            "Farmers’ markets where producers sell straight to buyers."        
          ],
          applications: "Direct channels increase margins and customer data but require investment in sales infrastructure."
        },
        timeLimit: 90
      },
      {
        id: 16171819,
        category: 'Management',
        topic: 'Agile Methodologies',
        difficulty: 'Medium',
        question: "In Agile project management, which ceremony focuses on reflecting on the past sprint to improve the next one?",
        options: [
          "Sprint Planning",
          "Daily Stand-up",
          "Sprint Review",
          "Sprint Retrospective"
        ],
        correct: 3,
        explanation: "Sprint Retrospective gathers the team to discuss successes and improvements for the upcoming sprint.",
        detailedExplanation: {
          concept: "Agile frameworks like Scrum include ceremonies to plan, execute, review, and learn from sprints.",
          whyCorrect: "Option D is correct because retrospectives specifically aim at continuous improvement by analyzing the last cycle.",
          whyOthersWrong: {
            "A": "Sprint Planning sets the work for the sprint, not retrospective learning.",
            "B": "Daily Stand-ups synchronize daily progress, not deep reflection.",
            "C": "Sprint Review inspects delivered increment with stakeholders, not internal process improvement."
          },
          examples: [
            "Team identifies blockers and creates action items in the retrospective.",
            "Discussing process adjustments to reduce defects next sprint."        
          ],
          applications: "Retrospectives drive Agile maturity by embedding feedback loops into team processes."
        },
        timeLimit: 100
      },
      {
        id: 17181920,
        category: 'Strategy',
        topic: 'Stakeholder Management',
        difficulty: 'Medium',
        question: "Which technique prioritizes stakeholders based on their power and interest?",
        options: [
          "RACI matrix",
          "Stakeholder salience model",
          "Power-interest grid",
          "SWOT analysis"
        ],
        correct: 2,
        explanation: "The Power-Interest Grid maps stakeholders by their level of power over and interest in the project.",
        detailedExplanation: {
          concept: "Stakeholder analysis techniques identify and categorize individuals or groups to inform engagement strategies.",
          whyCorrect: "Option C is correct because the power-interest grid visually plots stakeholders to determine management approaches.",
          whyOthersWrong: {
            "A": "RACI matrix clarifies roles and responsibilities, not prioritization by power or interest.",
            "B": "Stakeholder salience considers power, legitimacy, and urgency, but not specifically interest dimension.",
            "D": "SWOT analyzes internal/external factors, not stakeholder attributes."
          },
          examples: [
            "Identifying key sponsors (high power, high interest) for frequent engagement.",
            "Monitoring regulators (high power, low interest) periodically."
          ],
          applications: "Helps project managers allocate resources for stakeholder communication and risk mitigation."    
        },
        timeLimit: 110
      },
      {
        id: 18192021,
        category: 'Strategy',
        topic: 'PESTEL Analysis',
        difficulty: 'Easy',
        question: "What is the primary purpose of conducting a PESTEL analysis?",
        options: [
          "To evaluate internal strengths and weaknesses",
          "To assess macro-environmental factors influencing an organization",
          "To calculate product profitability",
          "To analyze competitor strategies"
        ],
        correct: 1,
        explanation: "PESTEL analysis helps organizations scan political, economic, social, technological, environmental, and legal factors in the macro environment.",
        detailedExplanation: {
          concept: "PESTEL (or PESTLE) analysis is a strategic tool used to identify and analyze external macro-environmental factors that can impact an organization’s performance and decision-making.",
          whyCorrect: "Option B is correct because PESTEL specifically examines external factors—Political, Economic, Social, Technological, Environmental, and Legal—that lie outside company control but affect strategy.",
          whyOthersWrong: {
            "A": "Internal strengths and weaknesses are assessed via SWOT, not PESTEL.",
            "C": "Product profitability calculations involve financial analysis, not macro-environmental scanning.",
            "D": "Competitor strategies are evaluated through competitive analysis frameworks like Porter’s Five Forces."
          },
          examples: [
            "Evaluating regulatory changes and their impact on manufacturing costs.",
            "Assessing economic downturn effects on consumer spending.",
            "Understanding social trends towards sustainability influencing product design."
          ],
          applications: "PESTEL guides strategic planning, market entry decisions, risk assessment, and long-term forecasting."
        },
        timeLimit: 90
      },
      {
        id: 19202122,
        category: 'Strategy',
        topic: 'Blue Ocean Strategy',
        difficulty: 'Medium',
        question: "Which action is central to a Blue Ocean strategy?",
        options: [
          "Competing in existing markets with price wars",
          "Creating uncontested market space through innovation",
          "Focusing on cost leadership in established industries",
          "Building on large advertising budgets"
        ],
        correct: 1,
        explanation: "Blue Ocean strategy emphasizes creating new, uncontested markets rather than competing in existing ones.",
        detailedExplanation: {
          concept: "Developed by Kim & Mauborgne, Blue Ocean strategy encourages firms to pursue differentiation and low cost to open new value frontiers, making competition irrelevant.",
          whyCorrect: "Option B is correct because the strategy revolves around value innovation to tap markets with little or no competition.",
          whyOthersWrong: {
            "A": "Price wars occur in red oceans; Blue Ocean avoids direct competition.",
            "C": "Cost leadership in existing markets is a red ocean approach.",
            "D": "Advertising budgets support tactics, but the core is market creation, not promotion alone."
          },
          examples: [
            "Cirque du Soleil creating a hybrid of circus and theater.",
            "Nintendo Wii targeting casual gamers with motion controls."
          ],
          applications: "Used in corporate growth strategy, product development, and market innovation initiatives."
        },
        timeLimit: 100
      },
      {
        id: 20212223,
        category: 'Leadership',
        topic: 'Leadership Styles',
        difficulty: 'Medium',
        question: "Which leadership style is characterized by inspiring vision and intellectual stimulation?",
        options: [
          "Transactional leadership",
          "Autocratic leadership",
          "Transformational leadership",
          "Laissez-faire leadership"
        ],
        correct: 2,
        explanation: "Transformational leaders motivate followers by articulating a compelling vision and fostering innovation.",
        detailedExplanation: {
          concept: "Transformational leadership focuses on inspiring and elevating followers’ motivations and performance by fostering intellectual engagement and personal development.",
          whyCorrect: "Option C is correct because transformational leaders encourage creativity, challenge assumptions, and communicate a strong vision.",
          whyOthersWrong: {
            "A": "Transactional leadership relies on exchanges and rewards, not vision-driven inspiration.",
            "B": "Autocratic leaders impose decisions with little follower input.",
            "D": "Laissez-faire leaders provide minimal guidance and oversight."
          },
          examples: [
            "Steve Jobs inspiring Apple employees toward breakthrough products.",
            "Elon Musk motivating teams with ambitious visions for space and electric vehicles."
          ],
          applications: "Used to drive change initiatives, cultural transformation, and high-performance teams."
        },
        timeLimit: 110
      },
      {
        id: 21222324,
        category: 'Decision Making',
        topic: 'Rational Decision Model',
        difficulty: 'Medium',
        question: "Which step is not part of the classical rational decision-making model?",
        options: [
          "Define the problem",
          "Generate alternatives",
          "Analyze political implications",
          "Select the best alternative"
        ],
        correct: 2,
        explanation: "Analyzing political implications is an informal consideration, not a formal step in the rational decision process.",
        detailedExplanation: {
          concept: "The classical rational model involves identifying a problem, generating and evaluating alternatives, and choosing the optimal solution.",
          whyCorrect: "Option C is correct because the standard model focuses on logical analysis and comparisons, not political dynamics.",
          whyOthersWrong: {
            "A": "Problem definition is the first essential step.",
            "B": "Generating potential solutions is a core step.",
            "D": "Selecting the best alternative concludes the process."
          },
          examples: [
            "Selecting a software vendor by listing criteria and scoring options.",
            "Choosing a new supplier after analyzing cost and quality metrics."
          ],
          applications: "Used in strategic planning, project management, and operational decisions requiring structured analysis."
        },
        timeLimit: 100
      },
      {
        id: 22232425,
        category: 'Quality Management',
        topic: 'Total Quality Management',
        difficulty: 'Easy',
        question: "What is the core focus of Total Quality Management (TQM)?",
        options: [
          "Reducing marketing costs",
          "Continuous process and product improvement",
          "Maximizing short-term profits",
          "Implementing strict hierarchical controls"
        ],
        correct: 1,
        explanation: "TQM emphasizes ongoing improvement of processes, products, and services to enhance quality and customer satisfaction.",
        detailedExplanation: {
          concept: "TQM is a management approach that integrates quality principles across all operations, involving all employees in continuous improvement efforts.",
          whyCorrect: "Option B is correct because TQM’s hallmark is iterative refinement of processes to eliminate defects and meet customer expectations.",
          whyOthersWrong: {
            "A": "Marketing costs are not the primary target of TQM.",
            "C": "TQM balances long-term quality with profitability, not just short-term gains.",
            "D": "Hierarchical controls contradict TQM’s collaborative, cross-functional ethos."
          },
          examples: [
            "Toyota’s Kaizen events for incremental process improvements.",
            "Six Sigma projects reducing defect rates in manufacturing."
          ],
          applications: "Applied in manufacturing, service industries, healthcare, and any sector prioritizing quality-driven culture."
        },
        timeLimit: 90
      },
      {
        id: 23242526,
        category: 'Ethics & Governance',
        topic: 'Corporate Social Responsibility',
        difficulty: 'Medium',
        question: "Corporate Social Responsibility (CSR) initiatives primarily aim to:",
        options: [
          "Maximize shareholder dividends",
          "Integrate social and environmental concerns into business operations",
          "Reduce operational transparency",
          "Eliminate all risks"
        ],
        correct: 1,
        explanation: "CSR focuses on embedding social and environmental responsibility into company strategies and practices.",
        detailedExplanation: {
          concept: "CSR involves voluntary corporate efforts that go beyond compliance, addressing stakeholder interests and societal expectations.",
          whyCorrect: "Option B is correct because CSR aligns business goals with positive social and environmental outcomes."
          ,whyOthersWrong: {
            "A": "While shareholder returns matter, CSR emphasizes broader stakeholder value.",
            "C": "CSR promotes transparency, not reduction of disclosures.",
            "D": "Risk elimination is unrealistic; CSR aims at responsible risk management."
          },
          examples: [
            "Unilever’s Sustainable Living Plan reducing environmental footprint.",
            "Patagonia’s initiatives to use recycled materials and fair labor practices."
          ],
          applications: "Used in sustainability reporting, stakeholder engagement, and ethical branding."
        },
        timeLimit: 100
      },
      {
        id: 24252627,
        category: 'Marketing',
        topic: 'B2B vs B2C',
        difficulty: 'Easy',
        question: "Which of the following is typically true of B2B marketing compared to B2C?",
        options: [
          "Shorter sales cycles",
          "Larger customer bases",
          "Lower price points",
          "More personalized relationship management"
        ],
        correct: 3,
        explanation: "B2B marketing often involves tailored, relationship-driven approaches due to fewer, high-value customers.",
        detailedExplanation: {
          concept: "B2B marketing targets businesses and organizations, focusing on relationship building and customized solutions."
          ,whyCorrect: "Option D is correct because B2B purchases are complex, involving multiple stakeholders and long-term partnerships.",
          whyOthersWrong: {
            "A": "B2B cycles are typically longer, not shorter.",
            "B": "Customer bases in B2B are smaller but more concentrated.",
            "C": "Price points in B2B are often higher per transaction."
          },
          examples: [
            "Enterprise software vendors working closely with IT departments.",
            "Industrial equipment suppliers developing custom maintenance contracts."
          ],
          applications: "Guides channel strategies, content marketing, and account-based marketing programs."
        },
        timeLimit: 90
      },
      {
        id: ********,
        category: 'Strategy',
        topic: 'Economies of Scale',
        difficulty: 'Medium',
        question: "Economies of scale primarily help firms to:",
        options: [
          "Increase unit costs with higher output",
          "Decrease unit costs as production volume rises",
          "Stabilize prices regardless of volume",
          "Eliminate fixed costs entirely"
        ],
        correct: 1,
        explanation: "Economies of scale reduce average costs by spreading fixed costs over more units and improving operational efficiency.",
        detailedExplanation: {
          concept: "Economies of scale occur when cost per unit declines as total production increases due to fixed cost distribution and process optimizations.",
          whyCorrect: "Option B is correct because higher volumes allow firms to dilute fixed expenses and leverage bulk purchasing and specialization."
          ,whyOthersWrong: {
            "A": "Unit costs decrease, not increase.",
            "C": "Price stability is influenced by market factors, not directly by scale efficiencies.",
            "D": "Fixed costs remain, but their impact per unit diminishes."
          },
          examples: [
            "Automobile assembly lines producing thousands of cars daily to minimize per-car costs.",
            "Cloud service providers using large data centers to lower hosting costs per user."        
          ],
          applications: "Informs capacity planning, pricing strategy, and investment in automation."
        },
        timeLimit: 110
      },
      {
        id: 26272829,
        category: 'Performance Management',
        topic: 'KPI Definitions',
        difficulty: 'Easy',
        question: "What does KPI stand for in performance management?",
        options: [
          "Key Profit Allocation",
          "Knowledge Process Indicator",
          "Key Performance Indicator",
          "Kinetic Process Insight"
        ],
        correct: 2,
        explanation: "KPI stands for Key Performance Indicator—metrics used to measure success against objectives.",
        detailedExplanation: {
          concept: "KPIs are quantifiable measures that reflect critical success factors for organizations, departments, or processes.",
          whyCorrect: "Option C is correct because KPIs track performance against strategic goals using relevant metrics.",
          whyOthersWrong: {
            "A": "Profit allocation is not a common indicator acronym.",
            "B": "Knowledge Process Indicator is not standard terminology.",
            "D": "Kinetic Process Insight is incorrect and unrelated."
          },
          examples: [
            "Sales revenue growth rate.",
            "Customer churn rate.",
            "Average order fulfillment time."
          ],
          applications: "Used for dashboards, scorecards, and performance reviews."
        },
        timeLimit: 80
      },
      {
        id: 27282930,
        category: 'Negotiation',
        topic: 'BATNA',
        difficulty: 'Medium',
        question: "In negotiation theory, BATNA refers to:",
        options: [
          "Best Action To Negotiate Agreement",
          "Best Alternative To a Negotiated Agreement",
          "Baseline Analysis for Negotiation Approach",
          "Broad Agreement on Terms and Actions"
        ],
        correct: 1,
        explanation: "BATNA is the fallback option if negotiations fail, guiding leverage and reservation points.",
        detailedExplanation: {
          concept: "Introduced by Fisher and Ury, BATNA defines the best outcome a party can secure outside the current negotiation, influencing bargaining power.",
          whyCorrect: "Option B is correct because BATNA literally stands for Best Alternative To a Negotiated Agreement."
          ,whyOthersWrong: {
            "A": "Incorrect expansion lacking the alternative context.",
            "C": "Baseline analysis is not the recognized term.",
            "D": "Broad agreement refers to consensus, not fallback options."
          },
          examples: [
            "A buyer’s BATNA might be purchasing from another supplier if talks stall.",
            "An employee’s BATNA could be another job offer when salary negotiations lag."
          ],
          applications: "Helps negotiators assess walk-away points, set realistic goals, and strengthen bargaining positions."
        },
        timeLimit: 100
      },
      {
        id: 28293031,
        category: 'Digital Business',
        topic: 'Digital Transformation',
        difficulty: 'Medium',
        question: "Digital transformation primarily involves:",
        options: [
          "Replacing all legacy systems simultaneously",
          "Incrementally leveraging digital technologies to improve processes and business models",
          "Cutting IT budgets to reduce costs",
          "Outsourcing all IT functions"
        ],
        correct: 1,
        explanation: "Digital transformation is a continuous journey of embedding digital capabilities into operations and offerings.",
        detailedExplanation: {
          concept: "Digital transformation harnesses technologies like cloud, AI, and IoT to optimize processes, enhance customer experiences, and create new business models.",
          whyCorrect: "Option B is correct because effective transformation is iterative, aligning technology adoption with strategic goals."
          ,whyOthersWrong: {
            "A": "Big-bang replacements carry high risk; transformation is usually phased.",
            "C": "Reducing budgets alone doesn’t drive digital innovation.",
            "D": "Outsourcing can be part of a strategy but isn’t the essence of transformation."
          },
          examples: [
            "Banks implementing mobile apps and AI chatbots to improve service.",
            "Manufacturers using IoT sensors for predictive maintenance."        
          ],
          applications: "Guides enterprise digital roadmaps, change management, and technology investments."
        },
        timeLimit: 120
      }
  ];