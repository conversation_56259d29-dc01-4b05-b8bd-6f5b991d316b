import { MCQQuestion } from './businessFundamentals'; // Assuming this path is correct

export const consultingQuestions: MCQQuestion[] = [
  {
    id: 85739642,
    category: 'Frameworks',
    topic: 'Problem Diagnosis',
    difficulty: 'Medium',
    question: "When analyzing declining profitability, which should you examine FIRST?",
    options: [
      "Market share analysis",
      "Revenue and cost structure breakdown",
      "Competitive positioning",
      "Operational efficiency metrics"
    ],
    correct: 1,
    explanation: "Start with the profitability framework: Revenue (Price × Volume) and Costs (Fixed + Variable) to identify whether the issue is on the revenue or cost side.",
    detailedExplanation: {
      concept: "The profitability framework is arguably the most fundamental and widely used diagnostic tool in management consulting, particularly for case interviews and initial project scoping. It systematically dissects a company's profit into its two primary levers: Revenue and Costs. Revenue is further broken down into its drivers (typically Price per unit × Volume of units sold), and Costs are categorized into Fixed Costs (those that don't vary with production/sales volume, e.g., rent, salaries) and Variable Costs (those that do, e.g., raw materials, direct labor). The beauty of this framework lies in its MECE (Mutually Exclusive, Collectively Exhaustive) nature, ensuring a comprehensive yet structured approach to identifying the root causes of profit erosion or opportunities for profit enhancement. It serves as a roadmap for the initial phase of problem-solving.",
      whyCorrect: "Examining the 'Revenue and cost structure breakdown' is the paramount first step because the profitability equation (Profit = Revenue - Costs) is the foundational lens through which financial performance is understood. Before delving into external market dynamics (like market share or competitive positioning) or drilling down into specific internal operational metrics, a consultant must first pinpoint whether the profitability erosion stems from a decline in revenues, an escalation in costs, or a detrimental combination of both. This initial segmentation dictates all subsequent lines of inquiry. For instance, if revenues are identified as the primary issue, the next layer of investigation would be to understand if it's due to falling prices, decreasing sales volume, or an unfavorable mix. Conversely, if costs are the culprit, the analysis would then explore whether fixed costs have surged or variable costs per unit have increased. This top-down, structured approach ensures a methodical and efficient diagnosis, preventing premature jumps to conclusions or getting lost in less critical details.",
      whyOthersWrong: {
        "A": "Market share analysis is important but secondary - you first need to understand if the issue is revenue-driven, cost-driven, or both.",
        "C": "Competitive positioning matters, but you need to understand your own P&L structure before analyzing external competitive dynamics.",
        "D": "Operational efficiency is a subset of cost analysis - you should first understand the overall cost structure before diving into specific operational metrics."
      },
      examples: [
        "Airline facing profit decline: First check if revenue fell (lower prices or fewer passengers) or costs rose (fuel, labor, maintenance)",
        "Retail chain analysis: Start with revenue per store and cost per store before analyzing market share or competitive positioning",
        "SaaS company: Begin with revenue per customer and cost per customer acquisition before examining competitive dynamics"
      ],
      applications: "Used in consulting case interviews, business turnarounds, performance improvement projects, and strategic planning. Forms the foundation for most consulting engagements focused on financial performance."
    },
    timeLimit: 100
  },
  {
    id: 47283951,
    category: 'Case Interview',
    topic: 'Financial Analysis',
    difficulty: 'Hard',
    question: "A client's revenue declined 20% but profit declined 50%. What's the most likely cause?",
    options: [
      "Decrease in market share",
      "High fixed cost structure with operating leverage",
      "Increased competition",
      "Poor product quality"
    ],
    correct: 1,
    explanation: "When profit declines more than revenue, it indicates high operating leverage. Fixed costs remain constant while revenue drops, causing disproportionate profit decline.",
    detailedExplanation: {
      concept: "Operating leverage measures the sensitivity of a company’s operating income (or profit) to changes in its sales revenue. It arises when a company has a significant proportion of fixed costs in its cost structure relative to variable costs. A high degree of operating leverage means that a small percentage change in revenue will result in a larger percentage change in operating profit. While this can be beneficial when revenues are increasing (profits grow disproportionately faster), it becomes a significant risk when revenues are declining, as profits will fall disproportionately faster. The formula for Degree of Operating Leverage (DOL) is often expressed as Percentage Change in EBIT / Percentage Change in Sales, or Contribution Margin / EBIT.",
      whyCorrect: "A 'High fixed cost structure with operating leverage' is the most direct and quantitatively supported explanation for why profit would decline substantially more (50%) than revenue (20%). With high fixed costs, these costs remain constant regardless of the sales volume. When revenue drops by 20%, the company still has to cover the same absolute amount of fixed costs from a smaller revenue base. This means a much larger proportion of the revenue decline directly impacts the bottom line. The contribution margin (Revenue - Variable Costs) shrinks, and once fixed costs are subtracted, the profit erosion is amplified. The 2.5x amplification (50% profit decline / 20% revenue decline) is a clear indicator of significant operating leverage at play.",
      whyOthersWrong: {
        "A": "Market share decrease could cause revenue decline, but wouldn't explain why profit declined disproportionately more than revenue.",
        "C": "Increased competition might reduce revenue, but wouldn't necessarily create the 2.5x amplification effect on profit unless it specifically affected the cost structure.",
        "D": "Poor product quality might reduce demand and revenue, but wouldn't explain the disproportionate profit impact unless it led to significant cost increases."
      },
      examples: [
        "Airlines: High fixed costs (planes, gates, crew) mean a 20% drop in passengers can cause 50%+ profit decline",
        "Manufacturing: Factory lease, equipment depreciation are fixed - lower production volume doesn't reduce these costs",
        "SaaS companies: Development and infrastructure costs are largely fixed - customer churn hits profit harder than revenue"
      ],
      applications: "Critical for understanding business model risks, pricing decisions, capacity planning, and cost structure optimization. Key concept in financial analysis and valuation."
    },
    timeLimit: 120
  },
  {
    id: 92836475,
    category: 'Market Entry',
    topic: 'Strategic Planning',
    difficulty: 'Medium',
    question: "What's the FIRST factor to consider when entering a new market?",
    options: [
      "Competitive landscape",
      "Market size and attractiveness",
      "Company's competitive advantages and fit",
      "Regulatory requirements"
    ],
    correct: 1,
    explanation: "Before anything else, assess if the market is large enough and attractive enough to justify entry. If not, other factors become irrelevant.",
    detailedExplanation: {
      concept: "A structured market entry framework typically prioritizes understanding the 'prize' before evaluating the 'price of entry' or the 'ability to win.' Market size and attractiveness (which includes growth rate, profitability levels, structural factors like Porter's Five Forces, etc.) represent the potential opportunity. If the market is too small, shrinking, or structurally unattractive (e.g., hyper-competitive with low margins, high barriers to exit), then even strong company capabilities or a favorable regulatory environment might not justify entry. This acts as a critical initial filter in the decision-making process.",
      whyCorrect: "'Market size and attractiveness' must be the initial consideration because it establishes the fundamental viability and potential reward of the venture. A consultant must first confirm that the target market offers sufficient scale for the client to achieve its strategic and financial objectives (e.g., revenue targets, market share goals, profitability). If the market itself is deemed unattractive (e.g., too small, stagnant, highly fragmented with low profit pools, or facing imminent decline), then further analysis into competition, company fit, or regulations becomes a moot point. It’s about assessing whether there’s a 'game worth playing' before figuring out how to play it or if the company *can* play it.",
      whyOthersWrong: {
        "A": "Competitive landscape is crucial but secondary - even if competition is weak, you shouldn't enter a small or declining market.",
        "C": "Company fit is important but comes after establishing market attractiveness - your advantages are irrelevant in an unattractive market.",
        "D": "Regulatory requirements matter but are a feasibility check after determining if the market opportunity is worth the regulatory effort."
      },
      examples: [
        "Netflix entering international markets: First assessed market size (broadband penetration, disposable income) before analyzing local competitors",
        "Uber's expansion: Prioritized large cities with sufficient population density before analyzing taxi regulations or local competition",
        "Starbucks global expansion: First identified markets with growing middle class and coffee culture potential"
      ],
      applications: "Used in market entry strategy, geographic expansion, new product launches, and investment prioritization. Forms the foundation of most growth strategy consulting projects."
    },
    timeLimit: 110
  },
  {
    id: 58473926,
    category: 'Problem Solving',
    topic: 'Methodology',
    difficulty: 'Hard',
    question: "In a hypothesis-driven approach, what should you do FIRST?",
    options: [
      "Gather all available data",
      "Form a hypothesis about the most likely answer",
      "Interview all stakeholders",
      "Benchmark against competitors"
    ],
    correct: 1,
    explanation: "Hypothesis-driven problem solving starts with forming educated guesses about the answer, then testing these hypotheses efficiently rather than gathering data randomly.",
    detailedExplanation: {
      concept: "The hypothesis-driven approach, often associated with the scientific method and heavily utilized in management consulting (e.g., McKinsey's methodology), is a problem-solving technique that begins with formulating a potential answer or explanation (the hypothesis) early in the process. This initial hypothesis is based on available information, experience, logic, and preliminary analysis. Subsequent efforts are then focused on gathering specific data and conducting analyses designed to rigorously test, validate, refine, or refute this hypothesis. This contrasts with a more data-first or exploratory approach where one might collect vast amounts of data hoping patterns will emerge. The hypothesis-driven method prioritizes efficiency and focus.",
      whyCorrect: "In a truly hypothesis-driven approach, the FIRST action is to 'Form a hypothesis about the most likely answer.' This initial, educated guess provides a clear direction and scope for the ensuing investigation. It allows the consultant to be targeted in their data collection and analysis, focusing only on what's necessary to prove or disprove the primary hypothesis (and perhaps a few key alternatives). Without a guiding hypothesis, data gathering can become an overwhelming and inefficient 'boil the ocean' exercise. The hypothesis acts as a filter, helping to prioritize analyses and avoid wasting time on less relevant avenues. It's about making an informed conjecture and then seeking evidence to support or reject it, rather than collecting everything and then trying to find an answer.",
      whyOthersWrong: {
        "A": "Gathering all data first is inefficient and can lead to analysis paralysis - you should gather data to test specific hypotheses.",
        "C": "Interviewing stakeholders is valuable but should be guided by hypotheses about what you're trying to prove or disprove.",
        "D": "Benchmarking is a tool to test hypotheses, not the starting point - you should first hypothesize what you expect to find in benchmarks."
      },
      examples: [
        "Client's sales declining: Hypothesize it's due to pricing, product quality, or competition, then design tests for each",
        "Cost reduction project: Hypothesize largest opportunities are in procurement, operations, or overhead, then analyze accordingly",
        "Market entry decision: Hypothesize success factors, then gather data to test each hypothesis"
      ],
      applications: "Core methodology in management consulting, strategy development, root cause analysis, and business problem solving. Essential skill for case interviews and consulting work."
    },
    timeLimit: 130
  },
  {
    id: 29293041,
    category: 'Strategy',
    topic: 'Market Entry Timing',
    difficulty: 'Hard',
    question: "A SaaS firm debates early versus late market entry. Which advantage does a late entrant typically enjoy?",
    options: [
      "Higher brand recognition",
      "Learning from pioneers’ mistakes",
      "Establishing strong network effects first",
      "Securing lowest-cost resources"
    ],
    correct: 1,
    explanation: "Late entrants observe industry pioneers, avoiding missteps and optimizing value propositions.",
    detailedExplanation: {
      concept: "Market entry timing is a critical strategic decision. First-movers (pioneers) often aim to capture benefits like brand leadership, preemption of scarce resources, setting industry standards, and building switching costs or network effects. However, pioneering is risky and costly. Late entrants (followers or 'fast followers') can benefit from reduced uncertainty, lower market development costs, and the ability to learn from the successes and failures of early entrants. They can often enter with a more refined product or business model.",
      whyCorrect: "'Learning from pioneers’ mistakes' is a significant advantage for late entrants. Pioneers bear the costs of market education, R&D for unproven technologies, and navigating uncertain customer preferences. They might make errors in product design, pricing, channel strategy, or target market selection. Late entrants can observe these missteps, understand what works and what doesn't, and then enter the market with an improved value proposition, a more efficient operating model, or by targeting underserved niches identified from the pioneer's experience. This reduces their own R&D expenditure and market development risk.",
      whyOthersWrong: {
        "A": "Brand recognition often accrues to early entrants, not followers.",
        "C": "Network effects favor those who build scale first; late entrants struggle to catch up.",
        "D": "Lowest-cost resources depend on scale and supplier relationships typically established by pioneers."
      },
      examples: [
        "Google improved upon Yahoo’s search model by learning user behavior insights.",
        "Facebook entered social networking after MySpace’s missteps with privacy and UX."
      ],
      applications: "Guides timing decisions, allowing firms to choose imitation strategies or niche positioning post-pioneer learning."
    },
    timeLimit: 140
  },
  {
    id: 29293042,
    category: 'Strategy',
    topic: 'Strategic Group Mapping',
    difficulty: 'Medium',
    question: "On a strategic-group map of airlines (service level vs. network size), which white space is most promising?",
    options: [
      "High service, small network",
      "Low service, large network",
      "Medium service, medium network",
      "High service, large network"
    ],
    correct: 0,
    explanation: "High service with narrow routes enables premium niche focus without major network investment.",
    detailedExplanation: {
      concept: "Strategic group mapping is an analytical tool used to understand the competitive positioning of industry rivals. It involves plotting firms on a two-dimensional map where the axes represent key strategic dimensions (e.g., price, quality, geographic scope, service level, distribution channels). Firms with similar strategic approaches will cluster together into 'strategic groups.' 'White spaces' on the map are areas where no firms are currently positioned, potentially indicating an unexploited strategic opportunity or, conversely, an inherently unattractive position.",
      whyCorrect: "'High service, small network' represents a potentially promising white space or niche. This model allows an airline to focus on delivering a premium experience (high service) to a select group of customers on a limited number of routes (small network), often high-traffic business or luxury leisure routes. This avoids the massive capital expenditure and operational complexity of a large network, enabling a focus on quality and potentially higher margins from a less price-sensitive clientele. It differentiates from low-cost carriers (low service, potentially large network) and full-service network carriers (high service, large network).",
      whyOthersWrong: {
        "B": "Large network at low service duplicates low-cost majors and faces intense price-based competition.",
        "C": "Medium-medium is crowded by regional carriers and emerging low-cost players.",
        "D": "High-high is dominated by legacy global airlines with significant capital requirements."
      },
      examples: [
        "Boutique airlines like Surf Air offering premium shuttle services on specific corridors.",
        "Private jet fractional ownership models on limited routes."
      ],
      applications: "Helps airlines and service firms identify high-margin niches and invest in targeted capabilities."
    },
    timeLimit: 130
  },
  {
    id: 29293043,
    category: 'Strategy',
    topic: 'Diversification',
    difficulty: 'Hard',
    question: "Which form of diversification best leverages core competencies?",
    options: [
      "Horizontal diversification",
      "Vertical integration",
      "Conglomerate diversification",
      "Allied diversification"
    ],
    correct: 3,
    explanation: "Allied diversification expands into related markets where existing assets and capabilities apply.",
    detailedExplanation: {
      concept: "Diversification is a corporate strategy to enter into a new market or industry in which the business doesn't currently operate, while also creating a new product for that new market. Forms include: 1) Related/Concentric/Allied Diversification: Entering a new business activity that is linked to the company's existing business activities by common skills, technologies, or resources. 2) Unrelated/Conglomerate Diversification: Entering a new business activity that has no deliberate relationship with the company's existing activities. The primary aim of leveraging core competencies is to achieve synergy, where the combined entity is more valuable than the sum of its parts (2+2=5 effect).",
      whyCorrect: "'Allied diversification' (also known as related or concentric diversification) is the strategy that best leverages a company's existing core competencies. Core competencies are the unique strengths, skills, knowledge, technologies, or processes that a company excels at and that provide a competitive advantage. In allied diversification, the firm enters new markets or develops new products that are related to its current operations, allowing it to exploit these established competencies. This can lead to economies of scope, faster market entry, lower risk, and enhanced competitive advantage in the new area because the firm is not starting from scratch.",
      whyOthersWrong: {
        "A": "Horizontal diversification often refers to acquiring competitors or offering similar products to the same market, which is more about market share consolidation than leveraging core competencies into *new* related areas.",
        "B": "Vertical integration is expanding along the value chain (e.g., acquiring suppliers or distributors), which is about control and efficiency within the *current* industry, not diversification into new markets leveraging competencies in a broader sense.",
        "C": "Conglomerate diversification involves entering entirely unrelated businesses where existing core competencies typically have little applicability, relying more on financial management or general managerial skills rather than specific operational or technological competencies."
      },
      examples: [
        "Disney leveraging media content into theme parks and merchandise.",
        "Amazon extending e-commerce platform into cloud services with AWS."
      ],
      applications: "Guides corporate portfolio choices to build synergy, share capabilities, and optimize resource allocation."
    },
    timeLimit: 150
  },
  {
    id: 29293044,
    category: 'Strategy',
    topic: 'Scenario Planning',
    difficulty: 'Medium',
    question: "Which dimension is least useful in seismic scenario planning?",
    options: [
      "Regulatory shifts",
      "Technological breakthroughs",
      "Day-to-day competitor marketing tactics",
      "Macro-economic cycles"
    ],
    correct: 2,
    explanation: "Day-to-day tactics are volatile and don’t shape long-term strategic scenarios.",
    detailedExplanation: {
      concept: "Scenario planning is a strategic planning method organizations use to make flexible long-term plans. It involves identifying a set of key uncertainties or driving forces in the external environment, and then constructing several distinct, plausible future narratives (scenarios) based on how these uncertainties might unfold. The goal is not to predict the future, but to understand the range of possibilities and develop strategies that are robust across multiple scenarios or to identify signposts that indicate which scenario is emerging. 'Seismic' implies large-scale, impactful, structural changes.",
      whyCorrect: "'Day-to-day competitor marketing tactics' are generally least useful for *seismic* scenario planning. Seismic scenarios focus on major, structural, long-term shifts that can fundamentally alter the business landscape over a 5, 10, or 20-year horizon. Daily marketing tactics (e.g., a competitor's weekly promotion, a new ad campaign) are operational, short-term, and often reactive. While important for tactical responses, they typically don't represent the kind of profound, game-changing uncertainties that scenario planning for major disruptions is designed to address. They are noise rather than signal at the seismic level.",
      whyOthersWrong: {
        "A": "Regulations can redefine industries (e.g., emissions standards).",
        "B": "Breakthroughs (e.g., AI) alter business models dramatically.",
        "D": "Economic cycles impact demand, investment, and risk appetite significantly."
      },
      examples: [
        "OPEC policy changes shaping energy market scenarios.",
        "AI advances forecast in strategic IT roadmaps."
      ],
      applications: "Helps leadership anticipate discontinuities and craft flexible strategies and contingency plans."
    },
    timeLimit: 120
  },
  {
    id: 29293045,
    category: 'Strategy',
    topic: 'Platform Ecosystems',
    difficulty: 'Hard',
    question: "Which action best fosters a multisided platform’s growth?",
    options: [
      "Raising transaction fees",
      "Investing in core user base engagement",
      "Monopolizing supplier access",
      "Limiting developer APIs"
    ],
    correct: 1,
    explanation: "Core-user engagement attracts more participants, creating positive network effects across sides.",
    detailedExplanation: {
      concept: "Multisided platforms (MSPs) are businesses that create value by enabling direct interactions between two or more distinct groups of users (e.g., buyers and sellers on eBay, riders and drivers on Uber, users and advertisers on Facebook). Their growth and success heavily depend on network effects, particularly 'cross-side' network effects, where the value of the platform to one group of users increases with the number or engagement of users on another side. Effective MSP strategy involves carefully managing these network effects, often by subsidizing one side to attract users who then draw in the other, monetizable side.",
      whyCorrect: "'Investing in core user base engagement' is crucial for fostering an MSP's growth because highly engaged users on one side of the platform (e.g., active buyers, content creators, or service consumers) make the platform more attractive to users on the other side(s) (e.g., sellers, advertisers, service providers). This triggers positive cross-side network effects. For example, if a social media platform invests in features that keep users highly engaged (creating and consuming content), it becomes more valuable to advertisers. Similarly, an e-commerce platform that ensures buyers are active and satisfied will attract more sellers. Engagement leads to value creation, which in turn attracts more participants, creating a virtuous cycle of growth.",
      whyOthersWrong: {
        "A": "Higher fees deter transactions and curb growth.",
        "C": "Supplier monopolization stifles diversity and innovation.",
        "D": "Restricting APIs limits third-party value creation, reducing platform attractiveness."
      },
      examples: [
        "Airbnb focusing on host success and community building.",
        "Stripe prioritizing developer experience to expand payment ecosystem."
      ],
      applications: "Informs investment in community, incentives, and governance for platform-led business models."
    },
    timeLimit: 160
  },
  {
    id: ********,
    category: 'Strategy',
    topic: 'Dynamic Capabilities',
    difficulty: 'Hard',
    question: "Which process exemplifies dynamic capabilities?",
    options: [
      "Routine cost accounting",
      "Annual budgeting",
      "Rapid reconfiguration of supply chain partners",
      "Legacy IT system maintenance"
    ],
    correct: 2,
    explanation: "Dynamic capabilities involve sensing change and swiftly reconfiguring resources and partnerships.",
    detailedExplanation: {
      concept: "Dynamic capabilities are defined as 'the firm's ability to integrate, build, and reconfigure internal and external competences to address rapidly changing environments' (Teece, Pisano, and Shuen, 1997). They are higher-level capabilities that enable a firm to modify its operational capabilities and resource base over time. They involve three main clusters of activities: (1) Sensing opportunities and threats in the environment, (2) Seizing those opportunities by mobilizing resources and making investment decisions, and (3) Transforming/Reconfiguring the organization's tangible and intangible assets as needed to maintain competitiveness.",
      whyCorrect: "'Rapid reconfiguration of supply chain partners' is a strong example of dynamic capabilities in action. It demonstrates the ability to sense changes in the market or supplier landscape (e.g., new low-cost suppliers, geopolitical risks, shifts in demand), seize the opportunity or mitigate the threat by making decisions to change partners, and then transform the operational setup by onboarding new partners and phasing out old ones. This agility in reconfiguring a critical external resource base (supply chain) is a hallmark of a firm with strong dynamic capabilities, allowing it to adapt to or even shape its environment.",
      whyOthersWrong: {
        "A": "Cost accounting is static and inward-focused, not adaptive.",
        "B": "Budgeting is periodic planning, lacking real-time agility.",
        "D": "Maintenance preserves existing assets, not reconfiguration."
      },
      examples: [
        "Fast retailers like Zara shifting suppliers to match emerging fashion trends.",
        "Tech firms reallocating cloud capacity to high-demand regions in real time."
      ],
      applications: "Guides investments in organizational learning, modular processes, and agile governance."
    },
    timeLimit: 170
  },
  {
    id: ********,
    category: 'Strategy',
    topic: 'M&A Integration',
    difficulty: 'Medium',
    question: "Which factor most often derails post-merger integration?",
    options: [
      "Overlapping product lines",
      "Cultural misalignment",
      "Financial due diligence errors",
      "Technology incompatibilities"
    ],
    correct: 1,
    explanation: "Clashing cultures erode synergy, leading to talent loss and execution failures.",
    detailedExplanation: {
      concept: "Post-merger integration (PMI) is the complex process of combining and reorganizing two formerly independent companies to realize the anticipated synergies and strategic objectives of an acquisition. Successful PMI involves aligning strategy, operations, systems, organizational structures, and critically, the people and cultures of the merged entities. Failure in any of these areas can lead to the destruction of value rather than its creation.",
      whyCorrect: "'Cultural misalignment' is widely cited in M&A research and practice as a primary reason for integration failure and the inability to achieve expected synergies. Culture encompasses shared values, beliefs, norms, communication styles, decision-making processes, and behaviors within an organization. When two companies with vastly different or conflicting cultures merge, it can lead to employee resistance, power struggles, loss of key talent, communication breakdowns, decreased morale and productivity, and an inability to effectively collaborate. These 'soft' issues often prove more challenging and detrimental than 'hard' issues like technology or product overlap because they are deeply ingrained and harder to change or reconcile.",
      whyOthersWrong: {
        "A": "Product overlap can be rationalized via portfolio management.",
        "C": "Financial diligence issues surface before close, less disruptive post-merger.",
        "D": "Tech issues are solvable with integration teams and common platforms."
      },
      examples: [
        "Daimler-Chrysler failed on leadership culture clash."
      ],
      applications: "Informs pre-merger cultural due diligence and joint leadership development programs."
    },
    timeLimit: 140
  },
  {
    id: 29293048,
    category: 'Strategy',
    topic: 'Digital Disruption',
    difficulty: 'Medium',
    question: "A taxi company faces ride-sharing disruption. Which strategy best defends incumbents?",
    options: [
      "Increasing fares",
      "Adding app-based hailing and dynamic pricing",
      "Partnering exclusively with corporate clients",
      "Reducing vehicle fleet size"
    ],
    correct: 1,
    explanation: "Adopting app-based services neutralizes convenience advantages of disruptors.",
    detailedExplanation: {
      concept: "Digital disruption occurs when new digital technologies and business models affect the value proposition of existing goods and services. Incumbents facing such disruption must adapt or risk obsolescence. Effective responses often involve embracing similar technologies to neutralize the disruptor's advantages, focusing on unique strengths, or transforming the business model.",
      whyCorrect: "'Adding app-based hailing and dynamic pricing' directly addresses the core value propositions that ride-sharing disruptors (like Uber and Lyft) brought to the market: convenience of on-demand hailing via a mobile app, transparent pricing, and often, dynamic pricing to match supply and demand. By adopting these features, the incumbent taxi company can neutralize some of the disruptor's key competitive advantages, retain existing customers who value these conveniences, and potentially attract new ones. This strategy allows them to leverage their existing infrastructure (vehicles, knowledge of local routes) while modernizing the customer experience.",
      whyOthersWrong: {
        "A": "Higher prices push customers to rivals.",
        "C": "Corporate partnerships limit broader customer engagement.",
        "D": "Shrinking fleets reduces coverage and responsiveness."
      },
      examples: [
        "Traditional taxi firms launching their own apps in response to Uber."
      ],
      applications: "Guides digital investment strategies and operational modernization for legacy players."
    },
    timeLimit: 150
  },
  {
    id: 29293049,
    category: 'Strategy',
    topic: 'Sustaining vs Disruptive Innovation',
    difficulty: 'Hard',
    question: "Which approach aligns with sustaining innovation in the auto parts sector?",
    options: [
      "Building separate 3D-print unit",
      "Incremental improvement of existing parts",
      "Lowering quality standards to reduce cost",
      "Selling OEM data analytics services"
    ],
    correct: 1,
    explanation: "Sustaining innovation focuses on enhancing performance of current products for existing customers.",
    detailedExplanation: {
      concept: "Clayton Christensen's theory distinguishes between sustaining and disruptive innovations. Sustaining innovations improve existing products along dimensions of performance that mainstream customers in major markets have historically valued (e.g., better, faster, more powerful). These innovations are typically developed and introduced by established incumbents. Disruptive innovations, in contrast, are often initially simpler, cheaper, or more convenient, appealing to new or less demanding customers, and may initially underperform existing products on traditional metrics. Over time, they improve and eventually displace established technologies or firms.",
      whyCorrect: "'Incremental improvement of existing parts' is the quintessential example of sustaining innovation. In the auto parts sector, this would involve making existing components (e.g., brake pads, engine components, filters) perform better, last longer, be more efficient, or be slightly cheaper to produce, all within the established technological paradigm and for existing automotive customers (OEMs or aftermarket). This directly aligns with serving the needs of current customers by enhancing the attributes they already value in existing product categories.",
      whyOthersWrong: {
        "A": "A separate spin-off is disruptive, not sustaining.",
        "C": "Reducing quality undermines performance and brand reputation.",
        "D": "Data services represent new business models, not product sustenance."
      },
      examples: [
        "Automakers refining engine efficiency through minor design tweaks."
      ],
      applications: "Helps R&D teams allocate resources between core product enhancement vs. new-market ventures."
    },
    timeLimit: 160
  },
  {
    id: ********,
    category: 'Strategy',
    topic: 'Vertical vs. Horizontal Integration',
    difficulty: 'Medium',
    question: "Which scenario best calls for backward vertical integration?",
    options: [
      "A retailer facing supplier quality issues",
      "A software firm acquiring a competitor",
      "A bank expanding into insurance products",
      "An airline investing in travel agencies"
    ],
    correct: 0,
    explanation: "Backward integration secures supply and quality by bringing upstream activities in-house.",
    detailedExplanation: {
      concept: "Vertical integration is a corporate strategy where a company expands its business operations into different steps on the same production path, such as when a manufacturer owns its supplier and/or distributor. Backward (or upstream) vertical integration occurs when a company takes control of earlier stages in the value chain (e.g., acquiring or developing its own suppliers). Forward (or downstream) vertical integration occurs when it takes control of later stages (e.g., distribution channels or customer service). Horizontal integration, by contrast, involves acquiring or merging with competitors at the same stage of the value chain.",
      whyCorrect: "For 'A retailer facing supplier quality issues,' backward vertical integration is a highly relevant strategic response. By acquiring or developing its own manufacturing or sourcing capabilities for the products it sells, the retailer can gain direct control over the production process, materials used, and quality control standards. This can help ensure product consistency, reliability, and meet customer expectations, thereby mitigating the risks and costs associated with poor supplier quality (e.g., returns, reputational damage, lost sales). It directly addresses the root cause of the problem by internalizing the problematic upstream activity.",
      whyOthersWrong: {
        "B": "Acquiring peers is horizontal, not vertical.",
        "C": "Entering finance products is diversification, not supply chain integration.",
        "D": "Travel agencies are distribution—forward integration."
      },
      examples: [
        "Starbucks building its own roasting facilities to ensure bean quality."
      ],
      applications: "Informs decisions on investments in supplier assets to improve reliability and reduce costs."
    },
    timeLimit: 140
  }
];