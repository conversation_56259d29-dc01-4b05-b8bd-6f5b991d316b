import { businessFundamentalsQuestions } from './businessFundamentals';
import { financeQuestions } from './finance';
import { consultingQuestions } from './consulting';
import { supplyChainQuestions } from './supplyChain';
import { marketingQuestions } from './marketing';
import type { MCQQuestion } from './businessFundamentals';

export interface QuestionBank {
  'business-fundamentals': MCQQuestion[];
  'finance': MCQQuestion[];
  'consulting': MCQQuestion[];
  'supply-chain': MCQQuestion[];
  'marketing': MCQQuestion[];
}

export const mcqQuestions: QuestionBank = {
  'business-fundamentals': businessFundamentalsQuestions,
  'finance': financeQuestions,
  'consulting': consultingQuestions,
  'supply-chain': supplyChainQuestions,
  'marketing': marketingQuestions,
};

export type CategoryKey = keyof QuestionBank;

export const categoryInfo = {
  'business-fundamentals': {
    title: 'Business Fundamentals',
    description: 'Strategy, Marketing, Operations',
    icon: 'BarChart3',
    color: 'blue',
  },
  'finance': {
    title: 'Finance & Valuation',
    description: 'DCF, Ratios, Investment Banking',
    icon: 'TrendingUp',
    color: 'green',
  },
  'consulting': {
    title: 'Consulting Prep',
    description: 'Frameworks, Cases, Problem Solving',
    icon: 'Brain',
    color: 'purple',
  },
  'supply-chain': {
    title: 'Supply Chain Management',
    description: 'Procurement, Logistics, Operations',
    icon: 'Truck',
    color: 'orange',
  },
  'marketing': {
    title: 'Marketing & Brand Management',
    description: 'STP, Product Management, Digital Marketing',
    icon: 'Megaphone',
    color: 'pink',
  },
};

export { MCQQuestion };