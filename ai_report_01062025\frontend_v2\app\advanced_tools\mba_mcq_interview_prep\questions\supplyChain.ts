import { MCQQuestion } from './businessFundamentals';

export const supplyChainQuestions: MCQQuestion[] = [
  // Original 4 questions
  {
    id: 74629851,
    category: 'Supply Chain Strategy',
    topic: 'Network Design',
    difficulty: 'Medium',
    question: "What is the primary trade-off in supply chain network design?",
    options: [
      "Quality vs. speed",
      "Cost vs. responsiveness",
      "Automation vs. manual processes",
      "Local vs. international suppliers"
    ],
    correct: 1,
    explanation: "The fundamental trade-off in supply chain design is between cost efficiency (fewer, centralized facilities) and responsiveness (more, distributed facilities closer to customers).",
    detailedExplanation: {
      concept: "Supply chain network design involves strategic decisions about the number, location, and capacity of facilities (warehouses, distribution centers, manufacturing plants). The core tension is between achieving low costs through economies of scale versus providing fast, responsive service to customers.",
      whyCorrect: "Cost vs. responsiveness is the primary trade-off because centralizing operations reduces costs (economies of scale, lower inventory, fewer facilities) but increases response time and transportation costs. Distributing operations improves responsiveness but increases facility and inventory costs.",
      whyOthersWrong: {
        "A": "Quality vs. speed is an operational trade-off, not the primary network design consideration.",
        "C": "Automation decisions are tactical choices within facilities, not the main network design trade-off.",
        "D": "Local vs. international is one aspect of sourcing strategy, not the overarching network design principle."
      },
      examples: [
        "Amazon's evolution: Started with few warehouses (cost focus) to 1000+ fulfillment centers (responsiveness focus)",
        "Zara's responsive supply chain: Higher costs but 2-week product cycles vs. industry's 6-month cycles",
        "Walmart's hub-and-spoke model: Balances cost efficiency with regional responsiveness"
      ],
      applications: "Used in supply chain strategy, facility location decisions, distribution network optimization, and balancing service levels with operational costs."
    },
    timeLimit: 120
  },
  {
    id: 38571629,
    category: 'Procurement',
    topic: 'Supplier Management',
    difficulty: 'Hard',
    question: "In supplier relationship management, what does a 'preferred supplier' status typically provide?",
    options: [
      "Guaranteed minimum order volumes",
      "Exclusive rights to supply all products",
      "Priority consideration and long-term partnership benefits",
      "Immediate payment terms"
    ],
    correct: 2,
    explanation: "Preferred supplier status provides strategic advantages like priority consideration for new business, collaborative partnerships, and long-term contracts, rather than guarantees or exclusivity.",
    detailedExplanation: {
      concept: "Preferred supplier programs are strategic initiatives where companies develop closer relationships with select suppliers who consistently demonstrate superior performance. These suppliers receive preferential treatment in exchange for enhanced collaboration and performance commitments.",
      whyCorrect: "Preferred status means priority consideration for new opportunities, early involvement in product development, longer-term contracts, collaborative improvement programs, and strategic partnership benefits. It's about relationship depth and mutual value creation, not contractual guarantees.",
      whyOthersWrong: {
        "A": "Preferred status doesn't guarantee volumes - it provides preference when opportunities arise, but business must still be earned.",
        "B": "Exclusivity is rare and typically limited to specific categories or regions, not blanket exclusive rights.",
        "D": "Payment terms are negotiated separately and aren't automatically improved with preferred status."
      },
      examples: [
        "Toyota's supplier development programs: Preferred suppliers get technical assistance and long-term contracts",
        "Apple's strategic suppliers: Priority access to new product launches and co-development opportunities",
        "Boeing's Gold suppliers: Early involvement in new aircraft programs and collaborative cost reduction initiatives"
      ],
      applications: "Used in strategic sourcing, supplier development, risk management, innovation partnerships, and building competitive advantages through supply base optimization."
    },
    timeLimit: 140
  },
  {
    id: 92746183,
    category: 'Operations',
    topic: 'Inventory Management',
    difficulty: 'Easy',
    question: "What does the Economic Order Quantity (EOQ) model help determine?",
    options: [
      "The maximum inventory capacity needed",
      "The optimal order quantity that minimizes total inventory costs",
      "The number of suppliers to use",
      "The best transportation mode"
    ],
    correct: 1,
    explanation: "EOQ calculates the optimal order quantity that minimizes the total of ordering costs and holding costs.",
    detailedExplanation: {
      concept: "Economic Order Quantity (EOQ) is a fundamental inventory management model that determines the optimal order quantity to minimize total inventory costs. It balances two competing costs: ordering costs (which decrease with larger orders) and holding costs (which increase with larger orders).",
      whyCorrect: "EOQ finds the sweet spot where the sum of ordering costs and holding costs is minimized. The formula is √(2DS/H) where D=demand, S=ordering cost, H=holding cost. This optimal quantity reduces total inventory expenses while maintaining adequate stock levels.",
      whyOthersWrong: {
        "A": "EOQ determines order quantity, not storage capacity - that's a separate facilities planning decision.",
        "C": "Supplier selection is a sourcing decision, not directly addressed by EOQ calculations.",
        "D": "Transportation mode selection involves different cost and service trade-offs, not EOQ optimization."
      },
      examples: [
        "Office supplies: EOQ might suggest ordering 500 pens quarterly instead of 100 monthly or 2000 annually",
        "Manufacturing parts: EOQ balances setup costs vs. carrying costs for optimal production runs",
        "Retail inventory: EOQ helps determine optimal reorder quantities for different product categories"
      ],
      applications: "Used in inventory planning, procurement decisions, production scheduling, and working capital optimization. Fundamental tool for inventory managers and supply chain analysts."
    },
    timeLimit: 100
  },
  {
    id: 65839247,
    category: 'Logistics',
    topic: 'Distribution Strategy',
    difficulty: 'Medium',
    question: "What is the main advantage of cross-docking in distribution operations?",
    options: [
      "Increased storage capacity",
      "Reduced inventory holding and faster product flow",
      "Lower transportation costs per unit",
      "Better product quality control"
    ],
    correct: 1,
    explanation: "Cross-docking eliminates storage by directly transferring goods from inbound to outbound transportation, reducing inventory costs and speeding up delivery.",
    detailedExplanation: {
      concept: "Cross-docking is a logistics strategy where products are received from suppliers and immediately prepared for shipment to customers with minimal or no storage time. Products 'cross the dock' from receiving to shipping areas, bypassing traditional warehousing.",
      whyCorrect: "The main advantage is reduced inventory holding costs and faster product flow. By eliminating storage time, companies reduce warehousing costs, minimize inventory carrying costs, decrease handling, and speed up order fulfillment. This is especially valuable for fast-moving or perishable goods.",
      whyOthersWrong: {
        "A": "Cross-docking actually reduces storage needs by eliminating warehouse storage - it doesn't increase capacity.",
        "C": "Transportation costs may actually increase due to more frequent, smaller shipments and coordination complexity, though this is not its primary disadvantage.",
        "D": "Quality control opportunities may be reduced since products spend less time in the facility for inspection."
      },
      examples: [
        "Walmart's grocery distribution: Fresh produce moves from suppliers directly to stores within 24 hours",
        "FedEx hub operations: Packages are sorted and redistributed without storage for next-day delivery",
        "Automotive parts: Components flow directly from suppliers to assembly plants via cross-dock facilities"
      ],
      applications: "Used in retail distribution, e-commerce fulfillment, food and beverage logistics, and any application where speed and inventory reduction are prioritized over storage economies."
    },
    timeLimit: 130
  },
  // New questions start here
  {
    id: 50100001,
    category: 'Planning',
    topic: 'Forecasting Methods',
    difficulty: 'Easy',
    question: "Which forecasting method relies primarily on historical data patterns to predict future demand?",
    options: [
      "Market research surveys",
      "Delphi method",
      "Time series analysis",
      "Executive judgment"
    ],
    correct: 2,
    explanation: "Time series analysis uses past demand data (e.g., moving averages, exponential smoothing) to project future demand.",
    detailedExplanation: {
      concept: "Time series analysis is a quantitative forecasting technique that analyzes historical data to identify patterns (trend, seasonality, cyclical variations) and extrapolates these patterns to predict future values. It assumes that past behavior is indicative of future behavior.",
      whyCorrect: "Time series analysis explicitly uses historical data sequences to make predictions. Methods like moving averages, exponential smoothing, and ARIMA are all time series techniques.",
      whyOthersWrong: {
        "A": "Market research surveys gather new, current data from potential customers, not solely historical data.",
        "B": "The Delphi method is a qualitative technique based on expert opinions gathered iteratively.",
        "D": "Executive judgment is a qualitative method relying on the intuition and experience of senior management."
      },
      examples: [
        "Using a 3-month moving average of past sales to forecast next month's demand.",
        "Applying seasonal decomposition to historical sales to predict sales for the upcoming holiday season.",
        "An ARIMA model predicting stock prices based on past price movements."
      ],
      applications: "Widely used for demand forecasting, inventory planning, resource allocation, and financial forecasting in various industries."
    },
    timeLimit: 90
  },
  {
    id: 50100002,
    category: 'Planning',
    topic: 'Sales & Operations Planning (S&OP)',
    difficulty: 'Medium',
    question: "What is the primary objective of the Sales and Operations Planning (S&OP) process?",
    options: [
      "To develop detailed daily production schedules.",
      "To achieve consensus and alignment between demand, supply, and financial plans.",
      "To negotiate lower prices with key suppliers.",
      "To select and implement new supply chain software."
    ],
    correct: 1,
    explanation: "S&OP aims to balance demand and supply by fostering collaboration and creating a single, agreed-upon operational plan.",
    detailedExplanation: {
      concept: "Sales & Operations Planning (S&OP) is an integrated business management process that enables leadership to focus on key supply chain drivers, including sales, marketing, demand management, production, inventory management, and new product introduction, to achieve a consensus operating plan.",
      whyCorrect: "The core of S&OP is to bring together different functional plans (sales forecasts, marketing plans, operations capabilities, financial budgets) to create a unified plan that balances demand and supply, aligning with the company's strategic objectives.",
      whyOthersWrong: {
        "A": "Detailed daily scheduling is a more tactical activity (Master Production Scheduling or shop floor control) that follows S&OP.",
        "C": "Supplier negotiation is part of procurement, which can be an input to or output of S&OP, but not its primary objective.",
        "D": "Software selection is a project, not the ongoing process goal of S&OP."
      },
      examples: [
        "A CPG company using S&OP to align promotional plans with production capacity and inventory targets.",
        "A manufacturer using S&OP to decide on aggregate production rates for the next 6-18 months.",
        "A retailer using S&OP to balance merchandise plans with logistics capabilities."
      ],
      applications: "Strategic and tactical planning, demand-supply balancing, inventory management, resource planning, financial forecasting, and improving cross-functional communication."
    },
    timeLimit: 120
  },
  {
    id: 50100003,
    category: 'Supply Chain Dynamics',
    topic: 'Bullwhip Effect',
    difficulty: 'Medium',
    question: "The 'bullwhip effect' in a supply chain is characterized by:",
    options: [
      "Increasingly stable orders as one moves upstream from the customer.",
      "Demand variability decreasing as one moves upstream from the customer.",
      "Demand variability amplifying as one moves upstream from the customer.",
      "Perfect synchronization of orders with actual customer demand throughout the chain."
    ],
    correct: 2,
    explanation: "The bullwhip effect describes how small variations in end-customer demand can become progressively larger as they move up the supply chain (retailer to wholesaler to manufacturer to supplier).",
    detailedExplanation: {
      concept: "The bullwhip effect is a phenomenon observed in forecast-driven distribution channels. It refers to the tendency of orders to exhibit increasing variability as one moves further up the supply chain, away from the end customer.",
      whyCorrect: "Small fluctuations in customer demand can cause retailers to place slightly larger, more variable orders to their distributors. These distributors, in turn, might place even more variable orders to manufacturers, and so on. This amplification leads to inefficiencies like excess inventory, stockouts, and underutilized capacity.",
      whyOthersWrong: {
        "A": "The effect is the opposite; orders become less stable (more variable) upstream.",
        "B": "Demand variability increases, not decreases, upstream.",
        "D": "The bullwhip effect is a symptom of a lack of synchronization and information sharing."
      },
      examples: [
        "Procter & Gamble's study on Pampers diapers, which showed much higher order variability at their plants compared to retail sales.",
        "Fluctuations in orders for components being much larger than the sales of the final assembled product.",
        "Retailers overreacting to a slight increase in demand by significantly increasing their orders to wholesalers."
      ],
      applications: "Understanding this helps companies implement strategies like information sharing, vendor-managed inventory (VMI), and lead time reduction to mitigate its negative impacts."
    },
    timeLimit: 110
  },
  {
    id: 50100004,
    category: 'Operations',
    topic: 'Inventory Management (EOQ Numerical)',
    difficulty: 'Medium',
    question: "A company has an annual demand (D) of 12,000 units for a component. The cost to place an order (S) is $75, and the annual holding cost per unit (H) is $10. What is the Economic Order Quantity (EOQ)?",
    options: [
      "300 units",
      "424 units",
      "600 units",
      "1,200 units"
    ],
    correct: 1,
    explanation: "Using the EOQ formula: EOQ = √(2DS/H) = √((2 * 12000 * 75) / 10) = √(1,800,000 / 10) = √180,000 ≈ 424 units.",
    detailedExplanation: {
      concept: "The Economic Order Quantity (EOQ) model calculates the ideal order quantity a company should purchase to minimize total inventory costs, which include ordering costs and holding costs.",
      whyCorrect: "Given D = 12,000 units, S = $75, H = $10. \nEOQ = √((2 * 12,000 * 75) / 10) \nEOQ = √(1,800,000 / 10) \nEOQ = √180,000 \nEOQ ≈ 424.26 units. The closest option is 424 units.",
      whyOthersWrong: {
        "A": "300 units would result if H was higher or D or S were lower.",
        "C": "600 units might be chosen for other reasons but is not the EOQ with these parameters.",
        "D": "1,200 units represents ordering 10 times a year, which is not optimal based on EOQ."
      },
      examples: [
        "A retailer calculating how many units of a popular beverage to order at a time.",
        "A manufacturer determining the optimal batch size for producing a standard part."
      ],
      applications: "Inventory planning, procurement, production lot sizing, and minimizing total inventory-related costs."
    },
    timeLimit: 150
  },
  {
    id: 50100005,
    category: 'Logistics',
    topic: 'Transportation Modes',
    difficulty: 'Easy',
    question: "Which transportation mode is generally characterized by high capacity, low cost per ton-mile, but slow speed and limited accessibility?",
    options: [
      "Air freight",
      "Truck (Road freight)",
      "Rail freight",
      "Water (Maritime/Inland) freight"
    ],
    correct: 3,
    explanation: "Water transport (ships, barges) can carry very large volumes at low cost but is slow and restricted to navigable waterways.",
    detailedExplanation: {
      concept: "Transportation modes each have distinct characteristics regarding cost, speed, capacity, reliability, and accessibility, making them suitable for different types of cargo and supply chain needs.",
      whyCorrect: "Water freight (maritime for oceans, inland for rivers/canals) offers the highest cargo capacity (e.g., container ships, bulk carriers) and generally the lowest cost per ton-mile for long hauls. However, it is the slowest mode and accessibility is limited to ports and navigable waterways.",
      whyOthersWrong: {
        "A": "Air freight is fast but expensive and has lower capacity.",
        "B": "Truck freight offers good accessibility and speed for shorter distances but has higher costs per ton-mile than rail or water for long hauls and bulk.",
        "C": "Rail freight has good capacity and is cost-effective for long hauls of bulk goods, generally faster than water but slower than truck for shorter distances, with accessibility limited to rail networks."
      },
      examples: [
        "Transporting crude oil in supertankers across oceans.",
        "Moving bulk grain on barges down a river system.",
        "Shipping containerized consumer goods from Asia to Europe via container ships."
      ],
      applications: "Ideal for international trade of bulk commodities (oil, ore, grain) and containerized goods where transit time is less critical than cost."
    },
    timeLimit: 100
  },
  {
    id: 50100006,
    category: 'Logistics',
    topic: 'Reverse Logistics',
    difficulty: 'Medium',
    question: "Which of the following activities is a primary focus of reverse logistics?",
    options: [
      "Forecasting future sales of new products.",
      "Sourcing raw materials from suppliers.",
      "Managing product returns, repairs, and disposal.",
      "Optimizing outbound transportation routes for new products."
    ],
    correct: 2,
    explanation: "Reverse logistics deals with the flow of goods from the point of consumption back to the point of origin for purposes such as returns, reuse, recycling, or disposal.",
    detailedExplanation: {
      concept: "Reverse logistics is the process of planning, implementing, and controlling the efficient, cost-effective flow of raw materials, in-process inventory, finished goods, and related information from the point of consumption to the point of origin for the purpose of recapturing value or proper disposal.",
      whyCorrect: "Managing product returns from customers, handling repairs or refurbishment, and ensuring environmentally sound disposal or recycling are all core activities within the scope of reverse logistics.",
      whyOthersWrong: {
        "A": "Forecasting new product sales is part of demand planning (forward logistics).",
        "B": "Sourcing raw materials is part of procurement (inbound logistics).",
        "D": "Optimizing outbound routes for new products is part of distribution (forward logistics)."
      },
      examples: [
        "A customer returning an online purchase.",
        "A company refurbishing and reselling returned electronics.",
        "A beverage company collecting and recycling used bottles.",
        "Managing warranty claims and product recalls."
      ],
      applications: "E-commerce returns management, product lifecycle management, sustainability initiatives, warranty and repair services, and asset recovery."
    },
    timeLimit: 110
  },
  {
    id: 50100007,
    category: 'Sustainability',
    topic: 'Green Supply Chains',
    difficulty: 'Medium',
    question: "What is a key characteristic of a 'Triple Bottom Line' approach in sustainable supply chain management?",
    options: [
      "Focusing exclusively on maximizing shareholder profit.",
      "Prioritizing environmental impact reduction over social and economic factors.",
      "Balancing economic, environmental, and social performance.",
      "Measuring success solely by the speed of delivery."
    ],
    correct: 2,
    explanation: "The Triple Bottom Line (TBL) framework emphasizes sustainability by measuring performance across three dimensions: People (social), Planet (environmental), and Profit (economic).",
    detailedExplanation: {
      concept: "The Triple Bottom Line (TBL or 3BL) is an accounting framework with three dimensions: social, environmental, and financial. It encourages businesses to consider a broader spectrum of values and criteria for measuring organizational success and societal impact, beyond just profits.",
      whyCorrect: "A TBL approach in SCM seeks to achieve a balance between economic viability (profit), environmental stewardship (planet), and social responsibility (people). Decisions are made considering their impact on all three areas.",
      whyOthersWrong: {
        "A": "This describes a traditional, profit-only focus, not TBL.",
        "B": "TBL aims for balance, not prioritizing one aspect to the detriment of others.",
        "D": "Speed of delivery is an operational metric, not the holistic view of TBL."
      },
      examples: [
        "A company investing in renewable energy for its factories (Planet) while ensuring fair labor practices (People) and maintaining profitability (Profit).",
        "Designing products for disassembly and recycling (Planet), using ethically sourced materials (People), and reducing waste costs (Profit).",
        "Patagonia's commitment to environmental activism, fair labor, and producing durable products."
      ],
      applications: "Corporate social responsibility (CSR) reporting, sustainable sourcing, ethical manufacturing, circular economy initiatives, and building long-term business resilience."
    },
    timeLimit: 130
  },
  {
    id: 50100008,
    category: 'Technology',
    topic: 'RFID',
    difficulty: 'Medium',
    question: "What is a primary advantage of RFID (Radio-Frequency Identification) over traditional barcode systems in supply chain tracking?",
    options: [
      "RFID tags are significantly cheaper than barcode labels.",
      "RFID does not require line-of-sight for reading tags.",
      "RFID systems have a shorter read range than barcodes.",
      "RFID technology is less susceptible to environmental interference."
    ],
    correct: 1,
    explanation: "RFID allows for reading multiple tags simultaneously without direct line-of-sight, unlike barcodes which require individual, direct scans.",
    detailedExplanation: {
      concept: "RFID uses radio waves to identify and track tags attached to objects. An RFID tag contains electronically stored information that can be read by an RFID reader. Barcodes use optical scanners to read printed patterns.",
      whyCorrect: "A key advantage of RFID is its ability to read tags without needing a direct line of sight between the reader and the tag. Readers can also identify multiple tags at once, improving efficiency. Barcodes require the scanner to 'see' the barcode directly.",
      whyOthersWrong: {
        "A": "Generally, passive RFID tags are more expensive than simple barcode labels, though costs are decreasing.",
        "C": "RFID can have longer read ranges, especially active RFID tags, compared to most barcode scanners.",
        "D": "RFID can be susceptible to interference from metals, liquids, or other radio frequencies, similar to how barcodes can be affected by damage or poor printing."
      },
      examples: [
        "Tracking pallets of goods through a warehouse without unpacking them.",
        "Automated toll collection systems (e.g., E-ZPass).",
        "Inventory management in retail stores like Decathlon or Zara for quick stock counts.",
        "Livestock tracking."
      ],
      applications: "Inventory management, asset tracking, supply chain visibility, access control, and automated data capture."
    },
    timeLimit: 120
  },
  {
    id: 50100009,
    category: 'Risk Management',
    topic: 'Supply Chain Disruption',
    difficulty: 'Medium',
    question: "Which of the following strategies is primarily aimed at mitigating the risk of supply chain disruptions due to geopolitical instability?",
    options: [
      "Implementing a Just-In-Time (JIT) inventory system.",
      "Increasing reliance on a single, low-cost supplier in the affected region.",
      "Geographic diversification of sourcing and manufacturing operations.",
      "Reducing investment in supply chain visibility technologies."
    ],
    correct: 2,
    explanation: "Diversifying sources and production locations across different geographic regions reduces dependency on any single area, making the supply chain more resilient to localized geopolitical issues.",
    detailedExplanation: {
      concept: "Supply chain risk management involves identifying potential disruptions, assessing their impact, and developing strategies to mitigate or respond to them. Geopolitical instability (e.g., wars, trade disputes, political unrest) is a significant source of risk.",
      whyCorrect: "Geographic diversification means not putting 'all eggs in one basket.' By having alternative suppliers or manufacturing sites in different countries/regions, a company can shift production or sourcing if one area becomes unstable, thus maintaining supply continuity.",
      whyOthersWrong: {
        "A": "JIT systems, while efficient, can increase vulnerability to disruptions as they minimize inventory buffers.",
        "B": "Increasing reliance on a single supplier in an unstable region amplifies risk.",
        "D": "Reducing investment in visibility technologies would make it harder to detect and respond to disruptions."
      },
      examples: [
        "A company sourcing a critical component from suppliers in both Southeast Asia and Eastern Europe.",
        "An electronics manufacturer having assembly plants in multiple countries to avoid over-reliance on one.",
        "Fashion brands diversifying production away from a single country experiencing political turmoil."
      ],
      applications: "Global sourcing strategy, business continuity planning, supply chain network design, and strategic risk management."
    },
    timeLimit: 130
  },
  {
    id: 50100010,
    category: 'Operations',
    topic: 'Lean Principles',
    difficulty: 'Medium',
    question: "In the context of Lean supply chain management, what does 'Poka-Yoke' refer to?",
    options: [
      "A system of continuous improvement.",
      "A method for visualizing workflow (e.g., Kanban board).",
      "A mistake-proofing technique or device.",
      "The practice of keeping high levels of safety stock."
    ],
    correct: 2,
    explanation: "Poka-Yoke (mistake-proofing) involves designing processes or devices that prevent errors from occurring or make them immediately obvious.",
    detailedExplanation: {
      concept: "Poka-Yoke is a Japanese term that means 'mistake-proofing' or 'inadvertent error prevention.' It's a lean manufacturing technique aimed at eliminating defects by preventing, correcting, or drawing attention to human errors as they occur.",
      whyCorrect: "The core idea of Poka-Yoke is to design systems and processes in such a way that it's difficult or impossible for an operator to make a mistake. If an error cannot be prevented, the Poka-Yoke device signals that an error has occurred.",
      whyOthersWrong: {
        "A": "'Kaizen' refers to continuous improvement.",
        "B": "'Kanban' is a scheduling system for lean and JIT production, often visualized.",
        "D": "Lean principles aim to minimize waste, including excessive inventory; Poka-Yoke focuses on error prevention."
      },
      examples: [
        "USB connectors designed to fit only one way.",
        "Assembly line fixtures that only allow parts to be inserted in the correct orientation.",
        "Software that grays out a 'save' button until all required fields are filled.",
        "A car that won't start if the gear is not in 'Park' or 'Neutral'."
      ],
      applications: "Manufacturing processes, assembly operations, service delivery, software design, and any process where human error can lead to defects or problems."
    },
    timeLimit: 110
  },
  {
    id: 50100011,
    category: 'Global Supply Chain',
    topic: 'Incoterms',
    difficulty: 'Hard',
    question: "Under which Incoterm does the seller fulfill their obligation when the goods are made available at their premises (e.g., factory, warehouse), and the buyer bears all costs and risks involved in taking the goods from there?",
    options: [
      "DDP (Delivered Duty Paid)",
      "CIF (Cost, Insurance and Freight)",
      "FOB (Free on Board)",
      "EXW (Ex Works)"
    ],
    correct: 3,
    explanation: "EXW (Ex Works) places the maximum obligation on the buyer and minimum obligations on the seller. The seller only needs to make the goods available at their location.",
    detailedExplanation: {
      concept: "Incoterms® (International Commercial Terms) are a set of pre-defined commercial terms published by the International Chamber of Commerce (ICC) relating to international commercial law. They define the responsibilities of buyers and sellers for the delivery of goods under sales contracts.",
      whyCorrect: "With EXW, the seller's responsibility ends once the goods are made available at their premises (factory, warehouse, etc.). The buyer is responsible for all subsequent costs and risks, including loading, export clearance, main carriage, import clearance, and unloading at destination.",
      whyOthersWrong: {
        "A": "DDP (Delivered Duty Paid) represents the seller's maximum obligation, where the seller delivers goods to the buyer's named place, cleared for import.",
        "B": "CIF (Cost, Insurance and Freight) means the seller pays for cost and freight to bring goods to a named port of destination and procures marine insurance. Risk transfers to buyer when goods pass ship's rail at port of shipment.",
        "C": "FOB (Free on Board) means the seller delivers goods on board the vessel nominated by the buyer at the named port of shipment. Risk transfers when goods pass ship's rail."
      },
      examples: [
        "A buyer arranges their own truck to pick up goods directly from the seller's factory gate.",
        "A small business exporting for the first time might prefer EXW to minimize their logistics responsibilities.",
        "The seller simply notifies the buyer that the goods are ready for collection at their warehouse on a specific date."
      ],
      applications: "International trade contracts, defining transfer of risk, cost allocation, and responsibilities for tasks like loading, transport, insurance, and customs clearance between buyer and seller."
    },
    timeLimit: 140
  },
  {
    id: 50100012,
    category: 'Performance Measurement',
    topic: 'KPIs (Inventory Turnover Numerical)',
    difficulty: 'Medium',
    question: "A company reported a Cost of Goods Sold (COGS) of $2,400,000 for the year. Its beginning inventory was $400,000 and ending inventory was $600,000. What was its inventory turnover ratio?",
    options: [
      "4.0 times",
      "4.8 times",
      "5.0 times",
      "6.0 times"
    ],
    correct: 1,
    explanation: "Average Inventory = ($400,000 + $600,000) / 2 = $500,000. Inventory Turnover = COGS / Average Inventory = $2,400,000 / $500,000 = 4.8 times.",
    detailedExplanation: {
      concept: "Inventory turnover is a ratio showing how many times a company has sold and replaced its inventory during a given period. A higher turnover generally indicates efficient inventory management and strong sales, while a low turnover might suggest overstocking or poor sales.",
      whyCorrect: "First, calculate Average Inventory: (Beginning Inventory + Ending Inventory) / 2 = ($400,000 + $600,000) / 2 = $1,000,000 / 2 = $500,000. \nThen, calculate Inventory Turnover: COGS / Average Inventory = $2,400,000 / $500,000 = 4.8 times.",
      whyOthersWrong: {
        "A": "4.0 times would be if average inventory was $600,000.",
        "C": "5.0 times would be if average inventory was $480,000 or COGS was $2,500,000 with $500,000 average inventory.",
        "D": "6.0 times would be if average inventory was $400,000."
      },
      examples: [
        "A fast-fashion retailer aims for high inventory turnover to keep styles fresh.",
        "A grocery store has high inventory turnover for perishable goods.",
        "A seller of luxury, high-value items might have a lower inventory turnover."
      ],
      applications: "Assessing inventory management efficiency, financial health analysis, benchmarking against competitors, and identifying potential issues with overstocking or obsolescence."
    },
    timeLimit: 150
  },
  {
    id: 50100013,
    category: 'Logistics',
    topic: 'Warehousing Functions',
    difficulty: 'Easy',
    question: "Which of the following is NOT a primary function of a typical warehouse or distribution center?",
    options: [
      "Receiving goods from suppliers",
      "Storing inventory",
      "Order picking and assembly",
      "Final product manufacturing"
    ],
    correct: 3,
    explanation: "Warehouses and distribution centers focus on storage and movement of goods; final product manufacturing typically occurs in factories or production plants.",
    detailedExplanation: {
      concept: "Warehouses and distribution centers (DCs) are critical nodes in a supply chain. While a traditional warehouse focuses more on storage, a DC emphasizes rapid movement of goods (throughput) and value-added services.",
      whyCorrect: "Final product manufacturing involves transforming raw materials or components into finished goods, which is the role of a manufacturing plant, not typically a warehouse or DC. DCs may perform light assembly or customization (e.g., kitting, labeling), but not core manufacturing.",
      whyOthersWrong: {
        "A": "Receiving goods from suppliers, inspecting them, and logging them into inventory is a fundamental warehousing function.",
        "B": "Storing inventory, whether short-term or long-term, is a core purpose of warehousing.",
        "C": "Order picking (retrieving items from storage to fulfill customer orders) and assembly (if kitting or light value-added services are offered) are key DC activities."
      },
      examples: [
        "Amazon fulfillment centers receive, store, pick, pack, and ship millions of items.",
        "A grocery DC receives pallet loads from manufacturers and ships store-specific orders.",
        "A parts warehouse stores spare components for machinery."
      ],
      applications: "Inventory holding, order fulfillment, consolidation of shipments, break-bulk operations, cross-docking, and supporting value-added services."
    },
    timeLimit: 90
  },
  {
    id: 50100014,
    category: 'Operations',
    topic: 'Inventory Management (JIT)',
    difficulty: 'Medium',
    question: "A key requirement for the successful implementation of a Just-In-Time (JIT) inventory system is:",
    options: [
      "High levels of buffer stock to handle demand uncertainty.",
      "Infrequent, large-volume deliveries from suppliers.",
      "Highly reliable suppliers and consistent production processes.",
      "A focus on minimizing transportation costs above all else."
    ],
    correct: 2,
    explanation: "JIT relies on receiving materials and producing goods only when needed, which demands highly dependable suppliers and stable, high-quality internal processes to avoid disruptions.",
    detailedExplanation: {
      concept: "Just-In-Time (JIT) is an inventory strategy companies employ to increase efficiency and decrease waste by receiving goods only as they are needed in the production process, thereby reducing inventory holding costs.",
      whyCorrect: "For JIT to work, suppliers must deliver high-quality materials exactly on time, in the correct quantities. Production processes must also be highly reliable and capable of producing defect-free output, as there are minimal inventory buffers to absorb disruptions or quality issues.",
      whyOthersWrong: {
        "A": "JIT aims to minimize buffer stock, not maintain high levels.",
        "B": "JIT typically involves frequent, small-volume deliveries to match immediate needs.",
        "D": "While cost efficiency is a goal, JIT prioritizes waste reduction and flow; sometimes more frequent, smaller shipments (potentially increasing unit transport cost) are needed, offset by lower inventory costs."
      },
      examples: [
        "Toyota Production System, where parts are delivered to the assembly line just as they are needed.",
        "Restaurants that receive daily deliveries of fresh ingredients.",
        "Dell's build-to-order model for computers."
      ],
      applications: "Lean manufacturing, waste reduction, inventory cost minimization, improving production flow, and increasing responsiveness to customer demand."
    },
    timeLimit: 120
  },
  {
    id: 50100015,
    category: 'Demand Management',
    topic: 'Demand Shaping',
    difficulty: 'Medium',
    question: "Which of the following actions is an example of 'demand shaping' by a company?",
    options: [
      "Increasing safety stock levels for a popular product.",
      "Implementing a new ERP system to improve forecast accuracy.",
      "Offering a limited-time discount on a product during an off-peak season.",
      "Expanding warehouse capacity to accommodate higher inventory."
    ],
    correct: 2,
    explanation: "Demand shaping involves using marketing levers like pricing, promotions, and product features to influence customer demand to better align with available supply or strategic goals.",
    detailedExplanation: {
      concept: "Demand shaping refers to the set of practices that aim to influence customer demand patterns. Instead of just reacting to demand, companies proactively try to modify it to match supply capabilities, smooth out peaks and valleys, or drive sales of specific products.",
      whyCorrect: "Offering a discount during an off-peak season is a classic demand shaping tactic. It incentivizes customers to buy when demand is typically low, helping to utilize capacity better and generate revenue that might otherwise be missed.",
      whyOthersWrong: {
        "A": "Increasing safety stock is a supply-side response to demand uncertainty, not an attempt to shape demand.",
        "B": "Implementing an ERP system is about improving planning and execution, not directly influencing customer buying behavior.",
        "D": "Expanding warehouse capacity is a supply-side capacity adjustment."
      },
      examples: [
        "Airlines offering lower fares for mid-week flights.",
        "Hotels offering weekend package deals to fill rooms during typically slower periods.",
        "Software companies offering discounts for annual subscriptions paid upfront.",
        "Retailers running promotions on seasonal items before or after peak season."
      ],
      applications: "Revenue management, capacity utilization, inventory optimization, promotion planning, and aligning demand with strategic product launches or phase-outs."
    },
    timeLimit: 110
  },
  {
    id: 50100016,
    category: 'Quality Management',
    topic: 'Six Sigma',
    difficulty: 'Hard',
    question: "A process achieving a Six Sigma level of quality is expected to have no more than approximately how many defects per million opportunities (DPMO)?",
    options: [
      "3.4 DPMO",
      "34 DPMO",
      "233 DPMO (corresponding to 4.5 sigma)",
      "6,210 DPMO (corresponding to 4 sigma)"
    ],
    correct: 0,
    explanation: "A Six Sigma process aims for a defect rate of 3.4 defects per million opportunities, considering a 1.5 sigma shift in the mean over time.",
    detailedExplanation: {
      concept: "Six Sigma is a set of techniques and tools for process improvement. It seeks to improve the quality of process outputs by identifying and removing the causes of defects (errors) and minimizing variability in manufacturing and business processes.",
      whyCorrect: "Statistically, six standard deviations from the mean in a normal distribution would encompass virtually all (99.9999998%) of the data. However, the widely accepted 'Six Sigma quality' standard of 3.4 DPMO accounts for a potential 1.5 sigma shift in the process mean over the long term. Without this shift, 6 sigma would be about 2 defects per billion.",
      whyOthersWrong: {
        "B": "34 DPMO is too high for Six Sigma.",
        "C": "233 DPMO corresponds to approximately 4.5 sigma performance (with the 1.5 sigma shift considered, or 5 sigma without).",
        "D": "6,210 DPMO corresponds to approximately 4 sigma performance (with the 1.5 sigma shift considered, or 3.5 sigma without)."
      },
      examples: [
        "Motorola, which pioneered Six Sigma, used it to drastically reduce defects in its manufacturing processes.",
        "General Electric (GE) famously adopted Six Sigma across its businesses, leading to significant cost savings and quality improvements.",
        "Financial institutions using Six Sigma to reduce errors in transaction processing."
      ],
      applications: "Manufacturing quality control, service process improvement, healthcare safety, financial services, and any domain where minimizing defects and variability is critical."
    },
    timeLimit: 140
  },
  {
    id: 50100017,
    category: 'Operations',
    topic: 'Inventory Management (Safety Stock)',
    difficulty: 'Medium',
    question: "The primary reason for a company to hold 'safety stock' is to:",
    options: [
      "Take advantage of bulk purchase discounts from suppliers.",
      "Reduce the frequency of placing orders.",
      "Protect against stockouts due to variability in demand or lead time.",
      "Smooth out production levels when demand is highly seasonal."
    ],
    correct: 2,
    explanation: "Safety stock (or buffer stock) is extra inventory held to mitigate the risk of stockouts caused by uncertainties in customer demand and/or supplier lead times.",
    detailedExplanation: {
      concept: "Safety stock is an additional quantity of an item held in inventory to reduce the risk that the item will be out of stock if demand increases or supplier lead time is longer than expected. It acts as a buffer.",
      whyCorrect: "Uncertainty is inherent in most supply chains. Demand can fluctuate unexpectedly, and supplier delivery times (lead times) can vary. Safety stock provides a cushion to ensure products are available to meet customer orders even when these variations occur.",
      whyOthersWrong: {
        "A": "Ordering larger quantities to get discounts relates to cycle stock or strategic buys, not primarily safety stock's purpose.",
        "B": "Reducing order frequency is related to balancing ordering costs and holding costs (EOQ), not the primary role of safety stock.",
        "D": "While safety stock can help, smoothing production for seasonal demand is more directly addressed by strategies like building seasonal inventory (anticipation stock) or flexible capacity."
      },
      examples: [
        "A retailer holding extra units of a popular toy before the holiday season due to unpredictable demand.",
        "A manufacturer keeping additional raw materials on hand because a key supplier has inconsistent delivery times.",
        "An e-commerce company maintaining safety stock for fast-moving items to prevent backorders."
      ],
      applications: "Inventory planning, service level management, mitigating supply chain risk, and ensuring product availability."
    },
    timeLimit: 110
  },
  {
    id: 50100018,
    category: 'Technology',
    topic: 'Blockchain in SCM',
    difficulty: 'Hard',
    question: "One of the most significant potential benefits of implementing blockchain technology in supply chains is:",
    options: [
      "Eliminating the need for all intermediaries like freight forwarders.",
      "Drastically reducing the physical transportation time of goods.",
      "Providing an immutable and transparent record of transactions and product provenance.",
      "Automating all supplier negotiation and contract management processes."
    ],
    correct: 2,
    explanation: "Blockchain offers a decentralized, immutable ledger that can enhance traceability, transparency, and trust among supply chain partners by creating a secure record of events and transactions.",
    detailedExplanation: {
      concept: "Blockchain is a distributed ledger technology (DLT) where transactions are recorded in 'blocks' that are cryptographically linked together in a 'chain.' This creates a secure, transparent, and tamper-proof record shared among network participants.",
      whyCorrect: "In SCM, blockchain can provide a single, shared source of truth for tracking goods from origin to consumer, verifying authenticity, recording compliance data, and documenting handoffs. This immutability and transparency can reduce fraud, improve traceability (e.g., for food safety or ethical sourcing), and build trust.",
      whyOthersWrong: {
        "A": "While blockchain can streamline processes and reduce reliance on some intermediaries, it's unlikely to eliminate all of them, as many provide physical services or specialized expertise.",
        "B": "Blockchain is an information technology; it doesn't directly impact the physical speed of trucks, ships, or planes.",
        "D": "While smart contracts on a blockchain can automate parts of contract execution, the complex negotiation and relationship management aspects are unlikely to be fully automated solely by blockchain."
      },
      examples: [
        "Walmart using blockchain to trace pork products in China for food safety.",
        "De Beers using blockchain (Tracr platform) to track diamonds and ensure they are conflict-free.",
        "Maersk and IBM's TradeLens platform for digitizing global shipping documentation.",
        "Tracking pharmaceutical products to combat counterfeiting."
      ],
      applications: "Product traceability, provenance tracking, anti-counterfeiting, trade finance, smart contracts for automated payments, and enhancing overall supply chain visibility and security."
    },
    timeLimit: 150
  },
  {
    id: 50100019,
    category: 'Operations',
    topic: 'Inventory Management (Reorder Point Numerical)',
    difficulty: 'Medium',
    question: "A product has an average daily demand of 50 units. The average lead time for replenishment is 4 days. The company maintains a safety stock of 80 units. What is the Reorder Point (ROP)?",
    options: [
      "200 units",
      "230 units",
      "280 units",
      "320 units"
    ],
    correct: 2,
    explanation: "ROP = (Average Daily Demand × Average Lead Time) + Safety Stock = (50 units/day × 4 days) + 80 units = 200 units + 80 units = 280 units.",
    detailedExplanation: {
      concept: "The Reorder Point (ROP) is the inventory level at which a new order should be placed to replenish stock before it runs out. It considers demand during the lead time and a safety buffer.",
      whyCorrect: "The formula for ROP is: Demand during lead time + Safety Stock. \nDemand during lead time = Average Daily Demand × Average Lead Time = 50 units/day × 4 days = 200 units. \nROP = 200 units + 80 units (Safety Stock) = 280 units. When inventory drops to 280 units, an order should be placed.",
      whyOthersWrong: {
        "A": "200 units is only the demand during lead time, without accounting for safety stock.",
        "B": "230 units would be if safety stock was 30, or a miscalculation.",
        "D": "320 units would be if safety stock was 120, or a miscalculation."
      },
      examples: [
        "A retail store setting ROPs for various SKUs to trigger reorders from the DC.",
        "A manufacturing plant setting ROPs for raw materials to ensure continuous production."
      ],
      applications: "Inventory control systems, automated replenishment systems, preventing stockouts, and optimizing inventory levels."
    },
    timeLimit: 140
  },
  {
    id: ********,
    category: 'Supply Chain Strategy',
    topic: 'Postponement',
    difficulty: 'Medium',
    question: "The primary benefit of using a 'postponement' strategy in supply chain management is to:",
    options: [
      "Reduce raw material costs through early bulk purchasing.",
      "Increase economies of scale in manufacturing by producing large batches of identical products.",
      "Improve responsiveness to diverse customer demands while minimizing finished goods inventory risk.",
      "Shorten the overall product development lifecycle."
    ],
    correct: 2,
    explanation: "Postponement involves delaying the final customization or differentiation of a product until a customer order is received, allowing companies to hold generic inventory and customize it late in the process.",
    detailedExplanation: {
      concept: "Postponement (also known as delayed differentiation) is a supply chain strategy where some activities in the value chain (e.g., assembly, packaging, labeling, customization) are intentionally delayed until customer orders are received. This allows for maintaining inventory in a more generic, less differentiated state.",
      whyCorrect: "By delaying final differentiation, companies can better match specific customer requirements with less risk of holding obsolete or slow-moving finished goods. Generic inventory can satisfy a wider range of demands. This enhances responsiveness to specific needs while mitigating the risks associated with forecasting diverse finished product configurations.",
      whyOthersWrong: {
        "A": "Postponement focuses on finished goods inventory and customization, not primarily raw material purchasing strategies.",
        "B": "While generic components might be made in large batches, the core of postponement is about late-stage differentiation, which might run counter to large batches of *finished* identical products if demand is diverse.",
        "D": "Postponement is an operational/supply chain strategy, not directly a product development lifecycle strategy, though it can support mass customization which might be part of a product strategy."
      },
      examples: [
        "Dell computers: Assembling PCs to customer specifications after an order is placed.",
        "Paint retailers: Mixing custom paint colors at the store from base paints and colorants.",
        "Benetton: Dyeing sweaters in popular colors after initial demand signals, rather than producing all colors upfront.",
        "HP printers: Packaging printers with country-specific power cords and manuals at regional DCs."
      ],
      applications: "Industries with high demand uncertainty or a wide variety of product configurations, mass customization, e-commerce fulfillment, and reducing finished goods obsolescence."
    },
    timeLimit: 130
  },
  {
    id: 50100021,
    category: 'Procurement',
    topic: 'Strategic Sourcing',
    difficulty: 'Medium',
    question: "Which of the following is LEAST likely to be a key activity in a formal strategic sourcing process?",
    options: [
      "Analyzing historical spend data across the organization.",
      "Conducting daily price checks for commodity items.",
      "Developing a comprehensive category strategy for key purchasing areas.",
      "Negotiating long-term agreements with selected preferred suppliers."
    ],
    correct: 1,
    explanation: "Strategic sourcing is a long-term, analytical process. Daily price checks are more of a tactical, operational purchasing activity, especially for commodities.",
    detailedExplanation: {
      concept: "Strategic sourcing is a systematic and fact-based approach for optimizing an organization's supply base and improving the overall value proposition. It goes beyond tactical purchasing to focus on long-term relationships, total cost of ownership, and strategic alignment.",
      whyCorrect: "While monitoring market prices is important, conducting *daily* price checks for commodities is a highly tactical, operational task. Strategic sourcing focuses on broader market analysis, supplier capability assessment, developing category strategies, and building partnerships, rather than frequent, transactional price tracking for all items.",
      whyOthersWrong: {
        "A": "Spend analysis is a fundamental first step in strategic sourcing to understand what is being bought, from whom, and at what cost.",
        "C": "Developing category strategies (e.g., for IT, marketing spend, raw materials) is a core output of strategic sourcing, outlining how each category will be managed.",
        "D": "Negotiating and establishing long-term agreements with carefully selected suppliers is a key outcome of the strategic sourcing process, aiming for mutual benefit and value."
      },
      examples: [
        "A company analyzing its total spend on IT hardware to consolidate suppliers and negotiate better terms.",
        "A manufacturer developing a global sourcing strategy for a critical raw material.",
        "A retailer conducting an RFP process to select a logistics partner for a new market."
      ],
      applications: "Cost reduction, risk mitigation, supplier relationship management, improving quality and innovation from suppliers, and achieving competitive advantage through the supply base."
    },
    timeLimit: 120
  },
  {
    id: 50100022,
    category: 'Logistics',
    topic: 'Outsourcing (3PL)',
    difficulty: 'Easy',
    question: "A company that outsources its warehousing, transportation, and customs brokerage activities to an external firm is utilizing a:",
    options: [
      "Freight forwarder",
      "Fourth-Party Logistics (4PL) provider",
      "Third-Party Logistics (3PL) provider",
      "Common carrier"
    ],
    correct: 2,
    explanation: "A Third-Party Logistics (3PL) provider offers outsourced logistics services, such as transportation, warehousing, cross-docking, inventory management, packaging, and freight forwarding.",
    detailedExplanation: {
      concept: "Outsourcing logistics involves contracting with external providers to manage part or all of a company's supply chain functions. Different types of providers (3PL, 4PL) offer varying levels of service and integration.",
      whyCorrect: "A 3PL provider takes over the execution of specific logistics functions. When a company hands over its warehousing, transportation, and customs brokerage, it's typically engaging a 3PL to perform these operational tasks.",
      whyOthersWrong: {
        "A": "A freight forwarder typically arranges shipping on behalf of others but may not own assets or manage warehousing directly; they are often a type of 3PL or work with them.",
        "B": "A 4PL provider is a supply chain integrator that assembles and manages the resources, capabilities, and technology of its own organization and other organizations (including 3PLs) to design, build, and run comprehensive supply chain solutions. They are more strategic and managerial.",
        "D": "A common carrier is a transportation company that offers its services to the general public under license or authority granted by a regulatory body (e.g., a trucking company, airline)."
      },
      examples: [
        "A retailer using DHL Supply Chain or XPO Logistics for its warehousing and distribution.",
        "A manufacturer contracting with a 3PL to manage its international shipping and customs clearance.",
        "An e-commerce startup outsourcing its order fulfillment to a specialized 3PL."
      ],
      applications: "Reducing logistics costs, accessing expertise and technology, improving service levels, focusing on core competencies, and increasing supply chain flexibility."
    },
    timeLimit: 100
  },
  {
    id: 50100023,
    category: 'Supply Chain Strategy',
    topic: 'Sourcing Decisions (Make-or-Buy)',
    difficulty: 'Medium',
    question: "A key factor that would typically favor a 'make' decision (producing in-house) over a 'buy' decision (outsourcing) is:",
    options: [
      "The company lacks the expertise or technology to produce the item efficiently.",
      "The item is a non-core commodity readily available from multiple reliable suppliers.",
      "The production process involves proprietary technology or intellectual property crucial to the company's competitive advantage.",
      "The company wants to reduce its capital investment in manufacturing assets."
    ],
    correct: 2,
    explanation: "If producing an item involves proprietary knowledge or technology that is a source of competitive advantage, a company is more likely to 'make' it in-house to protect that IP and control quality.",
    detailedExplanation: {
      concept: "The make-or-buy decision is a strategic choice companies face about whether to manufacture a product or component internally ('make') or purchase it from an external supplier ('buy'). It involves analyzing costs, capacity, quality, strategic importance, and other factors.",
      whyCorrect: "When a component or process is core to a company's unique value proposition and involves sensitive intellectual property or specialized know-how, producing it in-house (make) provides greater control, protects trade secrets, and can be a strategic imperative.",
      whyOthersWrong: {
        "A": "Lack of expertise or technology would favor a 'buy' decision, leveraging a supplier's capabilities.",
        "B": "Commodity items readily available from multiple suppliers are often ideal candidates for 'buy' to leverage market competition and supplier specialization.",
        "D": "Reducing capital investment is a common driver for 'buy' (outsourcing), as it avoids the need to invest in plant, equipment, and labor for production."
      },
      examples: [
        "Apple designs its own chips (A-series, M-series) but outsources their manufacturing (a complex make *and* buy scenario, but the design is 'make'). Historically, many tech companies 'made' core software.",
        "A pharmaceutical company 'making' its patented drug formula in-house.",
        "An automotive company 'making' its core engine technology while 'buying' standard fasteners."
      ],
      applications: "Strategic planning, operations strategy, sourcing and procurement decisions, capacity planning, and managing intellectual property."
    },
    timeLimit: 130
  },
  {
    id: 50100024,
    category: 'Performance Measurement',
    topic: 'KPIs (Forecast Accuracy)',
    difficulty: 'Medium',
    question: "Which of the following metrics is commonly used to evaluate the accuracy of demand forecasts by expressing the average error as a percentage of actual demand?",
    options: [
      "Order Cycle Time",
      "Inventory Holding Cost",
      "Mean Absolute Percentage Error (MAPE)",
      "Perfect Order Percentage"
    ],
    correct: 2,
    explanation: "Mean Absolute Percentage Error (MAPE) measures the average magnitude of forecast errors as a percentage of actual values, providing a relative measure of forecast accuracy.",
    detailedExplanation: {
      concept: "Forecast accuracy is a measure of how close a forecast's predictions are to the actual outcomes. Various metrics are used to quantify this, each with its own strengths and weaknesses. MAPE is widely used due to its intuitive percentage-based interpretation.",
      whyCorrect: "MAPE calculates the absolute difference between actual and forecasted values for each period, divides it by the actual value for that period (to get a percentage error), and then averages these percentage errors over all periods. It's useful for comparing accuracy across different items or time series.",
      whyOthersWrong: {
        "A": "Order Cycle Time measures the time elapsed from order placement to order receipt; it's a fulfillment performance metric.",
        "B": "Inventory Holding Cost is a financial measure of the expense of storing inventory.",
        "D": "Perfect Order Percentage measures the rate of orders delivered complete, on-time, undamaged, and with correct documentation; it's a customer service and operational excellence metric."
      },
      examples: [
        "If actual sales were 100 units and forecast was 90, the absolute error is 10, and percentage error is (10/100)*100% = 10%. MAPE averages such percentages.",
        "A company targeting a MAPE below 15% for its key products.",
        "Comparing MAPE for different forecasting models to select the best one."
      ],
      applications: "Demand planning, inventory management, S&OP process evaluation, and continuous improvement of forecasting processes."
    },
    timeLimit: 120
  },
  {
    id: 50100025,
    category: 'Risk Management',
    topic: 'Supply Chain Resilience',
    difficulty: 'Hard',
    question: "Building supply chain resilience involves designing systems that can effectively:",
    options: [
      "Eliminate all possible sources of disruption through extensive control.",
      "Absorb, adapt to, and recover from disruptions quickly and efficiently.",
      "Focus solely on minimizing costs, assuming stability will prevail.",
      "Rely on a single, highly efficient global source for all critical components."
    ],
    correct: 1,
    explanation: "Supply chain resilience is the capacity of a supply chain to persist, adapt, or transform in the face of change, particularly unexpected disruptive events. It's about bouncing back effectively.",
    detailedExplanation: {
      concept: "Supply chain resilience refers to the ability of a supply chain to prepare for and adapt to unexpected events, and to recover from disruptions while maintaining continuity of operations at the desired level of connectedness and control over structure and function.",
      whyCorrect: "Resilience isn't about preventing all disruptions (which is often impossible) but about having the capabilities to withstand them (absorb), adjust strategies and operations in response (adapt), and return to normal or an improved state quickly (recover). This includes elements like visibility, flexibility, redundancy, and collaboration.",
      whyOthersWrong: {
        "A": "Eliminating all disruptions is unrealistic. Resilience is about managing their impact.",
        "C": "A sole focus on cost minimization often leads to lean, brittle supply chains that lack resilience.",
        "D": "Relying on a single source, even if efficient, creates a major vulnerability and is antithetical to resilience, which often involves diversification."
      },
      examples: [
        "Companies diversifying their supplier base geographically after the COVID-19 pandemic.",
        "Developing alternative transportation routes in case of port congestion or strikes.",
        "Investing in real-time visibility tools to detect and respond to disruptions faster.",
        "Holding strategic buffers of inventory or capacity for critical items."
      ],
      applications: "Strategic supply chain design, business continuity planning, risk management, sourcing strategy, and network optimization to enhance robustness and agility."
    },
    timeLimit: 140
  },
  {
    id: 50100026,
    category: 'Technology',
    topic: 'Artificial Intelligence (AI) in SCM',
    difficulty: 'Medium',
    question: "A significant application of Artificial Intelligence (AI) and Machine Learning (ML) in supply chain management is:",
    options: [
      "Manually creating and adjusting safety stock levels based on gut feeling.",
      "Replacing all human decision-making in logistics planning without oversight.",
      "Improving demand forecast accuracy by identifying complex patterns in large datasets.",
      "Designing physical warehouse layouts using traditional architectural software."
    ],
    correct: 2,
    explanation: "AI/ML algorithms can analyze vast amounts of historical data, external factors (weather, events, social media), and identify complex, non-linear relationships to generate more accurate demand forecasts than traditional methods.",
    detailedExplanation: {
      concept: "AI and ML involve creating systems that can learn from data, identify patterns, and make decisions or predictions with minimal human intervention. In SCM, they are used to optimize various processes.",
      whyCorrect: "Demand forecasting is a prime area for AI/ML. These technologies can process diverse datasets (sales history, promotions, economic indicators, weather, social trends, etc.) to uncover hidden correlations and improve predictive accuracy, leading to better inventory management and resource allocation.",
      whyOthersWrong: {
        "A": "This describes a manual, subjective approach, which AI aims to improve upon with data-driven insights.",
        "B": "While AI can automate many decisions, complete replacement without oversight is generally not feasible or advisable, especially for complex strategic decisions. Human-in-the-loop systems are common.",
        "D": "Traditional architectural software is used for design, but AI might be used to optimize layouts or simulate flows within those designs, not replace the design software itself in this context."
      },
      examples: [
        "Amazon using ML for demand forecasting and inventory positioning.",
        "Route optimization for delivery fleets considering real-time traffic and weather.",
        "Predictive maintenance for machinery in manufacturing plants using ML to analyze sensor data.",
        "Dynamic pricing in response to demand and competitor actions."
      ],
      applications: "Demand forecasting, inventory optimization, logistics and transportation planning, warehouse automation, predictive analytics for risk management, and intelligent procurement."
    },
    timeLimit: 130
  },
  {
    id: 50100027,
    category: 'Procurement',
    topic: 'Total Cost of Ownership (TCO)',
    difficulty: 'Hard',
    question: "When evaluating suppliers, the Total Cost of Ownership (TCO) approach considers:",
    options: [
      "Only the initial purchase price of goods or services.",
      "The purchase price plus transportation costs.",
      "All direct and indirect costs associated with acquiring, using, and disposing of a product or service over its entire lifecycle.",
      "Primarily the supplier's quoted price and payment terms."
    ],
    correct: 2,
    explanation: "TCO includes not just the acquisition price but also costs related to use, maintenance, support, and end-of-life (e.g., disposal, salvage value), providing a more holistic view of the true cost.",
    detailedExplanation: {
      concept: "Total Cost of Ownership (TCO) is a financial estimate intended to help buyers and owners determine the direct and indirect costs of a product or system. It's a management accounting concept that looks beyond the initial purchase price to include all costs incurred during the asset's lifetime.",
      whyCorrect: "TCO provides a comprehensive assessment by including acquisition costs (price, shipping, installation), operating costs (energy, labor, maintenance, consumables), and end-of-life costs (disposal, decommissioning, salvage value). This helps in making more informed sourcing decisions by revealing that the supplier with the lowest price may not always offer the lowest total cost.",
      whyOthersWrong: {
        "A": "This is a simplistic view, ignoring many other significant costs.",
        "B": "This is better than just price but still incomplete; it misses operational and disposal costs.",
        "D": "Quoted price and payment terms are part of TCO but do not represent its entirety."
      },
      examples: [
        "Comparing two machines: one has a lower purchase price but higher energy consumption and maintenance costs over its lifespan, potentially leading to a higher TCO.",
        "Evaluating software: considering license fees, implementation costs, training, ongoing support, and upgrade costs.",
        "Sourcing components: factoring in quality-related costs (defects, rework), inventory holding costs, and supplier management costs, not just the unit price."
      ],
      applications: "Supplier selection, capital equipment purchasing, IT procurement, strategic sourcing, and making investment decisions."
    },
    timeLimit: 140
  },
  {
    id: 50100028,
    category: 'Logistics',
    topic: 'Intermodal Transportation',
    difficulty: 'Medium',
    question: "What is the defining characteristic of intermodal transportation?",
    options: [
      "Using only one mode of transport for the entire journey.",
      "Shipping goods in bulk without using containers.",
      "Moving freight in a container or vehicle, using multiple modes of transportation (e.g., rail, ship, and truck) without handling the freight itself when changing modes.",
      "Exclusively using air and truck for expedited shipments."
    ],
    correct: 2,
    explanation: "Intermodal transportation involves using two or more modes of transport to move goods in the same loading unit (e.g., container) from origin to destination, with minimized handling of the freight itself during transfers.",
    detailedExplanation: {
      concept: "Intermodal freight transport involves the transportation of freight in an intermodal container or vehicle, using multiple modes of transportation (rail, ship, truck, etc.), without any handling of the freight itself when changing modes. The freight remains in its loading unit throughout the journey.",
      whyCorrect: "The key is the use of standardized containers (like ISO containers) or vehicles (like truck trailers on flatcars - TOFC) that can be seamlessly transferred between different modes (e.g., from a ship to a train, then to a truck) without unpacking and repacking the cargo. This improves efficiency, reduces damage, and can lower costs.",
      whyOthersWrong: {
        "A": "This describes unimodal transportation.",
        "B": "Intermodal heavily relies on containers; bulk shipping is different.",
        "D": "While air and truck can be part of an intermodal move, intermodal is not exclusive to them and often involves rail and sea for longer distances."
      },
      examples: [
        "A container shipped from Asia to Europe by sea, then moved by rail inland, and finally delivered to the customer by truck.",
        "Piggyback service: Truck trailers transported on railway flatcars.",
        "Roadrailers: Highway trailers with integrated rail bogies."
      ],
      applications: "Global trade, long-distance domestic freight, reducing road congestion, improving fuel efficiency, and leveraging the strengths of different transport modes."
    },
    timeLimit: 120
  },
  {
    id: 50100029,
    category: 'Operations',
    topic: 'Capacity Planning',
    difficulty: 'Medium',
    question: "Which capacity planning strategy involves adding capacity in anticipation of future demand increases?",
    options: [
      "Lag strategy",
      "Match strategy",
      "Lead strategy",
      "Adjustment strategy"
    ],
    correct: 2,
    explanation: "A lead strategy adds capacity proactively, ahead of demand, to ensure capacity is available to meet forecasted growth and potentially gain market share.",
    detailedExplanation: {
      concept: "Capacity planning is the process of determining the production capacity needed by an organization to meet changing demands for its products. Strategies include leading demand, lagging demand, or matching demand.",
      whyCorrect: "The lead strategy is an aggressive approach where capacity is increased before actual demand materializes. This ensures that there is always sufficient capacity, can deter competitors, and allows the company to capture demand growth. However, it risks underutilization if demand doesn't grow as expected.",
      whyOthersWrong: {
        "A": "A lag strategy involves adding capacity only after demand has already increased, which can result in lost sales if demand outstrips capacity.",
        "B": "A match strategy (or tracking strategy) tries to add capacity in small increments to keep pace with increasing demand.",
        "D": "Adjustment strategy is a general term and not a specific capacity planning approach like lead, lag, or match. It might refer to short-term adjustments."
      },
      examples: [
        "A tech company building a new data center well in advance of projected user growth.",
        "A manufacturer investing in new machinery expecting a surge in orders for a new product.",
        "A retailer opening new stores in a rapidly growing suburban area."
      ],
      applications: "Strategic long-term planning, facility investment decisions, resource allocation, and ensuring production capabilities align with market demand forecasts."
    },
    timeLimit: 110
  },
  {
    id: 50100030,
    category: 'Supply Chain Strategy',
    topic: 'Vendor-Managed Inventory (VMI)',
    difficulty: 'Hard',
    question: "In a Vendor-Managed Inventory (VMI) arrangement, who is primarily responsible for determining order quantities and managing inventory levels at the customer's location?",
    options: [
      "The customer, based on their own forecasting.",
      "A third-party logistics provider (3PL) hired by the customer.",
      "The supplier (vendor), based on shared demand data and agreed service levels.",
      "An independent auditing firm."
    ],
    correct: 2,
    explanation: "Under VMI, the supplier takes responsibility for monitoring the customer's inventory levels (often via data feeds) and making replenishment decisions to maintain agreed-upon stock levels.",
    detailedExplanation: {
      concept: "Vendor-Managed Inventory (VMI) is a supply chain collaboration model where the supplier (vendor) takes on the responsibility for managing the inventory of their products at the customer's location. The customer shares demand and inventory data, and the supplier decides when and how much to replenish.",
      whyCorrect: "In VMI, the decision-making for replenishment shifts from the customer to the supplier. The supplier uses data from the customer (e.g., sales data, current inventory levels) to determine appropriate order sizes and timing, aiming to prevent stockouts while optimizing inventory for both parties.",
      whyOthersWrong: {
        "A": "This describes a traditional ordering process, not VMI.",
        "B": "While a 3PL might execute physical replenishment, the decision-making responsibility in VMI lies with the vendor, not a 3PL hired by the customer for this specific VMI decision role.",
        "D": "Auditing firms verify inventory, they don't manage it in a VMI context."
      },
      examples: [
        "Procter & Gamble managing inventory of its products at Walmart's distribution centers.",
        "A supplier of industrial components monitoring stock levels at a manufacturing plant and automatically shipping replacements.",
        "Beverage distributors restocking shelves at retail stores based on sales data."
      ],
      applications: "Improving inventory turnover, reducing stockouts, lowering administrative costs for the customer, strengthening supplier-customer relationships, and improving overall supply chain efficiency."
    },
    timeLimit: 140
  }
];