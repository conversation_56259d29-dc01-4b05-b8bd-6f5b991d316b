'use client'
import React, { useEffect, useRef, useState } from 'react';
import { 
  <PERSON>ertCircle, 
  CheckCircle, 
  Download, 
  Mic, 
  <PERSON>c<PERSON>ff,
  Pa<PERSON>, 
  Play, 
  RotateCcw, 
  Star, 
  StarOff, 
  StopCircle,
  Video,
  VideoOff,
  Clock,
  Target,
  BookOpen,
  FileText,
  Settings,
  Award,
  Zap
} from 'lucide-react';

// Define types for the question data
type InterviewQuestion = {
  id: number;
  question: string;
  category: string;
  difficulty: string;
  concept: string;
  goodAnswer: string;
  commonMistakes: string;
  exampleApplications: string;
};

// Mock data for behavioral questions
const behavioralQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: "Tell me about a time when you had to lead a team through a difficult project.",
    category: "Leadership",
    difficulty: "Medium",
    concept: "Leadership under pressure",
    goodAnswer: "Use STAR method: Situation, Task, Action, Result. Focus on specific actions you took.",
    commonMistakes: "Being too vague, not taking ownership, focusing on team failures",
    exampleApplications: "Project management, crisis management, team motivation"
  },
  {
    id: 2,
    question: "Describe a situation where you had to work with a difficult team member.",
    category: "Teamwork",
    difficulty: "Easy",
    concept: "Conflict resolution and collaboration",
    goodAnswer: "Show empathy, communication skills, and problem-solving approach.",
    commonMistakes: "Badmouthing colleagues, not showing personal growth",
    exampleApplications: "Cross-functional collaboration, remote work, diverse teams"
  },
  {
    id: 3,
    question: "How do you handle tight deadlines and pressure?",
    category: "Communication",
    difficulty: "Hard",
    concept: "Stress management and time prioritization",
    goodAnswer: "Demonstrate planning, prioritization, and stakeholder communication.",
    commonMistakes: "Claiming you never feel stressed, not providing concrete examples",
    exampleApplications: "Product launches, client deliverables, emergency responses"
  }
];

export default function MockInterviewPage() {
  const previewRef = useRef<HTMLVideoElement>(null);
  const recordedRef = useRef<HTMLVideoElement>(null);

  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [mediaStream, setMediaStream] = useState<MediaStream | null>(null);
  const [recordedChunks, setRecordedChunks] = useState<Blob[]>([]);

  const [countdown, setCountdown] = useState<number | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);

  const [timer, setTimer] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [loading, setLoading] = useState(false);

  const [transcript, setTranscript] = useState('Sample transcript would appear here after processing...');
  const [showTranscript, setShowTranscript] = useState(false);

  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedDifficulty, setSelectedDifficulty] = useState('All');
  const [currentQuestion, setCurrentQuestion] = useState<InterviewQuestion>(behavioralQuestions[0]); // Use first question deterministically
  const [shownQuestions, setShownQuestions] = useState<InterviewQuestion[]>([]);
  const [favorites, setFavorites] = useState(new Set<number>());
  const [showOnlyFavorites, setShowOnlyFavorites] = useState(false);
  const [notes, setNotes] = useState<Record<number, string>>({});
  
  // Define types for the feedback data
  type EmotionAnalysis = {
    audio_emotions: Record<string, number>;
    text_sentiment: Record<string, number | string>;
  };
  
  type Feedback = {
    strengths: string[];
    improvementAreas: string[];
    tips: string;
    emotion_analysis: EmotionAnalysis;
  };
  
  const [feedbacks, setFeedbacks] = useState<Record<number, Feedback>>({});
  
  // Calculate progress for the timer
  const timerProgress = Math.min(100, (timer / 180) * 100);

  // Utility: format seconds into MM:SS
  const formatTime = (s: number) =>
    `${String(Math.floor(s/60)).padStart(2,'0')}:${String(s%60).padStart(2,'0')}`;

  // Select a random question based on filters
  function getRandomQuestion(category: string, difficulty: string): InterviewQuestion {
    let filtered = category === 'All'
      ? behavioralQuestions
      : behavioralQuestions.filter((q) => q.category === category);
    filtered = difficulty === 'All'
      ? filtered
      : filtered.filter((q) => q.difficulty === difficulty);
    const idx = Math.floor(Math.random() * filtered.length);
    return filtered[idx] || behavioralQuestions[0];
  }

  // Countdown and timer effects
  useEffect(() => {
    if (countdown !== null) {
      if (countdown > 0) {
        countdownRef.current = setTimeout(() => setCountdown(countdown - 1), 1000);
      } else {
        startActualRecording();
        setCountdown(null);
      }
    }
    return () => {
      if (countdownRef.current) clearTimeout(countdownRef.current);
    };
  }, [countdown]);

  // Auto-stop at 3 minutes and rotate question every 60s
  useEffect(() => {
    if (isRecording && timer >= 180) {
      showToast("Time's up! Interview completed.", "success");
      stopRecording();
    }
    if (isRecording && timer > 0 && timer % 60 === 0) {
      const newQ = getRandomQuestion(selectedCategory, selectedDifficulty);
      setCurrentQuestion(newQ);
      setShownQuestions((prev) => [...prev, newQ]);
      showToast("New question loaded!", "info");
    }
  }, [timer, isRecording, selectedCategory, selectedDifficulty]);

  // Timer effect - only run when recording AND not paused
  useEffect(() => {
    if (isRecording && !isPaused) {
      timerRef.current = setInterval(() => setTimer((t) => t + 1), 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [isRecording, isPaused]);

  const showToast = (message: string, type = "info") => {
    // Simple toast simulation - in real app you'd use a proper toast library
    console.log(`${type.toUpperCase()}: ${message}`);
  };

  // Start the 3-second countdown before recording
  const startRecording = () => {
    setCurrentQuestion(getRandomQuestion(selectedCategory, selectedDifficulty));
    setShownQuestions([]);
    setFeedbacks({});
    setTranscript('');
    setShowTranscript(false);
    setCountdown(3);
    showToast("Starting interview in 3 seconds...", "info");
  };

  // Initialize MediaRecorder and start recording
  const startActualRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
      setMediaStream(stream);
      if (previewRef.current) previewRef.current.srcObject = stream;

      const recorder = new MediaRecorder(stream, { mimeType: 'video/webm;codecs=vp8,opus' });
      const chunks: Blob[] = [];

      recorder.ondataavailable = (e) => { 
        if (e.data.size > 0) {
          console.log('Data chunk received:', e.data.size);
          chunks.push(e.data); 
        }
      };
      
      recorder.onstop = () => {
        console.log('Recording stopped, chunks:', chunks.length);
        try {
          const blob = new Blob(chunks, { type: 'video/webm' });
          console.log('Blob created:', blob.size);
          
          if (recordedRef.current && blob.size > 0) {
            const videoUrl = URL.createObjectURL(blob);
            console.log('Video URL created:', videoUrl);
            recordedRef.current.src = videoUrl;
            recordedRef.current.load();
            
            // Add event listeners for debugging
            recordedRef.current.onloadstart = () => console.log('Video load started');
            recordedRef.current.onloadeddata = () => console.log('Video data loaded');
            recordedRef.current.onerror = (e) => console.error('Video error:', e);
          }
          setRecordedChunks(chunks);
        } catch (error) {
          console.error('Error creating video blob:', error);
          showToast('Error processing video recording', 'error');
        }
        
        stream.getTracks().forEach((track) => track.stop());
        setMediaStream(null);
      };

      recorder.start(1000); // Record in 1-second chunks for better reliability
      setMediaRecorder(recorder);
      setIsRecording(true);
      setIsPaused(false);
      setTimer(0);
      showToast("Recording started!", "success");
    } catch (err) {
      console.error('Recording error:', err);
      showToast('Camera/Microphone access denied. Please allow permissions.', "error");
    }
  };

  // Pause recording
  const pauseRecording = () => {
    if (mediaRecorder?.state === 'recording' && !isPaused) {
      mediaRecorder.pause();
      setIsPaused(true);
      showToast("Interview paused. Click resume when ready.", "info");
    }
  };

  // Resume recording
  const resumeRecording = () => {
    if (mediaRecorder?.state === 'paused' && isPaused) {
      mediaRecorder.resume();
      setIsPaused(false);
      showToast("Interview resumed!", "success");
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorder?.state === 'recording' || mediaRecorder?.state === 'paused') {
      mediaRecorder.stop();
    }
    setIsRecording(false);
    setIsPaused(false);
  };

  // Reset entire session
  const resetSession = () => {
    setRecordedChunks([]);
    setTimer(0);
    setShownQuestions([]);
    setFeedbacks({});
    setTranscript('');
    setShowTranscript(false);
    setIsPaused(false);
    if (recordedRef.current) {
      if (recordedRef.current.src) {
        URL.revokeObjectURL(recordedRef.current.src); // Clean up blob URL
      }
      recordedRef.current.src = '';
    }
    showToast("Session reset. Ready for a new interview!", "info");
  };

  // Download recorded video
  const downloadVideo = () => {
    if (!recordedChunks.length) return;
    const blob = new Blob(recordedChunks, { type: 'video/webm' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mock_interview_${new Date().toISOString().slice(0,10)}.webm`;
    a.click();
    URL.revokeObjectURL(url);
    showToast("Video downloaded successfully!", "success");
  };

  // Download transcript as .txt
  const downloadTranscript = () => {
    if (!transcript) return;
    const blob = new Blob([transcript], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `interview_transcript_${new Date().toISOString().slice(0,10)}.txt`;
    a.click();
    URL.revokeObjectURL(url);
    showToast("Transcript downloaded!", "success");
  };

  // Simulate AI feedback processing
  const processFeedback = async () => {
    setLoading(true);
    showToast("Processing your interview responses...", "info");
    
    // Simulate API processing time
    setTimeout(() => {
      const mockFeedback: Record<number, Feedback> = {
        [currentQuestion.id]: {
          strengths: [
            "Clear articulation of your approach",
            "Good use of specific examples", 
            "Demonstrated leadership qualities"
          ],
          improvementAreas: [
            "Could provide more quantifiable results",
            "Consider discussing stakeholder impact"
          ],
          tips: "Try using the STAR method more consistently throughout your response",
          emotion_analysis: {
            audio_emotions: {
              confident: 0.75,
              nervous: 0.15,
              enthusiastic: 0.65,
              calm: 0.55
            },
            text_sentiment: {
              compound: 0.6,
              positive: 0.7,
              neutral: 0.25,
              negative: 0.05
            }
          }
        }
      };
      setFeedbacks(mockFeedback);
      setLoading(false);
      showToast("AI feedback generated successfully!", "success");
    }, 2000);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch(difficulty) {
      case 'Easy': return 'bg-green-50 text-green-600 border-green-200';
      case 'Medium': return 'bg-amber-50 text-amber-600 border-amber-200';
      case 'Hard': return 'bg-rose-50 text-rose-600 border-rose-200';
      default: return 'bg-slate-50 text-slate-600 border-slate-200';
    }
  };

  // Use a stable, deterministic approach to return icons
  const getCategoryIcon = (category: string) => {
    // Ensure consistent rendering between server and client
    switch (category) {
      case 'Leadership':
        return <Target className="w-4 h-4" />;
      case 'Teamwork': 
        return <Award className="w-4 h-4" />;
      case 'Communication':
        return <Zap className="w-4 h-4" />;
      default:
        return <BookOpen className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-sky-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-sky-400 to-indigo-400 rounded-lg flex items-center justify-center">
                <Video className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-slate-800">InterviewAce</h1>
                <p className="text-sm text-slate-500">AI-Powered Mock Interviews</p>
              </div>
            </div>
            <div className="flex items-center space-x-2 text-sm text-slate-500">
              <Clock className="w-4 h-4" />
              <span>Professional Practice Platform</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Interview Panel */}
          <div className="lg:col-span-2 space-y-6">
            {/* Video Section */}
            <div className="bg-white rounded-2xl shadow-lg border border-slate-100 overflow-hidden">
              <div className="bg-gradient-to-r from-sky-300 to-indigo-300 px-6 py-4">
                <h2 className="text-xl font-semibold text-white flex items-center">
                  <Video className="w-5 h-5 mr-2" />
                  Interview Session
                </h2>
              </div>
              
              <div className="p-6">
                {/* Video Display */}
                <div className="relative mb-6">
                  {countdown !== null ? (
                    <div className="w-full h-80 bg-gradient-to-br from-sky-300 to-indigo-400 rounded-xl flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-8xl font-bold text-white mb-4">{countdown}</div>
                        <div className="text-white/80 text-lg">Get ready...</div>
                      </div>
                    </div>
                  ) : (
                    <div className="relative">
                      <video 
                        ref={previewRef} 
                        autoPlay 
                        muted 
                        playsInline 
                        className="w-full h-80 rounded-xl bg-slate-800 object-cover"
                      />
                      {!mediaStream && !isRecording && (
                        <div className="absolute inset-0 bg-slate-800 rounded-xl flex items-center justify-center">
                          <div className="text-center text-white">
                            <VideoOff className="w-16 h-16 mx-auto mb-4 opacity-50" />
                            <p className="text-lg">Camera will activate when you start recording</p>
                          </div>
                        </div>
                      )}
                      
                      {/* Recording Indicator */}
                      {isRecording && (
                        <div className="absolute top-4 left-4 flex items-center space-x-2">
                          <div className={`w-3 h-3 ${isPaused ? 'bg-amber-500' : 'bg-red-500'} rounded-full ${!isPaused ? 'animate-pulse' : ''}`}></div>
                          <span className="text-white font-medium bg-black/50 px-2 py-1 rounded">
                            {isPaused ? 'PAUSED' : 'RECORDING'}
                          </span>
                        </div>
                      )}
                      
                      {/* Timer Display */}
                      {isRecording && (
                        <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded font-mono">
                          {formatTime(timer)} / 03:00
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Progress Bar */}
                {isRecording && (
                  <div className="mb-6">
                    <div className="flex justify-between text-sm text-slate-600 mb-2">
                      <span>Progress</span>
                      <span>{Math.round(timerProgress)}%</span>
                    </div>
                    <div className="w-full bg-slate-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-300 ${
                          timerProgress > 80 ? 'bg-rose-400' : timerProgress > 60 ? 'bg-amber-400' : 'bg-sky-400'
                        }`}
                        style={{width: `${timerProgress}%`}}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Control Buttons */}
                <div className="flex flex-wrap justify-center gap-3 mb-6">
                  <button
                    onClick={startRecording}
                    disabled={isRecording || countdown !== null}
                    className="flex items-center space-x-2 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-slate-300 disabled:to-slate-400 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg"
                  >
                    <Play className="w-5 h-5" />
                    <span>Start Interview</span>
                  </button>

                  {/* Pause/Resume Button */}
                  <button
                    onClick={isPaused ? resumeRecording : pauseRecording}
                    disabled={!isRecording}
                    className="flex items-center space-x-2 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 disabled:from-slate-300 disabled:to-slate-400 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg"
                  >
                    {isPaused ? <Play className="w-5 h-5" /> : <Pause className="w-5 h-5" />}
                    <span>{isPaused ? 'Resume' : 'Pause'}</span>
                  </button>

                  <button
                    onClick={stopRecording}
                    disabled={!isRecording}
                    className="flex items-center space-x-2 bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 disabled:from-slate-300 disabled:to-slate-400 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg"
                  >
                    <StopCircle className="w-5 h-5" />
                    <span>Stop</span>
                  </button>

                  <button
                    onClick={resetSession}
                    className="flex items-center space-x-2 bg-slate-400 hover:bg-slate-500 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg"
                  >
                    <RotateCcw className="w-5 h-5" />
                    <span>Reset</span>
                  </button>
                </div>

                {/* Status Display */}
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="bg-slate-50 rounded-lg p-3 border border-slate-200">
                    <div className={`text-2xl font-bold ${isRecording ? (isPaused ? 'text-amber-500' : 'text-green-500') : 'text-slate-400'}`}>
                      {isRecording ? (isPaused ? '⏸' : '●') : '○'}
                    </div>
                    <div className="text-sm text-slate-600">Status</div>
                    <div className="text-xs font-medium text-slate-500">
                      {isRecording ? (isPaused ? 'Paused' : 'Recording') : 'Ready'}
                    </div>
                  </div>
                  <div className="bg-slate-50 rounded-lg p-3 border border-slate-200">
                    <div className="text-2xl font-bold text-sky-500">{formatTime(180 - timer)}</div>
                    <div className="text-sm text-slate-600">Time Left</div>
                    <div className={`text-xs font-medium ${180-timer <= 30 ? 'text-rose-500' : 'text-slate-500'}`}>
                      of 3:00 minutes
                    </div>
                  </div>
                  <div className="bg-slate-50 rounded-lg p-3 border border-slate-200">
                    <div className="text-2xl font-bold text-indigo-500">{shownQuestions.length}</div>
                    <div className="text-sm text-slate-600">Questions</div>
                    <div className="text-xs font-medium text-slate-500">answered</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Recorded Video Playback */}
            {recordedChunks.length > 0 && (
              <div className="bg-white rounded-2xl shadow-lg border border-slate-100 overflow-hidden">
                <div className="bg-gradient-to-r from-violet-300 to-purple-300 px-6 py-4">
                  <h3 className="text-xl font-semibold text-white flex items-center">
                    <Play className="w-5 h-5 mr-2" />
                    Your Recording
                  </h3>
                </div>
                <div className="p-6">
                  <video 
                    ref={recordedRef} 
                    controls 
                    className="w-full rounded-xl mb-4"
                  />
                  <div className="flex gap-3">
                    <button
                      onClick={downloadVideo}
                      className="flex items-center space-x-2 bg-sky-400 hover:bg-sky-500 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg"
                    >
                      <Download className="w-4 h-4" />
                      <span>Download Video</span>
                    </button>
                    <button
                      onClick={processFeedback}
                      disabled={loading}
                      className="flex items-center space-x-2 bg-emerald-400 hover:bg-emerald-500 disabled:bg-slate-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg"
                    >
                      {loading ? (
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <Zap className="w-4 h-4" />
                      )}
                      <span>{loading ? 'Analyzing...' : 'Get AI Feedback'}</span>
                    </button>
                    
                    <button
                      onClick={() => {
                        const newQ = getRandomQuestion(selectedCategory, selectedDifficulty);
                        setCurrentQuestion(newQ);
                        showToast("New question loaded!", "info");
                      }}
                      disabled={isRecording || countdown !== null}
                      className="flex items-center space-x-2 bg-indigo-400 hover:bg-indigo-500 disabled:bg-slate-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg"
                    >
                      <Target className="w-4 h-4" />
                      <span>Next Question</span>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Current Question */}
            <div className="bg-white rounded-2xl shadow-lg border border-slate-100 overflow-hidden">
              <div className="bg-gradient-to-r from-indigo-300 to-purple-300 px-6 py-4">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <Target className="w-5 h-5 mr-2" />
                  Current Question
                </h3>
              </div>
              <div className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(currentQuestion.category)}`}>
                    {getCategoryIcon(currentQuestion.category)}
                    {currentQuestion.category}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(currentQuestion.difficulty)}`}>
                    {currentQuestion.difficulty}
                  </span>
                </div>
                <p className="text-slate-700 font-medium leading-relaxed">
                  {countdown === null && !isRecording && "Preview: "}
                  {currentQuestion.question}
                </p>
                {countdown === null && !isRecording && (
                  <div className="mt-4 p-3 bg-sky-50 rounded-lg border border-sky-200">
                    <p className="text-sm text-sky-700">
                      <strong>Tip:</strong> {currentQuestion.goodAnswer}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-2xl shadow-lg border border-slate-100 overflow-hidden">
              <div className="bg-gradient-to-r from-emerald-300 to-teal-300 px-6 py-4">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <Settings className="w-5 h-5 mr-2" />
                  Question Filters
                </h3>
              </div>
              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Category</label>
                  <div className="grid grid-cols-2 gap-2">
                    {['All','Leadership','Teamwork','Communication'].map(cat => (
                      <button
                        key={cat}
                        onClick={() => {
                          setSelectedCategory(cat);
                          // Update current question immediately when filter changes
                          if (!isRecording && countdown === null) {
                            setCurrentQuestion(getRandomQuestion(cat, selectedDifficulty));
                          }
                        }}
                        disabled={isRecording || countdown !== null}
                        className={`px-3 py-2 text-sm rounded-lg font-medium transition-all duration-200 border ${
                          selectedCategory === cat
                            ? 'bg-indigo-100 text-indigo-700 border-indigo-300'
                            : 'bg-slate-50 text-slate-600 border-slate-200 hover:bg-slate-100'
                        } disabled:opacity-50 disabled:cursor-not-allowed`}
                      >
                        {cat}
                      </button>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Difficulty</label>
                  <div className="grid grid-cols-2 gap-2">
                    {['All','Easy','Medium','Hard'].map(diff => (
                      <button
                        key={diff}
                        onClick={() => {
                          setSelectedDifficulty(diff);
                          // Update current question immediately when filter changes
                          if (!isRecording && countdown === null) {
                            setCurrentQuestion(getRandomQuestion(selectedCategory, diff));
                          }
                        }}
                        disabled={isRecording || countdown !== null}
                        className={`px-3 py-2 text-sm rounded-lg font-medium transition-all duration-200 border ${
                          selectedDifficulty === diff
                            ? 'bg-emerald-100 text-emerald-700 border-emerald-300'
                            : 'bg-slate-50 text-slate-600 border-slate-200 hover:bg-slate-100'
                        } disabled:opacity-50 disabled:cursor-not-allowed`}
                      >
                        {diff}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white rounded-2xl shadow-lg border border-slate-100 overflow-hidden">
              <div className="bg-gradient-to-r from-orange-300 to-pink-300 px-6 py-4">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <Award className="w-5 h-5 mr-2" />
                  Session Stats
                </h3>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-slate-600">Questions Answered</span>
                    <span className="font-semibold text-slate-800">{shownQuestions.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-slate-600">Recording Time</span>
                    <span className="font-semibold text-slate-800">{formatTime(timer)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-slate-600">Favorites</span>
                    <span className="font-semibold text-slate-800">{favorites.size}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-slate-600">Category Focus</span>
                    <span className="font-semibold text-slate-800">{selectedCategory}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Review Section - Always show with demo data or actual questions */}
        <div className="mt-8 bg-white rounded-2xl shadow-lg border border-slate-100 overflow-hidden">
          <div className="bg-gradient-to-r from-violet-300 to-purple-300 px-6 py-4">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold text-white flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Interview Review & Analysis
              </h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowOnlyFavorites(!showOnlyFavorites)}
                  className="flex items-center space-x-1 bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200"
                >
                  <Star className="w-4 h-4" />
                  <span>{showOnlyFavorites ? 'Show All' : 'Favorites Only'}</span>
                </button>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Transcript Section */}
            <div className="mb-6 p-4 bg-slate-50 rounded-xl border border-slate-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-lg font-semibold text-slate-700 flex items-center">
                  <Mic className="w-5 h-5 mr-2" />
                  Interview Transcript
                </h4>
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowTranscript(!showTranscript)}
                    className="flex items-center space-x-1 bg-sky-400 hover:bg-sky-500 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200"
                  >
                    <FileText className="w-4 h-4" />
                    <span>{showTranscript ? 'Hide' : 'Show'} Transcript</span>
                  </button>
                  <button
                    onClick={downloadTranscript}
                    disabled={!transcript}
                    className="flex items-center space-x-1 bg-emerald-400 hover:bg-emerald-500 disabled:bg-slate-300 disabled:cursor-not-allowed text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200"
                  >
                    <Download className="w-4 h-4" />
                    <span>Download</span>
                  </button>
                </div>
              </div>
              
              {showTranscript && (
                <div className="bg-white p-4 rounded-lg border border-slate-200 text-sm leading-relaxed text-slate-600">
                  {transcript || 'Transcript will appear here after processing your recording...'}
                </div>
              )}
            </div>

            {/* Questions Review - Show demo data if no actual questions yet */}
            <div className="space-y-6">
              {shownQuestions.length > 0 ? (
                /* Show actual answered questions */
                (showOnlyFavorites ? shownQuestions.filter(q => favorites.has(q.id)) : shownQuestions).map(q => {
                  const fb = feedbacks[q.id];
                  return (
                    <div key={q.id} className="border border-slate-200 rounded-xl p-6 hover:border-slate-300 transition-all duration-200">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-3">
                            <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(q.category)}`}>
                              {getCategoryIcon(q.category)}
                              {q.category}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(q.difficulty)}`}>
                              {q.difficulty}
                            </span>
                          </div>
                          <h4 className="text-lg font-semibold text-slate-700 mb-3">{q.question}</h4>
                        </div>
                        <button
                          onClick={() => {
                            const newFavorites = new Set(favorites);
                            favorites.has(q.id) ? newFavorites.delete(q.id) : newFavorites.add(q.id);
                            setFavorites(newFavorites);
                          }}
                          className={`flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-200 ${
                            favorites.has(q.id) 
                              ? 'bg-amber-100 text-amber-500 hover:bg-amber-200' 
                              : 'bg-slate-100 text-slate-400 hover:bg-slate-200'
                          }`}
                        >
                          {favorites.has(q.id) ? <Star className="w-5 h-5 fill-current" /> : <StarOff className="w-5 h-5" />}
                        </button>
                      </div>

                      {/* Question Details */}
                      <div className="grid md:grid-cols-2 gap-4 mb-4">
                        <div className="bg-sky-50 p-3 rounded-lg border border-sky-200">
                          <h5 className="font-medium text-sky-700 mb-1">Key Concept</h5>
                          <p className="text-sm text-sky-600">{q.concept}</p>
                        </div>
                        <div className="bg-emerald-50 p-3 rounded-lg border border-emerald-200">
                          <h5 className="font-medium text-emerald-700 mb-1">Good Answer Approach</h5>
                          <p className="text-sm text-emerald-600">{q.goodAnswer}</p>
                        </div>
                        <div className="bg-rose-50 p-3 rounded-lg border border-rose-200">
                          <h5 className="font-medium text-rose-700 mb-1">Common Mistakes</h5>
                          <p className="text-sm text-rose-600">{q.commonMistakes}</p>
                        </div>
                        <div className="bg-violet-50 p-3 rounded-lg border border-violet-200">
                          <h5 className="font-medium text-violet-700 mb-1">Applications</h5>
                          <p className="text-sm text-violet-600">{q.exampleApplications}</p>
                        </div>
                      </div>

                      {/* Personal Notes */}
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-slate-700 mb-2">Your Reflection Notes</label>
                        <textarea
                          className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-sky-400 focus:border-sky-400 resize-none transition-all duration-200"
                          rows={3}
                          placeholder="Write your thoughts, learnings, and areas for improvement..."
                          value={notes[q.id] || ''}
                          onChange={e => {
                            const updatedNotes = {...notes};
                            updatedNotes[q.id] = e.target.value;
                            setNotes(updatedNotes);
                          }}
                        />
                      </div>

                      {/* AI Feedback */}
                      {loading && !fb && (
                        <div className="bg-slate-50 p-4 rounded-lg border border-slate-200">
                          <div className="flex items-center justify-center space-x-2">
                            <div className="w-5 h-5 border-2 border-sky-400 border-t-transparent rounded-full animate-spin"></div>
                            <span className="text-slate-600">Analyzing your response...</span>
                          </div>
                        </div>
                      )}

                      {fb && (
                        <div className="bg-gradient-to-br from-slate-50 to-sky-50 p-5 rounded-xl border border-slate-200">
                          <h5 className="font-semibold text-slate-700 mb-4 flex items-center">
                            <Zap className="w-5 h-5 mr-2 text-sky-500" />
                            AI Performance Analysis
                          </h5>
                          
                          <div className="grid md:grid-cols-2 gap-4 mb-4">
                            {/* Strengths */}
                            <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-200">
                              <h6 className="font-medium text-emerald-700 mb-3 flex items-center">
                                <CheckCircle className="w-4 h-4 mr-1" />
                                Strengths
                              </h6>
                              <ul className="space-y-2">
                                {fb.strengths?.map((strength, i) => (
                                  <li key={i} className="text-sm text-emerald-600 flex items-start">
                                    <span className="text-emerald-500 mr-2">✓</span>
                                    {strength}
                                  </li>
                                ))}
                              </ul>
                            </div>

                            {/* Improvement Areas */}
                            <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                              <h6 className="font-medium text-amber-700 mb-3 flex items-center">
                                <AlertCircle className="w-4 h-4 mr-1" />
                                Areas for Improvement
                              </h6>
                              <ul className="space-y-2">
                                {fb.improvementAreas?.map((area, i) => (
                                  <li key={i} className="text-sm text-amber-600 flex items-start">
                                    <span className="text-amber-500 mr-2">⚠</span>
                                    {area}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>

                          {/* Tips */}
                          <div className="bg-sky-50 p-4 rounded-lg border border-sky-200 mb-4">
                            <h6 className="font-medium text-sky-700 mb-2">💡 Pro Tip</h6>
                            <p className="text-sm text-sky-600">{fb.tips}</p>
                          </div>

                          {/* Emotion Analysis */}
                          {fb.emotion_analysis && (
                            <div className="bg-violet-50 p-4 rounded-lg border border-violet-200">
                              <h6 className="font-medium text-violet-700 mb-3">🎭 Emotional Intelligence Analysis</h6>
                              
                              <div className="grid md:grid-cols-2 gap-4">
                                <div>
                                  <h6 className="text-sm font-medium text-violet-600 mb-2 block">Voice Emotions</h6>
                                  <div className="space-y-2">
                                    {Object.entries(fb.emotion_analysis.audio_emotions).map(([emotion, score]) => (
                                      <div key={emotion} className="flex items-center justify-between">
                                        <span className="text-sm text-violet-600 capitalize">{emotion}</span>
                                        <div className="flex items-center space-x-2">
                                          <div className="w-20 bg-violet-200 rounded-full h-2">
                                            <div 
                                              className="bg-violet-400 h-2 rounded-full" 
                                              style={{width: `${score * 100}%`}}
                                            ></div>
                                          </div>
                                          <span className="text-xs text-violet-600 font-medium">
                                            {(score * 100).toFixed(0)}%
                                          </span>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                                
                                <div>
                                  <h6 className="text-sm font-medium text-violet-600 mb-2 block">Overall Sentiment</h6>
                                  <div className="space-y-2">
                                    {Object.entries(fb.emotion_analysis.text_sentiment).map(([sentiment, score]) => (
                                      <div key={sentiment} className="flex items-center justify-between">
                                        <span className="text-sm text-violet-600 capitalize">{sentiment}</span>
                                        <span className="text-xs text-violet-600 font-medium">
                                          {typeof score === 'number' ? score.toFixed(2) : score}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })
              ) : (
                /* Show demo question when no questions answered yet */
                <div className="border border-slate-200 rounded-xl p-6 hover:border-slate-300 transition-all duration-200">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-3">
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(currentQuestion.category)}`}>
                          {getCategoryIcon(currentQuestion.category)}
                          {currentQuestion.category}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(currentQuestion.difficulty)}`}>
                          {currentQuestion.difficulty}
                        </span>
                      </div>
                      <h4 className="text-lg font-semibold text-slate-700 mb-3">{currentQuestion.question}</h4>
                    </div>
                    <button
                      onClick={() => {
                        const newFavorites = new Set(favorites);
                        favorites.has(currentQuestion.id) ? newFavorites.delete(currentQuestion.id) : newFavorites.add(currentQuestion.id);
                        setFavorites(newFavorites);
                      }}
                      className={`flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-200 ${
                        favorites.has(currentQuestion.id) 
                          ? 'bg-amber-100 text-amber-500 hover:bg-amber-200' 
                          : 'bg-slate-100 text-slate-400 hover:bg-slate-200'
                      }`}
                    >
                      {favorites.has(currentQuestion.id) ? <Star className="w-5 h-5 fill-current" /> : <StarOff className="w-5 h-5" />}
                    </button>
                  </div>

                  {/* Question Details */}
                  <div className="grid md:grid-cols-2 gap-4 mb-4">
                    <div className="bg-sky-50 p-3 rounded-lg border border-sky-200">
                      <h5 className="font-medium text-sky-700 mb-1">Key Concept</h5>
                      <p className="text-sm text-sky-600">{currentQuestion.concept}</p>
                    </div>
                    <div className="bg-emerald-50 p-3 rounded-lg border border-emerald-200">
                      <h5 className="font-medium text-emerald-700 mb-1">Good Answer Approach</h5>
                      <p className="text-sm text-emerald-600">{currentQuestion.goodAnswer}</p>
                    </div>
                    <div className="bg-rose-50 p-3 rounded-lg border border-rose-200">
                      <h5 className="font-medium text-rose-700 mb-1">Common Mistakes</h5>
                      <p className="text-sm text-rose-600">{currentQuestion.commonMistakes}</p>
                    </div>
                    <div className="bg-violet-50 p-3 rounded-lg border border-violet-200">
                      <h5 className="font-medium text-violet-700 mb-1">Applications</h5>
                      <p className="text-sm text-violet-600">{currentQuestion.exampleApplications}</p>
                    </div>
                  </div>

                  {/* Personal Notes */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-slate-700 mb-2">Your Reflection Notes</label>
                    <textarea
                      className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-sky-400 focus:border-sky-400 resize-none transition-all duration-200"
                      rows={3}
                      placeholder="Write your thoughts, learnings, and areas for improvement..."
                      value={notes[currentQuestion.id] || ''}
                      onChange={e => {
                        const updatedNotes = {...notes};
                        updatedNotes[currentQuestion.id] = e.target.value;
                        setNotes(updatedNotes);
                      }}
                    />
                  </div>

                  {/* Demo AI Feedback */}
                  <div className="bg-gradient-to-br from-slate-50 to-sky-50 p-5 rounded-xl border border-slate-200">
                    <h5 className="font-semibold text-slate-700 mb-4 flex items-center">
                      <Zap className="w-5 h-5 mr-2 text-sky-500" />
                      Demo AI Performance Analysis
                    </h5>
                    
                    <div className="grid md:grid-cols-2 gap-4 mb-4">
                      {/* Demo Strengths */}
                      <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-200">
                        <h6 className="font-medium text-emerald-700 mb-3 flex items-center">
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Strengths
                        </h6>
                        <ul className="space-y-2">
                          <li className="text-sm text-emerald-600 flex items-start">
                            <span className="text-emerald-500 mr-2">✓</span>
                            Clear articulation of your approach
                          </li>
                          <li className="text-sm text-emerald-600 flex items-start">
                            <span className="text-emerald-500 mr-2">✓</span>
                            Good use of specific examples
                          </li>
                          <li className="text-sm text-emerald-600 flex items-start">
                            <span className="text-emerald-500 mr-2">✓</span>
                            Demonstrated leadership qualities
                          </li>
                        </ul>
                      </div>

                      {/* Demo Improvement Areas */}
                      <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                        <h6 className="font-medium text-amber-700 mb-3 flex items-center">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          Areas for Improvement
                        </h6>
                        <ul className="space-y-2">
                          <li className="text-sm text-amber-600 flex items-start">
                            <span className="text-amber-500 mr-2">⚠</span>
                            Could provide more quantifiable results
                          </li>
                          <li className="text-sm text-amber-600 flex items-start">
                            <span className="text-amber-500 mr-2">⚠</span>
                            Consider discussing stakeholder impact
                          </li>
                        </ul>
                      </div>
                    </div>

                    {/* Demo Tips */}
                    <div className="bg-sky-50 p-4 rounded-lg border border-sky-200 mb-4">
                      <h6 className="font-medium text-sky-700 mb-2">💡 Pro Tip</h6>
                      <p className="text-sm text-sky-600">Try using the STAR method more consistently throughout your response</p>
                    </div>

                    {/* Demo Emotion Analysis */}
                    <div className="bg-violet-50 p-4 rounded-lg border border-violet-200">
                      <h6 className="font-medium text-violet-700 mb-3">🎭 Emotional Intelligence Analysis</h6>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h6 className="text-sm font-medium text-violet-600 mb-2 block">Voice Emotions</h6>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-violet-600 capitalize">Confident</span>
                              <div className="flex items-center space-x-2">
                                <div className="w-20 bg-violet-200 rounded-full h-2">
                                  <div className="bg-violet-400 h-2 rounded-full" style={{width: '75%'}}></div>
                                </div>
                                <span className="text-xs text-violet-600 font-medium">75%</span>
                              </div>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-violet-600 capitalize">Enthusiastic</span>
                              <div className="flex items-center space-x-2">
                                <div className="w-20 bg-violet-200 rounded-full h-2">
                                  <div className="bg-violet-400 h-2 rounded-full" style={{width: '65%'}}></div>
                                </div>
                                <span className="text-xs text-violet-600 font-medium">65%</span>
                              </div>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-violet-600 capitalize">Calm</span>
                              <div className="flex items-center space-x-2">
                                <div className="w-20 bg-violet-200 rounded-full h-2">
                                  <div className="bg-violet-400 h-2 rounded-full" style={{width: '55%'}}></div>
                                </div>
                                <span className="text-xs text-violet-600 font-medium">55%</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div>
                          <h6 className="text-sm font-medium text-violet-600 mb-2 block">Overall Sentiment</h6>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-violet-600 capitalize">Positive</span>
                              <span className="text-xs text-violet-600 font-medium">0.70</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-violet-600 capitalize">Neutral</span>
                              <span className="text-xs text-violet-600 font-medium">0.25</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-violet-600 capitalize">Negative</span>
                              <span className="text-xs text-violet-600 font-medium">0.05</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <p className="text-sm text-blue-700">
                      <strong>Demo Preview:</strong> This shows how your questions will appear after recording. Start an interview to see real AI feedback!
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            {shownQuestions.length > 0 && !loading && Object.keys(feedbacks).length === 0 && (
              <div className="mt-8 text-center">
                <button
                  onClick={processFeedback}
                  disabled={!recordedChunks.length}
                  className="inline-flex items-center space-x-2 bg-gradient-to-r from-sky-400 to-indigo-400 hover:from-sky-500 hover:to-indigo-500 disabled:from-slate-300 disabled:to-slate-400 disabled:cursor-not-allowed text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <Zap className="w-6 h-6" />
                  <span>Generate AI Performance Analysis</span>
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="mt-12 text-center text-slate-500">
          <p className="text-sm">
            Practice makes perfect. Keep improving your interview skills with AI-powered feedback.
          </p>
        </div>
      </div>
    </div>
  );
}