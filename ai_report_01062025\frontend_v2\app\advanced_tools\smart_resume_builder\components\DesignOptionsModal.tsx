import React, { useState } from 'react';
import { X, ChevronDown, Check } from 'lucide-react';

export interface DesignOptions {
  fontFamily: string;
  fontSize: string;
  lineHeight: string;
  margin: string;
  columnLayout: string;
  template?: string;
}

interface DesignOptionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  designOptions: DesignOptions;
  onDesignOptionChange: (option: keyof DesignOptions, value: string) => void;
}

interface LayoutOption {
  id: string;
  label: string;
  icon: string;
}

interface TemplateOption {
  id: string;
  name: string;
}

interface FontOption {
  value: string;
  label: string;
}

const DesignOptionsModal: React.FC<DesignOptionsModalProps> = ({
  isOpen,
  onClose,
  designOptions,
  onDesignOptionChange,
}) => {
  const [activeTab, setActiveTab] = useState('layout');
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  if (!isOpen) return null;

  const fontFamilies: FontOption[] = [
    { value: 'Arial, sans-serif', label: 'Arial' },
    { value: 'Georgia, serif', label: 'Georgia' },
    { value: 'Times New Roman, serif', label: 'Times New Roman' },
    { value: 'Helvetica, sans-serif', label: 'Helvetica' },
    { value: 'Courier New, monospace', label: 'Courier New' },
  ];

  const fontSizes: string[] = ['10px', '11px', '12px', '14px', '16px', '18px', '20px', '22px', '24px'];
  const lineHeights: string[] = ['1.0', '1.15', '1.2', '1.4', '1.6', '1.8', '2.0'];
  const margins: string[] = ['0.25in', '0.5in', '0.75in', '1in', '1.25in'];
  const columnLayouts: LayoutOption[] = [
    { id: 'single', label: 'Single', icon: '📄' },
    { id: 'double', label: 'Double', icon: '📑' },
  ];
  
  const templates: TemplateOption[] = [
    { id: 'professional', name: 'Professional' },
    { id: 'modern', name: 'Modern' },
    { id: 'creative', name: 'Creative' },
    { id: 'minimal', name: 'Minimal' },
  ];
  
  const toggleDropdown = (dropdown: string) => {
    setOpenDropdown(openDropdown === dropdown ? null : dropdown);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="text-lg font-medium">Design Options</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="flex flex-1 overflow-hidden">
          {/* Left sidebar */}
          <div className="w-48 border-r bg-gray-50">
            <div 
              className={`p-3 cursor-pointer hover:bg-gray-100 ${activeTab === 'layout' ? 'bg-white border-r-2 border-blue-500' : ''}`}
              onClick={() => setActiveTab('layout')}
            >
              Layout
            </div>
            <div 
              className={`p-3 cursor-pointer hover:bg-gray-100 ${activeTab === 'typography' ? 'bg-white border-r-2 border-blue-500' : ''}`}
              onClick={() => setActiveTab('typography')}
            >
              Typography
            </div>
            <div 
              className={`p-3 cursor-pointer hover:bg-gray-100 ${activeTab === 'spacing' ? 'bg-white border-r-2 border-blue-500' : ''}`}
              onClick={() => setActiveTab('spacing')}
            >
              Spacing
            </div>
          </div>
          
          {/* Main content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {activeTab === 'layout' && (
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-3">Template</h4>
                  <div className="grid grid-cols-2 gap-3">
                    {templates.map(template => (
                      <div 
                        key={template.id}
                        className="border rounded p-3 text-center cursor-pointer hover:border-blue-500"
                        onClick={() => onDesignOptionChange('template', template.id)}
                      >
                        <div className="h-20 bg-gray-100 mb-2 flex items-center justify-center text-2xl">
                          {template.name === 'Professional' && '📄'}
                          {template.name === 'Modern' && '✨'}
                          {template.name === 'Creative' && '🎨'}
                          {template.name === 'Minimal' && '⬜'}
                        </div>
                        <div className="text-sm">{template.name}</div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Layout</h4>
                  <div className="grid grid-cols-2 gap-3">
                    {columnLayouts.map(layout => (
                      <div 
                        key={layout.id}
                        className={`border rounded p-3 text-center cursor-pointer ${designOptions.columnLayout === layout.id ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-400'}`}
                        onClick={() => onDesignOptionChange('columnLayout', layout.id)}
                      >
                        <div className="h-20 bg-gray-100 mb-2 flex items-center justify-center text-2xl">
                          {layout.icon}
                        </div>
                        <div className="text-sm">{layout.label}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'typography' && (
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-3">Font Family</h4>
                  <div className="relative">
                    <div 
                      className="border rounded p-2 flex justify-between items-center cursor-pointer"
                      onClick={() => toggleDropdown('fontFamily')}
                    >
                      <span>{fontFamilies.find(f => f.value === designOptions.fontFamily)?.label || 'Select font'}</span>
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    </div>
                    {openDropdown === 'fontFamily' && (
                      <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow-lg">
                        {fontFamilies.map(font => (
                          <div 
                            key={font.value}
                            className="p-2 hover:bg-gray-100 cursor-pointer flex justify-between items-center"
                            onClick={() => {
                              onDesignOptionChange('fontFamily', font.value);
                              setOpenDropdown(null);
                            }}
                            style={{ fontFamily: font.value }}
                          >
                            {font.label}
                            {designOptions.fontFamily === font.value && <Check className="w-4 h-4 text-blue-500" />}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Font Size</h4>
                  <div className="relative">
                    <div 
                      className="border rounded p-2 flex justify-between items-center cursor-pointer"
                      onClick={() => toggleDropdown('fontSize')}
                    >
                      <span>{designOptions.fontSize}</span>
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    </div>
                    {openDropdown === 'fontSize' && (
                      <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow-lg">
                        {fontSizes.map(size => (
                          <div 
                            key={size}
                            className="p-2 hover:bg-gray-100 cursor-pointer flex justify-between items-center"
                            onClick={() => {
                              onDesignOptionChange('fontSize', size);
                              setOpenDropdown(null);
                            }}
                          >
                            {size}
                            {designOptions.fontSize === size && <Check className="w-4 h-4 text-blue-500" />}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-3">Line Height</h4>
                  <div className="relative">
                    <div 
                      className="border rounded p-2 flex justify-between items-center cursor-pointer"
                      onClick={() => toggleDropdown('lineHeight')}
                    >
                      <span>{designOptions.lineHeight}</span>
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    </div>
                    {openDropdown === 'lineHeight' && (
                      <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow-lg">
                        {lineHeights.map(height => (
                          <div 
                            key={height}
                            className="p-2 hover:bg-gray-100 cursor-pointer flex justify-between items-center"
                            onClick={() => {
                              onDesignOptionChange('lineHeight', height);
                              setOpenDropdown(null);
                            }}
                          >
                            {height}
                            {designOptions.lineHeight === height && <Check className="w-4 h-4 text-blue-500" />}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'spacing' && (
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-3">Margins</h4>
                  <div className="relative">
                    <div 
                      className="border rounded p-2 flex justify-between items-center cursor-pointer"
                      onClick={() => toggleDropdown('margin')}
                    >
                      <span>{designOptions.margin}</span>
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    </div>
                    {openDropdown === 'margin' && (
                      <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow-lg">
                        {margins.map(margin => (
                          <div 
                            key={margin}
                            className="p-2 hover:bg-gray-100 cursor-pointer flex justify-between items-center"
                            onClick={() => {
                              onDesignOptionChange('margin', margin);
                              setOpenDropdown(null);
                            }}
                          >
                            {margin}
                            {designOptions.margin === margin && <Check className="w-4 h-4 text-blue-500" />}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="p-4 border-t flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
          >
            Apply Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default DesignOptionsModal;
