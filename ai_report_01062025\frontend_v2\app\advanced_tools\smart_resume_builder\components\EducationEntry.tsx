import React from 'react';
import { Trash2 } from 'lucide-react';
import { Education } from '../types';

interface EducationEntryProps {
  education: Education;
  index: number;
  updateEducation: (index: number, field: string, value: string) => void;
  removeEducation: (index: number) => void;
}

const EducationEntry: React.FC<EducationEntryProps> = ({ 
  education, 
  index, 
  updateEducation, 
  removeEducation 
}) => {
  return (
    <div className="mb-4 p-3 border rounded bg-white">
      <div className="flex justify-between items-start mb-2">
        <h4 className="font-medium text-sm">Education Entry #{index + 1}</h4>
        <button
          onClick={() => removeEducation(index)}
          className="text-red-500 hover:text-red-700"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
      
      <input
        type="text"
        placeholder="Degree"
        value={education.degree}
        onChange={(e) => updateEducation(index, 'degree', e.target.value)}
        className="w-full p-2 border rounded mb-2"
      />
      <input
        type="text"
        placeholder="School/University"
        value={education.school}
        onChange={(e) => updateEducation(index, 'school', e.target.value)}
        className="w-full p-2 border rounded mb-2"
      />
      <div className="grid grid-cols-2 gap-2 mb-2">
        <input
          type="text"
          placeholder="Graduation Year"
          value={education.year}
          onChange={(e) => updateEducation(index, 'year', e.target.value)}
          className="p-2 border rounded"
        />
        <select
          value={education.gpa ? (education.gpa.includes('/') ? 'gpa' : 'percentage') : ''}
          onChange={(e) => {
            const type = e.target.value;
            if (type === 'gpa') {
              updateEducation(index, 'gpa', '3.5/4.0');
            } else if (type === 'percentage') {
              updateEducation(index, 'gpa', '85%');
            } else {
              updateEducation(index, 'gpa', '');
            }
          }}
          className="p-2 border rounded"
        >
          <option value="">Select Grade Type</option>
          <option value="gpa">GPA</option>
          <option value="percentage">Percentage</option>
        </select>
      </div>
      
      {education.gpa && (
        <input
          type="text"
          placeholder={education.gpa.includes('/') ? "GPA (e.g., 3.5/4.0)" : "Percentage (e.g., 85%)"}
          value={education.gpa}
          onChange={(e) => updateEducation(index, 'gpa', e.target.value)}
          className="w-full p-2 border rounded mb-2"
        />
      )}
      
      <input
        type="text"
        placeholder="Honors (optional)"
        value={education.honors || ''}
        onChange={(e) => updateEducation(index, 'honors', e.target.value)}
        className="w-full p-2 border rounded mb-2"
      />
      
      <input
        type="text"
        placeholder="Location (optional)"
        value={education.location || ''}
        onChange={(e) => updateEducation(index, 'location', e.target.value)}
        className="w-full p-2 border rounded mb-2"
      />
      
      <textarea
        placeholder="Relevant courses (comma separated)"
        value={education.courses ? education.courses.join(', ') : ''}
        onChange={(e) => {
          const courses = e.target.value.split(',').map(c => c.trim()).filter(c => c);
          const newEdu = { ...education, courses };
          updateEducation(index, 'courses', JSON.stringify(courses));
        }}
        className="w-full p-2 border rounded h-20 resize-none"
      />
    </div>
  );
};

export default EducationEntry;
