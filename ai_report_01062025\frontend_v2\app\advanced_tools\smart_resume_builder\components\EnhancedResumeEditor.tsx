import React from 'react';
import { UserData, ColorScheme } from '../types';
import { X, Plus, Trash2, Palette } from 'lucide-react';
import SectionSelector from './SectionSelector';
import EducationEntry from './EducationEntry';
import DesignOptionsModal from './DesignOptionsModal';

interface EnhancedResumeEditorProps {
  userData: UserData;
  updateUserData: (field: string, value: any) => void;
  colorScheme: string;
  colorSchemes: Record<string, ColorScheme>;
  setColorScheme: (scheme: string) => void;
  onClose: () => void;
  downloadResume: () => void;
  availableSections: { id: string; name: string }[];
  selectedSections: string[];
  onToggleSection: (sectionId: string) => void;
}

const EnhancedResumeEditor: React.FC<EnhancedResumeEditorProps> = ({
  userData,
  updateUserData,
  colorScheme,
  colorSchemes,
  setColorScheme,
  onClose,
  downloadResume,
  availableSections,
  selectedSections,
  onToggleSection
}) => {
  // Design options state
  const [showDesignOptions, setShowDesignOptions] = React.useState(false);
  const [designOptions, setDesignOptions] = React.useState({
    fontFamily: 'Arial, sans-serif',
    fontSize: '14px',
    lineHeight: '1.4',
    margin: '0.25in',
    columnLayout: 'single',
  });

  // Helper functions for managing resume data
  // Add a new strength
  const addStrength = () => {
    setUserData(prev => ({
      ...prev,
      strengths: [...(prev.strengths || []), {
        name: '',
        level: 3
      }]
    }));
  };

  // Update a strength
  const updateStrength = (index: number, field: string, value: any) => {
    setUserData(prev => ({
      ...prev,
      strengths: (prev.strengths || []).map((strength, i) => 
        i === index ? { ...strength, [field]: value } : strength
      )
    }));
  };

  // Remove a strength
  const removeStrength = (index: number) => {
    setUserData(prev => ({
      ...prev,
      strengths: (prev.strengths || []).filter((_, i) => i !== index)
    }));
  };

  // Add a new skill group
  const addSkillGroup = () => {
    // Check if skills is already an array of objects with 'category' and 'items'
    const isSkillGroupArray = Array.isArray(userData.skills) && 
      userData.skills.length > 0 && 
      typeof userData.skills[0] === 'object' && 
      'category' in userData.skills[0] && 
      'items' in userData.skills[0];
    
    if (isSkillGroupArray) {
      // Add to existing skill groups
      setUserData(prev => ({
        ...prev,
        skills: [...prev.skills, {
          category: 'New Category',
          items: []
        }]
      }));
    } else {
      // Convert from simple array to skill groups
      const existingSkills = Array.isArray(userData.skills) ? userData.skills : [];
      setUserData(prev => ({
        ...prev,
        skills: [
          {
            category: 'Skills',
            items: existingSkills
          },
          {
            category: 'New Category',
            items: []
          }
        ]
      }));
    }
  };

  // Update a skill group
  const updateSkillGroup = (index: number, field: string, value: any) => {
    // Only proceed if skills is an array of objects with 'category' and 'items'
    const isSkillGroupArray = Array.isArray(userData.skills) && 
      userData.skills.length > 0 && 
      typeof userData.skills[0] === 'object' && 
      'category' in userData.skills[0] && 
      'items' in userData.skills[0];
    
    if (isSkillGroupArray) {
      if (field === 'items') {
        // Handle items as a string with line breaks
        const items = typeof value === 'string' ? value.split('\n').filter(item => item.trim() !== '') : value;
        
        setUserData(prev => ({
          ...prev,
          skills: prev.skills.map((group, i) => 
            i === index ? { ...group, items } : group
          )
        }));
      } else {
        // Handle other fields normally
        setUserData(prev => ({
          ...prev,
          skills: prev.skills.map((group, i) => 
            i === index ? { ...group, [field]: value } : group
          )
        }));
      }
    }
  };

  // Remove a skill group
  const removeSkillGroup = (index: number) => {
    // Only proceed if skills is an array of objects with 'category' and 'items'
    const isSkillGroupArray = Array.isArray(userData.skills) && 
      userData.skills.length > 0 && 
      typeof userData.skills[0] === 'object' && 
      'category' in userData.skills[0] && 
      'items' in userData.skills[0];
    
    if (isSkillGroupArray) {
      if (userData.skills.length === 1) {
        // If this is the last group, convert back to simple array
        const items = userData.skills[0].items;
        setUserData(prev => ({
          ...prev,
          skills: items
        }));
      } else {
        // Remove the group
        setUserData(prev => ({
          ...prev,
          skills: prev.skills.filter((_, i) => i !== index)
        }));
      }
    }
  };

  const addExperience = () => {
    setUserData(prev => ({
      ...prev,
      experience: [...prev.experience, {
        title: '',
        company: '',
        duration: '',
        description: '',
        location: '',
        achievements: []
      }]
    }));
  };

  const updateExperience = (index: number, field: string, value: string) => {
    setUserData(prev => ({
      ...prev,
      experience: prev.experience.map((exp, i) => 
        i === index ? { ...exp, [field]: value } : exp
      )
    }));
  };

  const removeExperience = (index: number) => {
    setUserData(prev => ({
      ...prev,
      experience: prev.experience.filter((_, i) => i !== index)
    }));
  };

  const addEducation = () => {
    setUserData(prev => ({
      ...prev,
      education: [...prev.education, {
        degree: '',
        school: '',
        year: '',
        gpa: '',
        honors: '',
        location: '',
        courses: []
      }]
    }));
  };

  const updateEducation = (index: number, field: string, value: string) => {
    if (field === 'courses') {
      try {
        const courses = JSON.parse(value);
        setUserData(prev => ({
          ...prev,
          education: prev.education.map((edu, i) => 
            i === index ? { ...edu, courses } : edu
          )
        }));
      } catch (e) {
        console.error('Error parsing courses:', e);
      }
    } else {
      setUserData(prev => ({
        ...prev,
        education: prev.education.map((edu, i) => 
          i === index ? { ...edu, [field]: value } : edu
        )
      }));
    }
  };

  const removeEducation = (index: number) => {
    setUserData(prev => ({
      ...prev,
      education: prev.education.filter((_, i) => i !== index)
    }));
  };

  const addProject = () => {
    setUserData(prev => ({
      ...prev,
      projects: [...(prev.projects || []), {
        name: '',
        description: '',
        technologies: '',
        url: '',
        duration: '',
        achievements: []
      }]
    }));
  };

  const updateProject = (index: number, field: string, value: string) => {
    setUserData(prev => ({
      ...prev,
      projects: (prev.projects || []).map((project, i) => 
        i === index ? { ...project, [field]: value } : project
      )
    }));
  };

  const removeProject = (index: number) => {
    setUserData(prev => ({
      ...prev,
      projects: (prev.projects || []).filter((_, i) => i !== index)
    }));
  };

  const setUserData = (updater: (prev: UserData) => UserData) => {
    const newData = updater(userData);
    updateUserData('userData', newData);
  };

  // Apply design options to the resume preview
  React.useEffect(() => {
    const resumePreview = document.getElementById('resume-preview');
    if (resumePreview) {
      resumePreview.style.setProperty('--font-family', designOptions.fontFamily);
      resumePreview.style.setProperty('--font-size', designOptions.fontSize);
      resumePreview.style.setProperty('--line-height', designOptions.lineHeight);
      resumePreview.style.setProperty('--page-margin', designOptions.margin);
      
      if (designOptions.columnLayout === 'double') {
        resumePreview.classList.add('two-column');
      } else {
        resumePreview.classList.remove('two-column');
      }
    }
  }, [designOptions]);

  const handleDesignOptionChange = (option: string, value: string) => {
    setDesignOptions(prev => ({
      ...prev,
      [option]: value
    }));
  };

  return (
    <div className="w-1/3 border-r bg-gray-50 p-6 overflow-y-auto max-h-screen">
      {/* Design Options Modal */}
      <DesignOptionsModal
        isOpen={showDesignOptions}
        onClose={() => setShowDesignOptions(false)}
        designOptions={designOptions}
        onDesignOptionChange={handleDesignOptionChange}
      />
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">Edit Resume</h2>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Section Selector */}
      <SectionSelector 
        availableSections={availableSections}
        selectedSections={selectedSections}
        onToggleSection={onToggleSection}
      />

      {/* Design Options Button */}
      <div className="mb-6">
        <button
          onClick={() => setShowDesignOptions(true)}
          className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50"
        >
          <Palette className="w-4 h-4" />
          <span>Design Options</span>
        </button>
      </div>

      {/* Color Scheme */}
      <div className="mb-6">
        <h3 className="font-semibold mb-3">Color Scheme</h3>
        <div className="grid grid-cols-5 gap-2">
          {Object.entries(colorSchemes).map(([key, scheme]) => (
            <button
              key={key}
              onClick={() => setColorScheme(key)}
              className={`w-8 h-8 rounded border-2 ${
                colorScheme === key ? 'border-gray-800' : 'border-gray-300'
              }`}
              style={{ backgroundColor: scheme.primary }}
            ></button>
          ))}
        </div>
      </div>

      {/* Personal Info */}
      {selectedSections.includes('personal') && (
        <div className="mb-6">
          <h3 className="font-semibold mb-3">Personal Information</h3>
          <div className="space-y-3">
            <input
              type="text"
              placeholder="Full Name"
              value={userData.name}
              onChange={(e) => updateUserData('name', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              placeholder="Job Title"
              value={userData.title}
              onChange={(e) => updateUserData('title', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="email"
              placeholder="Email"
              value={userData.email}
              onChange={(e) => updateUserData('email', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              placeholder="Phone"
              value={userData.phone}
              onChange={(e) => updateUserData('phone', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              placeholder="Location"
              value={userData.location}
              onChange={(e) => updateUserData('location', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              placeholder="LinkedIn (optional)"
              value={userData.linkedin || ''}
              onChange={(e) => updateUserData('linkedin', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              placeholder="Website (optional)"
              value={userData.website || ''}
              onChange={(e) => updateUserData('website', e.target.value)}
              className="w-full p-2 border rounded"
            />
            {selectedSections.includes('github') && (
              <input
                type="text"
                placeholder="GitHub (optional)"
                value={userData.github || ''}
                onChange={(e) => updateUserData('github', e.target.value)}
                className="w-full p-2 border rounded"
              />
            )}
            {selectedSections.includes('portfolio') && (
              <input
                type="text"
                placeholder="Portfolio URL (optional)"
                value={userData.portfolioUrl || ''}
                onChange={(e) => updateUserData('portfolioUrl', e.target.value)}
                className="w-full p-2 border rounded"
              />
            )}
          </div>
        </div>
      )}

      {/* Summary */}
      {selectedSections.includes('summary') && (
        <div className="mb-6">
          <h3 className="font-semibold mb-3">Professional Summary</h3>
          <textarea
            placeholder="Write a brief professional summary..."
            value={userData.summary}
            onChange={(e) => updateUserData('summary', e.target.value)}
            className="w-full p-2 border rounded h-24 resize-none"
          />
        </div>
      )}

      {/* Experience */}
      {selectedSections.includes('experience') && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Experience</h3>
            <button
              onClick={addExperience}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          {userData.experience.map((exp, index) => (
            <div key={index} className="mb-4 p-3 border rounded bg-white">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-sm">Experience #{index + 1}</h4>
                <button
                  onClick={() => removeExperience(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
              <input
                type="text"
                placeholder="Job Title"
                value={exp.title}
                onChange={(e) => updateExperience(index, 'title', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Company"
                value={exp.company}
                onChange={(e) => updateExperience(index, 'company', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Duration (e.g., 2022 - Present)"
                value={exp.duration}
                onChange={(e) => updateExperience(index, 'duration', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Location (optional)"
                value={exp.location || ''}
                onChange={(e) => updateExperience(index, 'location', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <textarea
                placeholder="Job description and responsibilities..."
                value={exp.description}
                onChange={(e) => updateExperience(index, 'description', e.target.value)}
                className="w-full p-2 border rounded h-20 resize-none mb-2"
              />
              <div className="mb-2">
                <label className="text-sm font-medium mb-1 block">Key Achievements (one per line)</label>
                <textarea
                  placeholder="• Increased sales by 20%
• Led a team of 5 developers
• Implemented new system"
                  value={exp.achievements ? exp.achievements.join('\n') : ''}
                  onChange={(e) => {
                    // Split by newlines and filter out empty lines
                    const achievements = e.target.value.split('\n').filter(a => a !== undefined);
                    setUserData(prev => ({
                      ...prev,
                      experience: prev.experience.map((exp, i) => 
                        i === index ? { ...exp, achievements } : exp
                      )
                    }));
                  }}
                  onKeyDown={(e) => {
                    // Allow Enter key to create new lines
                    if (e.key === 'Enter') {
                      // Don't prevent default to allow newline
                    }
                  }}
                  className="w-full p-2 border rounded h-20 resize-none"
                  rows={5}
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Strengths Section (for ModernVisual template) */}
      {selectedSections.includes('strengths') && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Strengths</h3>
            <button
              onClick={addStrength}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          {(userData.strengths || []).map((strength, index) => (
            <div key={index} className="mb-4 p-3 border rounded bg-white">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-sm">Strength #{index + 1}</h4>
                <button
                  onClick={() => removeStrength(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
              <input
                type="text"
                placeholder="Strength Name"
                value={strength.name}
                onChange={(e) => updateStrength(index, 'name', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <div className="mb-2">
                <label className="text-sm font-medium mb-1 block">Level (1-5)</label>
                <input
                  type="range"
                  min="1"
                  max="5"
                  value={strength.level}
                  onChange={(e) => updateStrength(index, 'level', parseInt(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Beginner</span>
                  <span>Expert</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Skills */}
      {selectedSections.includes('skills') && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Skills</h3>
            <button
              onClick={addSkillGroup}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              {Array.isArray(userData.skills) && 
               userData.skills.length > 0 && 
               typeof userData.skills[0] === 'object' && 
               'category' in userData.skills[0] ? 'Add Group' : 'Group Skills'}
            </button>
          </div>
          
          {/* Simple Skills List */}
          {(!Array.isArray(userData.skills) || 
            userData.skills.length === 0 || 
            typeof userData.skills[0] !== 'object' || 
            !('category' in userData.skills[0])) && (
            <div>
              <textarea
                placeholder="Enter one skill per line"
                value={Array.isArray(userData.skills) ? userData.skills.join('\n') : ''}
                onChange={(e) => {
                  const skills = e.target.value.split('\n');
                  updateUserData('skills', skills);
                }}
                className="w-full p-2 border rounded h-20 resize-none"
              />
              <p className="text-xs text-gray-500 mt-1">Each line will be treated as a separate skill</p>
            </div>
          )}
          
          {/* Skill Groups */}
          {Array.isArray(userData.skills) && 
           userData.skills.length > 0 && 
           typeof userData.skills[0] === 'object' && 
           'category' in userData.skills[0] && 
           userData.skills.map((group, index) => (
            <div key={index} className="mb-4 p-3 border rounded bg-white">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-sm">Skill Group #{index + 1}</h4>
                <button
                  onClick={() => removeSkillGroup(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
              <input
                type="text"
                placeholder="Category Name"
                value={(group as any).category}
                onChange={(e) => updateSkillGroup(index, 'category', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <textarea
                placeholder="Enter one skill per line"
                value={Array.isArray((group as any).items) ? (group as any).items.join('\n') : ''}
                onChange={(e) => updateSkillGroup(index, 'items', e.target.value)}
                className="w-full p-2 border rounded h-20 resize-none"
              />
              <p className="text-xs text-gray-500 mt-1">Each line will be treated as a separate skill</p>
            </div>
          ))}
        </div>
      )}

      {/* Education */}
      {selectedSections.includes('education') && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Education</h3>
            <button
              onClick={addEducation}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          {userData.education.map((edu, index) => (
            <EducationEntry 
              key={index}
              education={edu}
              index={index}
              updateEducation={updateEducation}
              removeEducation={removeEducation}
            />
          ))}
        </div>
      )}

      {/* Projects */}
      {selectedSections.includes('projects') && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Projects</h3>
            <button
              onClick={addProject}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          {(userData.projects || []).map((project, index) => (
            <div key={index} className="mb-4 p-3 border rounded bg-white">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-sm">Project #{index + 1}</h4>
                <button
                  onClick={() => removeProject(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
              <input
                type="text"
                placeholder="Project Name"
                value={project.name}
                onChange={(e) => updateProject(index, 'name', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <textarea
                placeholder="Project description..."
                value={project.description}
                onChange={(e) => updateProject(index, 'description', e.target.value)}
                className="w-full p-2 border rounded mb-2 h-16 resize-none"
              />
              <input
                type="text"
                placeholder="Technologies used"
                value={project.technologies}
                onChange={(e) => updateProject(index, 'technologies', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="URL (optional)"
                value={project.url || ''}
                onChange={(e) => updateProject(index, 'url', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Duration (optional)"
                value={project.duration || ''}
                onChange={(e) => updateProject(index, 'duration', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <div className="mb-2">
                <label className="text-sm font-medium mb-1 block">Key Achievements (one per line)</label>
                <textarea
                  placeholder="• Increased user engagement by 30%
• Reduced load time by 40%
• Won award for design"
                  value={project.achievements ? project.achievements.join('\n') : ''}
                  onChange={(e) => {
                    const achievements = e.target.value.split('\n').filter(a => a.trim() !== '');
                    setUserData(prev => ({
                      ...prev,
                      projects: (prev.projects || []).map((proj, i) => 
                        i === index ? { ...proj, achievements } : proj
                      )
                    }));
                  }}
                  className="w-full p-2 border rounded h-20 resize-none"
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Certifications */}
      {selectedSections.includes('certifications') && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Certifications</h3>
            <button
              onClick={() => {
                const newCertifications = userData.certifications ? [...userData.certifications] : [];
                newCertifications.push({
                  name: '',
                  issuer: '',
                  date: '',
                  expiration: ''
                });
                updateUserData('certifications', newCertifications);
              }}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          {(userData.certifications || []).map((cert, index) => (
            <div key={index} className="mb-4 p-3 border rounded bg-white">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-sm">Certification #{index + 1}</h4>
                <button
                  onClick={() => {
                    const newCertifications = [...(userData.certifications || [])];
                    newCertifications.splice(index, 1);
                    updateUserData('certifications', newCertifications);
                  }}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
              <input
                type="text"
                placeholder="Certification Name"
                value={cert.name}
                onChange={(e) => {
                  const newCertifications = [...(userData.certifications || [])];
                  newCertifications[index] = { ...cert, name: e.target.value };
                  updateUserData('certifications', newCertifications);
                }}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Issuing Organization"
                value={cert.issuer}
                onChange={(e) => {
                  const newCertifications = [...(userData.certifications || [])];
                  newCertifications[index] = { ...cert, issuer: e.target.value };
                  updateUserData('certifications', newCertifications);
                }}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Date Issued"
                value={cert.date}
                onChange={(e) => {
                  const newCertifications = [...(userData.certifications || [])];
                  newCertifications[index] = { ...cert, date: e.target.value };
                  updateUserData('certifications', newCertifications);
                }}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Expiration Date (optional)"
                value={cert.expiration || ''}
                onChange={(e) => {
                  const newCertifications = [...(userData.certifications || [])];
                  newCertifications[index] = { ...cert, expiration: e.target.value };
                  updateUserData('certifications', newCertifications);
                }}
                className="w-full p-2 border rounded mb-2"
              />
            </div>
          ))}
        </div>
      )}

      {/* Languages */}
      {selectedSections.includes('languages') && (
        <div className="mb-6">
          <h3 className="font-semibold mb-3">Languages</h3>
          <div className="space-y-3">
            {(userData.languages || []).map((lang, index) => (
              <div key={index} className="flex gap-2">
                <input
                  type="text"
                  placeholder="Language"
                  value={lang.name}
                  onChange={(e) => {
                    const newLanguages = [...(userData.languages || [])];
                    newLanguages[index] = { ...lang, name: e.target.value };
                    updateUserData('languages', newLanguages);
                  }}
                  className="w-1/2 p-2 border rounded"
                />
                <input
                  type="text"
                  placeholder="Proficiency"
                  value={lang.proficiency}
                  onChange={(e) => {
                    const newLanguages = [...(userData.languages || [])];
                    newLanguages[index] = { ...lang, proficiency: e.target.value };
                    updateUserData('languages', newLanguages);
                  }}
                  className="w-1/2 p-2 border rounded"
                />
                <button
                  onClick={() => {
                    const newLanguages = [...(userData.languages || [])];
                    newLanguages.splice(index, 1);
                    updateUserData('languages', newLanguages);
                  }}
                  className="text-red-500 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            ))}
            <button
              onClick={() => {
                const newLanguages = [...(userData.languages || []), { name: '', proficiency: '' }];
                updateUserData('languages', newLanguages);
              }}
              className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add Language
            </button>
          </div>
        </div>
      )}

      {/* Interests */}
      {selectedSections.includes('interests') && (
        <div className="mb-6">
          <h3 className="font-semibold mb-3">Interests</h3>
          <textarea
            placeholder="Enter interests separated by commas"
            value={userData.interests ? userData.interests.join(', ') : ''}
            onChange={(e) => updateUserData('interests', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
            className="w-full p-2 border rounded h-20 resize-none"
          />
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        <button
          onClick={downloadResume}
          className="w-full bg-green-600 text-white py-3 rounded hover:bg-green-700 flex items-center justify-center gap-2"
        >
          Download Resume
        </button>
        <button
          onClick={() => {
            const resumeElement = document.getElementById('resume-preview');
            if (resumeElement) {
              const resumeHTML = resumeElement.outerHTML;
              navigator.clipboard.writeText(resumeHTML).then(() => {
                alert('Resume HTML copied to clipboard!');
              });
            }
          }}
          className="w-full bg-blue-600 text-white py-3 rounded hover:bg-blue-700 flex items-center justify-center gap-2"
        >
          Copy HTML
        </button>
        <button
          onClick={onClose}
          className="w-full bg-gray-600 text-white py-3 rounded hover:bg-gray-700"
        >
          Back to Templates
        </button>
      </div>
    </div>
  );
};

export default EnhancedResumeEditor;
