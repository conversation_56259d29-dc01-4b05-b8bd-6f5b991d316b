import React from 'react';
import { Industry } from '../types';

interface IndustrySelectorProps {
  industries: Record<string, Industry>;
  selectedIndustry: string;
  onSelectIndustry: (industry: string) => void;
}

const IndustrySelector: React.FC<IndustrySelectorProps> = ({ 
  industries, 
  selectedIndustry, 
  onSelectIndustry 
}) => {
  return (
    <div className="mb-8">
      <h2 className="text-xl font-semibold mb-4">Choose Your Industry</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Object.entries(industries).map(([key, industry]) => (
          <div
            key={key}
            onClick={() => onSelectIndustry(selectedIndustry === key ? '' : key)}
            className={`p-4 border-2 rounded-lg cursor-pointer transition-all hover:shadow-md ${
              selectedIndustry === key 
                ? 'border-purple-500 bg-purple-50' 
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-3 mb-2">
              <span className="text-2xl">{industry.icon}</span>
              <h3 className="font-semibold">{industry.name}</h3>
            </div>
            <p className="text-sm text-gray-600">{industry.description}</p>
            
            {industry.emphasis && industry.emphasis.length > 0 && (
              <div className="mt-3">
                <p className="text-xs text-gray-500 mb-1">Focus Areas:</p>
                <div className="flex flex-wrap gap-1">
                  {industry.emphasis.map((area, index) => (
                    <span 
                      key={index} 
                      className="text-xs px-2 py-0.5 bg-gray-100 rounded"
                    >
                      {area}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default IndustrySelector;
