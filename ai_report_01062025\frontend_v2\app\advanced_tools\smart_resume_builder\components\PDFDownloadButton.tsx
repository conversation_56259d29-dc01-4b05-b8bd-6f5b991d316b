"use client";

import React, { useState } from 'react';
import { Download } from 'lucide-react';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

interface PDFDownloadButtonProps {
  resumeUrl?: string;
  filename?: string;
  className?: string;
}

export const PDFDownloadButton: React.FC<PDFDownloadButtonProps> = ({
  resumeUrl,
  filename = 'resume',
  className = '',
}) => {
  const [isGenerating, setIsGenerating] = useState(false);

  // OKLCH to RGB conversion function
  const convertOklchToRgb = (oklchString: string): string => {
    // Parse OKLCH values
    const match = oklchString.match(/oklch\(([0-9.]+)\s+([0-9.]+)\s+([0-9.]+)\)/);
    if (!match) return oklchString;

    const l = parseFloat(match[1]); // Lightness (0-1)
    const c = parseFloat(match[2]); // Chroma (0-0.4)
    const h = parseFloat(match[3]); // Hue (0-360)

    // Simplified OKLCH to RGB conversion
    // Convert hue to radians
    const hRad = (h * Math.PI) / 180;
    
    // Convert to Lab color space
    const a = c * Math.cos(hRad);
    const b = c * Math.sin(hRad);
    
    // Convert Lab to XYZ (simplified)
    const fy = (l + 16) / 116;
    const fx = a / 500 + fy;
    const fz = fy - b / 200;
    
    const x = 0.95047 * (fx ** 3 > 0.008856 ? fx ** 3 : (fx - 16/116) / 7.787);
    const y = 1.00000 * (fy ** 3 > 0.008856 ? fy ** 3 : (fy - 16/116) / 7.787);
    const z = 1.08883 * (fz ** 3 > 0.008856 ? fz ** 3 : (fz - 16/116) / 7.787);
    
    // Convert XYZ to RGB
    let r = x *  3.2406 + y * -1.5372 + z * -0.4986;
    let g = x * -0.9689 + y *  1.8758 + z *  0.0415;
    let bl = x *  0.0557 + y * -0.2040 + z *  1.0570;
    
    // Gamma correction
    r = r > 0.0031308 ? 1.055 * Math.pow(r, 1/2.4) - 0.055 : 12.92 * r;
    g = g > 0.0031308 ? 1.055 * Math.pow(g, 1/2.4) - 0.055 : 12.92 * g;
    bl = bl > 0.0031308 ? 1.055 * Math.pow(bl, 1/2.4) - 0.055 : 12.92 * bl;
    
    // Clamp to 0-255
    r = Math.max(0, Math.min(255, Math.round(r * 255)));
    g = Math.max(0, Math.min(255, Math.round(g * 255)));
    bl = Math.max(0, Math.min(255, Math.round(bl * 255)));
    
    return `rgb(${r}, ${g}, ${bl})`;
  };

  // Predefined OKLCH color mappings for common values in your grid template
  const getOklchMapping = (oklchString: string): string => {
    const mappings: { [key: string]: string } = {
      // Blues (common in professional templates)
      'oklch(0.488 0.243 264.376)': '#2563eb', // Primary blue
      'oklch(0.546 0.245 262.881)': '#3b82f6', // Lighter blue
      'oklch(0.551 0.027 264.364)': '#64748b', // Slate blue
      
      // Grays
      'oklch(0.373 0.034 259.733)': '#374151', // Dark gray
      'oklch(0.446 0.03 256.802)': '#6b7280',  // Medium gray
      'oklch(0.278 0.033 256.848)': '#1f2937', // Very dark gray
      
      // Light backgrounds
      'oklch(0.97 0.014 254.604)': '#f8fafc',  // Very light blue-gray
      'oklch(0.967 0.003 264.542)': '#f1f5f9', // Very light gray
    };

    return mappings[oklchString] || convertOklchToRgb(oklchString);
  };

  const handleDownload = async () => {
    setIsGenerating(true);
    
    try {
      console.log('📲 Starting PDF generation...');
      
      // Find the resume preview element
      const resumeElement = document.getElementById('resume-preview');
      if (!resumeElement) {
        throw new Error('Resume preview element not found');
      }
      
      // Create a clone to avoid modifying the original
      const clone = resumeElement.cloneNode(true) as HTMLElement;
      
      // Create a temporary container
      const container = document.createElement('div');
      container.style.position = 'absolute';
      container.style.left = '-9999px';
      container.style.top = '-9999px';
      container.appendChild(clone);
      document.body.appendChild(container);
      
      // Process the clone to convert OKLCH colors properly
      const processElement = (element: HTMLElement) => {
        const style = window.getComputedStyle(element);
        
        // Convert OKLCH text colors
        if (style.color && (style.color.includes('oklch') || style.color.includes('oklab'))) {
          const convertedColor = getOklchMapping(style.color);
          console.log('🎨 Converting OKLCH color:', style.color, '→', convertedColor);
          element.style.setProperty('color', convertedColor, 'important');
        }
        
        // Convert OKLCH background colors
        if (style.backgroundColor && (style.backgroundColor.includes('oklch') || style.backgroundColor.includes('oklab'))) {
          const convertedBg = getOklchMapping(style.backgroundColor);
          console.log('🎨 Converting OKLCH background:', style.backgroundColor, '→', convertedBg);
          element.style.setProperty('background-color', convertedBg, 'important');
        }
        
        // Convert OKLCH border colors
        if (style.borderColor && (style.borderColor.includes('oklch') || style.borderColor.includes('oklab'))) {
          const convertedBorder = getOklchMapping(style.borderColor);
          console.log('🎨 Converting OKLCH border:', style.borderColor, '→', convertedBorder);
          element.style.setProperty('border-color', convertedBorder, 'important');
        }
        
        // Process children recursively
        Array.from(element.children).forEach(child => {
          processElement(child as HTMLElement);
        });
      };
      
      // Icon alignment and spacing fix
      const fixIconAlignment = () => {
        // Create a style tag for PDF-specific fixes
        const style = document.createElement('style');
        style.textContent = `
          /* PDF Icon Alignment and Spacing Fixes */
          * { 
            box-sizing: border-box;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          
          /* Compact spacing for experience/education sections */
          .experience > div, .education > div, .projects > div,
          [class*="experience"] > div, [class*="education"] > div, [class*="projects"] > div {
            margin-bottom: 12px !important;
          }
          
          /* Reduce spacing between company name and description */
          .experience h4, .education h4, .projects h4,
          .experience h3, .education h3, .projects h3 {
            margin-bottom: 2px !important;
            line-height: 1.2 !important;
          }
          
          /* Compact paragraph spacing */
          .experience p, .education p, .projects p,
          [class*="experience"] p, [class*="education"] p, [class*="projects"] p {
            margin-bottom: 3px !important;
            margin-top: 1px !important;
            line-height: 1.3 !important;
          }
          
          /* Reduce list spacing */
          .experience ul, .education ul, .projects ul,
          [class*="experience"] ul, [class*="education"] ul, [class*="projects"] ul {
            margin-top: 4px !important;
            margin-bottom: 4px !important;
          }
          
          .experience li, .education li, .projects li,
          [class*="experience"] li, [class*="education"] li, [class*="projects"] li {
            margin-bottom: 2px !important;
            line-height: 1.3 !important;
          }
          
          /* Compact section headers */
          h2, h3 {
            margin-bottom: 8px !important;
            margin-top: 16px !important;
            line-height: 1.2 !important;
          }
          
          /* First section header */
          h2:first-of-type, h3:first-of-type {
            margin-top: 8px !important;
          }
          
          /* General text elements */
          p {
            line-height: 1.3 !important;
            margin: 0 0 3px 0 !important;
          }
          
          /* Date spans and small text */
          span[class*="text-sm"], .text-sm, 
          span[class*="text-xs"], .text-xs {
            line-height: 1.2 !important;
            margin: 0 !important;
          }
          
          svg, .lucide {
            width: 16px !important;
            height: 16px !important;
            vertical-align: middle !important;
            margin-right: 6px !important;
            transform: translateY(2px) !important;
            display: inline !important;
          }
          
          /* Special rule for bullet icons in lists */
          li svg, .list-item svg, [class*="bullet"] svg {
            transform: translateY(0px) !important;
            width: 12px !important;
            height: 12px !important;
          }
          
          /* Add padding to containers with icons */
          .flex.items-center, [class*="flex"][class*="items-center"] {
            padding: 6px 8px !important;
          }
          
          .px-3.py-1, .px-3 {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            line-height: 1.2 !important;
            vertical-align: middle !important;
            text-align: center !important;
            padding: 2px 12px 14px 12px !important;
            min-height: 32px !important;
            box-sizing: border-box !important;
          }
          
          /* Increase spacing between skill name and progress bar */
          .skills > div, [class*="skills"] > div {
            margin-bottom: 12px !important;
          }
          
          .skills span:first-child, [class*="skills"] span:first-child {
            margin-bottom: 8px !important;
            display: block !important;
          }
          
           
          
          /* Fix progress bar colors to match preview */
          .bg-gray-200, [class*="bg-gray-200"], 
          div[style*="background-color: rgb(229, 231, 235)"] {
            background-color: #e5e7eb !important;
          }
          
          .bg-blue-500, .bg-blue-600, [class*="bg-blue"],
          div[style*="background-color: rgb(59, 130, 246)"],
          div[style*="background-color: rgb(37, 99, 235)"] {
            background-color: #3b82f6 !important;
          }

          /* Font size scaling for PDF */
  h1 {
    font-size: 1.5rem !important; /* Reduce from typical 2rem */
    line-height: 1.2 !important;
  }
  
  h2 {
    font-size: 1.25rem !important; /* Reduce from typical 1.5rem */
    line-height: 1.2 !important;
    margin-bottom: 8px !important;
    margin-top: 16px !important;
  }
  
  h3 {
    font-size: 1.1rem !important; /* Reduce from typical 1.25rem */
    line-height: 1.2 !important;
    margin-bottom: 6px !important;
    margin-top: 12px !important;
  }
  
  h4 {
    font-size: 0.8rem !important; /* Reduce from typical 1.1rem */
    line-height: 1.3 !important;
    margin-bottom: 4px !important;
  }
  
  /* Body text scaling */
  p, span, div {
    font-size: 0.7rem !important; /* Reduce from typical 1rem */
    line-height: 1.3 !important;
  }
  
  /* Small text scaling */
  .text-sm, [class*="text-sm"] {
    font-size: 0.825rem !important; /* Reduce from typical 0.875rem */
    line-height: 1.2 !important;
  }
  
  .text-xs, [class*="text-xs"] {
    font-size: 0.7rem !important; /* Reduce from typical 0.75rem */
    line-height: 1.2 !important;
  }
  
  /* Large text scaling */
  .text-lg, [class*="text-lg"] {
    font-size: 1.05rem !important; /* Reduce from typical 1.125rem */
    line-height: 1.2 !important;
  }
  
  .text-xl, [class*="text-xl"] {
    font-size: 1.15rem !important; /* Reduce from typical 1.25rem */
    line-height: 1.2 !important;
  }
  
  /* Contact info and skills might need special sizing */
  .contact-info, [class*="contact"] {
    font-size: 0.75rem !important;
  }
  
  .skills-text, [class*="skills"] span {
    font-size: 0.85rem !important;
  }
`;
        
        clone.appendChild(style);
      };
      
      // Apply fixes
      processElement(clone);
      fixIconAlignment();
      
      // Force style recalculation
      clone.style.display = 'none';
      clone.offsetHeight;
      clone.style.display = '';
      
      // Wait for styles to settle
      await new Promise(resolve => setTimeout(resolve, 300));
      
      console.log('📸 Generating canvas...');
      
      // Generate canvas
      const canvas = await html2canvas(clone, {
        scale: 2,
        logging: false,
        backgroundColor: '#ffffff',
        useCORS: true,
      });
      
      console.log('✅ Canvas generated:', canvas.width, 'x', canvas.height);
      
      // Clean up temporary elements
      document.body.removeChild(container);
      
      // Create PDF with proper scaling to match preview
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });
      
      // Calculate dimensions properly
      const pageWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const margin = 2; // Reduced margin to 5mm (was 10mm)
      
      // Calculate the image dimensions to fit A4 with margins
      const availableWidth = pageWidth - (2 * margin);
      const scaledHeight = (canvas.height * availableWidth) / canvas.width;
      
      console.log('📐 PDF dimensions:', { 
        canvas: { width: canvas.width, height: canvas.height },
        page: { width: pageWidth, height: pageHeight },
        scaled: { width: availableWidth, height: scaledHeight },
        margin: margin
      });
      
      // Add first page
      pdf.addImage(
        canvas.toDataURL('image/png'),
        'PNG',
        margin,
        margin,
        availableWidth,
        scaledHeight
      );
      
      // Handle multiple pages if content is taller than one page
      let remainingHeight = scaledHeight - (pageHeight - (2 * margin));
      let currentPosition = pageHeight - (2 * margin);
      
      while (remainingHeight > 0) {
        pdf.addPage();
        
        const yOffset = margin - currentPosition;
        
        pdf.addImage(
          canvas.toDataURL('image/png'),
          'PNG',
          margin,
          yOffset,
          availableWidth,
          scaledHeight
        );
        
        currentPosition += pageHeight - (2 * margin);
        remainingHeight -= pageHeight - (2 * margin);
        
        console.log('📄 Added page, remaining height:', remainingHeight);
      }
      
      // Save PDF
      pdf.save(`${filename}.pdf`);
      
      console.log('✅ PDF generation successful!');
    } catch (error) {
      console.error('❌ PDF generation error:', error);
      
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      alert(`PDF generation failed: ${errorMessage}\nPlease try again later.`);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <button
      onClick={handleDownload}
      disabled={isGenerating}
      className={`flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md ${className}`}
    >
      {isGenerating ? (
        <>
          <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
          <span>Generating...</span>
        </>
      ) : (
        <>
          <Download size={16} />
          <span>Download PDF</span>
        </>
      )}
    </button>
  );
};

export default PDFDownloadButton;