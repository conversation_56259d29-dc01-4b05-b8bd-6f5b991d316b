import React from 'react';
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';
import { ColorScheme } from '../types';

interface ResumeData {
  name: string;
  title: string;
  summary: string;
  experience: { company: string; role: string; start: string; end: string; description: string }[];
  education: { school: string; degree: string; start: string; end: string }[];
  skills: string[];
  projects?: { name: string; description: string }[];
  certifications?: { name: string; issuer: string; date: string }[];
}

interface PDFResumeProps {
  resumeData: ResumeData;
  templateStyle?: string;
  colors?: ColorScheme;
}

// Create a simple style object for the PDF
const styles = StyleSheet.create({
  page: {
    padding: 30,
    backgroundColor: '#ffffff',
  },
  section: {
    margin: 10,
    padding: 10,
  },
  heading: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subheading: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
    marginTop: 10,
  },
  text: {
    fontSize: 12,
    marginBottom: 5,
  },
  skillList: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    marginTop: 5,
  },
  skill: {
    margin: 5,
    padding: 5,
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
  },
  experienceItem: {
    marginBottom: 10,
  },
  header: {
    marginBottom: 20,
  },
});

export const PDFResume: React.FC<PDFResumeProps> = ({ resumeData }) => {
  const { name, title, summary, experience, education, skills, projects = [], certifications = [] } = resumeData;
  
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.heading}>{name}</Text>
          <Text style={styles.text}>{title}</Text>
          {summary && <Text style={styles.text}>{summary}</Text>}
        </View>
        
        {/* Experience Section */}
        <View style={styles.section}>
          <Text style={styles.subheading}>Experience</Text>
          {experience.map((exp, i) => (
            <View key={i} style={styles.experienceItem}>
              <Text style={styles.text}><Text style={{fontWeight: 'bold'}}>{exp.role}</Text> at {exp.company}</Text>
              <Text style={styles.text}>{exp.start} - {exp.end}</Text>
              <Text style={styles.text}>{exp.description}</Text>
            </View>
          ))}
        </View>
        
        {/* Education Section */}
        <View style={styles.section}>
          <Text style={styles.subheading}>Education</Text>
          {education.map((edu, i) => (
            <View key={i} style={styles.experienceItem}>
              <Text style={styles.text}><Text style={{fontWeight: 'bold'}}>{edu.degree}</Text> at {edu.school}</Text>
              <Text style={styles.text}>{edu.start} - {edu.end}</Text>
            </View>
          ))}
        </View>
        
        {/* Skills Section */}
        <View style={styles.section}>
          <Text style={styles.subheading}>Skills</Text>
          <View style={styles.skillList}>
            {skills.map((skill, i) => (
              <Text key={i} style={styles.skill}>{skill}</Text>
            ))}
          </View>
        </View>
        
        {/* Projects Section (if available) */}
        {projects.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.subheading}>Projects</Text>
            {projects.map((project, i) => (
              <View key={i} style={styles.experienceItem}>
                <Text style={styles.text}><Text style={{fontWeight: 'bold'}}>{project.name}</Text></Text>
                <Text style={styles.text}>{project.description}</Text>
              </View>
            ))}
          </View>
        )}
        
        {/* Certifications Section (if available) */}
        {certifications.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.subheading}>Certifications</Text>
            {certifications.map((cert, i) => (
              <View key={i} style={styles.experienceItem}>
                <Text style={styles.text}><Text style={{fontWeight: 'bold'}}>{cert.name}</Text> - {cert.issuer}</Text>
                <Text style={styles.text}>{cert.date}</Text>
              </View>
            ))}
          </View>
        )}
      </Page>
    </Document>
  );
};

export default PDFResume;
