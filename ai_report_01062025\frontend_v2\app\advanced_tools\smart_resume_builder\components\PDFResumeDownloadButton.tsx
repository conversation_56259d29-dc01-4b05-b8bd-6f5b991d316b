import React from 'react';
import { PDFDownloadLink } from '@react-pdf/renderer';
import PDFResume from './PDFResume';
import { Download } from 'lucide-react';

interface PDFResumeDownloadButtonProps {
  resumeData: {
    name: string;
    title: string;
    summary: string;
    experience: { company: string; role: string; start: string; end: string; description: string }[];
    education: { school: string; degree: string; start: string; end: string }[];
    skills: string[];
    projects?: { name: string; description: string }[];
    certifications?: { name: string; issuer: string; date: string }[];
  };
}

const PDFResumeDownloadButton: React.FC<PDFResumeDownloadButtonProps> = ({ resumeData }) => (
  <PDFDownloadLink
    document={<PDFResume resumeData={resumeData} />}
    fileName="resume.pdf"
    style={{
      textDecoration: 'none',
      padding: '8px 16px',
      backgroundColor: '#334155',
      color: '#fff',
      borderRadius: 4,
      fontWeight: 600,
      fontSize: 16,
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    }}
  >
    {({ loading }) => (
      <>
        <Download size={16} />
        {loading ? 'Preparing PDF...' : 'Download PDF'}
      </>
    )}
  </PDFDownloadLink>
);

export default PDFResumeDownloadButton;
