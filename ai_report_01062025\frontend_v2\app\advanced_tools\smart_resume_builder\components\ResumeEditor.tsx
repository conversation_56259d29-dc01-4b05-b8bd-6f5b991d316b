import React from 'react';
import { UserData, ColorScheme } from '../types';
import { X, Plus } from 'lucide-react';

interface ResumeEditorProps {
  userData: UserData;
  updateUserData: (field: string, value: any) => void;
  colorScheme: string;
  colorSchemes: Record<string, ColorScheme>;
  setColorScheme: (scheme: string) => void;
  onClose: () => void;
  addExperience: () => void;
  updateExperience: (index: number, field: string, value: string) => void;
  addEducation: () => void;
  updateEducation: (index: number, field: string, value: string) => void;
  addProject: () => void;
  updateProject: (index: number, field: string, value: string) => void;
  addCertification: () => void;
  updateCertification: (index: number, field: string, value: string) => void;
  downloadResume: () => void;
  sections: string[];
}

const ResumeEditor: React.FC<ResumeEditorProps> = ({
  userData,
  updateUserData,
  colorScheme,
  colorSchemes,
  setColorScheme,
  onClose,
  addExperience,
  updateExperience,
  addEducation,
  updateEducation,
  addProject,
  updateProject,
  addCertification,
  updateCertification,
  downloadResume,
  sections
}) => {
  return (
    <div className="w-1/3 border-r bg-gray-50 p-6 overflow-y-auto max-h-screen">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">Edit Resume</h2>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Color Scheme */}
      <div className="mb-6">
        <h3 className="font-semibold mb-3">Color Scheme</h3>
        <div className="grid grid-cols-5 gap-2">
          {Object.entries(colorSchemes).map(([key, scheme]) => (
            <button
              key={key}
              onClick={() => setColorScheme(key)}
              className={`w-8 h-8 rounded border-2 ${
                colorScheme === key ? 'border-gray-800' : 'border-gray-300'
              }`}
              style={{ backgroundColor: scheme.primary }}
            ></button>
          ))}
        </div>
      </div>

      {/* Personal Info */}
      {sections.includes('personal') && (
        <div className="mb-6">
          <h3 className="font-semibold mb-3">Personal Information</h3>
          <div className="space-y-3">
            <input
              type="text"
              placeholder="Full Name"
              value={userData.name}
              onChange={(e) => updateUserData('name', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              placeholder="Job Title"
              value={userData.title}
              onChange={(e) => updateUserData('title', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="email"
              placeholder="Email"
              value={userData.email}
              onChange={(e) => updateUserData('email', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              placeholder="Phone"
              value={userData.phone}
              onChange={(e) => updateUserData('phone', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              placeholder="Location"
              value={userData.location}
              onChange={(e) => updateUserData('location', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              placeholder="LinkedIn (optional)"
              value={userData.linkedin || ''}
              onChange={(e) => updateUserData('linkedin', e.target.value)}
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              placeholder="Website (optional)"
              value={userData.website || ''}
              onChange={(e) => updateUserData('website', e.target.value)}
              className="w-full p-2 border rounded"
            />
            {sections.includes('github') && (
              <input
                type="text"
                placeholder="GitHub (optional)"
                value={userData.github || ''}
                onChange={(e) => updateUserData('github', e.target.value)}
                className="w-full p-2 border rounded"
              />
            )}
            {sections.includes('portfolio') && (
              <input
                type="text"
                placeholder="Portfolio URL (optional)"
                value={userData.portfolioUrl || ''}
                onChange={(e) => updateUserData('portfolioUrl', e.target.value)}
                className="w-full p-2 border rounded"
              />
            )}
          </div>
        </div>
      )}

      {/* Summary */}
      {sections.includes('summary') && (
        <div className="mb-6">
          <h3 className="font-semibold mb-3">Professional Summary</h3>
          <textarea
            placeholder="Write a brief professional summary..."
            value={userData.summary}
            onChange={(e) => updateUserData('summary', e.target.value)}
            className="w-full p-2 border rounded h-24 resize-none"
          />
        </div>
      )}

      {/* Experience */}
      {sections.includes('experience') && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Experience</h3>
            <button
              onClick={addExperience}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          {userData.experience.map((exp, index) => (
            <div key={index} className="mb-4 p-3 border rounded bg-white">
              <input
                type="text"
                placeholder="Job Title"
                value={exp.title}
                onChange={(e) => updateExperience(index, 'title', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Company"
                value={exp.company}
                onChange={(e) => updateExperience(index, 'company', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Duration (e.g., 2022 - Present)"
                value={exp.duration}
                onChange={(e) => updateExperience(index, 'duration', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Location (optional)"
                value={exp.location || ''}
                onChange={(e) => updateExperience(index, 'location', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <textarea
                placeholder="Job description and responsibilities..."
                value={exp.description}
                onChange={(e) => updateExperience(index, 'description', e.target.value)}
                className="w-full p-2 border rounded h-20 resize-none mb-2"
              />
              <div className="mb-2">
                <label className="text-sm font-medium mb-1 block">Key Achievements (one per line)</label>
                <textarea
                  placeholder="• Increased sales by 20%
• Led a team of 5 developers
• Implemented new system"
                  value={exp.achievements ? exp.achievements.join('\n') : ''}
                  onChange={(e) => {
                    const achievements = e.target.value.split('\n').filter(a => a.trim() !== '');
                    const newExp = { ...exp, achievements };
                    const newExperiences = [...userData.experience];
                    newExperiences[index] = newExp;
                    updateUserData('experience', newExperiences);
                  }}
                  className="w-full p-2 border rounded h-20 resize-none"
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Skills */}
      {sections.includes('skills') && (
        <div className="mb-6">
          <h3 className="font-semibold mb-3">Skills</h3>
          <textarea
            placeholder="Enter skills separated by commas"
            value={userData.skills.join(', ')}
            onChange={(e) => updateUserData('skills', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
            className="w-full p-2 border rounded h-20 resize-none"
          />
        </div>
      )}

      {/* Education */}
      {sections.includes('education') && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Education</h3>
            <button
              onClick={addEducation}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          {userData.education.map((edu, index) => (
            <div key={index} className="mb-4 p-3 border rounded bg-white">
              <input
                type="text"
                placeholder="Degree"
                value={edu.degree}
                onChange={(e) => updateEducation(index, 'degree', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="School/University"
                value={edu.school}
                onChange={(e) => updateEducation(index, 'school', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Graduation Year"
                value={edu.year}
                onChange={(e) => updateEducation(index, 'year', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="GPA (optional)"
                value={edu.gpa || ''}
                onChange={(e) => updateEducation(index, 'gpa', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Honors (optional)"
                value={edu.honors || ''}
                onChange={(e) => updateEducation(index, 'honors', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <textarea
                placeholder="Relevant courses (comma separated)"
                value={edu.courses ? edu.courses.join(', ') : ''}
                onChange={(e) => {
                  const courses = e.target.value.split(',').map(c => c.trim()).filter(c => c);
                  const newEdu = { ...edu, courses };
                  const newEducation = [...userData.education];
                  newEducation[index] = newEdu;
                  updateUserData('education', newEducation);
                }}
                className="w-full p-2 border rounded h-20 resize-none"
              />
            </div>
          ))}
        </div>
      )}

      {/* Projects */}
      {sections.includes('projects') && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Projects</h3>
            <button
              onClick={addProject}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          {userData.projects.map((project, index) => (
            <div key={index} className="mb-4 p-3 border rounded bg-white">
              <input
                type="text"
                placeholder="Project Name"
                value={project.name}
                onChange={(e) => updateProject(index, 'name', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <textarea
                placeholder="Project description..."
                value={project.description}
                onChange={(e) => updateProject(index, 'description', e.target.value)}
                className="w-full p-2 border rounded mb-2 h-16 resize-none"
              />
              <input
                type="text"
                placeholder="Technologies used"
                value={project.technologies}
                onChange={(e) => updateProject(index, 'technologies', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="URL (optional)"
                value={project.url || ''}
                onChange={(e) => updateProject(index, 'url', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <div className="mb-2">
                <label className="text-sm font-medium mb-1 block">Key Achievements (one per line)</label>
                <textarea
                  placeholder="• Increased user engagement by 30%
• Reduced load time by 40%
• Won award for design"
                  value={project.achievements ? project.achievements.join('\n') : ''}
                  onChange={(e) => {
                    const achievements = e.target.value.split('\n').filter(a => a.trim() !== '');
                    const newProject = { ...project, achievements };
                    const newProjects = [...userData.projects];
                    newProjects[index] = newProject;
                    updateUserData('projects', newProjects);
                  }}
                  className="w-full p-2 border rounded h-20 resize-none"
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Certifications */}
      {sections.includes('certifications') && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Certifications</h3>
            <button
              onClick={addCertification}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          {userData.certifications && userData.certifications.map((cert, index) => (
            <div key={index} className="mb-4 p-3 border rounded bg-white">
              <input
                type="text"
                placeholder="Certification Name"
                value={cert.name}
                onChange={(e) => updateCertification(index, 'name', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Issuing Organization"
                value={cert.issuer}
                onChange={(e) => updateCertification(index, 'issuer', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Date Issued"
                value={cert.date}
                onChange={(e) => updateCertification(index, 'date', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Expiration Date (optional)"
                value={cert.expiration || ''}
                onChange={(e) => updateCertification(index, 'expiration', e.target.value)}
                className="w-full p-2 border rounded mb-2"
              />
            </div>
          ))}
        </div>
      )}

      {/* Languages */}
      {sections.includes('languages') && (
        <div className="mb-6">
          <h3 className="font-semibold mb-3">Languages</h3>
          <div className="space-y-3">
            {userData.languages && userData.languages.map((lang, index) => (
              <div key={index} className="flex gap-2">
                <input
                  type="text"
                  placeholder="Language"
                  value={lang.name}
                  onChange={(e) => {
                    const newLanguages = [...(userData.languages || [])];
                    newLanguages[index] = { ...lang, name: e.target.value };
                    updateUserData('languages', newLanguages);
                  }}
                  className="w-1/2 p-2 border rounded"
                />
                <input
                  type="text"
                  placeholder="Proficiency"
                  value={lang.proficiency}
                  onChange={(e) => {
                    const newLanguages = [...(userData.languages || [])];
                    newLanguages[index] = { ...lang, proficiency: e.target.value };
                    updateUserData('languages', newLanguages);
                  }}
                  className="w-1/2 p-2 border rounded"
                />
              </div>
            ))}
            <button
              onClick={() => {
                const newLanguages = [...(userData.languages || []), { name: '', proficiency: '' }];
                updateUserData('languages', newLanguages);
              }}
              className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add Language
            </button>
          </div>
        </div>
      )}

      {/* Interests */}
      {sections.includes('interests') && (
        <div className="mb-6">
          <h3 className="font-semibold mb-3">Interests</h3>
          <textarea
            placeholder="Enter interests separated by commas"
            value={userData.interests ? userData.interests.join(', ') : ''}
            onChange={(e) => updateUserData('interests', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
            className="w-full p-2 border rounded h-20 resize-none"
          />
        </div>
      )}

      {/* Publications */}
      {sections.includes('publications') && userData.publications && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">Publications</h3>
            <button
              onClick={() => {
                const newPublications = [...(userData.publications || []), { 
                  title: '', 
                  publisher: '', 
                  date: '',
                  description: ''
                }];
                updateUserData('publications', newPublications);
              }}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              Add
            </button>
          </div>
          {userData.publications.map((pub, index) => (
            <div key={index} className="mb-4 p-3 border rounded bg-white">
              <input
                type="text"
                placeholder="Publication Title"
                value={pub.title}
                onChange={(e) => {
                  const newPublications = [...userData.publications];
                  newPublications[index] = { ...pub, title: e.target.value };
                  updateUserData('publications', newPublications);
                }}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Publisher/Journal"
                value={pub.publisher}
                onChange={(e) => {
                  const newPublications = [...userData.publications];
                  newPublications[index] = { ...pub, publisher: e.target.value };
                  updateUserData('publications', newPublications);
                }}
                className="w-full p-2 border rounded mb-2"
              />
              <input
                type="text"
                placeholder="Publication Date"
                value={pub.date}
                onChange={(e) => {
                  const newPublications = [...userData.publications];
                  newPublications[index] = { ...pub, date: e.target.value };
                  updateUserData('publications', newPublications);
                }}
                className="w-full p-2 border rounded mb-2"
              />
              <textarea
                placeholder="Brief description"
                value={pub.description || ''}
                onChange={(e) => {
                  const newPublications = [...userData.publications];
                  newPublications[index] = { ...pub, description: e.target.value };
                  updateUserData('publications', newPublications);
                }}
                className="w-full p-2 border rounded h-16 resize-none"
              />
            </div>
          ))}
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        <button
          onClick={downloadResume}
          className="w-full bg-green-600 text-white py-3 rounded hover:bg-green-700 flex items-center justify-center gap-2"
        >
          Download Resume
        </button>
        <button
          onClick={() => {
            const resumeElement = document.getElementById('resume-preview');
            if (resumeElement) {
              const resumeHTML = resumeElement.outerHTML;
              navigator.clipboard.writeText(resumeHTML).then(() => {
                alert('Resume HTML copied to clipboard!');
              });
            }
          }}
          className="w-full bg-blue-600 text-white py-3 rounded hover:bg-blue-700 flex items-center justify-center gap-2"
        >
          Copy HTML
        </button>
        <button
          onClick={onClose}
          className="w-full bg-gray-600 text-white py-3 rounded hover:bg-gray-700"
        >
          Back to Templates
        </button>
      </div>
    </div>
  );
};

export default ResumeEditor;
