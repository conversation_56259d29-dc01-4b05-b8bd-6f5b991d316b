import React from 'react';
import { Template, UserData, ColorScheme } from '../types';

interface ResumePreviewProps {
  template: Template;
  userData: UserData;
  colorScheme: ColorScheme;
}

const ResumePreview: React.FC<ResumePreviewProps> = ({ template, userData, colorScheme }) => {
  const TemplateComponent = template.component;

  return (
    <div className="w-2/3 p-6 bg-gray-100 overflow-y-auto max-h-screen">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold">Live Preview</h2>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Template: {template.name}</span>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span className="text-sm text-gray-600">Live</span>
        </div>
      </div>

      <div 
        id="resume-preview" 
        className="bg-white rounded-lg shadow-lg overflow-hidden"
        style={{ 
          transform: 'scale(0.8)', 
          transformOrigin: 'top left',
          width: '125%'
        }}
      >
        <TemplateComponent userData={userData} colors={colorScheme} />
      </div>
    </div>
  );
};

export default ResumePreview;
