import React from 'react';
import { ToggleLeft, ToggleRight } from 'lucide-react';

interface SectionSelectorProps {
  availableSections: { id: string; name: string }[];
  selectedSections: string[];
  onToggleSection: (sectionId: string) => void;
}

const SectionSelector: React.FC<SectionSelectorProps> = ({ 
  availableSections, 
  selectedSections, 
  onToggleSection 
}) => {
  return (
    <div className="mb-6">
      <h3 className="font-semibold mb-3">Resume Sections</h3>
      <p className="text-sm text-gray-600 mb-3">Select which sections to include in your resume</p>
      
      <div className="grid grid-cols-2 gap-2">
        {availableSections.map(section => (
          <div 
            key={section.id}
            onClick={() => onToggleSection(section.id)}
            className="flex items-center justify-between p-2 border rounded cursor-pointer hover:bg-gray-50"
          >
            <span className="text-sm">{section.name}</span>
            {selectedSections.includes(section.id) ? (
              <ToggleRight className="w-5 h-5 text-green-600" />
            ) : (
              <ToggleLeft className="w-5 h-5 text-gray-400" />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SectionSelector;
