import React from 'react';
import { Template } from '../types';

interface TemplateSelectorProps {
  templates: Template[];
  onSelectTemplate: (template: Template) => void;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({ templates, onSelectTemplate }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Select a Template</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map(template => (
          <div
            key={template.id}
            onClick={() => onSelectTemplate(template)}
            className="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-purple-300 hover:shadow-lg transition-all"
          >
            <div className="h-40 bg-gradient-to-br from-gray-50 to-gray-100 rounded mb-4 flex items-center justify-center">
              <div className="w-24 h-32 bg-white border rounded shadow-sm"></div>
            </div>
            <h3 className="font-semibold mb-2">{template.name}</h3>
            <p className="text-sm text-gray-600 mb-3">{template.description}</p>
            <button className="w-full bg-purple-600 text-white py-2 rounded hover:bg-purple-700">
              Use Template
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TemplateSelector;
