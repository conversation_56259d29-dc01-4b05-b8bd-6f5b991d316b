'use client';

import React, { useState, useEffect } from 'react';
import { 
  FileText, Download, ChevronLeft, Layout, X, Copy,
  Mail, Phone, MapPin, User, Plus, Trash2, ToggleLeft, ToggleRight,
  Eye, Github
} from 'lucide-react';
import { UserData, Template, ColorScheme, Experience, Education, TemplateLayout, TemplateProps } from './types';
import { industries, colorSchemes, defaultUserData, getIndustryUserData, getRecommendedSections } from './utils';
import { getTemplatesByIndustry } from './templates';
// Import the default data from ModernVisual template
import { defaultData as modernVisualDefaultData } from './templates/general/ModernVisual';
import EnhancedResumeEditor from './components/EnhancedResumeEditor';
import PDFDownloadButton from './components/PDFDownloadButton';

export default function SmartTemplates() {
  // State management
  const [selectedIndustry, setSelectedIndustry] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<{
    id: string;
    name: string;
    industry: string;
    description: string;
    layout: string;
    component: React.ComponentType<TemplateProps>;
    previewImage?: string;
  } | null>(null);
  const [isPreview, setIsPreview] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [customizationPanel, setCustomizationPanel] = useState(false);
  const [selectedColorScheme, setSelectedColorScheme] = useState('blue');
  const [selectedSections, setSelectedSections] = useState<string[]>([
    'personal', 'summary', 'experience', 'education', 'skills', 'projects'
  ]);
  
  // Get the current color scheme
  const currentColorScheme = colorSchemes[selectedColorScheme as keyof typeof colorSchemes];
  
  // Initialize with default user data
  const [userData, setUserData] = useState<UserData>({
    ...defaultUserData,
    experience: defaultUserData.experience as Experience[],
    education: defaultUserData.education as Education[]
  });

  // Available sections for the resume
  const availableSections = [
    { id: 'personal', name: 'Personal Information' },
    { id: 'summary', name: 'Professional Summary' },
    { id: 'experience', name: 'Work Experience' },
    { id: 'education', name: 'Education' },
    { id: 'skills', name: 'Skills' },
    { id: 'strengths', name: 'Strengths & Proficiency' },
    { id: 'projects', name: 'Projects' },
    { id: 'certifications', name: 'Certifications' },
    { id: 'languages', name: 'Languages' },
    { id: 'interests', name: 'Interests' },
    { id: 'github', name: 'GitHub' },
    { id: 'portfolio', name: 'Portfolio' }
  ];

  // Handle section toggling
  const toggleSection = (sectionId: string) => {
    setSelectedSections(prev => {
      if (prev.includes(sectionId)) {
        return prev.filter(id => id !== sectionId);
      } else {
        return [...prev, sectionId];
      }
    });
  };

  // Update user data
  const updateUserData = (field: string, value: any) => {
    if (field === 'userData') {
      setUserData(value as UserData);
    } else {
      setUserData(prev => ({ ...prev, [field]: value }));
    }
  };

  // Get templates for the selected industry using the imported function
  const getFilteredTemplates = (industry: string): Template[] => {
    try {
      const templates = getTemplatesByIndustry(industry);
      if (!Array.isArray(templates)) return [];
      
      // Ensure all templates have the required properties with proper typing
      return templates.filter((t): t is Template => {
        return (
          t !== null &&
          typeof t === 'object' &&
          'id' in t &&
          'name' in t &&
          'component' in t
        );
      }).map(template => ({
        id: String(template.id),
        name: String(template.name),
        industry: String(template.industry || 'general'),
        description: String(template.description || ''),
        layout: String(template.layout || 'single-column'),
        component: template.component,
        previewImage: 'previewImage' in template ? String(template.previewImage) : undefined
      } as Template));
    } catch (error) {
      console.error('Error loading templates:', error);
      return [];
    }
  };

  // Set industry and load templates
  const selectIndustry = (industry: string) => {
    setSelectedIndustry(industry);
    setSelectedTemplate(null);
    setCustomizationPanel(false);
    
    // Update color scheme based on industry
    const industryData = industries[industry as keyof typeof industries];
    if (industryData && industryData.color) {
      setSelectedColorScheme(industryData.color);
    }
    
    // Load industry-specific user data
    const newUserData = getIndustryUserData(industry);
    setUserData(newUserData);
    
    // Set recommended sections for this industry
    const recommendedSections = getRecommendedSections(industry);
    setSelectedSections(recommendedSections);
  };

  // Function to handle template selection with type safety
  const selectTemplate = (template: {
    id: string;
    name: string;
    industry: string;
    description: string;
    layout: string;
    component: React.ComponentType<TemplateProps>;
    previewImage?: string;
  }) => {
    if (template?.id && template?.name && template?.component) {
      // Store the selected template
      setSelectedTemplate({
        id: String(template.id),
        name: String(template.name),
        industry: String(template.industry || 'general'),
        description: String(template.description || ''),
        layout: String(template.layout || 'single-column'),
        component: template.component,
        previewImage: template.previewImage ? String(template.previewImage) : undefined
      });
      
      // If selecting the ModernVisual template, load its default data
      if (template.id === 'modern-visual') {
        // Update user data with ModernVisual default data
        // We need to adapt the ModernVisual data to match our UserData type
        const adaptedExperience = modernVisualDefaultData.experience.map(exp => ({
          role: exp.role || exp.title,
          title: exp.title,
          company: exp.company,
          description: exp.description,
          start: exp.start || '',
          end: exp.end || '',
          duration: exp.duration || `${exp.start || ''} - ${exp.end || ''}`,
          location: '',
          achievements: exp.achievements || []
        })) as Experience[];
        
        // Add strengths to userData
        setUserData(userData => ({
          ...userData,
          // Keep personal info from current data
          // Use ModernVisual template data for the rest
          summary: modernVisualDefaultData.summary,
          skills: Array.isArray(modernVisualDefaultData.skills[0]) && 
                 typeof modernVisualDefaultData.skills[0] === 'object' ? 
            (modernVisualDefaultData.skills as any).flatMap((group: any) => group.items) : 
            modernVisualDefaultData.skills,
          experience: adaptedExperience,
          education: modernVisualDefaultData.education as Education[],
          projects: modernVisualDefaultData.projects,
          // Store strengths in additionalSections
          additionalSections: {
            ...userData.additionalSections,
            strengths: modernVisualDefaultData.strengths
          }
        }));
        
        // Make sure the strengths section is selected
        if (!selectedSections.includes('strengths')) {
          setSelectedSections(prev => [...prev, 'strengths']);
        }
      }
      
      setCustomizationPanel(true);
    } else {
      console.error('Invalid template selected:', template);
    }
  };

  // Generate resume based on selected template and sections
  const generateResume = () => {
    if (!selectedTemplate || !selectedTemplate.component) {
      console.error('No template or template component selected');
      return null;
    }
    
    try {
      // Filter user data based on selected sections
      const filteredUserData = {
        ...userData,
        experience: selectedSections.includes('experience') ? userData.experience : [],
        education: selectedSections.includes('education') ? userData.education : [],
        skills: selectedSections.includes('skills') ? userData.skills : [],
        strengths: selectedSections.includes('strengths') ? userData.strengths || [] : [],
        projects: selectedSections.includes('projects') ? userData.projects : [],
        certifications: selectedSections.includes('certifications') ? userData.certifications : [],
      };
      
      const TemplateComponent = selectedTemplate.component;
      
      return (
        <div className="w-full max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-md">
          <TemplateComponent userData={filteredUserData} colors={currentColorScheme} />
        </div>
      );
    } catch (error) {
      console.error('Error rendering template:', error);
      return (
        <div className="p-4 bg-red-50 text-red-700 rounded-md">
          Error rendering template. Please try another template.
        </div>
      );
    }
  };

  // Main render function
  // Add global styles for resume preview
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      #resume-preview {
        --font-family: Arial, sans-serif;
        --font-size: 14px;
        --line-height: 1.4;
        --page-margin: 1in;
        font-family: var(--font-family);
        font-size: var(--font-size);
        line-height: var(--line-height);
        margin: 0 auto;
        max-width: 8.5in;
        background: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        padding: var(--page-margin);
      }
      
      #resume-preview.two-column {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
      }
      
      #resume-preview.two-column > * {
        grid-column: 1 / -1;
      }
      
      #resume-preview.two-column .section-left {
        grid-column: 1;
      }
      
      #resume-preview.two-column .section-right {
        grid-column: 2;
      }
      
      @media print {
        body { margin: 0; padding: 0; }
        #resume-preview {
          box-shadow: none;
          margin: 0;
          padding: var(--page-margin);
          width: 100%;
          max-width: 100%;
        }
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {!selectedTemplate ? (
        // Industry and Template Selection
        <div className="container mx-auto py-8 px-4">
          <h1 className="text-3xl font-bold mb-8 text-center">Smart Resume Builder</h1>
          
          {/* Industry Selection */}
          {!selectedIndustry ? (
            <div>
              <h2 className="text-2xl font-semibold mb-6 text-center">Select Your Industry</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Object.entries(industries).map(([id, industry]) => (
                  <div 
                    key={id}
                    onClick={() => selectIndustry(id)}
                    className="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow"
                  >
                    <div className="flex items-center mb-4">
                      <span className="text-3xl mr-3">{industry.icon}</span>
                      <h3 className="text-xl font-semibold">{industry.name}</h3>
                    </div>
                    <p className="text-gray-600 mb-4">{industry.description}</p>
                    <div>
                      <span className="text-sm font-medium">Focus areas:</span>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {industry.emphasis.map((item: string, index: number) => (
                          <span 
                            key={index} 
                            className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded"
                          >
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            // Template Selection
            <div>
              <div className="flex items-center mb-6">
                <button 
                  onClick={() => setSelectedIndustry('')}
                  className="flex items-center text-blue-600 hover:text-blue-800"
                >
                  <ChevronLeft className="w-5 h-5" />
                  Back to Industries
                </button>
              </div>
              
              <h2 className="text-2xl font-semibold mb-6">
                {industries[selectedIndustry as keyof typeof industries]?.name} Templates
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {getFilteredTemplates(selectedIndustry).map((template) => {
                  const layout = template.layout;
                  
                  return (
                    <div 
                      key={template.id}
                      onClick={() => selectTemplate(template)}
                      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border border-gray-200"
                    >
                      <div className="bg-gray-100 p-6 flex items-center justify-center">
                        <FileText className="w-16 h-16 text-gray-400" />
                      </div>
                      <div className="p-4">
                        <h3 className="text-lg font-semibold mb-2">{template.name}</h3>
                        <p className="text-gray-600 text-sm mb-3">{template.description}</p>
                        <div className="flex items-center text-sm text-gray-500">
                          <Layout className="w-4 h-4 mr-1" />
                          {layout === 'single-column' ? 'Single Column' : 
                           layout === 'two-column' ? 'Two Column' : 
                           layout === 'creative' ? 'Creative Layout' : 'Traditional'}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="flex h-full">
          {/* Editor Panel */}
          <EnhancedResumeEditor 
            userData={userData}
            updateUserData={updateUserData}
            colorScheme={selectedColorScheme}
            colorSchemes={colorSchemes}
            setColorScheme={setSelectedColorScheme}
            onClose={() => setSelectedTemplate(null)}
            availableSections={availableSections}
            selectedSections={selectedSections}
            onToggleSection={toggleSection}
            downloadResume={() => {}}
          />
          
          {/* Resume Preview */}
          <div className="flex-1 bg-gray-200 overflow-y-auto pt-0 p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Live Preview</h2>
              <div className="flex items-center gap-2">
                <PDFDownloadButton
                  filename={`${userData.name.replace(/\s+/g, '_')}_Resume`}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center gap-2 transition-colors"
                />
              </div>
              <div className="flex items-center gap-2">
                {selectedTemplate && (
                  <span className="text-xs text-gray-600">
                    {typeof selectedTemplate === 'object' && selectedTemplate !== null && 'name' in selectedTemplate 
                      ? String(selectedTemplate.name) 
                      : 'Selected Template'}
                  </span>
                )}
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <span className="text-xs text-gray-600">
                  {isPreview ? 'Preview' : 'Live'}
                </span>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md" id="resume-preview">
              {selectedTemplate && typeof selectedTemplate === 'object' && 'component' in selectedTemplate && selectedTemplate.component ? (
                React.createElement(selectedTemplate.component, {
                  userData: {
                    ...userData,
                    experience: selectedSections.includes('experience') ? userData.experience : [],
                    education: selectedSections.includes('education') ? userData.education : [],
                    skills: selectedSections.includes('skills') ? userData.skills : [],
                    projects: selectedSections.includes('projects') ? userData.projects : [],
                    certifications: selectedSections.includes('certifications') ? userData.certifications : [],
                    // Pass strengths from additionalSections if available
                    ...(selectedSections.includes('strengths') && userData.additionalSections?.strengths ? 
                      { strengths: userData.additionalSections.strengths } : {})
                  },
                  colors: currentColorScheme
                })
              ) : (
                <div className="text-center p-8 text-gray-500">
                  Select a template to preview your resume
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
