'use client'
import React, { useState } from 'react';
import { 
  FileText, Download, ChevronLeft, Layout, X, Copy,
  Mail, Phone, MapPin, User, Plus, Trash2, ToggleLeft, ToggleRight
} from 'lucide-react';
import { UserData, Template, ColorScheme } from './types';
import { industries, colorSchemes, defaultUserData, getIndustryUserData, getRecommendedSections } from './utils';
import allTemplates, { getTemplatesByIndustry } from './templates';
import EnhancedResumeEditor from './components/EnhancedResumeEditor';

export default function SmartTemplates() {
  // State management
  const [selectedIndustry, setSelectedIndustry] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [customizationPanel, setCustomizationPanel] = useState(false);
  const [selectedColorScheme, setSelectedColorScheme] = useState('blue');
  const [selectedSections, setSelectedSections] = useState<string[]>([
    'personal', 'summary', 'experience', 'education', 'skills', 'projects'
  ]);
  
  // Initialize with default user data
  const [userData, setUserData] = useState<UserData>(defaultUserData);

  // Available sections for the resume
  const availableSections = [
    { id: 'personal', name: 'Personal Information' },
    { id: 'summary', name: 'Professional Summary' },
    { id: 'experience', name: 'Work Experience' },
    { id: 'education', name: 'Education' },
    { id: 'skills', name: 'Skills' },
    { id: 'projects', name: 'Projects' },
    { id: 'certifications', name: 'Certifications' },
    { id: 'languages', name: 'Languages' },
    { id: 'interests', name: 'Interests' },
    { id: 'github', name: 'GitHub' },
    { id: 'portfolio', name: 'Portfolio' }
  ];

  // Handle section toggling
  const toggleSection = (sectionId: string) => {
    setSelectedSections(prev => {
      if (prev.includes(sectionId)) {
        return prev.filter(id => id !== sectionId);
      } else {
        return [...prev, sectionId];
      }
    });
  };

  // Update user data
  const updateUserData = (field: string, value: any) => {
    if (field === 'userData') {
      setUserData(value as UserData);
    } else {
      setUserData(prev => ({ ...prev, [field]: value }));
    }
  };

  // Set industry and load templates
  const selectIndustry = (industry: string) => {
    setSelectedIndustry(industry);
    setSelectedTemplate(null);
    setCustomizationPanel(false);
    
    // Update color scheme based on industry
    const industryData = industries[industry as keyof typeof industries];
    if (industryData && industryData.color) {
      setSelectedColorScheme(industryData.color);
    }
    
    // Load industry-specific user data
    const newUserData = getIndustryUserData(industry);
    setUserData(newUserData);
    
    // Set recommended sections for this industry
    const recommendedSections = getRecommendedSections(industry);
    setSelectedSections(recommendedSections);
  };

  // Select a template
  const selectTemplate = (template: Template) => {
    setSelectedTemplate(template);
    setCustomizationPanel(true);
  };

  // Generate resume based on selected template and sections
  const generateResume = () => {
    if (!selectedTemplate) return null;
    
    // Find the actual template component
    const templateComponent = allTemplates.find(t => t.id === selectedTemplate.id);
    if (!templateComponent) return null;
    
    const colors = colorSchemes[selectedColorScheme as keyof typeof colorSchemes];
    const TemplateComponent = templateComponent.component;
    
    // Filter user data based on selected sections
    const filteredUserData = { ...userData };
    
    // Only include selected sections
    if (!selectedSections.includes('summary')) filteredUserData.summary = '';
    if (!selectedSections.includes('experience')) filteredUserData.experience = [];
    if (!selectedSections.includes('education')) filteredUserData.education = [];
    if (!selectedSections.includes('skills')) filteredUserData.skills = [];
    if (!selectedSections.includes('projects')) filteredUserData.projects = [];
    if (!selectedSections.includes('certifications')) filteredUserData.certifications = [];
    if (!selectedSections.includes('languages')) filteredUserData.languages = [];
    if (!selectedSections.includes('interests')) filteredUserData.interests = [];
    
    return <TemplateComponent userData={filteredUserData} colors={colors} />;
  };

  // Download resume as PDF
  const downloadResume = () => {
    // Create a new window with the resume content
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow pop-ups to download your resume');
      return;
    }
    
    const resumeContent = document.getElementById('resume-preview')?.innerHTML;
    if (!resumeContent) {
      alert('Resume content not found');
      return;
    }
    
    printWindow.document.write(`
      <html>
        <head>
          <title>${userData.name} - Resume</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
            .resume-container { max-width: 800px; margin: 0 auto; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="resume-container">
            ${resumeContent}
          </div>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
  };

  // Main render function
  return (
    <div className="min-h-screen bg-gray-100">
      {!selectedTemplate ? (
        // Industry and Template Selection
        <div className="container mx-auto py-8 px-4">
          <h1 className="text-3xl font-bold mb-8 text-center">Smart Resume Builder</h1>
          
          {/* Industry Selection */}
          {!selectedIndustry ? (
            <div>
              <h2 className="text-2xl font-semibold mb-6 text-center">Select Your Industry</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Object.entries(industries).map(([id, industry]) => (
                  <div 
                    key={id}
                    onClick={() => selectIndustry(id)}
                    className="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow"
                  >
                    <div className="flex items-center mb-4">
                      <span className="text-3xl mr-3">{industry.icon}</span>
                      <h3 className="text-xl font-semibold">{industry.name}</h3>
                    </div>
                    <p className="text-gray-600 mb-4">{industry.description}</p>
                    <div>
                      <span className="text-sm font-medium">Focus areas:</span>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {industry.emphasis.map((item: string, index: number) => (
                          <span 
                            key={index} 
                            className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded"
                          >
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            // Template Selection
            <div>
              <div className="flex items-center mb-6">
                <button 
                  onClick={() => setSelectedIndustry('')}
                  className="flex items-center text-blue-600 hover:text-blue-800"
                >
                  <ChevronLeft className="w-5 h-5" />
                  Back to Industries
                </button>
              </div>
              
              <h2 className="text-2xl font-semibold mb-6">
                {industries[selectedIndustry as keyof typeof industries]?.name} Templates
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {getTemplatesByIndustry(selectedIndustry).map((template: Template) => (
                  <div 
                    key={template.id}
                    onClick={() => selectTemplate(template)}
                    className="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                  >
                    <div className="h-48 bg-gray-200 flex items-center justify-center">
                      {/* Template preview image or placeholder */}
                      <FileText className="w-16 h-16 text-gray-400" />
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-semibold mb-2">{template.name}</h3>
                      <p className="text-gray-600 text-sm mb-3">{template.description}</p>
                      <div className="flex items-center text-sm text-gray-500">
                        <Layout className="w-4 h-4 mr-1" />
                        {template.layout === 'single-column' ? 'Single Column' : 
                         template.layout === 'two-column' ? 'Two Column' : 
                         template.layout === 'creative' ? 'Creative Layout' : 'Traditional'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        // Resume Editor and Preview
        <div className="flex h-screen">
          {/* Editor Panel */}
          <EnhancedResumeEditor 
            userData={userData}
            updateUserData={updateUserData}
            colorScheme={selectedColorScheme}
            colorSchemes={colorSchemes}
            setColorScheme={setSelectedColorScheme}
            onClose={() => setSelectedTemplate(null)}
            downloadResume={downloadResume}
            availableSections={availableSections}
            selectedSections={selectedSections}
            onToggleSection={toggleSection}
          />
          
          {/* Resume Preview */}
          <div className="flex-1 bg-gray-200 p-8 overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Live Preview</h2>
              <div className="flex items-center gap-2">
                {selectedTemplate && (
                  <span className="text-sm text-gray-600">Template: {selectedTemplate.name}</span>
                )}
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-sm text-gray-600">Live</span>
              </div>
            </div>
            <div className="bg-white shadow-lg mx-auto" id="resume-preview">
              {generateResume()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
