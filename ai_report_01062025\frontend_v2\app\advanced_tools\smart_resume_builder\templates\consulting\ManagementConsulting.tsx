import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Briefcase, GraduationCap, Award, TrendingUp, <PERSON><PERSON><PERSON>, Bar<PERSON>hart2, Target, Users } from 'lucide-react';

const ManagementConsulting: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white max-w-4xl mx-auto" style={{ fontFamily: 'Baskerville, serif' }}>
      {/* Header */}
      <div className="flex flex-col md:flex-row p-8 border-b-2" style={{ borderColor: colors.primary }}>
        <div className="md:w-2/3">
          <h1 className="text-3xl font-bold mb-2" style={{ color: colors.primary }}>{userData.name}</h1>
          <h2 className="text-xl text-gray-700 mb-4">{userData.title}</h2>
          <p className="text-gray-600 mb-4 leading-relaxed">{userData.summary}</p>
        </div>
        
        <div className="md:w-1/3 md:border-l md:pl-6" style={{ borderColor: colors.secondary }}>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4" style={{ color: colors.primary }} />
              <span className="text-sm">{userData.email}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4" style={{ color: colors.primary }} />
              <span className="text-sm">{userData.phone}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4" style={{ color: colors.primary }} />
              <span className="text-sm">{userData.location}</span>
            </div>
            {userData.linkedin && (
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" style={{ color: colors.primary }} />
                <span className="text-sm">{userData.linkedin}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="grid md:grid-cols-3 gap-6 p-8">
        {/* Left column */}
        <div className="md:col-span-2 space-y-6">
          {/* Experience - Consulting Focus */}
          <div>
            <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
              <span className="flex items-center gap-2">
                <Briefcase className="w-5 h-5" />
                Consulting Experience
              </span>
            </h3>
            
            {userData.experience.map((exp, index) => (
              <div key={index} className="mb-5">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-semibold">{exp.title}</h4>
                    <p className="text-gray-600">{exp.company}</p>
                  </div>
                  <span className="text-sm px-2 py-1 rounded" style={{ backgroundColor: colors.secondary, color: colors.text }}>
                    {exp.duration}
                  </span>
                </div>
                <p className="text-gray-700 text-sm mb-2">{exp.description}</p>
                
                {exp.achievements && exp.achievements.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium mb-1" style={{ color: colors.primary }}>Key Client Results:</p>
                    <ul className="list-none text-sm text-gray-600">
                      {exp.achievements.map((achievement, i) => (
                        <li key={i} className="flex items-start gap-2 mb-1">
                          <div className="mt-1">
                            <Target className="w-4 h-4" style={{ color: colors.primary }} />
                          </div>
                          <span>{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Projects - Case Studies */}
          {userData.projects && userData.projects.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
                <span className="flex items-center gap-2">
                  <PieChart className="w-5 h-5" />
                  Case Studies
                </span>
              </h3>
              
              {userData.projects.map((project, index) => (
                <div key={index} className="mb-4 p-4 rounded" style={{ backgroundColor: colors.secondary }}>
                  <h4 className="font-semibold mb-2">{project.name}</h4>
                  <p className="text-gray-700 text-sm mb-2">{project.description}</p>
                  
                  <div className="flex flex-wrap gap-2 mb-2">
                    <span className="text-xs px-2 py-1 rounded" style={{ backgroundColor: 'white', color: colors.primary }}>
                      Methodologies: {project.technologies}
                    </span>
                  </div>
                  
                  {project.achievements && project.achievements.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium mb-1" style={{ color: colors.primary }}>Business Impact:</p>
                      <ul className="list-disc list-inside text-sm text-gray-600">
                        {project.achievements.map((achievement, i) => (
                          <li key={i}>{achievement}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Right column */}
        <div className="space-y-6">
          {/* Education - Emphasized for Consulting */}
          <div>
            <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
              <span className="flex items-center gap-2">
                <GraduationCap className="w-5 h-5" />
                Education
              </span>
            </h3>
            
            {userData.education.map((edu, index) => (
              <div key={index} className="mb-3 p-3 border rounded" style={{ borderColor: colors.secondary }}>
                <h4 className="font-semibold">{edu.degree}</h4>
                <p className="text-gray-600">{edu.school}</p>
                <p className="text-gray-500 text-sm">{edu.year}</p>
                {edu.gpa && <p className="text-gray-500 text-sm">GPA: {edu.gpa}</p>}
                {edu.honors && <p className="text-sm italic" style={{ color: colors.primary }}>{edu.honors}</p>}
              </div>
            ))}
          </div>

          {/* Skills - Consulting Specific */}
          <div>
            <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
              <span className="flex items-center gap-2">
                <BarChart2 className="w-5 h-5" />
                Consulting Expertise
              </span>
            </h3>
            
            <div className="space-y-3">
              {userData.skills.map((skill, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{skill}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div 
                      className="h-1.5 rounded-full" 
                      style={{ 
                        backgroundColor: colors.primary, 
                        width: `${85 + Math.random() * 15}%` 
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Certifications */}
          {userData.certifications && userData.certifications.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
                <span className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  Certifications
                </span>
              </h3>
              
              {userData.certifications.map((cert, index) => (
                <div key={index} className="mb-2">
                  <p className="font-medium">{cert.name}</p>
                  <p className="text-gray-600 text-sm">{cert.issuer}, {cert.date}</p>
                </div>
              ))}
            </div>
          )}

          {/* Languages */}
          {userData.languages && userData.languages.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
                Languages
              </h3>
              
              {userData.languages.map((lang, index) => (
                <div key={index} className="flex justify-between items-center mb-2">
                  <span className="text-sm">{lang.name}</span>
                  <span 
                    className="text-xs px-2 py-1 rounded"
                    style={{ backgroundColor: colors.secondary, color: colors.text }}
                  >
                    {lang.proficiency}
                  </span>
                </div>
              ))}
            </div>
          )}

          {/* Leadership & Interests */}
          {userData.interests && userData.interests.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
                <span className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Leadership & Interests
                </span>
              </h3>
              <ul className="list-disc list-inside text-sm text-gray-700">
                {userData.interests.map((interest, index) => (
                  <li key={index}>{interest}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ManagementConsulting;
