import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Briefcase, GraduationCap, Award, BookOpen, Users, FileText, Clock } from 'lucide-react';

const TeacherEducator: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white max-w-2xl mx-auto" style={{ fontFamily: 'Georgia, serif' }}>
      {/* Header */}
      <div className="p-6 text-center border-b-2" style={{ borderColor: colors.primary }}>
        <h1 className="text-3xl font-bold mb-2" style={{ color: colors.primary }}>{userData.name}</h1>
        <h2 className="text-xl text-gray-700 mb-4">{userData.title}</h2>
        
        <div className="flex justify-center items-center gap-4 text-sm flex-wrap">
          <span className="flex items-center gap-1">
            <Mail className="w-4 h-4" />
            {userData.email}
          </span>
          <span className="flex items-center gap-1">
            <Phone className="w-4 h-4" />
            {userData.phone}
          </span>
          <span className="flex items-center gap-1">
            <MapPin className="w-4 h-4" />
            {userData.location}
          </span>
        </div>
      </div>

      <div className="p-6">
        {/* Summary */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Professional Summary</h3>
          <p className="text-gray-700 leading-relaxed">{userData.summary}</p>
        </div>

        {/* Education - Placed first for education industry */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
            <span className="flex items-center gap-2">
              <GraduationCap className="w-5 h-5" />
              Education
            </span>
          </h3>
          
          {userData.education.map((edu, index) => (
            <div key={index} className="mb-3 p-3 border rounded" style={{ borderColor: colors.secondary }}>
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-semibold">{edu.degree}</h4>
                  <p className="text-gray-600">{edu.school}</p>
                </div>
                <div className="text-right text-sm text-gray-500">
                  <div>{edu.year}</div>
                  {edu.gpa && <div>GPA: {edu.gpa}</div>}
                </div>
              </div>
              {edu.honors && <p className="text-sm text-gray-600 italic">{edu.honors}</p>}
              
              {edu.courses && edu.courses.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm font-medium">Relevant Coursework:</p>
                  <p className="text-xs text-gray-600">{edu.courses.join(', ')}</p>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Teaching Experience */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
            <span className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Teaching Experience
            </span>
          </h3>
          
          {userData.experience.map((exp, index) => (
            <div key={index} className="mb-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">{exp.title}</h4>
                  <p className="text-gray-600">{exp.company}</p>
                  {exp.location && <p className="text-gray-500 text-sm">{exp.location}</p>}
                </div>
                <span className="text-sm px-2 py-1 rounded" style={{ backgroundColor: colors.secondary, color: colors.text }}>
                  {exp.duration}
                </span>
              </div>
              <p className="text-gray-700 text-sm mb-2">{exp.description}</p>
              
              {exp.achievements && exp.achievements.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm font-medium mb-1">Key Accomplishments:</p>
                  <ul className="list-disc list-inside text-sm text-gray-600 pl-2">
                    {exp.achievements.map((achievement, i) => (
                      <li key={i}>{achievement}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Certifications & Licenses - Education Specific */}
        {userData.certifications && userData.certifications.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
              <span className="flex items-center gap-2">
                <Award className="w-5 h-5" />
                Teaching Certifications & Licenses
              </span>
            </h3>
            
            <div className="grid grid-cols-2 gap-3">
              {userData.certifications.map((cert, index) => (
                <div key={index} className="p-3 border rounded" style={{ borderColor: colors.secondary }}>
                  <h4 className="font-semibold text-sm">{cert.name}</h4>
                  <p className="text-gray-600 text-xs">Issuing Authority: {cert.issuer}</p>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Issued: {cert.date}</span>
                    {cert.expiration && <span>Expires: {cert.expiration}</span>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Publications - Education Specific */}
        {userData.publications && userData.publications.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
              <span className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Publications & Research
              </span>
            </h3>
            
            {userData.publications.map((pub, index) => (
              <div key={index} className="mb-3">
                <h4 className="font-semibold text-sm">{pub.title}</h4>
                <p className="text-gray-600 text-sm">{pub.publisher}, {pub.date}</p>
                {pub.description && <p className="text-gray-500 text-xs">{pub.description}</p>}
                {pub.coAuthors && pub.coAuthors.length > 0 && (
                  <p className="text-gray-500 text-xs">Co-authors: {pub.coAuthors.join(', ')}</p>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Teaching Skills */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
            <span className="flex items-center gap-2">
              <BookOpen className="w-5 h-5" />
              Teaching Skills & Competencies
            </span>
          </h3>
          
          <div className="grid grid-cols-2 gap-2">
            {userData.skills.map((skill, index) => (
              <div key={index} className="flex items-center gap-2 p-2 rounded" style={{ backgroundColor: colors.secondary }}>
                <div className="w-2 h-2 rounded-full" style={{ backgroundColor: colors.primary }}></div>
                <span className="text-sm">{skill}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Sections */}
        <div className="grid grid-cols-2 gap-6">
          {/* Languages */}
          {userData.languages && userData.languages.length > 0 && (
            <div>
              <h3 className="text-md font-semibold mb-2" style={{ color: colors.primary }}>Languages</h3>
              {userData.languages.map((lang, index) => (
                <div key={index} className="flex justify-between text-sm mb-1">
                  <span>{lang.name}</span>
                  <span className="text-gray-600">{lang.proficiency}</span>
                </div>
              ))}
            </div>
          )}

          {/* Professional Development - Education Specific */}
          {userData.interests && userData.interests.length > 0 && (
            <div>
              <h3 className="text-md font-semibold mb-2" style={{ color: colors.primary }}>
                <span className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Professional Development
                </span>
              </h3>
              <ul className="list-disc list-inside text-sm text-gray-700">
                {userData.interests.map((interest, index) => (
                  <li key={index}>{interest}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Projects - Educational Projects */}
        {userData.projects && userData.projects.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
              <span className="flex items-center gap-2">
                <Briefcase className="w-5 h-5" />
                Curriculum & Educational Projects
              </span>
            </h3>
            
            {userData.projects.map((project, index) => (
              <div key={index} className="mb-3 p-3 border rounded" style={{ borderColor: colors.secondary }}>
                <h4 className="font-semibold">{project.name}</h4>
                <p className="text-gray-700 text-sm mb-1">{project.description}</p>
                <p className="text-gray-500 text-xs">Methodologies: {project.technologies}</p>
                
                {project.achievements && project.achievements.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium">Outcomes:</p>
                    <ul className="list-disc list-inside text-xs text-gray-600">
                      {project.achievements.map((achievement, i) => (
                        <li key={i}>{achievement}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TeacherEducator;
