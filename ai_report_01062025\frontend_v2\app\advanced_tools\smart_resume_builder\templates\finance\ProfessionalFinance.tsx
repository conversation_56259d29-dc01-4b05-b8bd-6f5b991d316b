import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Briefcase, GraduationCap, Award, TrendingUp, DollarSign, BarChart2 } from 'lucide-react';

const ProfessionalFinance: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white max-w-2xl mx-auto my-12 p-10 rounded-lg shadow-lg" style={{ fontFamily: 'Times New Roman, serif' }}>
      {/* Header */}
      <div className="text-center border-b-2 pb-8 mb-8" style={{ borderColor: colors.primary }}>
        <h1 className="text-3xl font-bold mb-2" style={{ color: colors.primary }}>{userData.name}</h1>
        <h2 className="text-xl text-gray-600 mb-4">{userData.title}</h2>
        <div className="flex justify-center items-center gap-4 text-sm flex-wrap">
          <span className="flex items-center gap-1">
            <Mail className="w-4 h-4" />
            {userData.email}
          </span>
          <span className="flex items-center gap-1">
            <Phone className="w-4 h-4" />
            {userData.phone}
          </span>
          <span className="flex items-center gap-1">
            <MapPin className="w-4 h-4" />
            {userData.location}
          </span>
          {userData.linkedin && (
            <span className="flex items-center gap-1">
              <TrendingUp className="w-4 h-4" />
              {userData.linkedin}
            </span>
          )}
        </div>
      </div>

      {/* Summary */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 uppercase tracking-wide" style={{ color: colors.primary }}>Professional Summary</h3>
        <p className="text-gray-700 leading-relaxed text-justify">{userData.summary}</p>
      </div>

      {/* Experience */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 uppercase tracking-wide" style={{ color: colors.primary }}>
          <span className="flex items-center gap-2">
            <Briefcase className="w-5 h-5" />
            Professional Experience
          </span>
        </h3>
        {userData.experience.map((exp, index) => (
          <div key={index} className="mb-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h4 className="font-bold">{exp.role}</h4>
                <p className="italic">{exp.company}</p>
              </div>
              <span className="text-sm text-gray-500">{exp.start} - {exp.end}</span>
            </div>
            <p className="text-gray-700 text-sm mb-2 text-justify">{exp.description}</p>
            
            {exp.achievements && exp.achievements.length > 0 && (
              <ul className="list-disc list-inside text-sm text-gray-600 pl-2">
                {exp.achievements.map((achievement, i) => (
                  <li key={i} className="text-justify">{achievement}</li>
                ))}
              </ul>
            )}
          </div>
        ))}
      </div>

      {/* Education */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 uppercase tracking-wide" style={{ color: colors.primary }}>
          <span className="flex items-center gap-2">
            <GraduationCap className="w-5 h-5" />
            Education
          </span>
        </h3>
        {userData.education.map((edu, index) => (
          <div key={index} className="mb-2">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-bold">{edu.degree}</h4>
                <p className="italic">{edu.school}</p>
                {edu.field && <p className="text-sm">{edu.field}</p>}
              </div>
              <div className="text-right text-sm text-gray-500">
                <div>{edu.start} - {edu.end}</div>
              </div>
            </div>
            {edu.description && <p className="text-sm text-gray-600">{edu.description}</p>}
          </div>
        ))}
      </div>

      {/* Skills */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 uppercase tracking-wide" style={{ color: colors.primary }}>
          <span className="flex items-center gap-2">
            <BarChart2 className="w-5 h-5" />
            Core Competencies
          </span>
        </h3>
        <div className="grid grid-cols-2 gap-2">
          {userData.skills.map((skill, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full" style={{ backgroundColor: colors.primary }}></div>
              <span className="text-sm">{skill}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Certifications */}
      {userData.certifications && userData.certifications.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 uppercase tracking-wide" style={{ color: colors.primary }}>
            <span className="flex items-center gap-2">
              <Award className="w-5 h-5" />
              Certifications
            </span>
          </h3>
          <div className="grid grid-cols-2 gap-3">
            {userData.certifications.map((cert, index) => (
              <div key={index} className="text-sm">
                <p className="font-bold">{cert.name}</p>
                <p className="text-gray-600">{cert.issuer}, {cert.date}</p>
                {cert.expiration && <p className="text-gray-500 text-xs">Expires: {cert.expiration}</p>}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Additional Sections */}
      <div className="grid grid-cols-2 gap-6">
        {/* Languages */}
        {userData.languages && userData.languages.length > 0 && (
          <div>
            <h3 className="text-md font-semibold mb-2 uppercase tracking-wide" style={{ color: colors.primary }}>Languages</h3>
            {userData.languages.map((lang, index) => (
              <div key={index} className="flex justify-between text-sm mb-1">
                <span>{lang.name}</span>
                <span className="text-gray-600">{lang.proficiency}</span>
              </div>
            ))}
          </div>
        )}

        {/* Interests */}
        {userData.interests && userData.interests.length > 0 && (
          <div>
            <h3 className="text-md font-semibold mb-2 uppercase tracking-wide" style={{ color: colors.primary }}>Interests</h3>
            <p className="text-sm text-gray-700">{userData.interests.join(', ')}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfessionalFinance;
