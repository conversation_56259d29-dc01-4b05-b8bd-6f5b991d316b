import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Globe, Briefcase, GraduationCap, Award, Star, FileText, Code } from 'lucide-react';

const ColoredSections: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white max-w-2xl mx-auto" style={{ fontFamily: 'Georgia, serif' }}>
      {/* Header */}
      <div className="p-6 text-center" style={{ backgroundColor: colors.secondary }}>
        <h1 className="text-2xl font-bold mb-1" style={{ color: colors.primary }}>{userData.name}</h1>
        <h2 className="text-md mb-3" style={{ color: colors.text }}>{userData.title}</h2>
        
        <div className="flex flex-wrap justify-center gap-4 text-sm">
          {userData.email && (
            <span className="flex items-center gap-1">
              <Mail className="w-4 h-4" style={{ color: colors.primary }} />
              {userData.email}
            </span>
          )}
          {userData.phone && (
            <span className="flex items-center gap-1">
              <Phone className="w-4 h-4" style={{ color: colors.primary }} />
              {userData.phone}
            </span>
          )}
          {userData.location && (
            <span className="flex items-center gap-1">
              <MapPin className="w-4 h-4" style={{ color: colors.primary }} />
              {userData.location}
            </span>
          )}
          {userData.website && (
            <span className="flex items-center gap-1">
              <Globe className="w-4 h-4" style={{ color: colors.primary }} />
              {userData.website}
            </span>
          )}
        </div>
      </div>

      <div className="p-6">
        {/* Summary */}
        <div className="mb-6">
          <h3 className="text-lg font-bold mb-2 pb-1 border-b-2" style={{ color: colors.primary, borderColor: colors.secondary }}>
            Summary
          </h3>
          <p className="text-sm text-gray-700">{userData.summary}</p>
        </div>

        {/* Experience */}
        <div className="mb-6">
          <h3 className="text-lg font-bold mb-3 pb-1 border-b-2" style={{ color: colors.primary, borderColor: colors.secondary }}>
            <span className="flex items-center gap-2">
              <Briefcase className="w-5 h-5" />
              Work Experience
            </span>
          </h3>
          
          {userData.experience.map((exp, index) => (
            <div key={index} className="mb-4">
              <div className="flex justify-between items-start mb-1">
                <div>
                  <h4 className="text-md font-semibold" style={{ color: colors.primary }}>{exp.title}</h4>
                  <p className="text-sm font-medium">{exp.company}</p>
                </div>
                <span className="text-sm text-gray-600">{exp.duration}</span>
              </div>
              
              <p className="text-sm text-gray-700 mb-2">{exp.description}</p>
              
              {exp.achievements && exp.achievements.length > 0 && (
                <ul className="list-disc list-inside text-sm text-gray-600 pl-2">
                  {exp.achievements.map((achievement, i) => (
                    <li key={i}>{achievement}</li>
                  ))}
                </ul>
              )}
            </div>
          ))}
        </div>

        {/* Education */}
        <div className="mb-6">
          <h3 className="text-lg font-bold mb-3 pb-1 border-b-2" style={{ color: colors.primary, borderColor: colors.secondary }}>
            <span className="flex items-center gap-2">
              <GraduationCap className="w-5 h-5" />
              Education
            </span>
          </h3>
          
          {userData.education.map((edu, index) => (
            <div key={index} className="mb-3">
              <div className="flex justify-between items-start mb-1">
                <div>
                  <h4 className="text-md font-semibold" style={{ color: colors.primary }}>{edu.degree}</h4>
                  <p className="text-sm font-medium">{edu.school}</p>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-600">{edu.year}</div>
                  {edu.gpa && (
                    <div className="text-sm text-gray-600">
                      GPA: {edu.gpa}
                    </div>
                  )}
                </div>
              </div>
              
              {edu.honors && <p className="text-sm text-gray-600 italic">{edu.honors}</p>}
              
              {edu.courses && edu.courses.length > 0 && (
                <div className="mt-1">
                  <p className="text-sm">
                    <span className="font-medium">Relevant Coursework:</span> {edu.courses.join(', ')}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Skills */}
        <div className="mb-6">
          <h3 className="text-lg font-bold mb-3 pb-1 border-b-2" style={{ color: colors.primary, borderColor: colors.secondary }}>
            <span className="flex items-center gap-2">
              <Star className="w-5 h-5" />
              Skills
            </span>
          </h3>
          
          <div className="grid grid-cols-2 gap-2">
            {userData.skills.map((skill, index) => (
              <div 
                key={index} 
                className="flex items-center gap-2 p-2 rounded"
                style={{ backgroundColor: index % 2 === 0 ? colors.secondary : 'white', border: `1px solid ${colors.secondary}` }}
              >
                <div className="w-2 h-2 rounded-full" style={{ backgroundColor: colors.primary }}></div>
                <span className="text-sm">{skill}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Projects */}
        {userData.projects && userData.projects.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-bold mb-3 pb-1 border-b-2" style={{ color: colors.primary, borderColor: colors.secondary }}>
              <span className="flex items-center gap-2">
                <Code className="w-5 h-5" />
                Key Projects
              </span>
            </h3>
            
            {userData.projects.map((project, index) => (
              <div key={index} className="mb-3 p-3 rounded" style={{ backgroundColor: colors.secondary }}>
                <div className="flex justify-between items-start mb-1">
                  <h4 className="text-md font-semibold" style={{ color: colors.primary }}>{project.name}</h4>
                  {project.duration && <span className="text-sm text-gray-600">{project.duration}</span>}
                </div>
                
                <p className="text-sm text-gray-700 mb-1">{project.description}</p>
                <p className="text-sm mb-2">
                  <span className="font-medium">Technologies:</span> {project.technologies}
                </p>
                
                {project.url && (
                  <a 
                    href={project.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm"
                    style={{ color: colors.primary }}
                  >
                    {project.url}
                  </a>
                )}
                
                {project.achievements && project.achievements.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium">Key Results:</p>
                    <ul className="list-disc list-inside text-sm text-gray-600 pl-2">
                      {project.achievements.map((achievement, i) => (
                        <li key={i}>{achievement}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Two-column layout for additional sections */}
        <div className="grid grid-cols-2 gap-6">
          {/* Certifications */}
          {userData.certifications && userData.certifications.length > 0 && (
            <div>
              <h3 className="text-md font-bold mb-2 pb-1 border-b-2" style={{ color: colors.primary, borderColor: colors.secondary }}>
                Certifications
              </h3>
              
              {userData.certifications.map((cert, index) => (
                <div key={index} className="mb-2">
                  <p className="text-sm font-medium" style={{ color: colors.primary }}>{cert.name}</p>
                  <p className="text-xs text-gray-600">{cert.issuer}, {cert.date}</p>
                </div>
              ))}
            </div>
          )}

          {/* Languages */}
          {userData.languages && userData.languages.length > 0 && (
            <div>
              <h3 className="text-md font-bold mb-2 pb-1 border-b-2" style={{ color: colors.primary, borderColor: colors.secondary }}>
                Languages
              </h3>
              
              {userData.languages.map((lang, index) => (
                <div key={index} className="flex justify-between mb-1">
                  <span className="text-sm">{lang.name}</span>
                  <span className="text-sm" style={{ color: colors.primary }}>{lang.proficiency}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Interests */}
        {userData.interests && userData.interests.length > 0 && (
          <div className="mt-6">
            <h3 className="text-md font-bold mb-2 pb-1 border-b-2" style={{ color: colors.primary, borderColor: colors.secondary }}>
              Interests
            </h3>
            
            <div className="flex flex-wrap gap-2">
              {userData.interests.map((interest, index) => (
                <span 
                  key={index} 
                  className="px-3 py-1 text-sm rounded"
                  style={{ backgroundColor: colors.secondary, color: colors.text }}
                >
                  {interest}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Key Achievements */}
        {userData.awards && userData.awards.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-bold mb-3 pb-1 border-b-2" style={{ color: colors.primary, borderColor: colors.secondary }}>
              <span className="flex items-center gap-2">
                <Award className="w-5 h-5" />
                Key Achievements
              </span>
            </h3>
            
            <div className="grid grid-cols-1 gap-3">
              {userData.awards.map((award, index) => (
                <div key={index} className="flex items-start gap-2">
                  <Star className="w-5 h-5 flex-shrink-0 mt-0.5" style={{ color: colors.primary }} />
                  <div>
                    <p className="text-sm font-medium">{award.title}</p>
                    <p className="text-xs text-gray-600">{award.issuer}, {award.date}</p>
                    {award.description && <p className="text-xs text-gray-600">{award.description}</p>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ColoredSections;
