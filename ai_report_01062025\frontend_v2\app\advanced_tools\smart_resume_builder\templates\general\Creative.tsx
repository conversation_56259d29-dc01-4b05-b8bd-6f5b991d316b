import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Briefcase, GraduationCap, Award, Linkedin, Github } from 'lucide-react';

const Creative: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white max-w-4xl mx-auto my-4 overflow-hidden" style={{ fontFamily: 'Poppins, sans-serif' }}>
      {/* Header with accent bar */}
      <div className="relative">
        <div className="h-4" style={{ backgroundColor: colors.primary }}></div>
        <div className="p-8 pb-0">
          <h1 className="text-4xl font-bold mb-2">{userData.name}</h1>
          <h2 className="text-xl text-gray-600 mb-6">{userData.title}</h2>
          
          {/* Contact Info */}
          <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-8">
            {userData.email && (
              <span className="flex items-center gap-1">
                <Mail className="w-4 h-4" />
                {userData.email}
              </span>
            )}
            {userData.phone && (
              <span className="flex items-center gap-1">
                <Phone className="w-4 h-4" />
                {userData.phone}
              </span>
            )}
            {userData.location && (
              <span className="flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                {userData.location}
              </span>
            )}
            {userData.linkedin && (
              <span className="flex items-center gap-1">
                <Linkedin className="w-4 h-4" />
                {userData.linkedin}
              </span>
            )}
            {userData.github && (
              <span className="flex items-center gap-1">
                <Github className="w-4 h-4" />
                {userData.github}
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="p-8 pt-0">
        {/* Summary */}
        {userData.summary && (
          <div className="mb-10">
            <h3 className="text-xl font-semibold mb-3 relative pb-2" style={{ color: colors.primary }}>
              <span className="relative z-10 bg-white pr-4">About Me</span>
              <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200"></div>
            </h3>
            <p className="text-gray-700 leading-relaxed">{userData.summary}</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* Experience */}
            <div>
              <h3 className="text-xl font-semibold mb-4 relative pb-2" style={{ color: colors.primary }}>
                <span className="relative z-10 bg-white pr-4">Experience</span>
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200"></div>
              </h3>
              <div className="space-y-6">
                {userData.experience.map((exp, index) => (
                  <div key={index} className="pl-4 border-l-4" style={{ borderColor: colors.primary }}>
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-semibold">{exp.title}</h4>
                        <p className="text-gray-600">{exp.company}</p>
                      </div>
                      <span className="text-sm text-gray-500 whitespace-nowrap">
                        {exp.duration}
                      </span>
                    </div>
                    <p className="mt-2 text-gray-700">{exp.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Education */}
            <div>
              <h3 className="text-xl font-semibold mb-4 relative pb-2" style={{ color: colors.primary }}>
                <span className="relative z-10 bg-white pr-4">Education</span>
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200"></div>
              </h3>
              <div className="space-y-4">
                {userData.education.map((edu, index) => (
                  <div key={index} className="flex justify-between items-start">
                    <div>
                      <h4 className="font-semibold">{edu.degree}</h4>
                      <p className="text-gray-600">{edu.school}</p>
                      {edu.honors && <p className="text-sm text-gray-700 mt-1">{edu.honors}</p>}
                    </div>
                    <span className="text-sm text-gray-500 whitespace-nowrap">
                      {edu.year}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-8">
            {/* Skills */}
            <div>
              <h3 className="text-xl font-semibold mb-4 relative pb-2" style={{ color: colors.primary }}>
                <span className="relative z-10 bg-white pr-4">Skills</span>
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200"></div>
              </h3>
              <div className="space-y-3">
                {userData.skills.map((skill, index) => (
                  <div key={index} className="relative">
                    <div className="text-sm font-medium mb-1">{skill}</div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="h-full rounded-full" 
                        style={{ 
                          width: '85%', 
                          backgroundColor: colors.primary,
                          opacity: 0.8 - (index * 0.1)
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Certifications */}
            {userData.certifications && userData.certifications.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold mb-4 relative pb-2" style={{ color: colors.primary }}>
                  <span className="relative z-10 bg-white pr-4">Certifications</span>
                  <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200"></div>
                </h3>
                <ul className="space-y-3">
                  {userData.certifications?.map((cert, index) => (
                    <li key={index} className="pl-4 border-l-2" style={{ borderColor: colors.primary }}>
                      <div className="font-medium">{cert.name}</div>
                      {cert.issuer && <div className="text-sm text-gray-600">{cert.issuer}</div>}
                      {cert.date && <div className="text-xs text-gray-500 mt-1">{cert.date}</div>}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Projects */}
            {userData.projects && userData.projects.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold mb-4 relative pb-2" style={{ color: colors.primary }}>
                  <span className="relative z-10 bg-white pr-4">Projects</span>
                  <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200"></div>
                </h3>
                <div className="space-y-4">
                  {userData.projects?.map((project, index) => (
                    <div key={index} className="p-4 rounded-lg" style={{ backgroundColor: `${colors.secondary}10` }}>
                      <h4 className="font-semibold">{project.name}</h4>
                      <p className="text-sm text-gray-700 mt-1">{project.description}</p>
                      {project.technologies && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {project.technologies.split(',').map((tech: string, i: number) => (
                            <span 
                              key={i} 
                              className="text-xs px-2 py-0.5 rounded-full"
                              style={{ 
                                backgroundColor: `${colors.primary}20`,
                                color: colors.primary
                              }}
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Creative;
