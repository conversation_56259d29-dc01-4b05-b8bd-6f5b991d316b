import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Briefcase, GraduationCap, Award, Linkedin, Github } from 'lucide-react';

const Minimalist: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white max-w-3xl mx-auto my-12 p-10 rounded-lg" style={{ fontFamily: 'Helvetica, Arial, sans-serif' }}>
      {/* Header */}
      <div className="text-center mb-10">
        <h1 className="text-4xl font-light tracking-wide mb-2">{userData.name}</h1>
        <div className="h-1 w-20 bg-gray-300 mx-auto mb-4"></div>
        <h2 className="text-xl text-gray-600 mb-6">{userData.title}</h2>
        
        <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600">
          {userData.email && (
            <span className="flex items-center gap-1">
              <Mail className="w-4 h-4" />
              {userData.email}
            </span>
          )}
          {userData.phone && (
            <span className="flex items-center gap-1">
              <Phone className="w-4 h-4" />
              {userData.phone}
            </span>
          )}
          {userData.location && (
            <span className="flex items-center gap-1">
              <MapPin className="w-4 h-4" />
              {userData.location}
            </span>
          )}
          {userData.linkedin && (
            <span className="flex items-center gap-1">
              <Linkedin className="w-4 h-4" />
              {userData.linkedin}
            </span>
          )}
          {userData.github && (
            <span className="flex items-center gap-1">
              <Github className="w-4 h-4" />
              {userData.github}
            </span>
          )}
        </div>
      </div>

      {/* Summary */}
      {userData.summary && (
        <div className="mb-10">
          <h3 className="text-lg font-medium mb-3 uppercase tracking-wider" style={{ color: colors.primary }}>
            Summary
          </h3>
          <p className="text-gray-700 leading-relaxed">{userData.summary}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Left Column */}
        <div className="md:col-span-2 space-y-8">
          {/* Experience */}
          <div>
            <h3 className="text-lg font-medium mb-4 uppercase tracking-wider border-b pb-2" style={{ color: colors.primary }}>
              Experience
            </h3>
            <div className="space-y-6">
              {userData.experience.map((exp, index) => (
                <div key={index} className="border-l-2 pl-4" style={{ borderColor: colors.primary }}>
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{exp.title}</h4>
                      <p className="text-gray-600">{exp.company}</p>
                    </div>
                    <span className="text-sm text-gray-500 whitespace-nowrap">
                      {exp.duration}
                    </span>
                  </div>
                  <p className="mt-1 text-gray-700">{exp.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Education */}
          <div>
            <h3 className="text-lg font-medium mb-4 uppercase tracking-wider border-b pb-2" style={{ color: colors.primary }}>
              Education
            </h3>
            <div className="space-y-4">
              {userData.education.map((edu, index) => (
                <div key={index} className="flex justify-between">
                  <div>
                    <h4 className="font-medium">{edu.degree}</h4>
                    <p className="text-gray-600">{edu.school}</p>
                  </div>
                  <span className="text-sm text-gray-500 whitespace-nowrap">
                    {edu.year}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-8">
          {/* Skills */}
          <div>
            <h3 className="text-lg font-medium mb-4 uppercase tracking-wider" style={{ color: colors.primary }}>
              Skills
            </h3>
            <div className="space-y-2">
              {userData.skills.map((skill, index) => (
                <div key={index} className="flex items-center">
                  <div 
                    className="w-2 h-2 rounded-full mr-2" 
                    style={{ backgroundColor: colors.primary }}
                  ></div>
                  <span>{skill}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Certifications */}
          {userData.certifications && userData.certifications.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-4 uppercase tracking-wider" style={{ color: colors.primary }}>
                Certifications
              </h3>
              <ul className="space-y-2">
                {userData.certifications?.map((cert, index) => (
                  <li key={index} className="text-sm">
                    <div className="font-medium">{cert.name}</div>
                    {cert.issuer && <div className="text-gray-600">{cert.issuer}</div>}
                    {cert.date && <div className="text-xs text-gray-500">{cert.date}</div>}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Projects */}
          {userData.projects && userData.projects.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-4 uppercase tracking-wider" style={{ color: colors.primary }}>
                Projects
              </h3>
              <div className="space-y-4">
                {userData.projects?.map((project, index) => (
                  <div key={index}>
                    <h4 className="font-medium">{project.name}</h4>
                    <p className="text-sm text-gray-700">{project.description}</p>
                    {project.technologies && (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {project.technologies.split(',').map((tech: string, i: number) => (
                          <span key={i} className="text-xs px-2 py-0.5 bg-gray-100 rounded">
                            {tech}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Minimalist;
