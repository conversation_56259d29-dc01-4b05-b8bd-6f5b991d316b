import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Globe, Briefcase, GraduationCap, Code } from 'lucide-react';

const MinimalistTemplate: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white p-8 max-w-2xl mx-auto" style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Header */}
      <div className="flex flex-col items-center mb-6">
        <h1 className="text-2xl font-bold uppercase tracking-wider mb-1" style={{ color: colors.primary }}>
          {userData.name}
        </h1>
        <h2 className="text-md text-gray-600 mb-2">{userData.title}</h2>
        
        <div className="flex flex-wrap justify-center gap-3 text-xs">
          {userData.email && (
            <span className="flex items-center gap-1">
              <Mail className="w-3 h-3" style={{ color: colors.primary }} />
              {userData.email}
            </span>
          )}
          {userData.phone && (
            <span className="flex items-center gap-1">
              <Phone className="w-3 h-3" style={{ color: colors.primary }} />
              {userData.phone}
            </span>
          )}
          {userData.location && (
            <span className="flex items-center gap-1">
              <MapPin className="w-3 h-3" style={{ color: colors.primary }} />
              {userData.location}
            </span>
          )}
          {userData.website && (
            <span className="flex items-center gap-1">
              <Globe className="w-3 h-3" style={{ color: colors.primary }} />
              {userData.website}
            </span>
          )}
        </div>
      </div>

      <div className="border-t border-b py-2 mb-4" style={{ borderColor: colors.primary }}>
        {/* Summary */}
        <div className="mb-4">
          <h3 className="text-sm font-bold uppercase mb-1" style={{ color: colors.primary }}>Summary</h3>
          <p className="text-xs text-gray-700">{userData.summary}</p>
        </div>
      </div>

      {/* Experience */}
      <div className="mb-4">
        <h3 className="text-sm font-bold uppercase mb-2" style={{ color: colors.primary }}>Experience</h3>
        {userData.experience.map((exp, index) => (
          <div key={index} className="mb-3">
            <div className="flex justify-between items-baseline mb-1">
              <div className="flex items-baseline">
                <h4 className="text-sm font-semibold mr-2">{exp.title}</h4>
                <p className="text-xs text-gray-600">{exp.company}</p>
              </div>
              <span className="text-xs text-gray-500">{exp.duration}</span>
            </div>
            <p className="text-xs text-gray-700 mb-1">{exp.description}</p>
            
            {exp.achievements && exp.achievements.length > 0 && (
              <ul className="list-disc list-inside text-xs text-gray-600 pl-1">
                {exp.achievements.map((achievement, i) => (
                  <li key={i}>{achievement}</li>
                ))}
              </ul>
            )}
          </div>
        ))}
      </div>

      {/* Education */}
      <div className="mb-4">
        <h3 className="text-sm font-bold uppercase mb-2" style={{ color: colors.primary }}>Education</h3>
        {userData.education.map((edu, index) => (
          <div key={index} className="mb-2">
            <div className="flex justify-between items-baseline">
              <div>
                <h4 className="text-sm font-semibold">{edu.degree}</h4>
                <p className="text-xs text-gray-600">{edu.school}</p>
              </div>
              <div className="text-right">
                <div className="text-xs text-gray-500">{edu.year}</div>
                {edu.gpa && <div className="text-xs text-gray-500">GPA: {edu.gpa}</div>}
              </div>
            </div>
            {edu.honors && <p className="text-xs text-gray-600 italic">{edu.honors}</p>}
            {edu.courses && edu.courses.length > 0 && (
              <p className="text-xs text-gray-600">
                <span className="font-medium">Courses:</span> {edu.courses.join(', ')}
              </p>
            )}
          </div>
        ))}
      </div>

      {/* Skills */}
      <div className="mb-4">
        <h3 className="text-sm font-bold uppercase mb-2" style={{ color: colors.primary }}>Skills</h3>
        <div className="flex flex-wrap gap-1">
          {userData.skills.map((skill, index) => (
            <span 
              key={index} 
              className="px-2 py-0.5 text-xs rounded"
              style={{ backgroundColor: colors.secondary, color: colors.text }}
            >
              {skill}
            </span>
          ))}
        </div>
      </div>

      {/* Projects */}
      {userData.projects && userData.projects.length > 0 && (
        <div className="mb-4">
          <h3 className="text-sm font-bold uppercase mb-2" style={{ color: colors.primary }}>Projects</h3>
          {userData.projects.map((project, index) => (
            <div key={index} className="mb-2">
              <div className="flex justify-between items-baseline">
                <h4 className="text-sm font-semibold">{project.name}</h4>
                {project.duration && <span className="text-xs text-gray-500">{project.duration}</span>}
              </div>
              <p className="text-xs text-gray-700 mb-1">{project.description}</p>
              <p className="text-xs text-gray-500 mb-1">
                <span className="font-medium">Technologies:</span> {project.technologies}
              </p>
              {project.url && (
                <p className="text-xs" style={{ color: colors.primary }}>
                  <a href={project.url} target="_blank" rel="noopener noreferrer">{project.url}</a>
                </p>
              )}
              {project.achievements && project.achievements.length > 0 && (
                <ul className="list-disc list-inside text-xs text-gray-600 pl-1">
                  {project.achievements.map((achievement, i) => (
                    <li key={i}>{achievement}</li>
                  ))}
                </ul>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Certifications */}
      {userData.certifications && userData.certifications.length > 0 && (
        <div className="mb-4">
          <h3 className="text-sm font-bold uppercase mb-2" style={{ color: colors.primary }}>Certifications</h3>
          <div className="grid grid-cols-2 gap-2">
            {userData.certifications.map((cert, index) => (
              <div key={index} className="text-xs">
                <p className="font-medium">{cert.name}</p>
                <p className="text-gray-600">{cert.issuer}, {cert.date}</p>
                {cert.expiration && <p className="text-gray-500">Expires: {cert.expiration}</p>}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Languages */}
      {userData.languages && userData.languages.length > 0 && (
        <div className="mb-4">
          <h3 className="text-sm font-bold uppercase mb-2" style={{ color: colors.primary }}>Languages</h3>
          <div className="flex flex-wrap gap-4">
            {userData.languages.map((lang, index) => (
              <div key={index} className="text-xs">
                <span className="font-medium">{lang.name}:</span> {lang.proficiency}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Interests */}
      {userData.interests && userData.interests.length > 0 && (
        <div>
          <h3 className="text-sm font-bold uppercase mb-2" style={{ color: colors.primary }}>Interests</h3>
          <p className="text-xs text-gray-700">{userData.interests.join(' • ')}</p>
        </div>
      )}
    </div>
  );
};

export default MinimalistTemplate;
