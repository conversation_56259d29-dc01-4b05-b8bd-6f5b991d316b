import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Globe, Briefcase, GraduationCap, Award, Star, FileText, Code } from 'lucide-react';

const ModernProfessional: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white max-w-4xl mx-auto my-12 rounded-lg overflow-hidden shadow-lg" style={{ fontFamily: 'Calibri, sans-serif' }}>
      {/* Header with sidebar layout */}
      <div className="flex flex-col md:flex-row">
        {/* Left Sidebar */}
        <div className="md:w-1/3 p-8 pb-12" style={{ backgroundColor: colors.secondary }}>
          {/* Profile */}
          <div className="flex flex-col items-center mb-6">
            <div className="w-32 h-32 rounded-full bg-white mb-4 flex items-center justify-center overflow-hidden">
              {/* Placeholder for profile image */}
              <span className="text-4xl" style={{ color: colors.primary }}>{userData.name.charAt(0)}</span>
            </div>
            <h1 className="text-xl font-bold text-center mb-1">{userData.name}</h1>
            <h2 className="text-md text-center mb-4" style={{ color: colors.primary }}>{userData.title}</h2>
          </div>

          {/* Contact Info */}
          <div className="mb-6">
            <h3 className="text-sm font-bold uppercase mb-3 pb-1 border-b" style={{ borderColor: colors.primary }}>Contact</h3>
            <div className="space-y-2 text-sm">
              {userData.email && (
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4" style={{ color: colors.primary }} />
                  <span>{userData.email}</span>
                </div>
              )}
              {userData.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4" style={{ color: colors.primary }} />
                  <span>{userData.phone}</span>
                </div>
              )}
              {userData.location && (
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" style={{ color: colors.primary }} />
                  <span>{userData.location}</span>
                </div>
              )}
              {userData.website && (
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4" style={{ color: colors.primary }} />
                  <span>{userData.website}</span>
                </div>
              )}
            </div>
          </div>

          {/* Skills */}
          <div className="mb-6">
            <h3 className="text-sm font-bold uppercase mb-3 pb-1 border-b" style={{ borderColor: colors.primary }}>Skills</h3>
            <div className="space-y-2">
              {userData.skills.map((skill, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm">{skill}</span>
                  <div className="w-16 bg-white/20 rounded-full h-1.5">
                    <div className="h-1.5 rounded-full" style={{ 
                      backgroundColor: colors.primary, 
                      width: `${85 + Math.random() * 15}%` 
                    }}></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Languages */}
          {userData.languages && userData.languages.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-bold uppercase mb-3 pb-1 border-b" style={{ borderColor: colors.primary }}>Languages</h3>
              {userData.languages.map((lang, index) => (
                <div key={index} className="flex justify-between mb-2">
                  <span className="text-sm">{lang.name}</span>
                  <span className="text-sm">{lang.proficiency}</span>
                </div>
              ))}
            </div>
          )}

          {/* Certifications */}
          {userData.certifications && userData.certifications.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-bold uppercase mb-3 pb-1 border-b" style={{ borderColor: colors.primary }}>Certifications</h3>
              {userData.certifications.map((cert, index) => (
                <div key={index} className="mb-2">
                  <p className="text-sm font-medium">{cert.name}</p>
                  <p className="text-xs">{cert.issuer}, {cert.date}</p>
                </div>
              ))}
            </div>
          )}

          {/* Interests */}
          {userData.interests && userData.interests.length > 0 && (
            <div>
              <h3 className="text-sm font-bold uppercase mb-3 pb-1 border-b" style={{ borderColor: colors.primary }}>Interests</h3>
              <div className="flex flex-wrap gap-2">
                {userData.interests.map((interest, index) => (
                  <span 
                    key={index} 
                    className="px-2 py-1 text-xs rounded"
                    style={{ backgroundColor: 'white', color: colors.primary }}
                  >
                    {interest}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="md:w-2/3 p-8 pb-12">
          {/* Summary */}
          <div className="mb-6">
            <h3 className="text-lg font-bold mb-3" style={{ color: colors.primary }}>Summary</h3>
            <p className="text-sm text-gray-700">{userData.summary}</p>
          </div>

          {/* Experience */}
          <div className="mb-6">
            <h3 className="text-lg font-bold mb-4" style={{ color: colors.primary }}>
              <span className="flex items-center gap-2">
                <Briefcase className="w-5 h-5" />
                Experience
              </span>
            </h3>
            
            {userData.experience.map((exp, index) => (
              <div key={index} className="mb-4 relative pl-6 pb-4">
                {/* Timeline dot */}
                <div 
                  className="absolute left-0 top-1.5 w-3 h-3 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                ></div>
                
                {/* Timeline line */}
                {index < userData.experience.length - 1 && (
                  <div 
                    className="absolute left-1.5 top-3 bottom-0 w-0.5"
                    style={{ backgroundColor: colors.secondary }}
                  ></div>
                )}
                
                <div className="flex justify-between items-start mb-1">
                  <div>
                    <h4 className="text-md font-semibold">{exp.title}</h4>
                    <p className="text-sm" style={{ color: colors.primary }}>{exp.company}</p>
                  </div>
                  <span className="text-xs px-2 py-1 rounded" style={{ backgroundColor: colors.secondary }}>
                    {exp.duration}
                  </span>
                </div>
                
                <p className="text-sm text-gray-700 mb-2">{exp.description}</p>
                
                {exp.achievements && exp.achievements.length > 0 && (
                  <ul className="list-disc list-inside text-sm text-gray-600 pl-2">
                    {exp.achievements.map((achievement, i) => (
                      <li key={i}>{achievement}</li>
                    ))}
                  </ul>
                )}
              </div>
            ))}
          </div>

          {/* Education */}
          <div className="mb-6">
            <h3 className="text-lg font-bold mb-4" style={{ color: colors.primary }}>
              <span className="flex items-center gap-2">
                <GraduationCap className="w-5 h-5" />
                Education
              </span>
            </h3>
            
            {userData.education.map((edu, index) => (
              <div key={index} className="mb-4 relative pl-6 pb-4">
                {/* Timeline dot */}
                <div 
                  className="absolute left-0 top-1.5 w-3 h-3 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                ></div>
                
                {/* Timeline line */}
                {index < userData.education.length - 1 && (
                  <div 
                    className="absolute left-1.5 top-3 bottom-0 w-0.5"
                    style={{ backgroundColor: colors.secondary }}
                  ></div>
                )}
                
                <div className="flex justify-between items-start mb-1">
                  <div>
                    <h4 className="text-md font-semibold">{edu.degree}</h4>
                    <p className="text-sm" style={{ color: colors.primary }}>{edu.school}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-xs px-2 py-1 rounded" style={{ backgroundColor: colors.secondary }}>
                      {edu.year}
                    </div>
                    {edu.gpa && (
                      <div className="text-xs mt-1">
                        GPA: {edu.gpa}
                      </div>
                    )}
                  </div>
                </div>
                
                {edu.honors && <p className="text-sm text-gray-600 italic">{edu.honors}</p>}
                
                {edu.courses && edu.courses.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium">Relevant Coursework:</p>
                    <p className="text-xs text-gray-600">{edu.courses.join(', ')}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Projects */}
          {userData.projects && userData.projects.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-bold mb-4" style={{ color: colors.primary }}>
                <span className="flex items-center gap-2">
                  <Code className="w-5 h-5" />
                  Projects
                </span>
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {userData.projects.map((project, index) => (
                  <div 
                    key={index} 
                    className="p-4 rounded"
                    style={{ backgroundColor: colors.secondary }}
                  >
                    <h4 className="text-md font-semibold mb-2">{project.name}</h4>
                    <p className="text-sm text-gray-700 mb-2">{project.description}</p>
                    
                    <div className="flex flex-wrap gap-1 mb-2">
                      {project.technologies.split(', ').map((tech, i) => (
                        <span 
                          key={i} 
                          className="px-2 py-0.5 text-xs rounded"
                          style={{ backgroundColor: 'white', color: colors.primary }}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                    
                    {project.url && (
                      <a 
                        href={project.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs flex items-center gap-1"
                        style={{ color: colors.primary }}
                      >
                        <Globe className="w-3 h-3" />
                        View Project
                      </a>
                    )}
                    
                    {project.achievements && project.achievements.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs font-medium">Key Achievements:</p>
                        <ul className="list-disc list-inside text-xs text-gray-600 pl-2">
                          {project.achievements.map((achievement, i) => (
                            <li key={i}>{achievement}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Key Achievements Section */}
          {userData.awards && userData.awards.length > 0 && (
            <div>
              <h3 className="text-lg font-bold mb-4" style={{ color: colors.primary }}>
                <span className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  Key Achievements
                </span>
              </h3>
              
              <div className="grid grid-cols-2 gap-4">
                {userData.awards.map((award, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <Star className="w-5 h-5 flex-shrink-0 mt-0.5" style={{ color: colors.primary }} />
                    <div>
                      <p className="text-sm font-medium">{award.title}</p>
                      <p className="text-xs text-gray-600">{award.issuer}, {award.date}</p>
                      {award.description && <p className="text-xs text-gray-500">{award.description}</p>}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernProfessional;
