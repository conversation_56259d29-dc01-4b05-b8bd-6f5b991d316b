import React from 'react';
import { TemplateProps, UserData, ColorScheme, Project as BaseProject, Experience, Education } from '../../types';

// Extend the base Project type with our required fields
type Project = BaseProject & {
  technologies: string[];
  role: string;
  duration: string;
};

// Extend the base UserData with our custom fields
type ExtendedUserData = Omit<UserData, 'projects'> & {
  projects: Project[];
  achievements?: string[];
};

// Define our component props
interface ModernProfessionalProps extends Omit<TemplateProps, 'userData'> {
  userData?: Partial<ExtendedUserData>;
}

// Helper function to ensure proper typing for projects
const createProject = (project: Omit<Project, 'technologies'> & { technologies: string[] }): Project => ({
  ...project,
  technologies: project.technologies.join(', ')
} as unknown as Project);

// Default data for preview
const defaultData: ExtendedUserData = {
  name: '<PERSON>',
  title: 'Senior Product Designer',
  email: '<EMAIL>',
  phone: '(*************',
  linkedin: 'linkedin.com/in/alex<PERSON><PERSON><PERSON>',
  location: 'San Francisco, CA',
  summary: 'Innovative Product Designer with 7+ years of experience in creating user-centered digital products. Specialized in UX/UI design, design systems, and user research. Passionate about solving complex problems through intuitive design.',
  experience: [
    {
      title: 'Senior Product Designer',
      company: 'TechCorp Inc.',
      start: '2020',
      end: 'Present',
      description: 'Lead designer for enterprise SaaS platform. Improved user engagement by 40% through UX enhancements.',
      role: 'Senior Product Designer',
      achievements: []
    },
    {
      title: 'UI/UX Designer',
      company: 'Creative Solutions',
      start: '2018',
      end: '2020',
      description: 'Designed mobile and web applications for various clients. Created design systems and conducted user testing.',
      role: 'UI/UX Designer',
      achievements: []
    },
    {
      title: 'Junior Designer',
      company: 'Digital Innovations',
      start: '2016',
      end: '2018',
      description: 'Collaborated on UI components and contributed to client projects under senior designers.',
      role: 'Junior Designer',
      achievements: []
    }
  ] as unknown as Experience[],
  education: [
    {
      degree: 'M.S. in Human-Computer Interaction',
      school: 'Stanford University',
      start: '2014',
      end: '2016',
      field: 'Human-Computer Interaction',
      description: 'Specialized in Interaction Design and User Research',
      gpa: '3.8',
      honors: ['Summa Cum Laude'],
      courses: []
    },
    {
      degree: 'B.A. in Graphic Design',
      school: 'California College of the Arts',
      start: '2010',
      end: '2014',
      field: 'Graphic Design',
      description: 'Minor in Digital Media',
      gpa: '3.7',
      honors: [],
      courses: []
    }
  ] as unknown as Education[],
  skills: [
    'UI/UX Design', 'Figma', 'Sketch', 'Adobe XD', 'User Research',
    'Prototyping', 'Design Systems', 'HTML/CSS', 'Agile Methodologies',
    'User Testing', 'Interaction Design', 'Mobile Design', 'Web Design'
  ],
  projects: [
    createProject({
      name: 'E-commerce Platform Redesign',
      role: 'Lead Designer',
      duration: '2023',
      description: 'Redesigned the user interface for a major e-commerce platform, resulting in a 30% increase in conversion rates.',
      technologies: ['Figma', 'UserTesting', 'Google Analytics', 'React'],
      url: ''
    }),
    createProject({
      name: 'Mobile Banking App',
      role: 'UI/UX Designer',
      duration: '2022',
      description: 'Designed a mobile-first banking application focused on financial literacy for young adults.',
      technologies: ['Sketch', 'InVision', 'iOS', 'Android'],
      url: ''
    })
  ],
  achievements: [
    'Led a team of 5 designers to deliver 10+ high-impact projects, resulting in a 40% increase in user engagement',
    'Reduced customer support tickets by 35% through improved UX design and documentation',
    'Mentored 3 junior designers, improving team productivity by 25%',
    'Spearheaded design system implementation, reducing design-to-development handoff time by 50%'
  ]
};

const ModernProfessional: React.FC<TemplateProps> = ({ 
  userData = defaultData,
  colors = {
    primary: '#2563eb',
    secondary: '#1e40af',
    accent: '#1e3a8a',
    text: '#1f2937',
    background: '#ffffff',
    muted: '#6b7280'
  }
}) => {
  // Safely merge default data with user data
  // Safely merge data with proper typing
  const mergedData = { 
    ...defaultData, 
    ...userData,
  } as ExtendedUserData;

  // Ensure projects and achievements are properly typed
  const projects = mergedData.projects || [];
  const achievements = mergedData.achievements || [];

  // Create final data object with proper typing
  const data: ExtendedUserData = {
    ...mergedData,
    projects,
    achievements
  };
  
  return (
    <div className="bg-white max-w-4xl mx-auto my-4 rounded-xl overflow-hidden" style={{ fontFamily: 'Inter, Arial, sans-serif' }}>
      {/* Header */}
      <div className="px-8 pt-8 pb-4 border-b border-gray-200">
        <h1 className="text-2xl font-bold text-gray-900 mb-1">{data.name}</h1>
        <div className="text-sm text-blue-700 font-medium mb-2">{data.title}</div>
        <div className="flex flex-wrap gap-4 text-xs text-gray-700 mb-2">
          {data.phone && <span>📞 {data.phone}</span>}
          {data.email && <span>✉️ {data.email}</span>}
          {data.linkedin && <span>🔗 {data.linkedin}</span>}
          {data.location && <span>📍 {data.location}</span>}
        </div>
      </div>
      
      {/* Main Content */}
      <div className="px-8 py-4 grid grid-cols-3 gap-8">
        {/* Left Column (2/3 width) */}
        <div className="col-span-2">
          {/* Experience */}
          <h2 className="text-lg font-semibold text-blue-700 mb-3 border-b pb-1">Experience</h2>
          <div className="space-y-4">
            {data.experience.map((exp, index) => (
              <div key={index} className="mb-4">
                <div className="flex justify-between items-start">
                  <h3 className="font-semibold">{exp.role}</h3>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{exp.duration}</span>
                </div>
                <p className="text-sm text-gray-700 mb-1">{exp.company}</p>
                <p className="text-xs text-gray-600">{exp.description}</p>
              </div>
            ))}
          </div>
          
          {/* Projects */}
          <h2 className="text-lg font-semibold text-blue-700 mt-6 mb-3 border-b pb-1">Projects</h2>
          <div className="space-y-4">
            {data.projects?.map((project, index) => (
              <div key={index} className="mb-4">
                <div className="flex justify-between items-start">
                  <h3 className="font-semibold">{project.name}</h3>
                  <span className="text-xs text-gray-500">{project.duration}</span>
                </div>
                <p className="text-sm text-gray-700 mb-1">{project.role}</p>
                <p className="text-xs text-gray-600 mb-2">{project.description}</p>
                {project.technologies && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {(typeof project.technologies === 'string' 
                      ? project.technologies.split(',')
                      : project.technologies
                    ).map((tech: string, i: number) => (
                      <span key={i} className="text-[10px] bg-blue-50 text-blue-700 px-1.5 py-0.5 rounded">
                        {typeof tech === 'string' ? tech.trim() : tech}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Skills */}
          <h2 className="text-lg font-semibold text-blue-700 mt-6 mb-3 border-b pb-1">Skills</h2>
          <div className="flex flex-wrap gap-2">
            {data.skills.map((skill, index) => (
              <span key={index} className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">
                {skill}
              </span>
            ))}
          </div>
        </div>
        
        {/* Right Column (1/3 width) */}
        <div>
          {/* Summary */}
          <h2 className="text-lg font-semibold text-blue-700 mb-3 border-b pb-1">Summary</h2>
          <p className="text-sm text-gray-700 mb-6">{data.summary}</p>
          
          {/* Key Achievements */}
          <h2 className="text-lg font-semibold text-blue-700 mt-6 mb-3 border-b pb-1">Key Achievements</h2>
          <ul className="space-y-2">
            {data.achievements?.map((achievement, index) => (
              <li key={index} className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                <span className="text-xs text-gray-700">{achievement}</span>
              </li>
            ))}
          </ul>
          
          {/* Education */}
          <h2 className="text-lg font-semibold text-blue-700 mt-6 mb-3 border-b pb-1">Education</h2>
          <div className="space-y-4">
            {data.education.map((edu, index) => (
              <div key={index} className="mb-3">
                <h3 className="font-semibold text-sm">{edu.degree}</h3>
                <p className="text-xs text-gray-700">{edu.school}</p>
                <p className="text-xs text-gray-500">{edu.year}</p>
                {edu.description && <p className="text-xs text-gray-600 mt-1">{edu.description}</p>}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernProfessional;
