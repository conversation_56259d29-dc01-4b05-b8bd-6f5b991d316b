import React from 'react';
import { TemplateProps, UserData, ColorScheme, Experience as BaseExperience, Project } from '../../types';

// Define types for the template data
interface SkillGroup {
  category: string;
  items: string[];
}

interface Strength {
  name: string;
  level: number;
}

// Create our own Experience interface based on the imported one
interface Experience {
  duration?: string;
  title: string; // Keep title required as per BaseExperience
  description: string;
  company: string;
  role?: string;
  achievements?: string[];
  start?: string;
  end?: string;
}

// Use the Education interface from the main types file
interface Education {
  school: string;
  degree: string;
  field: string;
  start: string;
  end: string;
  year: string;
  description?: string;
  gpa?: string;
  honors?: string;
  courses?: boolean;
}

// Extend the base UserData type with our custom fields
interface ModernVisualUserData extends Omit<UserData, 'skills' | 'experience' | 'photoUrl' | 'education'> {
  photoUrl?: string;
  strengths: Strength[];
  skills: SkillGroup[] | string[];
  experience: Experience[];
  education: Education[];
  projects: Project[]; // Use the proper Project type
}

// Default data for preview
// Export it so it can be used by the main page component
export const defaultData: ModernVisualUserData = {
  name: '<PERSON>',
  title: 'UX/UI Designer & Frontend Developer',
  email: '<EMAIL>',
  phone: '(*************',
  linkedin: 'linkedin.com/in/sarahwilliams',
  location: 'New York, NY',
  photoUrl: 'https://randomuser.me/api/portraits/women/44.jpg',
  summary: 'Creative and detail-oriented UX/UI Designer with 5+ years of experience in creating beautiful and functional digital experiences. Passionate about user-centered design and frontend development. Strong background in visual design and prototyping.',
  strengths: [
    { name: 'User Research', level: 5 },
    { name: 'UI/UX Design', level: 5 },
    { name: 'Prototyping', level: 4 },
    { name: 'Frontend Development', level: 4 },
    { name: 'Visual Design', level: 5 },
    { name: 'User Testing', level: 4 }
  ],
  skills: [
    { category: 'Design', items: ['Figma', 'Sketch', 'Adobe XD', 'Photoshop', 'Illustrator'] },
    { category: 'Development', items: ['HTML5', 'CSS3', 'JavaScript', 'React', 'Vue.js'] },
    { category: 'Tools', items: ['Jira', 'Trello', 'Zeplin', 'InVision', 'Miro'] }
  ],
  experience: [
    {
      company: 'DesignHub',
      role: 'Senior UX/UI Designer',
      title: 'Senior UX/UI Designer',
      duration: '2020 - Present',
      description: 'Leading the design team and creating user-centered designs for web and mobile applications.',
      achievements: [
        'Led a team of 5 designers to deliver 10+ successful projects',
        'Improved user engagement by 40% through UX optimization'
      ]
    },
    {
      company: 'CreativeMinds',
      role: 'UI/UX Designer',
      title: 'UI/UX Designer',
      duration: '2018 - 2020',
      description: 'Designed and prototyped user interfaces for various clients across different industries.',
      achievements: [
        'Designed 20+ mobile and web applications',
        'Conducted user research and testing for 15+ projects'
      ]
    },
    {
      company: 'PixelPerfect',
      role: 'Junior Designer',
      title: 'Junior Designer',
      duration: '2016 - 2018',
      description: 'Created visual designs and assisted in user research and testing.',
      achievements: [
        'Assisted in the design of 30+ client projects',
        'Created design systems and style guides'
      ]
    }
  ],
  education: [
    {
      degree: 'B.S. in Graphic Design',
      school: 'New York University',
      start: '2015',
      end: '2019',
      year: '2015 - 2019',
      field: 'User Experience Design',
      description: 'Graduated with honors. Focus on User Experience Design and Frontend Development.',
      gpa: '3.8',
      honors: 'Summa Cum Laude, Dean\'s List',
      courses: true
    },
    {
      degree: 'Web Development Certificate',
      school: 'General Assembly',
      start: '2018',
      end: '2018',
      year: '2018',
      field: 'Full-Stack Web Development',
      description: 'Intensive 12-week program in full-stack web development.',
      courses: true
    }
  ],
  projects: [
    {
      name: 'Portfolio Website',
      description: 'Designed and developed a personal portfolio website to showcase my work.',
      technologies: 'React, Next.js, Tailwind CSS',
      role: 'Designer & Developer',
      duration: '3 months',
      achievements: [
        'Implemented responsive design for all device sizes',
        'Achieved 100% performance score on Lighthouse'
      ]
    }
  ]
};

// Extend the base TemplateProps with our custom userData type
interface ModernVisualProps extends Omit<TemplateProps, 'userData'> {
  userData?: Partial<ModernVisualUserData>;
}

const ModernVisual: React.FC<ModernVisualProps> = ({ 
  userData = defaultData, 
  colors = {
    primary: '#2563eb',
    secondary: '#1e40af',
    accent: '#1e3a8a',
    text: '#1f2937',
    background: '#ffffff',
    muted: '#6b7280'
  },
  ...props 
}) => {
  const data: ModernVisualUserData = { 
    ...defaultData, 
    ...userData,
    // Ensure required fields are present
    projects: userData?.projects || []
  } as ModernVisualUserData;
  
  // Normalize skills data to handle both SkillGroup[] and string[] formats
  const normalizedSkills = React.useMemo(() => {
    if (!data.skills || data.skills.length === 0) return [];
    
    // If it's already in SkillGroup format
    if (typeof data.skills[0] === 'object' && 'items' in (data.skills[0] as any)) {
      return data.skills as SkillGroup[];
    }
    
    // If it's a string array, convert to SkillGroup format
    return [{
      category: 'Skills',
      items: data.skills as unknown as string[]
    }];
  }, [data.skills]);
  
  // Generate chart data based on strengths
  const generateChartData = (): { value: number; label: string }[] => {
    // If we have strengths data, use it
    if (data.strengths && data.strengths.length > 0) {
      return data.strengths.map(strength => ({
        value: strength.level * 20, // Convert level (1-5) to percentage (20-100)
        label: strength.name
      }));
    }
    
    // Fallback to random data if no strengths are available
    return [
      { value: 85, label: 'UX Design' },
      { value: 75, label: 'UI Design' },
      { value: 90, label: 'Wireframing' },
      { value: 65, label: 'Prototyping' },
      { value: 80, label: 'User Research' },
      { value: 70, label: 'Visual Design' },
      { value: 60, label: 'Frontend Dev' }
    ];
  };

  return (
    <div className="bg-white max-w-4xl mx-auto my-4 rounded-xl overflow-hidden shadow-md border border-gray-200" style={{ fontFamily: 'Montserrat, Arial, sans-serif' }}>
      {/* Header with Photo */}
      <div className="flex flex-row items-start p-8 pb-0">
        <div className="flex-1 pr-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-1">{data.name}</h1>
          <div className="text-sm text-blue-600 font-medium mb-2">{data.title}</div>
          <div className="flex flex-wrap gap-4 text-xs text-gray-700 mb-2">
            {data.phone && <span>📞 {data.phone}</span>}
            {data.email && <span>✉️ {data.email}</span>}
            {data.linkedin && <span>🔗 {data.linkedin}</span>}
            {data.location && <span>📍 {data.location}</span>}
          </div>
        </div>
        {data.photoUrl && (
          <img 
            src={data.photoUrl} 
            alt="Profile" 
            className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-md"
          />
        )}
      </div>

      {/* Main Content */}
      <div className="px-8 pt-2 pb-8 grid grid-cols-2 gap-8">
        {/* Left Column */}
        <div>
          {/* Summary */}
          <h2 className="text-lg font-semibold text-blue-600 mb-3 border-b border-gray-200 pb-1">Summary</h2>
          <p className="text-sm text-gray-700 mb-6">{data.summary}</p>
          
          {/* Strengths */}
          <h2 className="text-lg font-semibold text-blue-600 mb-3 border-b border-gray-200 pb-1">Strengths</h2>
          <div className="space-y-3 mb-6">
            {data.strengths.map((strength, index) => (
              <div key={index} className="mb-2">
                <div className="flex justify-between text-xs mb-1">
                  <span>{strength.name}</span>
                  <span>{strength.level * 20}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${strength.level * 20}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Skills */}
          <h2 className="text-lg font-semibold text-blue-600 mb-3 border-b border-gray-200 pb-1">Skills</h2>
          <div className="space-y-4">
            {normalizedSkills.map((skillGroup, index) => (
              <div key={index}>
                <h3 className="text-sm font-semibold text-gray-800 mb-1">{skillGroup.category}</h3>
                <div className="flex flex-wrap gap-2">
                  {skillGroup.items.map((skill: string, i: number) => (
                    <span key={i} className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Right Column */}
        <div>
          {/* Experience */}
          <h2 className="text-lg font-semibold text-blue-600 mb-3 border-b border-gray-200 pb-1">Experience</h2>
          <div className="space-y-4 mb-6">
            {data.experience.map((exp, index) => (
              <div key={index} className="relative pl-5 pb-4 border-l-2 border-blue-200">
                <div className="absolute w-3 h-3 bg-blue-600 rounded-full -left-[7px] top-1"></div>
                <div className="flex justify-between items-start">
                  <h3 className="font-semibold text-sm">{exp.title || exp.role}</h3>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">{exp.duration || `${exp.start} - ${exp.end}`}</span>
                </div>
                <p className="text-sm text-gray-700 font-medium mb-1">{exp.company}</p>
                <p className="text-xs text-gray-600">{exp.description}</p>
                {exp.achievements && exp.achievements.length > 0 && (
                  <div className="mt-2">
                    <p className="text-xs font-medium text-gray-700">Key Achievements:</p>
                    <ul className="text-xs text-gray-600 list-disc pl-4 mt-1 space-y-1">
                      {exp.achievements.map((achievement, i) => (
                        <li key={i}>{achievement}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
          {/* Education */}
          <h2 className="text-lg font-semibold text-blue-600 mb-3 border-b border-gray-200 pb-1">Education</h2>
          <div className="space-y-4">
            {data.education.map((edu, index) => {
              // Handle both string and object formats for education
              const school = typeof edu === 'string' ? edu : edu.school;
              const degree = (edu as any).degree || '';
              const description = (edu as any).description || '';
              const year = (edu as any).year || ((edu as any).start && (edu as any).end) ? 
                `${(edu as any).start} - ${(edu as any).end}` : '';
              
              return (
                <div key={index} className="relative pl-5 pb-4 border-l-2 border-blue-200">
                  <div className="absolute w-3 h-3 bg-blue-600 rounded-full -left-[7px] top-1"></div>
                  <h3 className="font-semibold text-sm">{degree || school}</h3>
                  <p className="text-sm text-gray-700 font-medium mb-1">{school}</p>
                  {year && <p className="text-xs text-gray-500 mb-1">{year}</p>}
                  {description && <p className="text-xs text-gray-600">{description}</p>}
                </div>
              );
            })}
          </div>
          
          {/* Visual Chart */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-semibold text-gray-700 mb-2">Design Skills</h3>
            <div className="h-24 flex items-end gap-1">
              {generateChartData().map((item, index) => (
                <div 
                  key={index}
                  className="bg-blue-500 w-full rounded-t"
                  style={{ 
                    height: `${item.value}%`,
                    opacity: 0.7 + (index * 0.05)
                  }}
                ></div>
              ))}
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              {generateChartData().map((item, index) => (
                <span key={index} className="text-[8px] truncate" title={item.label}>
                  {item.label.length > 5 ? item.label.substring(0, 5) + '...' : item.label}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernVisual;
