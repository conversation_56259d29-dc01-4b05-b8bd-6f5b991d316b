import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Briefcase, GraduationCap, Award, Linkedin, Github } from 'lucide-react';

const Professional: React.FC<TemplateProps> = ({ userData, colors }) => {
  const [isTwoColumn, setIsTwoColumn] = React.useState(false);
  
  React.useEffect(() => {
    const preview = document.getElementById('resume-preview');
    if (preview) {
      setIsTwoColumn(preview.classList.contains('two-column'));
    }
  }, []);
  
  const SectionWrapper: React.FC<{children: React.ReactNode, className?: string}> = ({ children, className = '' }) => (
    <div className={`mb-8 ${className}`}>
      {children}
    </div>
  );
  
  const SectionTitle: React.FC<{children: React.ReactNode}> = ({ children }) => (
    <h3 className="text-lg font-semibold mb-4" style={{ color: colors.primary }}>
      {children}
    </h3>
  );
  return (
    <div className={`max-w-4xl mx-auto p-8 bg-white shadow-lg ${isTwoColumn ? 'grid grid-cols-2 gap-8' : ''}`}>
      {/* Header */}
      <div className={`text-center mb-8 ${isTwoColumn ? 'col-span-2' : ''}`}>
        <h1 className="text-3xl font-bold mb-2" style={{ color: colors.primary }}>{userData.name}</h1>
        <h2 className="text-xl text-gray-600 mb-4">{userData.title}</h2>
        <div className="flex justify-center items-center gap-4 text-sm flex-wrap">
          {userData.email && (
            <span className="flex items-center gap-1">
              <Mail className="w-4 h-4" />
              {userData.email}
            </span>
          )}
          {userData.phone && (
            <span className="flex items-center gap-1">
              <Phone className="w-4 h-4" />
              {userData.phone}
            </span>
          )}
          {userData.location && (
            <span className="flex items-center gap-1">
              <MapPin className="w-4 h-4" />
              {userData.location}
            </span>
          )}
          {userData.linkedin && (
            <span className="flex items-center gap-1">
              <Linkedin className="w-4 h-4" />
              {userData.linkedin}
            </span>
          )}
          {userData.github && (
            <span className="flex items-center gap-1">
              <Github className="w-4 h-4" />
              {userData.github}
            </span>
          )}
        </div>
      </div>

      {/* Experience */}
      <SectionWrapper className={isTwoColumn ? 'section-left' : ''}>
        <SectionTitle>Experience</SectionTitle>
        {userData.experience.map((exp, index) => (
          <div key={index} className="mb-6">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold">{exp.title}</h4>
                <p className="text-gray-600">{exp.company}</p>
              </div>
              <span className="text-sm text-gray-500">
                {exp.duration}
              </span>
            </div>
            <p className="mt-1 text-gray-700">{exp.description}</p>
          </div>
        ))}
      </SectionWrapper>

      {/* Education */}
      <SectionWrapper className={isTwoColumn ? 'section-right' : ''}>
        <SectionTitle>Education</SectionTitle>
        {userData.education.map((edu, index) => (
          <div key={index} className="mb-4">
            <div className="flex justify-between">
              <h4 className="font-semibold">{edu.degree}</h4>
              <span className="text-sm text-gray-500">
                {edu.year}
              </span>
            </div>
            <p className="text-gray-600">{edu.school}</p>
            {edu.honors && <p className="text-sm text-gray-500 mt-1">{edu.honors}</p>}
          </div>
        ))}
      </SectionWrapper>

      {/* Skills */}
      <SectionWrapper className={isTwoColumn ? 'section-left' : ''}>
        <SectionTitle>Skills</SectionTitle>
        <div className="flex flex-wrap gap-2">
          {userData.skills.map((skill, index) => (
            <span 
              key={index}
              className="px-3 py-1 text-sm rounded-full"
              style={{ backgroundColor: `${colors.secondary}30`, color: colors.primary }}
            >
              {skill}
            </span>
          ))}
        </div>
      </SectionWrapper>

      {/* Certifications */}
      {userData.certifications && userData.certifications.length > 0 && (
        <SectionWrapper className={isTwoColumn ? 'section-right' : ''}>
          <SectionTitle>Certifications</SectionTitle>
          <ul className="space-y-2">
            {userData.certifications.map((cert, index) => (
              <li key={index} className="text-sm">
                <span className="font-medium">{cert.name}</span>
                {cert.issuer && <span className="text-gray-600"> - {cert.issuer}</span>}
                {cert.date && <div className="text-xs text-gray-500">{cert.date}</div>}
              </li>
            ))}
          </ul>
        </SectionWrapper>
      )}

      {/* Projects */}
      {userData.projects && userData.projects.length > 0 && (
        <SectionWrapper className={`${isTwoColumn ? 'col-span-2' : ''}`}>
          <SectionTitle>Projects</SectionTitle>
          <div className="space-y-4">
            {userData.projects.map((project, index) => (
              <div key={index}>
                <h4 className="font-medium">{project.name}</h4>
                <p className="text-sm text-gray-700">{project.description}</p>
                {project.technologies && (
                  <div className="flex flex-wrap gap-1 mt-1">
                    {project.technologies.split(',').map((tech: string, i: number) => (
                      <span key={i} className="text-xs px-2 py-0.5 bg-gray-100 rounded">
                        {tech.trim()}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </SectionWrapper>
      )}
    </div>
  );
};

export default Professional;
