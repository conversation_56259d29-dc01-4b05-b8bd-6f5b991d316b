import Professional from './Professional';
import Minimalist from './Minimalist';
import Creative from './Creative';
import ModernProfessional from './ModernProfessional';
import ColoredSections from './ColoredSections';
import ModernProfessional2 from './ModernProfessional2';
import ModernVisual from './ModernVisual';
import { Template } from '../../types';

const generalTemplates: Template[] = [
  {
    id: 'professional',
    name: 'Professional',
    industry: 'general',
    description: 'A clean, professional resume layout with a modern touch',
    component: Professional,
    layout: 'single-column',
    previewImage: '/templates/professional-preview.png'
  },
  {
    id: 'minimalist',
    name: 'Minimalist',
    industry: 'general',
    description: 'Clean, simple design with a focus on content and readability',
    component: Minimalist,
    layout: 'single-column',
    previewImage: '/templates/minimalist-preview.png'
  },
  {
    id: 'creative',
    name: 'Creative',
    industry: 'general',
    description: 'A modern and creative layout that stands out',
    component: Creative,
    layout: 'single-column',
    previewImage: '/templates/creative-preview.png'
  },
  {
    id: 'modern-professional-single',
    name: 'Modern Professional (Single Column)',
    industry: 'general',
    description: 'A contemporary design with a professional layout',
    layout: 'single-column',
    component: ModernProfessional
  },
  {
    id: 'modern-professional-two-col',
    name: 'Modern Professional (Two Column)',
    industry: 'general',
    description: 'Contemporary two-column layout with sidebar for skills and contact information',
    layout: 'two-column',
    component: ModernProfessional
  },
  {
    id: 'colored-sections',
    name: 'Colored Sections',
    industry: 'general',
    description: 'Elegant design with colored section headers for visual appeal',
    layout: 'single-column',
    component: ColoredSections
  },
  {
    id: 'modern-professional-2',
    name: 'Modern Professional (Grid)',
    industry: 'general',
    description: 'Modern, clean grid-based professional resume inspired by Enhancv',
    layout: 'grid',
    component: ModernProfessional2
  },
  {
    id: 'modern-visual',
    name: 'Modern Visual',
    industry: 'general',
    description: 'Modern visual resume with photo and strengths section',
    layout: 'grid',
    component: ModernVisual
  }
];

export default generalTemplates;
