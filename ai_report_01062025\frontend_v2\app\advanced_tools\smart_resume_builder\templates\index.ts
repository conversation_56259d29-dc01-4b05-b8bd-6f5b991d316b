import technologyTemplates from './technology';
import financeTemplates from './finance';
import healthcareTemplates from './healthcare';
import marketingTemplates from './marketing';
import educationTemplates from './education';
import consultingTemplates from './consulting';
import generalTemplates from './general';
import { Template } from '../types';

// Combine all templates from different industries
const allTemplates: Template[] = [
  ...generalTemplates, // Put general templates first
  ...technologyTemplates,
  ...financeTemplates,
  ...healthcareTemplates,
  ...marketingTemplates,
  ...educationTemplates,
  ...consultingTemplates
];

// Get templates by industry
export const getTemplatesByIndustry = (industry: string): Template[] => {
  return allTemplates.filter(template => template.industry === industry);
};

// Get template by ID
export const getTemplateById = (id: string): Template | undefined => {
  return allTemplates.find(template => template.id === id);
};

export default allTemplates;
