import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Briefcase, GraduationCap, Award, TrendingUp, Camera, Globe, Zap, Target, Users } from 'lucide-react';

const CreativeMarketing: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white max-w-4xl mx-auto my-2 rounded-lg overflow-hidden shadow-lg" style={{ fontFamily: 'Montserrat, sans-serif' }}>
      {/* Creative Header */}
      <div className="relative p-4 pb-12" style={{ background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})` }}>
        <div className="text-white">
          <h1 className="text-4xl font-bold mb-2">{userData.name}</h1>
          <h2 className="text-xl opacity-90 mb-4">{userData.title}</h2>
          <p className="text-white text-opacity-90 mb-6 max-w-xl leading-relaxed">{userData.summary}</p>
          
          <div className="flex flex-wrap gap-6 text-sm">
            <span className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              {userData.email}
            </span>
            <span className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              {userData.phone}
            </span>
            <span className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              {userData.location}
            </span>
            {userData.portfolioUrl && (
              <span className="flex items-center gap-2">
                <Globe className="w-4 h-4" />
                {userData.portfolioUrl}
              </span>
            )}
          </div>
        </div>
        
        <div className="absolute -bottom-5 right-8">
          <div className="flex gap-2">
            {['#marketing', '#creative', '#digital', '#strategy'].map((tag, i) => (
              <span 
                key={i} 
                className="px-3 py-1 text-xs rounded-full shadow-md"
                style={{ backgroundColor: 'white', color: colors.primary }}
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      </div>

      <div className="p-10 pb-12 pt-12">
        {/* Marketing Experience with Creative Layout */}
        <div className="mb-8">
          <h3 className="text-xl font-semibold mb-6 flex items-center gap-2" style={{ color: colors.primary }}>
            <Briefcase className="w-5 h-5" />
            Marketing Experience
          </h3>
          
          <div className="space-y-6">
            {userData.experience.map((exp, index) => (
              <div key={index} className="relative pl-8 border-l-2" style={{ borderColor: colors.secondary }}>
                <div className="absolute -left-2.5 top-0">
                  <div className="w-5 h-5 rounded-full" style={{ backgroundColor: colors.primary }}></div>
                </div>
                
                <div className="mb-2">
                  <div className="flex justify-between items-start">
                    <h4 className="font-bold text-lg">{exp.role}</h4>
                    <span 
                      className="text-sm px-3 py-1 rounded-full"
                      style={{ backgroundColor: colors.secondary, color: colors.text }}
                    >
                      {exp.start} - {exp.end}
                    </span>
                  </div>
                  <p className="text-gray-600 font-medium">{exp.company}</p>
                </div>
                
                <p className="text-gray-700 mb-3">{exp.description}</p>
                
                {exp.achievements && exp.achievements.length > 0 && (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="font-medium mb-2" style={{ color: colors.primary }}>Campaign Highlights:</p>
                    <ul className="space-y-2">
                      {exp.achievements.map((achievement, i) => (
                        <li key={i} className="flex items-start gap-2">
                          <Zap className="w-5 h-5 mt-0.5 flex-shrink-0" style={{ color: colors.primary }} />
                          <span className="text-sm">{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Skills with Visual Design */}
            <div>
              <h3 className="text-xl font-semibold mb-4 flex items-center gap-2" style={{ color: colors.primary }}>
                <Target className="w-5 h-5" />
                Marketing Expertise
              </h3>
              
              <div className="grid grid-cols-2 gap-3">
                {userData.skills.map((skill, index) => (
                  <div 
                    key={index} 
                    className="p-3 rounded-lg text-center"
                    style={{ backgroundColor: index % 2 === 0 ? colors.secondary : colors.primary }}
                  >
                    <span 
                      className="text-sm font-medium"
                      style={{ color: index % 2 === 0 ? colors.text : 'white' }}
                    >
                      {skill}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Education */}
            <div>
              <h3 className="text-xl font-semibold mb-4 flex items-center gap-2" style={{ color: colors.primary }}>
                <GraduationCap className="w-5 h-5" />
                Education
              </h3>
              
              {userData.education.map((edu, index) => (
                <div key={index} className="mb-4 p-4 rounded-lg border" style={{ borderColor: colors.secondary }}>
                  <h4 className="font-semibold">{edu.degree}</h4>
                  <p className="text-gray-600">{edu.school}</p>
                  {edu.field && <p className="text-sm text-gray-500">{edu.field}</p>}
                  <p className="text-gray-500 text-sm">{edu.start} - {edu.end}</p>
                  {edu.description && <p className="text-sm italic mt-1" style={{ color: colors.primary }}>{edu.description}</p>}
                </div>
              ))}
            </div>

            {/* Languages */}
            {userData.languages && userData.languages.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2" style={{ color: colors.primary }}>
                  <Globe className="w-5 h-5" />
                  Languages
                </h3>
                
                <div className="flex flex-wrap gap-2">
                  {userData.languages.map((lang, index) => (
                    <div 
                      key={index} 
                      className="px-4 py-2 rounded-full text-sm"
                      style={{ backgroundColor: colors.secondary, color: colors.text }}
                    >
                      {lang.name} ({lang.proficiency})
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Portfolio Projects */}
            {userData.projects && userData.projects.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2" style={{ color: colors.primary }}>
                  <Camera className="w-5 h-5" />
                  Portfolio Highlights
                </h3>
                
                {userData.projects.map((project, index) => (
                  <div 
                    key={index} 
                    className="mb-4 p-4 rounded-lg"
                    style={{ backgroundColor: colors.secondary }}
                  >
                    <h4 className="font-semibold mb-2">{project.name}</h4>
                    <p className="text-sm mb-3">{project.description}</p>
                    
                    <div className="flex flex-wrap gap-1 mb-2">
                      {project.technologies.split(', ').map((tech, i) => (
                        <span 
                          key={i} 
                          className="px-2 py-1 text-xs rounded-full"
                          style={{ backgroundColor: 'white', color: colors.primary }}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                    
                    {project.url && (
                      <a 
                        href={project.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs flex items-center gap-1 mt-2"
                        style={{ color: colors.primary }}
                      >
                        <Globe className="w-3 h-3" />
                        View Project
                      </a>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Certifications */}
            {userData.certifications && userData.certifications.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2" style={{ color: colors.primary }}>
                  <Award className="w-5 h-5" />
                  Certifications
                </h3>
                
                <div className="space-y-3">
                  {userData.certifications.map((cert, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div 
                        className="w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0"
                        style={{ backgroundColor: colors.primary }}
                      >
                        <Award className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="font-medium">{cert.name}</p>
                        <p className="text-gray-600 text-xs">{cert.issuer}, {cert.date}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Interests - Creative Presentation */}
            {userData.interests && userData.interests.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2" style={{ color: colors.primary }}>
                  <Users className="w-5 h-5" />
                  Creative Interests
                </h3>
                
                <div className="flex flex-wrap gap-2">
                  {userData.interests.map((interest, index) => (
                    <span 
                      key={index} 
                      className="px-3 py-2 text-sm rounded-lg"
                      style={{ 
                        backgroundColor: index % 2 === 0 ? colors.primary : colors.secondary,
                        color: index % 2 === 0 ? 'white' : colors.text
                      }}
                    >
                      {interest}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreativeMarketing;
