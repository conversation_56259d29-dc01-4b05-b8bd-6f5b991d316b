import React from 'react';
import { TemplateProps } from '../../types';
import { Code, Terminal, Github, Globe, Mail, MapPin, Phone, Database, Server, Cpu } from 'lucide-react';

const DevFocused: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white max-w-4xl mx-auto my-12 p-10 rounded-lg shadow-lg" style={{ fontFamily: 'Fira Code, monospace' }}>
      {/* Header styled like a code editor */}
      <div className="bg-gray-800 text-white p-8 rounded-t-lg">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span className="ml-2 text-xs text-gray-400">resume.jsx</span>
        </div>
        
        <div className="mb-4">
          <span className="text-purple-400">const</span> <span className="text-yellow-300">developer</span> <span className="text-white">=</span> <span className="text-yellow-300">{'{'}</span>
          <div className="ml-4">
            <span className="text-green-300">name:</span> <span className="text-orange-300">"{userData.name}"</span>,
            <br />
            <span className="text-green-300">role:</span> <span className="text-orange-300">"{userData.title}"</span>,
            <br />
            <span className="text-green-300">contact:</span> <span className="text-yellow-300">{'{'}</span>
            <div className="ml-4">
              <span className="text-green-300">email:</span> <span className="text-orange-300">"{userData.email}"</span>,
              <br />
              <span className="text-green-300">phone:</span> <span className="text-orange-300">"{userData.phone}"</span>,
              <br />
              <span className="text-green-300">location:</span> <span className="text-orange-300">"{userData.location}"</span>,
              {userData.github && (
                <>
                  <br />
                  <span className="text-green-300">github:</span> <span className="text-orange-300">"{userData.github}"</span>,
                </>
              )}
              {userData.website && (
                <>
                  <br />
                  <span className="text-green-300">website:</span> <span className="text-orange-300">"{userData.website}"</span>,
                </>
              )}
            </div>
            <span className="text-yellow-300">{'}'}</span>
          </div>
          <span className="text-yellow-300">{'}'}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white p-6 rounded-b-lg shadow-lg">
        {/* Summary */}
        <div className="mb-6 p-4 border-l-4 rounded" style={{ borderColor: colors.primary, backgroundColor: colors.secondary }}>
          <h3 className="text-lg font-semibold mb-2" style={{ color: colors.primary }}>
            <span className="flex items-center gap-2">
              <Terminal className="w-5 h-5" />
              // ABOUT
            </span>
          </h3>
          <p className="text-gray-700 leading-relaxed">{userData.summary}</p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="md:col-span-2 space-y-6">
            {/* Technical Skills */}
            <div>
              <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
                <span className="flex items-center gap-2">
                  <Code className="w-5 h-5" />
                  // SKILLS
                </span>
              </h3>
              <div className="grid grid-cols-2 gap-2">
                {userData.skills.map((skill, index) => (
                  <div 
                    key={index} 
                    className="flex items-center gap-2 p-2 rounded"
                    style={{ backgroundColor: colors.secondary }}
                  >
                    <div className="w-2 h-2 rounded-full" style={{ backgroundColor: colors.primary }}></div>
                    <span className="text-sm">{skill}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Experience */}
            <div>
              <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
                <span className="flex items-center gap-2">
                  <Server className="w-5 h-5" />
                  // EXPERIENCE
                </span>
              </h3>
              {userData.experience.map((exp, index) => (
                <div key={index} className="mb-4 p-3 border-l-2" style={{ borderColor: colors.primary }}>
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-semibold">{exp.title}</h4>
                      <p className="text-gray-600">{exp.company}</p>
                    </div>
                    <span 
                      className="text-xs px-2 py-1 rounded"
                      style={{ backgroundColor: colors.secondary, color: colors.text }}
                    >
                      {exp.duration}
                    </span>
                  </div>
                  <p className="text-gray-700 text-sm mb-2">{exp.description}</p>
                  
                  {exp.achievements && exp.achievements.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium mb-1">Key Achievements:</p>
                      <ul className="list-none text-sm text-gray-600">
                        {exp.achievements.map((achievement, i) => (
                          <li key={i} className="flex items-start gap-2 mb-1">
                            <span className="text-green-500 mt-1">✓</span>
                            <span>{achievement}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Projects */}
            {userData.projects && userData.projects.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
                  <span className="flex items-center gap-2">
                    <Github className="w-5 h-5" />
                    // PROJECTS
                  </span>
                </h3>
                
                {userData.projects.map((project, index) => (
                  <div key={index} className="mb-4 p-4 rounded" style={{ backgroundColor: colors.secondary }}>
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold">{project.name}</h4>
                      {project.url && (
                        <a 
                          href={project.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-xs flex items-center gap-1"
                          style={{ color: colors.primary }}
                        >
                          <Globe className="w-3 h-3" />
                          {project.url}
                        </a>
                      )}
                    </div>
                    <p className="text-gray-700 text-sm mb-2">{project.description}</p>
                    
                    <div className="flex flex-wrap gap-1">
                      {project.technologies.split(', ').map((tech, i) => (
                        <span 
                          key={i} 
                          className="text-xs px-2 py-1 rounded"
                          style={{ backgroundColor: colors.primary, color: 'white' }}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Education */}
            <div>
              <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
                <span className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  // EDUCATION
                </span>
              </h3>
              
              {userData.education.map((edu, index) => (
                <div key={index} className="mb-3 p-3 rounded" style={{ backgroundColor: colors.secondary }}>
                  <h4 className="font-semibold">{edu.degree}</h4>
                  <p className="text-gray-600 text-sm">{edu.school}</p>
                  <p className="text-gray-500 text-xs">{edu.year}</p>
                  {edu.gpa && <p className="text-gray-500 text-xs">GPA: {edu.gpa}</p>}
                  {edu.honors && <p className="text-gray-500 text-xs">{edu.honors}</p>}
                </div>
              ))}
            </div>

            {/* Certifications */}
            {userData.certifications && userData.certifications.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
                  <span className="flex items-center gap-2">
                    <Cpu className="w-5 h-5" />
                    // CERTIFICATIONS
                  </span>
                </h3>
                
                {userData.certifications.map((cert, index) => (
                  <div key={index} className="mb-2">
                    <p className="font-medium text-sm">{cert.name}</p>
                    <p className="text-gray-600 text-xs">{cert.issuer}, {cert.date}</p>
                    {cert.expiration && <p className="text-gray-500 text-xs">Expires: {cert.expiration}</p>}
                  </div>
                ))}
              </div>
            )}

            {/* Languages */}
            {userData.languages && userData.languages.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
                  <span className="flex items-center gap-2">
                    <Globe className="w-5 h-5" />
                    // LANGUAGES
                  </span>
                </h3>
                
                {userData.languages.map((lang, index) => (
                  <div key={index} className="flex justify-between items-center mb-2">
                    <span className="text-sm">{lang.name}</span>
                    <span 
                      className="text-xs px-2 py-1 rounded"
                      style={{ backgroundColor: colors.secondary, color: colors.text }}
                    >
                      {lang.proficiency}
                    </span>
                  </div>
                ))}
              </div>
            )}

            {/* Interests */}
            {userData.interests && userData.interests.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
                  <span className="flex items-center gap-2">
                    <Terminal className="w-5 h-5" />
                    // INTERESTS
                  </span>
                </h3>
                
                <div className="flex flex-wrap gap-2">
                  {userData.interests.map((interest, index) => (
                    <span 
                      key={index} 
                      className="px-2 py-1 text-xs rounded"
                      style={{ backgroundColor: colors.secondary, color: colors.text }}
                    >
                      {interest}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DevFocused;
