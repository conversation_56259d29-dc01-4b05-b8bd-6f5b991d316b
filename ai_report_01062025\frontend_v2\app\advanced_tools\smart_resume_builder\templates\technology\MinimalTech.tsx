import React from 'react';
import { TemplateProps } from '../../types';
import { Mail, Phone, MapPin, Github, Globe, Briefcase, GraduationCap, Award, Code } from 'lucide-react';

const MinimalTech: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white max-w-2xl mx-auto my-12 p-10 rounded-lg shadow-lg" style={{ fontFamily: 'Inter, sans-serif' }}>
      {/* Header */}
      <div className="text-center border-b-2 pb-8 mb-8" style={{ borderColor: colors.primary }}>
        <h1 className="text-3xl font-bold mb-2" style={{ color: colors.primary }}>{userData.name}</h1>
        <h2 className="text-xl text-gray-600 mb-4">{userData.title}</h2>
        <div className="flex justify-center items-center gap-4 text-sm flex-wrap">
          <span className="flex items-center gap-1">
            <Mail className="w-4 h-4" />
            {userData.email}
          </span>
          <span className="flex items-center gap-1">
            <Phone className="w-4 h-4" />
            {userData.phone}
          </span>
          <span className="flex items-center gap-1">
            <MapPin className="w-4 h-4" />
            {userData.location}
          </span>
          {userData.github && (
            <span className="flex items-center gap-1">
              <Github className="w-4 h-4" />
              {userData.github}
            </span>
          )}
          {userData.website && (
            <span className="flex items-center gap-1">
              <Globe className="w-4 h-4" />
              {userData.website}
            </span>
          )}
        </div>
      </div>

      {/* Summary */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Professional Summary</h3>
        <p className="text-gray-700 leading-relaxed">{userData.summary}</p>
      </div>

      {/* Technical Skills */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
          <span className="flex items-center gap-2">
            <Code className="w-5 h-5" />
            Technical Skills
          </span>
        </h3>
        <div className="flex flex-wrap gap-2">
          {userData.skills.map((skill, index) => (
            <span 
              key={index} 
              className="px-3 py-1 text-sm rounded"
              style={{ backgroundColor: colors.secondary, color: colors.text }}
            >
              {skill}
            </span>
          ))}
        </div>
      </div>

      {/* Experience */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
          <span className="flex items-center gap-2">
            <Briefcase className="w-5 h-5" />
            Experience
          </span>
        </h3>
        {userData.experience.map((exp, index) => (
          <div key={index} className="mb-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h4 className="font-semibold">{exp.title}</h4>
                <p className="text-gray-600">{exp.company}</p>
              </div>
              <span className="text-sm text-gray-500">{exp.duration}</span>
            </div>
            <p className="text-gray-700 text-sm mb-2">{exp.description}</p>
            
            {exp.achievements && exp.achievements.length > 0 && (
              <ul className="list-disc list-inside text-sm text-gray-600 pl-2">
                {exp.achievements.map((achievement, i) => (
                  <li key={i}>{achievement}</li>
                ))}
              </ul>
            )}
          </div>
        ))}
      </div>

      {/* Education */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
          <span className="flex items-center gap-2">
            <GraduationCap className="w-5 h-5" />
            Education
          </span>
        </h3>
        {userData.education.map((edu, index) => (
          <div key={index} className="mb-2">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold">{edu.degree}</h4>
                <p className="text-gray-600">{edu.school}</p>
              </div>
              <div className="text-right text-sm text-gray-500">
                <div>{edu.year}</div>
                {edu.gpa && <div>GPA: {edu.gpa}</div>}
              </div>
            </div>
            {edu.honors && <p className="text-sm text-gray-600">{edu.honors}</p>}
            {edu.courses && edu.courses.length > 0 && (
              <p className="text-sm text-gray-600 mt-1">
                <span className="font-medium">Relevant Coursework:</span> {edu.courses.join(', ')}
              </p>
            )}
          </div>
        ))}
      </div>

      {/* Projects */}
      {userData.projects && userData.projects.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
            <span className="flex items-center gap-2">
              <Github className="w-5 h-5" />
              Projects
            </span>
          </h3>
          {userData.projects.map((project, index) => (
            <div key={index} className="mb-3">
              <div className="flex justify-between items-start">
                <h4 className="font-semibold">{project.name}</h4>
                {project.url && (
                  <a 
                    href={project.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-xs"
                    style={{ color: colors.primary }}
                  >
                    {project.url}
                  </a>
                )}
              </div>
              <p className="text-gray-700 text-sm mb-1">{project.description}</p>
              <p className="text-gray-500 text-xs">Technologies: {project.technologies}</p>
            </div>
          ))}
        </div>
      )}

      {/* Certifications */}
      {userData.certifications && userData.certifications.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>
            <span className="flex items-center gap-2">
              <Award className="w-5 h-5" />
              Certifications
            </span>
          </h3>
          <div className="grid grid-cols-2 gap-3">
            {userData.certifications.map((cert, index) => (
              <div key={index} className="text-sm">
                <p className="font-medium">{cert.name}</p>
                <p className="text-gray-600">{cert.issuer}, {cert.date}</p>
                {cert.expiration && <p className="text-gray-500 text-xs">Expires: {cert.expiration}</p>}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Additional Sections */}
      <div className="grid grid-cols-2 gap-6">
        {/* Languages */}
        {userData.languages && userData.languages.length > 0 && (
          <div>
            <h3 className="text-md font-semibold mb-2" style={{ color: colors.primary }}>Languages</h3>
            {userData.languages.map((lang, index) => (
              <div key={index} className="flex justify-between text-sm mb-1">
                <span>{lang.name}</span>
                <span className="text-gray-600">{lang.proficiency}</span>
              </div>
            ))}
          </div>
        )}

        {/* Interests */}
        {userData.interests && userData.interests.length > 0 && (
          <div>
            <h3 className="text-md font-semibold mb-2" style={{ color: colors.primary }}>Interests</h3>
            <p className="text-sm text-gray-700">{userData.interests.join(', ')}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MinimalTech;
