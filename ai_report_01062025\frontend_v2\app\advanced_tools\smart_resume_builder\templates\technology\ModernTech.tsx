import React from 'react';
import { TemplateProps } from '../../types';
import { Code, Github, Globe, Mail, MapPin, Phone } from 'lucide-react';
 

const ModernTech: React.FC<TemplateProps> = ({ userData, colors }) => {
  return (
    <div className="bg-white w-full m-0 p-0" style={{ fontFamily: 'Inter, sans-serif' }}>
      {/* Header with tech-inspired design */}
      <div className="flex flex-col md:flex-row gap-4 p-6" style={{ backgroundColor: colors.secondary }}>
        <div className="md:w-2/3">
          <h1 className="text-2xl font-bold mb-1" style={{ color: colors.primary }}>{userData.name}</h1>
          <h2 className="text-lg text-gray-700 mb-1">{userData.title}</h2>
          <p className="text-gray-600 mb-1 leading-relaxed">{userData.summary}</p>
          
          <div className="flex flex-wrap gap-1 text-sm">
            {userData.email && (
              <span className="flex items-center gap-0">
                <Mail className="w-4 h-4" style={{ color: colors.primary }} />
                {userData.email}
              </span>
            )}
            {userData.phone && (
              <span className="flex items-center gap-0">
                <Phone className="w-4 h-4" style={{ color: colors.primary }} />
                {userData.phone}
              </span>
            )}
            {userData.location && (
              <span className="flex items-center gap-0">
                <MapPin className="w-4 h-4" style={{ color: colors.primary }} />
                {userData.location}
              </span>
            )}
            {userData.website && (
              <span className="flex items-center gap-0">
                <Globe className="w-4 h-4" style={{ color: colors.primary }} />
                {userData.website}
              </span>
            )}
            {userData.github && (
              <span className="flex items-center gap-0">
                <Github className="w-4 h-4" style={{ color: colors.primary }} />
                {userData.github}
              </span>
            )}
          </div>
        </div>
        
        <div className="md:w-1/3 flex flex-col md:items-end justify-center space-y-0">
          <div className="p-0 pl-4 rounded-lg" style={{ backgroundColor: 'white', boxShadow: '0 0px 0px rgba(0,0,0,0.0)' }}>
            <h3 className="font-semibold mb-2 pb-1 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
              Technical Skills
            </h3>
            <div className="flex flex-wrap gap-2 mt-2 ml-2">
              {userData.skills.map((skill, index) => (
                <span 
                  key={index} 
                  className="px-3 py-1 text-sm rounded-md"
                  style={{ backgroundColor: colors.primary, color: 'white' }}
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="grid md:grid-cols-3 gap-4 p-4">
        {/* Left column */}
        <div className="md:col-span-2 space-y-4">
          {/* Experience */}
          <div>
            <h3 className="text-lg font-semibold mb-3 pb-1 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
              <span className="flex items-center gap-2">
                <Code className="w-5 h-5" />
                Professional Experience
              </span>
            </h3>
            
            {userData.experience.map((exp, index) => (
              <div key={index} className="mb-4">
                <div className="flex justify-between items-start mb-1">
                  <div>
                    <h4 className="font-semibold">{exp.title}</h4>
                    <p className="text-gray-600">{exp.company}</p>
                  </div>
                  <span className="text-sm px-2 py-1 rounded" style={{ backgroundColor: colors.secondary, color: colors.text }}>
                    {exp.duration}
                  </span>
                </div>
                <p className="text-gray-700 text-sm mb-2">{exp.description}</p>
                
                {exp.achievements && exp.achievements.length > 0 && (
                  <ul className="list-disc list-inside text-sm text-gray-600 pl-4 space-y-1">
                    {exp.achievements.map((achievement, i) => (
                      <li key={i} className="mb-1">{achievement}</li>
                    ))}
                  </ul>
                )}
              </div>
            ))}
          </div>

          {/* Projects */}
          {userData.projects && userData.projects.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-1 pb-1 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
                <span className="icon-title-row">
                  <Github className="w-5 h-5" />
                  Projects
                </span>
              </h3>
              
              <div className="space-y-2 pl-2">
                {userData.projects.map((project, i) => (
                  <div key={i} className="mb-3">
                    <h4 className="font-semibold mb-1">{project.name}</h4>
                    <p className="text-gray-700 text-sm mb-2">{project.description}</p>
                    
                    <div className="flex flex-wrap gap-2 mt-2 ml-2">
                      {project.technologies.split(', ').map((tech, i) => (
                        <span key={i} className="text-xs px-2 py-1 rounded-md" style={{ backgroundColor: colors.primary, color: 'white' }}>
                          {tech}
                        </span>
                      ))}
                    </div>
                    
                    {project.url && (
                      <a 
                        href={project.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-xs flex items-center gap-1"
                        style={{ color: colors.primary }}
                      >
                        <Globe className="w-3 h-3" />
                        {project.url}
                      </a>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right column */}
        <div className="space-y-2">
          {/* Education */}
          <div>
            <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
              Education
            </h3>
            
            {userData.education.map((edu, index) => (
              <div key={index} className="mb-4">
                <h4 className="font-semibold">{edu.degree}</h4>
                <p className="text-gray-600">{edu.school}</p>
                <p className="text-gray-500 text-sm">{edu.year}</p>
                {edu.gpa && <p className="text-gray-500 text-sm">GPA: {edu.gpa}</p>}
                {edu.honors && <p className="text-gray-500 text-sm">{edu.honors}</p>}
                
                {Array.isArray(edu.courses) && edu.courses.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium">Relevant Coursework:</p>
                    <p className="text-xs text-gray-600">{edu.courses.join(', ')}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Certifications */}
          {userData.certifications && userData.certifications.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
                Certifications
              </h3>
              
              {userData.certifications.map((cert, index) => (
                <div key={index} className="mb-5">
                  <h4 className="font-semibold text-sm">{cert.name}</h4>
                  <p className="text-gray-600 text-sm">{cert.issuer}</p>
                  <p className="text-gray-500 text-xs">
                    {cert.date}{cert.expiration ? ` - Expires: ${cert.expiration}` : ''}
                  </p>
                </div>
              ))}
            </div>
          )}

          {/* Languages */}
          {userData.languages && userData.languages.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
                Languages
              </h3>
              
              {userData.languages.map((lang, index) => (
                <div key={index} className="flex justify-between items-center mb-2">
                  <span className="text-sm">{lang.name}</span>
                  <span className="text-xs px-2 py-1 rounded" style={{ backgroundColor: colors.secondary, color: colors.text }}>
                    {lang.proficiency}
                  </span>
                </div>
              ))}
            </div>
          )}

          {/* Interests */}
          {userData.interests && userData.interests.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4 pb-2 border-b" style={{ color: colors.primary, borderColor: colors.secondary }}>
                Interests
              </h3>
              
              <div className="flex flex-wrap gap-2">
                {userData.interests.map((interest, index) => (
                  <span 
                    key={index} 
                    className="px-3 py-1 text-sm rounded-full"
                    style={{ backgroundColor: colors.secondary, color: colors.text }}
                  >
                    {interest}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernTech;
