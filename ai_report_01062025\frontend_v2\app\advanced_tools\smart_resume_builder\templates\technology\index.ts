import ModernTech from './ModernTech';
import MinimalTech from './MinimalTech';
import DevFocused from './DevFocused';
import { Template } from '../../types';

const technologyTemplates: Template[] = [
  {
    id: 'modern-tech',
    name: 'Modern Tech',
    industry: 'technology',
    description: 'Clean, modern design with a focus on skills and projects',
    layout: 'two-column',
    component: ModernTech
  },
  {
    id: 'minimal-tech',
    name: 'Minimal Tech',
    industry: 'technology',
    description: 'Simple, ATS-friendly design perfect for software engineers',
    layout: 'single-column',
    component: MinimalTech
  },
  {
    id: 'dev-focused',
    name: 'Developer Focused',
    industry: 'technology',
    description: 'Code-inspired design showcasing technical expertise',
    layout: 'creative',
    component: DevFocused
  }
];

export default technologyTemplates;
