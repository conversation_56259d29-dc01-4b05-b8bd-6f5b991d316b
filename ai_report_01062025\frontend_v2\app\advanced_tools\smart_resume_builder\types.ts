// Resume data types for the smart resume builder

export type TemplateLayout = 'single-column' | 'two-column' | 'creative' | 'traditional';

export interface Experience {
  company: string;
  role: string;
  start: string;
  end: string;
  description: string;
  achievements?: string[];
  location?: string;
}

export interface Education {
  school: string;
  degree: string;
  field: string;
  start: string;
  end: string;
  description?: string;
}

export interface Project {
  name: string;
  description: string;
  technologies: string;
  url?: string;
  role?: string;
  duration?: string;
  achievements?: string[];
}

export interface Certification {
  name: string;
  issuer: string;
  date: string;
  url?: string;
  expiration?: string;
}

export interface Publication {
  title: string;
  publisher: string;
  date: string;
  url?: string;
  description?: string;
  coAuthors?: string[];
}

export interface Language {
  name: string;
  proficiency: string;
}

export interface Award {
  title: string;
  issuer: string;
  date: string;
  description?: string;
}

export interface Volunteer {
  organization: string;
  role: string;
  duration: string;
  description?: string;
}

export interface Reference {
  name: string;
  title: string;
  company: string;
  contact: string;
  relationship?: string;
}

export interface Patent {
  title: string;
  number: string;
  date: string;
  description?: string;
  coInventors?: string[];
}

export interface UserData {
  name: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  website?: string;
  linkedin?: string;
  github?: string;
  portfolioUrl?: string;
  summary: string;
  experience: Experience[];
  education: Education[];
  skills: string[];
  projects: Project[];
  certifications?: Certification[];
  publications?: Publication[];
  languages?: Language[];
  awards?: Award[];
  volunteer?: Volunteer[];
  references?: Reference[];
  patents?: Patent[];
  interests?: string[];
  additionalSections?: Record<string, any>;
}

export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  text: string;
  background?: string;
  muted?: string;
}

export interface Industry {
  id: string;
  name: string;
  icon: string;
  description: string;
  color: string;
  emphasis: string[];
  recommendedSections: string[];
}

export interface Template {
  id: string;
  name: string;
  industry: string;
  description: string;
  layout: string;
  previewImage?: string;
  component: React.ComponentType<TemplateProps>;
}

export interface TemplateProps {
  userData: UserData;
  colors: ColorScheme;
}

export interface ResumeSection {
  id: string;
  title: string;
  required: boolean;
  industryRelevance: Record<string, number>;
  component: React.ComponentType<any>;
}
