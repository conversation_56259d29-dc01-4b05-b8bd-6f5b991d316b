import { ColorScheme, Industry, UserData } from './types';

// Default user data for resume templates
export const defaultUserData: UserData = {
  name: '<PERSON>',
  title: 'Software Engineer',
  email: '<EMAIL>',
  phone: '(*************',
  location: 'San Francisco, CA',
  website: 'www.johnsmith.dev',
  linkedin: 'linkedin.com/in/johnsmith',
  github: 'github.com/johnsmith',
  summary: 'Passionate software engineer with 3+ years of experience in full-stack development. Skilled in React, Node.js, and cloud technologies.',
  experience: [
    {
      role: 'Software Engineer',
      company: 'TechCorp Inc.',
      start: '2022',
      end: 'Present',
      description: 'Developed scalable web applications using React and Node.js, improving user engagement by 40%.',
      location: 'San Francisco, CA'
    },
    {
      role: 'Junior Developer',
      company: 'StartupXYZ',
      start: '2021',
      end: '2022',
      description: 'Built responsive websites and collaborated with cross-functional teams to deliver high-quality software.',
      location: 'San Jose, CA'
    }
  ],
  education: [
    {
      school: 'University of Technology',
      degree: 'Bachelor of Science in Computer Science',
      field: 'Computer Science',
      start: '2017',
      end: '2021',
      description: 'GPA: 3.8/4.0, Magna Cum Laude. Coursework: Data Structures, Algorithms, Web Development, Database Systems'
    }
  ],
  skills: ['JavaScript', 'React', 'Node.js', 'Python', 'SQL', 'AWS', 'Git', 'Docker'],
  strengths: [
    { name: 'Problem Solving', level: 4 },
    { name: 'Communication', level: 3 },
    { name: 'Teamwork', level: 5 },
    { name: 'Adaptability', level: 4 },
    { name: 'Leadership', level: 3 }
  ],
  projects: [
    {
      name: 'E-commerce Platform',
      description: 'Full-stack web application with payment integration',
      technologies: 'React, Node.js, MongoDB, Stripe',
      url: 'github.com/johnsmith/ecommerce'
    }
  ],
  certifications: [
    {
      name: 'AWS Certified Solutions Architect',
      issuer: 'Amazon Web Services',
      date: '2022',
      expiration: '2025'
    }
  ],
  languages: [
    {
      name: 'English',
      proficiency: 'Native'
    },
    {
      name: 'Spanish',
      proficiency: 'Intermediate'
    }
  ],
  interests: ['Open Source Development', 'Machine Learning', 'Hiking', 'Photography']
};

// Color schemes for different resume styles
export const colorSchemes: Record<string, ColorScheme> = {
  blue: { primary: '#2563eb', secondary: '#dbeafe', accent: '#1d4ed8', text: '#1e40af' },
  green: { primary: '#059669', secondary: '#d1fae5', accent: '#047857', text: '#065f46' },
  purple: { primary: '#7c3aed', secondary: '#ede9fe', accent: '#6d28d9', text: '#5b21b6' },
  orange: { primary: '#ea580c', secondary: '#fed7aa', accent: '#c2410c', text: '#9a3412' },
  gray: { primary: '#4b5563', secondary: '#f3f4f6', accent: '#374151', text: '#1f2937' },
  teal: { primary: '#0d9488', secondary: '#ccfbf1', accent: '#0f766e', text: '#115e59' },
  red: { primary: '#dc2626', secondary: '#fee2e2', accent: '#b91c1c', text: '#991b1b' },
  indigo: { primary: '#4f46e5', secondary: '#e0e7ff', accent: '#4338ca', text: '#3730a3' },
  pink: { primary: '#db2777', secondary: '#fce7f3', accent: '#be185d', text: '#9d174d' },
  amber: { primary: '#d97706', secondary: '#fef3c7', accent: '#b45309', text: '#92400e' }
};

// Industry definitions with emphasis areas and recommended sections
export const industries: Record<string, Industry> = {
  'general': {
    id: 'general',
    name: 'General Purpose',
    icon: '📄',
    description: 'Universal templates suitable for any profession',
    color: 'indigo',
    emphasis: ['Experience', 'Education', 'Skills', 'Strengths', 'Projects'],
    recommendedSections: ['personal', 'summary', 'experience', 'education', 'skills', 'strengths', 'projects', 'certifications', 'languages', 'interests']
  },
  'technology': {
    id: 'technology',
    name: 'Technology & Software',
    icon: '💻',
    description: 'Software engineering, data science, cybersecurity, DevOps',
    color: 'blue',
    emphasis: ['Technical Skills', 'Projects', 'Certifications', 'GitHub/Portfolio'],
    recommendedSections: ['experience', 'skills', 'projects', 'education', 'certifications', 'github']
  },
  'finance': {
    id: 'finance',
    name: 'Finance & Banking',
    icon: '💰',
    description: 'Investment banking, financial analysis, accounting, consulting',
    color: 'green',
    emphasis: ['Quantitative Skills', 'Achievements', 'Education', 'Certifications'],
    recommendedSections: ['experience', 'education', 'certifications', 'skills', 'achievements']
  },
  'healthcare': {
    id: 'healthcare',
    name: 'Healthcare & Medical',
    icon: '🏥',
    description: 'Medical professionals, healthcare administration, research',
    color: 'teal',
    emphasis: ['Licenses', 'Clinical Experience', 'Research', 'Publications'],
    recommendedSections: ['experience', 'education', 'certifications', 'licenses', 'publications', 'research']
  },
  'marketing': {
    id: 'marketing',
    name: 'Marketing & Creative',
    icon: '🎨',
    description: 'Digital marketing, content creation, brand management',
    color: 'orange',
    emphasis: ['Portfolio', 'Campaigns', 'Creative Skills', 'Results/ROI'],
    recommendedSections: ['experience', 'skills', 'portfolio', 'achievements', 'education']
  },
  'education': {
    id: 'education',
    name: 'Education & Teaching',
    icon: '📚',
    description: 'Teaching, administration, curriculum development',
    color: 'purple',
    emphasis: ['Teaching Experience', 'Certifications', 'Education', 'Publications'],
    recommendedSections: ['experience', 'education', 'certifications', 'publications', 'skills']
  },
  'consulting': {
    id: 'consulting',
    name: 'Consulting & Strategy',
    icon: '📊',
    description: 'Management consulting, business analysis, strategy',
    color: 'indigo',
    emphasis: ['Problem Solving', 'Leadership', 'Case Studies', 'Education'],
    recommendedSections: ['experience', 'education', 'skills', 'achievements', 'projects']
  }
};

// Helper function to generate sample data for specific industries
export const generateIndustrySpecificData = (industry: string): Partial<UserData> => {
  switch (industry) {
    case 'technology':
      return {
        title: 'Senior Software Engineer',
        skills: ['JavaScript', 'React', 'Node.js', 'Python', 'AWS', 'Docker', 'Kubernetes', 'GraphQL', 'CI/CD', 'Microservices'],
        certifications: [
          {
            name: 'AWS Certified Solutions Architect',
            issuer: 'Amazon Web Services',
            date: '2022',
            expiration: '2025'
          },
          {
            name: 'Google Cloud Professional Developer',
            issuer: 'Google',
            date: '2021',
            expiration: '2024'
          }
        ],
        github: 'github.com/johnsmith',
        portfolioUrl: 'johnsmith.dev/portfolio'
      };
    case 'finance':
      return {
        title: 'Financial Analyst',
        skills: ['Financial Modeling', 'Valuation', 'Excel', 'Bloomberg Terminal', 'Financial Reporting', 'Risk Analysis', 'Python', 'SQL'],
        certifications: [
          {
            name: 'Chartered Financial Analyst (CFA)',
            issuer: 'CFA Institute',
            date: '2022'
          },
          {
            name: 'Financial Risk Manager (FRM)',
            issuer: 'GARP',
            date: '2021'
          }
        ]
      };
    case 'healthcare':
      return {
        title: 'Registered Nurse',
        skills: ['Patient Care', 'Medical Records', 'Clinical Procedures', 'Emergency Response', 'Patient Education', 'Medical Equipment'],
        certifications: [
          {
            name: 'Registered Nurse License',
            issuer: 'State Nursing Board',
            date: '2020',
            expiration: '2024'
          },
          {
            name: 'Basic Life Support (BLS)',
            issuer: 'American Heart Association',
            date: '2022',
            expiration: '2024'
          }
        ],
        publications: [
          {
            title: 'Improving Patient Outcomes Through Standardized Care Protocols',
            publisher: 'Journal of Nursing Practice',
            date: '2022',
            description: 'Research study on the effectiveness of standardized care protocols in improving patient outcomes'
          }
        ]
      };
    case 'marketing':
      return {
        title: 'Digital Marketing Manager',
        skills: ['SEO/SEM', 'Social Media Marketing', 'Content Strategy', 'Google Analytics', 'Email Marketing', 'A/B Testing', 'Adobe Creative Suite'],
        projects: [
          {
            name: 'Brand Redesign Campaign',
            description: 'Led company rebrand resulting in 45% increase in brand recognition',
            technologies: 'Adobe Creative Suite, Social Media Platforms, Google Analytics',
            achievements: ['Increased social media engagement by 78%', 'Grew email list by 5,000 subscribers']
          }
        ],
        portfolioUrl: 'johnsmith.com/marketing-portfolio'
      };
    case 'education':
      return {
        title: 'High School Science Teacher',
        skills: ['Curriculum Development', 'Classroom Management', 'Student Assessment', 'Educational Technology', 'Differentiated Instruction'],
        certifications: [
          {
            name: 'Teaching License',
            issuer: 'State Board of Education',
            date: '2019',
            expiration: '2024'
          },
          {
            name: 'Google Certified Educator',
            issuer: 'Google',
            date: '2021'
          }
        ],
        publications: [
          {
            title: 'Engaging Students Through Project-Based Learning',
            publisher: 'Journal of Education',
            date: '2022',
            description: 'Research on effective project-based learning strategies in science education'
          }
        ]
      };
    case 'consulting':
      return {
        title: 'Management Consultant',
        skills: ['Business Strategy', 'Process Optimization', 'Data Analysis', 'Project Management', 'Client Relationship Management', 'PowerPoint', 'Excel'],
        projects: [
          {
            name: 'Supply Chain Optimization',
            description: 'Reduced operational costs by 25% through supply chain redesign',
            technologies: 'Excel, PowerBI, Process Mapping',
            achievements: ['$2.3M annual cost savings', 'Reduced delivery times by 30%']
          }
        ],
        education: [
          {
            school: 'Harvard Business School',
            degree: 'Master of Business Administration (MBA)',
            field: 'Business Administration',
            start: '2018',
            end: '2020',
            description: 'GPA: 3.9/4.0'
          }
        ]
      };
    default:
      return {};
  }
};

// Helper function to merge default data with industry-specific data
export const getIndustryUserData = (industry: string): UserData => {
  const industryData = generateIndustrySpecificData(industry);
  return {
    ...defaultUserData,
    ...industryData
  };
};

// Helper function to get recommended sections for an industry
export const getRecommendedSections = (industry: string): string[] => {
  return industries[industry]?.recommendedSections || [];
};

// Helper function to get color scheme based on industry
export const getIndustryColorScheme = (industry: string): string => {
  return industries[industry]?.color || 'blue';
};
