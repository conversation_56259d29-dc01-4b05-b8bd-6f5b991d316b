import NextAuth, { NextAuthOptions } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";

export const authOptions: NextAuthOptions = {
  debug: true, // Enable debug logs
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req) {
        console.log("🔑 Starting authentication process...");
        console.log("📧 Email being used:", credentials?.email);

        if (!credentials?.email || !credentials?.password) {
          console.error("❌ Missing credentials");
          throw new Error("Missing credentials");
        }

        try {
          const loginUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/auth/login`;
          console.log("🌐 Attempting login at:", loginUrl);

          const formData = new URLSearchParams({
            username: credentials.email,
            password: credentials.password,
          });

          console.log("📦 Request payload:", {
            url: loginUrl,
            method: "POST",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              "Accept": "application/json",
            },
            body: formData.toString()
          });

          const response = await fetch(loginUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              "Accept": "application/json",
            },
            body: formData,
          });

          console.log("📥 Login response status:", response.status);
          const responseText = await response.text();
          console.log("📄 Raw response:", responseText);

          if (!response.ok) {
            console.error("❌ Authentication failed:", responseText);
            throw new Error(responseText);
          }

          let authResponse;
          try {
            authResponse = JSON.parse(responseText);
            console.log("🔓 Auth response parsed:", authResponse);
          } catch (e) {
            console.error("❌ Failed to parse auth response:", e);
            throw new Error("Invalid response format from server");
          }

          if (!authResponse.access_token) {
            console.error("❌ No access token in response");
            throw new Error("No access token received");
          }

          // Decode JWT to extract role
          let role = 'candidate';
          try {
            const payload = JSON.parse(Buffer.from(authResponse.access_token.split('.')[1], 'base64').toString());
            role = payload.role || 'candidate';
          } catch (e) {
            console.warn('Could not decode JWT for role, defaulting to candidate', e);
          }

          // For now, return a basic user object with the token and role
          return {
            id: credentials.email,
            email: credentials.email,
            name: credentials.email.split('@')[0],
            access_token: authResponse.access_token,
            role,
          };

        } catch (error) {
          console.error("❌ Authorization error:", error);
          if (error instanceof Error) {
            try {
              // Try to parse the error message as JSON
              const errorData = JSON.parse(error.message);
              throw new Error(errorData.detail || error.message);
            } catch (e) {
              // If parsing fails, use the original error message
              throw error;
            }
          }
          throw error;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      console.log("🔄 JWT Callback - Input:", { 
        token: { ...token, access_token: token.access_token ? '[REDACTED]' : undefined },
        user: user ? { ...user, access_token: '[REDACTED]' } : undefined 
      });

      if (user) {
        // Store the raw token exactly as received from the backend
        token.access_token = user.access_token;
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.role = user.role;
        
        // Log the first few characters of the token for debugging
        if (user.access_token) {
          console.log("Raw token first 20 chars:", user.access_token.substring(0, 20));
        }
      }

      console.log("🔄 JWT Callback - Output:", { 
        ...token, 
        access_token: '[REDACTED]' 
      });
      return token;
    },
    async session({ session, token }) {
      console.log("🔄 Session Callback - Input:", {
        session: { ...session, user: { ...session.user, access_token: undefined } },
        token: { ...token, access_token: '[REDACTED]' }
      });

      if (token) {
        session.user.id = token.id;
        session.user.access_token = token.access_token;
        session.user.email = token.email;
        session.user.name = token.name;
        session.user.role = token.role;
      }

      console.log("🔄 Session Callback - Output:", {
        ...session,
        user: { ...session.user, access_token: '[REDACTED]' }
      });
      return session;
    },
  },
  pages: {
    signIn: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST }; 