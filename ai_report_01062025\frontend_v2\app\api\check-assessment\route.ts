import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';

export async function GET(request: NextRequest) {
  try {
    // Get email from query params
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return NextResponse.json(
        { error: 'Email parameter is required' },
        { status: 400 }
      );
    }

    // Get session and token
    const session = await getServerSession(authOptions);
    const accessToken = session?.user?.access_token;
    if (!accessToken) {
      return NextResponse.json(
        { error: 'Unauthorized: No access token' },
        { status: 401 }
      );
    }

    // Call the backend API to check if assessment exists
    const backendUrl = 'http://localhost:8000/api/v1/assessment?force_new=false';
    try {
      const response = await fetch(backendUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bear<PERSON> ${accessToken}`
        },
        body: JSON.stringify({ email }),
      });
      
      // If we get a successful response, the assessment exists
      if (response.ok) {
        return NextResponse.json({ 
          exists: true,
          email: email
        });
      } else {
        // Check if it's a 404 (assessment not found) or another error
        if (response.status === 404) {
          return NextResponse.json({ exists: false });
        } else {
          // For other errors, log but still return exists: false
          const errorText = await response.text();
          console.error('Backend API error:', errorText);
          return NextResponse.json({ exists: false });
        }
      }
    } catch (error) {
      console.error('API request error:', error);
      return NextResponse.json({ exists: false });
    }
  } catch (error) {
    console.error('General error:', error);
    return NextResponse.json({ exists: false });
  }
} 