import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';

export async function POST(request: NextRequest) {
  console.log('🚀 PDF generation API called');
  
  try {
    // Get the request data
    const body = await request.json();
    const { url, filename = 'resume.pdf', cookies = [] } = body;
    
    if (!url) {
      console.error('❌ No URL provided for PDF generation');
      return NextResponse.json(
        { error: 'No URL provided for PDF generation' },
        { status: 400 }
      );
    }
    
    // Get authentication cookies from the request
    const requestCookies = request.cookies.getAll();
    console.log('🍪 Received cookies:', requestCookies.map(c => c.name));
    
    // Extract auth cookies if none were provided in the request body
    const authCookies = cookies.length > 0 ? cookies : requestCookies.map(cookie => ({
      name: cookie.name,
      value: cookie.value,
      domain: new URL(url).hostname,
      path: '/',
    }));
    
    console.log('📄 Generating PDF for URL:', url);
    
    // Launch browser with more debug options
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox', 
        '--disable-setuid-sandbox',
        '--disable-web-security', // Disable CORS
        '--disable-features=IsolateOrigins,site-per-process', // Disable site isolation
        '--ignore-certificate-errors' // Ignore HTTPS errors
      ]
    });
    
    const page = await browser.newPage();
    
    // Monitor console messages
    page.on('console', msg => console.log(`🖥️ Page console ${msg.type()}: ${msg.text()}`));
    page.on('pageerror', err => console.error(`❌ Page error: ${err.message}`));
    page.on('requestfailed', request => console.error(`❌ Request failed: ${request.url()}`));
    
    // Set viewport for consistent rendering
    await page.setViewport({ width: 1200, height: 800 });
    
    // Set cookies for authentication
    if (authCookies.length > 0) {
      console.log('🍪 Setting authentication cookies...');
      await page.setCookie(...authCookies);
    }
    
    // Navigate to the page
    console.log('🔎 Navigating to URL:', url);
    await page.goto(url, { 
      waitUntil: 'networkidle0', // Wait until no network requests for 500ms
      timeout: 30000 
    });
    
    // Check if we landed on the login page
    const pageTitle = await page.title();
    const pageUrl = page.url();
    console.log('📝 Page title:', pageTitle);
    console.log('🔗 Current URL:', pageUrl);
    
    if (pageUrl.includes('login') || pageTitle.toLowerCase().includes('login')) {
      console.error('❌ Redirected to login page - authentication failed');
      return NextResponse.json(
        { error: 'Authentication failed - redirected to login page' },
        { status: 401 }
      );
    }
    
    // Wait a bit more for any dynamic content
    // Use setTimeout with a promise instead of waitForTimeout
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if the resume-preview element exists
    const resumePreviewExists = await page.evaluate(() => {
      return !!document.getElementById('resume-preview');
    });
    
    if (!resumePreviewExists) {
      console.error('❌ Resume preview element not found');
      return NextResponse.json(
        { error: 'Resume preview element not found on the page' },
        { status: 404 }
      );
    }
    
    console.log('✅ Resume preview element found, capturing it...');
    
    // Modify the page to only show the resume preview
    await page.evaluate(() => {
      const resumePreview = document.getElementById('resume-preview');
      if (resumePreview) {
        // Save the original styles
        const originalStyles = {
          position: resumePreview.style.position,
          top: resumePreview.style.top,
          left: resumePreview.style.left,
          margin: resumePreview.style.margin,
          padding: resumePreview.style.padding,
          background: resumePreview.style.background,
        };
        
        // Hide everything else
        document.body.innerHTML = '';
        document.body.style.margin = '0';
        document.body.style.padding = '0';
        document.body.style.background = '#ffffff';
        
        // Append the resume preview to the body
        document.body.appendChild(resumePreview);
        
        // Ensure the resume preview takes up the full page
        resumePreview.style.position = 'absolute';
        resumePreview.style.top = '0';
        resumePreview.style.left = '0';
        resumePreview.style.margin = '0';
        resumePreview.style.padding = '20px';
        resumePreview.style.background = '#ffffff';
      }
    });
    
    // Wait for any style changes to apply
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Generate PDF
    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true, // IMPORTANT: This preserves colors and backgrounds
      margin: {
        top: '20px',
        right: '20px',
        bottom: '20px',
        left: '20px',
      },
      preferCSSPageSize: true
    });
    
    await browser.close();
    
    console.log('✅ PDF generated successfully');
    
    // Return PDF as response
    return new NextResponse(pdf, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    });
    
  } catch (error) {
    console.error('❌ PDF generation error:', error);
    
    let errorMessage = 'Unknown error';
    let errorDetails = '';
    
    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = error.stack || '';
      console.error('Error stack:', error.stack);
    }
    
    return NextResponse.json(
      { 
        error: 'Failed to generate PDF', 
        message: errorMessage,
        details: errorDetails 
      },
      { status: 500 }
    );
  }
}
