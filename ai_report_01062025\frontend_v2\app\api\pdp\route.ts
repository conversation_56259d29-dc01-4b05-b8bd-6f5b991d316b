import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Get the backend URL from environment variables
const backendUrl = process.env.BACKEND_API_URL || 'http://localhost:8000';

// Helper function to forward requests to the backend
async function forwardToBackend(req: NextRequest, endpoint: string, method: string, session: any) {
  try {
    // Get the token from the session
    console.log('Session:', JSON.stringify(session, null, 2));
    const token = session?.user?.access_token;
    console.log('Token:', token ? 'Token exists' : 'No token');
    
    if (!token) {
      console.error('No access token found in session');
      // Return 401 with a special header to indicate auth error
      const response = NextResponse.json(
        { error: 'Unauthorized: No access token', redirectToLogin: true },
        { status: 401 }
      );
      // Add a special header to indicate this is an auth error
      response.headers.set('X-Auth-Required', 'true');
      return response;
    }

    // Get request body if it exists
    let body = null;
    if (method !== 'GET' && method !== 'DELETE') {
      body = await req.json();
    }

    // Prepare headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`
    };

    // Build the URL
    const url = `${backendUrl}/pdp/${endpoint}`;
    console.log(`Making ${method} request to: ${url}`);
    console.log('Headers:', JSON.stringify(headers, null, 2));
    if (body) {
      console.log('Request body:', JSON.stringify(body, null, 2));
    }

    // Make the request to the backend
    const response = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
    });
    
    console.log(`Response status: ${response.status}`);

    // Handle response
    if (!response.ok) {
      console.error(`Error response: ${response.status}`);
      try {
        const errorData = await response.json();
        console.error('Error data:', JSON.stringify(errorData, null, 2));
        return NextResponse.json(errorData, { status: response.status });
      } catch (e) {
        console.error('Failed to parse error response:', e);
        const errorText = await response.text().catch(() => 'Could not read response text');
        console.error('Error text:', errorText);
        return NextResponse.json({ error: 'Unknown error', details: errorText }, { status: response.status });
      }
    }

    // For DELETE requests with 204 status, return empty response
    if (method === 'DELETE' && response.status === 204) {
      return new NextResponse(null, { status: 204 });
    }

    // Return the response data
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error forwarding request to backend:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}

// GET handler for listing PDP items
export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  
  // Get query parameters
  const url = new URL(req.url);
  const id = url.searchParams.get('id');
  
  // If ID is provided, get a specific item, otherwise list all items
  const endpoint = id ? `items/${id}` : 'items' + url.search;
  
  return forwardToBackend(req, endpoint, 'GET', session);
}

// POST handler for creating new PDP items
export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);
  return forwardToBackend(req, 'items', 'POST', session);
}

// PUT handler for updating PDP items
export async function PUT(req: NextRequest) {
  const session = await getServerSession(authOptions);
  
  // Get the item ID from the request body
  const body = await req.json();
  const { id, ...data } = body;
  
  if (!id) {
    return NextResponse.json(
      { error: 'Item ID is required' },
      { status: 400 }
    );
  }
  
  // Create a new request with the updated body (without ID)
  const newReq = new NextRequest(req.url, {
    method: req.method,
    headers: req.headers,
    body: JSON.stringify(data),
  });
  
  return forwardToBackend(newReq, `items/${id}`, 'PUT', session);
}

// DELETE handler for deleting PDP items
export async function DELETE(req: NextRequest) {
  const session = await getServerSession(authOptions);
  
  // Get the item ID from the URL
  const url = new URL(req.url);
  const id = url.searchParams.get('id');
  
  if (!id) {
    return NextResponse.json(
      { error: 'Item ID is required' },
      { status: 400 }
    );
  }
  
  return forwardToBackend(req, `items/${id}`, 'DELETE', session);
}
