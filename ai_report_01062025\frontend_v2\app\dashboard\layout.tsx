"use client";

import { useState, useEffect } from "react";
import { Inter } from "next/font/google";
import Header from "@/components/header";
import { cn } from "@/lib/utils";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";

const inter = Inter({ subsets: ["latin"] });

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Check authentication on client side
  if (isClient && status !== "loading" && !session) {
    redirect("/login");
  }

  return (
    <div className={cn(
      inter.className,
      "min-h-screen flex flex-col bg-background"
    )}>
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 z-50">
        <Header />
      </div>
      
      {/* Main Content Area */}
      <div className="pt-16 flex-grow">
        <main className="w-full max-w-full">
          {children}
        </main>
      </div>
    </div>
  );
} 