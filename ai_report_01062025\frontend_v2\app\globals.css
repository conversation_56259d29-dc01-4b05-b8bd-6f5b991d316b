@import "tailwindcss";
@import "tw-animate-css";

/* Theme variables */
:root {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: #ffffff;
  --foreground: #23272f;
  --card: #ffffff;
  --card-foreground: #23272f;
  --popover: #ffffff;
  --popover-foreground: #23272f;
  --primary: #334155;
  --primary-foreground: #fafcff;
  --secondary: #f1f5f9;
  --secondary-foreground: #334155;
  --muted: #f1f5f9;
  --muted-foreground: #94a3b8;
  --accent: #f1f5f9;
  --accent-foreground: #334155;
  --destructive: #e57373;
  --border: #e5e7eb; /* was oklch(0.922 0 0) */
  --input: #e5e7eb; /* was oklch(0.922 0 0) */
  --ring: #a3a3a3;
  --chart-1: #6366f1;
  --chart-2: #38bdf8;
  --chart-3: #818cf8;
  --chart-4: #fbbf24;
  --chart-5: #fde68a;
  --sidebar: #fafcff;
  --sidebar-foreground: #23272f;
  --sidebar-primary: #334155;
  --sidebar-primary-foreground: #fafcff;
  --sidebar-accent: #f1f5f9;
  --sidebar-accent-foreground: #334155;
  --sidebar-border: #e5e7eb; /* was oklch(0.922 0 0) */
  --sidebar-ring: #a3a3a3;
  --sidebar-width: 70px;
  --sidebar-width-expanded: 256px;
}

.dark {
  --background: #23272f;
  --foreground: #fafcff;
  --card: #334155;
  --card-foreground: #fafcff;
  --popover: #334155;
  --popover-foreground: #fafcff;
  --primary: #e5e7eb; /* was oklch(0.922 0 0) */
  --primary-foreground: #334155;
  --secondary: #475569;
  --secondary-foreground: #fafcff;
  --muted: #475569;
  --muted-foreground: #a3a3a3;
  --accent: #475569;
  --accent-foreground: #fafcff;
  --destructive: #eab308;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #94a3b8;
  --chart-1: #6366f1;
  --chart-2: #38bdf8; /* was oklch(0.696 0.17 162.48) */
  --chart-3: #fde68a;
  --chart-4: #818cf8;
  --chart-5: #fbbf24;
  --sidebar: #334155;
  --sidebar-foreground: #fafcff;
  --sidebar-primary: #6366f1;
  --sidebar-primary-foreground: #fafcff;
  --sidebar-accent: #475569;
  --sidebar-accent-foreground: #fafcff;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #94a3b8;
}

/* Base styles */
* {
  border-color: var(--color-border);
  outline-color: rgba(var(--color-ring), 0.5);
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
}
