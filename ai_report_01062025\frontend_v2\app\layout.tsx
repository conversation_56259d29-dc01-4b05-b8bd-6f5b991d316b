import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { getServerSession } from "next-auth";
import { SidebarProvider } from "@/context/SidebarContext";
import { AssessmentProvider } from "@/context/AssessmentContext";
import Header from "@/components/header";
import { cn } from "@/lib/utils";
import AuthProvider from "@/components/providers/auth-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "TalentMetrix - Kaleidoscope",
  description: "TalentMetrix Kaleidoscope Application",
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession();
  const isLoggedIn = !!session;
  
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(
        inter.className,
        "min-h-screen bg-background antialiased"
      )}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider session={session}>
            <AssessmentProvider>
              <SidebarProvider>
                <div className="relative min-h-screen flex flex-col">
                  {/* Fixed Header */}
                  {isLoggedIn && (
                    <div className="fixed top-0 left-0 right-0 z-50">
                      <Header />
                    </div>
                  )}
                  
                  {/* Main Content Area */}
                  <div className="pt-16"> {/* Reduced padding-top to minimize white space */}
                    <div className="flex min-h-screen">
                      {/* Main Content */}
                      <main className="flex-1 pb-0 w-full">
                        {children}
                      </main>
                    </div>
                  </div>
                </div>
              </SidebarProvider>
            </AssessmentProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
