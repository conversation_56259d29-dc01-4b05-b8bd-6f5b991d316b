"use client";

import React from 'react';
import Link from 'next/link';

interface CommonButtonProps {
  text: string;
  onClick?: () => void;
  href?: string;
}

export default function CommonButton({ text, onClick, href }: CommonButtonProps) {
  const ButtonContent = () => (
    <>
      {text}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="arrow-icon"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M5 12h14"></path>
        <path d="M12 5l7 7-7 7"></path>
      </svg>
    </>
  );

  return (
    <div className="common-button-wrapper">
      <style jsx>{`
        .common-button-wrapper {
          display: flex;
          justify-content: center;
          margin-bottom: 40px;
        }

        .common-button {
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: transparent;
          color: #222222;
          border: 2px solid #555555;
          border-radius: 25px;
          padding: 8px 16px;
          font-size: 0.75rem;
          font-weight: 700;
          cursor: pointer;
          text-decoration: none;
          text-transform: uppercase;
          width: 140px;
          transition: background-color 0.2s;
        }

        .common-button:hover {
          background-color: rgba(0, 0, 0, 0.03);
        }

        .arrow-icon {
          margin-left: 8px;
        }
      `}</style>

      {href ? (
        <Link href={href} className="common-button">
          <ButtonContent />
        </Link>
      ) : (
        <button className="common-button" onClick={onClick}>
          <ButtonContent />
        </button>
      )}
    </div>
  );
} 