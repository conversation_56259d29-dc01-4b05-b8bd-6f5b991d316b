"use client";
import React from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

interface NavigationButtonProps {
  text: string;
  href?: string;
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

export function NavigationButton({ text, href, onClick, className = "", style }: NavigationButtonProps) {
  const content = (
    <>
      {text}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="ml-2 group-hover:translate-x-1 transition-transform duration-150"
      >
        <path d="M5 12h14" />
        <path d="M12 5l7 7-7 7" />
      </svg>
    </>
  );

  const buttonClass =
    "rounded-full border-2 border-gray-800 dark:border-gray-200 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer " +
    (className || "");

  return (
    <div className="flex justify-center w-full my-8">
      {href ? (
        <Button asChild variant="outline" className={buttonClass} style={style} onClick={onClick}>
          <Link href={href}>{content}</Link>
        </Button>
      ) : (
        <Button
          variant="outline"
          className={buttonClass}
          style={style}
          onClick={onClick}
        >
          {content}
        </Button>
      )}
    </div>
  );
}

export default NavigationButton;