"use client";

import React from 'react';
import Sidebar from '@/components/Sidebar';
import { useSidebar } from '@/context/SidebarContext';
import { cn } from '@/lib/utils';

export default function DefaultLayout({ children }: { children: React.ReactNode }) {
  const { isExpanded } = useSidebar();
  
  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <aside className="shrink-0 sticky top-16 h-[calc(100vh-4rem)] z-10">
        <Sidebar />
      </aside>
      
      {/* Main Content - Remove margin and let it flow naturally next to sidebar */}
      <main className="flex-1 w-full">
        {children}
      </main>
    </div>
  );
} 