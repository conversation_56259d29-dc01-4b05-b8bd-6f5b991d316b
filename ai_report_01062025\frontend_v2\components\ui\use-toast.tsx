// File: components/ui/use-toast.tsx
// --------------------------------
// Toast provider and hook using Sonner, styled to fit ShadCN UI.

import { ReactNode } from 'react';
import { Toaster, toast as sonnerToast, ToastPosition } from 'sonner';

/**
 * Wrap your Next.js app in <ToastProvider> in app/layout.tsx to enable toasts.
 */
export function ToastProvider({ children }: { children: ReactNode }) {
  return (
    <>
      {children}
      <Toaster
        position="bottom-right"
        richColors
        closeButton
      />
    </>
  );
}

/**
 * useToast hook returns a toast() function to show notifications.
 * Supports optional variant: 'default', 'success', 'error', 'warning'.
 */
export function useToast() {
  return {
    toast: (opts: {
      title: string;
      description?: string;
      variant?: 'default' | 'success' | 'error' | 'warning';
    }) => {
      const message = opts.description
        ? `${opts.title}\n${opts.description}`
        : opts.title;
      if (opts.variant && opts.variant !== 'default' && sonnerToast[opts.variant]) {
        // Use type assertion to access the method safely
        const toastMethod = sonnerToast[opts.variant] as (message: string) => string | number;
        return toastMethod(message);
      }
      return sonnerToast(message);
    },
  };
}
