// File: data/interviewQuestions.ts

export interface BehavioralQuestion {
    id: number;
    question: string;
    concept: string;
    goodAnswer: string;
    commonMistakes: string;
    exampleApplications: string;
    category: 'Leadership' | 'Teamwork' | 'Communication';
    difficulty: 'Easy' | 'Medium' | 'Hard';
  }
  
  export const behavioralQuestions: BehavioralQuestion[] = [
    {
      id: 1,
      question: "Tell me about yourself.",
      concept: "Self-awareness, communication",
      goodAnswer: "Summarizes key professional milestones, motivations, and relevance to the role.",
      commonMistakes: "Rambling, listing resume without insight, irrelevant personal info.",
      exampleApplications: "Initial impression, personal branding, opening pitch.",
      category: "Communication",
      difficulty: "Easy"
    },
    {
      id: 2,
      question: "Describe a challenge you faced and how you handled it.",
      concept: "Problem-solving, resilience, decision-making",
      goodAnswer: "Uses STAR format with clear conflict, actions taken, and lessons learned.",
      commonMistakes: "Too vague, no specific role or impact, blaming others.",
      exampleApplications: "Leadership growth, adaptability, ownership.",
      category: "Leadership",
      difficulty: "Medium"
    },
    {
      id: 3,
      question: "What are your strengths and weaknesses?",
      concept: "Self-awareness, honesty, growth mindset",
      goodAnswer: "Identifies a strength with proof, and a real weakness with action plan to improve.",
      commonMistakes: "Cliché answers, fake weaknesses, defensiveness.",
      exampleApplications: "Personal development, coachability, fit assessment.",
      category: "Communication",
      difficulty: "Medium"
    },
    {
      id: 4,
      question: "Where do you see yourself in five years?",
      concept: "Career vision, ambition, alignment",
      goodAnswer: "A realistic and role-aligned answer showing growth and consistency.",
      commonMistakes: "Too generic, misaligned, or unrelated aspirations.",
      exampleApplications: "Long-term fit, leadership potential, retention signals.",
      category: "Teamwork",
      difficulty: "Easy"
    },
    {
      id: 5,
      question: "Tell me about a time you had to work closely with someone whose personality was very different from yours.",
      concept: "Collaboration, empathy, adaptability",
      goodAnswer: "Demonstrates understanding, compromise, and positive outcome from the collaboration.",
      commonMistakes: "Focusing only on conflict, blaming the other person.",
      exampleApplications: "Cross-functional teams, team diversity, conflict resolution.",
      category: "Teamwork",
      difficulty: "Hard"
    }
  ];
  