__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
.venv/
venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.vscode/
.idea/
# Environment files - should be managed at runtime, not baked into the image
.env
# .encryption_key # Decide how to handle this, see notes below

# Runtime generated directories (your app should create these if needed)
logs/
output/

# Exclude requirements-ollama.txt if it's not used
requirements-ollama.txt

# Any other local-only files or directories
local_dev_stuff/
*.sqlite3