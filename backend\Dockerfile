# Choose your desired Python version. python:3.11-slim is a good starting point.
FROM python:3.12
# Set the working directory in the container
WORKDIR /app

# Install system dependencies if any of your Python packages need them
# For example, if you were using PostgreSQL directly and needed libpq-dev:
# RUN apt-get update && apt-get install -y libpq-dev gcc && rm -rf /var/lib/apt/lists/*

# Copy only the main requirements file first to leverage Docker's build cache
COPY ./requirements.txt /app/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r /app/requirements.txt

# Copy the rest of the application code from your 'backend' directory
# to the '/app' directory in the container.
# This relies on the .dockerignore file to exclude unwanted files/folders.
COPY . .

# Expose the port the app runs on (defined in run_api.py)
EXPOSE 8000

# Command to run the application using your script
# Ensure run_api.py correctly imports your FastAPI 'app' instance
CMD ["python", "run_api.py"]