# api/app.py
import os
import sys
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
from api.routes import router
from api.auth import router as auth_router
from api.pdp_routes import router as pdp_router
from api.ollama_routes import router as ollama_router

# NEW: Import the unified chat router
from api.unified_chat_routes import router as unified_chat_router
from api.gemini_routes import router as gemini_router
from api.gemini_ats_router import router as gemini_ats_router

# Configure logging
log_file_path = os.path.join("logs", "api.log")
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logger.remove()  # Remove default handler
logger.add(sys.stderr, level="INFO")  # Add stderr handler
logger.add(
    log_file_path,
    rotation="500 MB",
    retention="10 days",
    level="DEBUG",
    backtrace=True,
    diagnose=True
)

# Create FastAPI app
app = FastAPI(
    title="Career Assessment API with AI Chat",  # Updated title
    description="API for career assessments with multi-provider AI chat support (Ollama, Gemini, OpenAI, Claude)",  # Updated description
    version="1.1.0"  # Updated version
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include existing API routes
app.include_router(router)
app.include_router(auth_router, prefix="/auth", tags=["authentication"])
app.include_router(pdp_router, prefix="/pdp", tags=["personal-development-plan"])
app.include_router(ollama_router)  # Keep existing Ollama routes for backwards compatibility

# NEW: Include unified chat routes
app.include_router(unified_chat_router)
app.include_router(gemini_router)
app.include_router(gemini_ats_router)

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "details": str(exc)}
    )

@app.get("/", tags=["Health"])
async def health_check():
    """Health check endpoint with API information"""
    return {
        "status": "healthy", 
        "service": "Career Assessment API with AI Chat",
        "version": "1.1.0",
        "features": {
            "career_assessment": "✅ Available",
            "authentication": "✅ Available", 
            "personal_development_plans": "✅ Available",
            "ollama_chat": "✅ Available",
            "unified_ai_chat": "✅ Available (Ollama, Gemini, OpenAI, Claude)"
        },
        "endpoints": {
            "health": "/",
            "career_routes": "/api/*",
            "auth": "/auth/*",
            "pdp": "/pdp/*",
            "ollama_specific": "/api/ollama/*",
            "unified_chat": "/api/chat/*",
            "chat_providers": "/api/chat/providers",
            "chat_health": "/api/chat/health"
        }
    }

# NEW: Enhanced info endpoint
@app.get("/info", tags=["Health"])
async def api_info():
    """Detailed API information"""
    return {
        "api_name": "Career Assessment API with AI Chat",
        "version": "1.1.0",
        "description": "Comprehensive API for career assessments with multi-provider AI chat capabilities",
        "available_services": [
            "Career Assessment",
            "Authentication", 
            "Personal Development Plans",
            "Ollama Local Chat",
            "Multi-Provider AI Chat (Gemini, OpenAI, Claude)"
        ],
        "ai_providers": {
            "ollama": "Local AI models (no API key required)",
            "gemini": "Google Gemini models (API key required)",
            "openai": "OpenAI GPT models (API key required)", 
            "claude": "Anthropic Claude models (API key required)"
        },
        "quick_start": {
            "check_providers": "GET /api/chat/providers",
            "chat_with_ai": "POST /api/chat/",
            "compare_models": "POST /api/chat/compare",
            "health_check": "GET /api/chat/health"
        }
    }

# Startup event
@app.on_event("startup")
async def startup_event():
    logger.info("Career Assessment API with AI Chat is starting up")
    
    # Log available AI providers
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        default_provider = os.getenv("DEFAULT_AI_PROVIDER", "ollama")
        logger.info(f"Default AI provider: {default_provider}")
        
        # Log which API keys are configured
        api_keys_status = {
            "GEMINI_API_KEY": "✅ Configured" if os.getenv("GEMINI_API_KEY") else "❌ Not configured",
            "OPENAI_API_KEY": "✅ Configured" if os.getenv("OPENAI_API_KEY") else "❌ Not configured", 
            "CLAUDE_API_KEY": "✅ Configured" if os.getenv("CLAUDE_API_KEY") else "❌ Not configured"
        }
        
        for key, status in api_keys_status.items():
            logger.info(f"{key}: {status}")
            
    except Exception as e:
        logger.warning(f"Could not check AI provider configuration: {e}")

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Career Assessment API with AI Chat is shutting down")