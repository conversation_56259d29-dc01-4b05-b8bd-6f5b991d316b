from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Depends
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr
from datetime import datetime, timezone
import uuid
from typing import Optional
from utils.auth import verify_password, get_password_hash, create_access_token, verify_token
from db_connection import get_db_connection
import logging
import traceback

router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

class CandidateCreate(BaseModel):
    email: EmailStr
    password: str
    name: str
    valid_until: Optional[datetime] = None

class Token(BaseModel):
    access_token: str
    token_type: str

@router.post("/register", response_model=dict)
async def register_candidate(candidate: CandidateCreate):
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Check if email already exists
        cursor.execute("SELECT id FROM candidate_auth WHERE email = %s", (candidate.email,))
        if cursor.fetchone():
            raise HTTPException(status_code=400, detail="Email already registered")
        
        # Create new candidate
        candidate_id = str(uuid.uuid4())
        hashed_password = get_password_hash(candidate.password)
        
        cursor.execute("""
            INSERT INTO candidate_auth (id, email, password_hash, name, valid_until)
            VALUES (%s, %s, %s, %s, %s)
        """, (candidate_id, candidate.email, hashed_password, candidate.name, candidate.valid_until))
        
        conn.commit()
        return {"message": "Candidate registered successfully", "candidate_id": candidate_id}
    
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        cursor.close()
        conn.close()

@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    logging.info(f"Login attempt for: {form_data.username}")
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Get candidate from database
        cursor.execute("""
            SELECT id, email, password_hash, status, valid_until, role
            FROM candidate_auth 
            WHERE email = %s
        """, (form_data.username,))
        
        candidate = cursor.fetchone()
        logging.info(f"Candidate fetched: {candidate}")
        if not candidate:
            logging.warning("No candidate found for email")
            raise HTTPException(status_code=401, detail="Invalid email or password")
        
        # Verify password
        if not verify_password(form_data.password, candidate[2]):
            logging.warning("Password verification failed")
            raise HTTPException(status_code=401, detail="Invalid email or password")
        
        # Check if candidate is active
        if candidate[3] != 'active':
            logging.warning("Account is not active")
            raise HTTPException(status_code=401, detail="Account is not active")
        
        # Check if account is still valid
        if candidate[4]:
            valid_until = candidate[4]
            if valid_until.tzinfo is None:
                valid_until = valid_until.replace(tzinfo=timezone.utc)
            current_time = datetime.now(timezone.utc)
            if current_time > valid_until:
                logging.warning("Account has expired")
                raise HTTPException(status_code=401, detail="Account has expired")
        
        # Update last login with UTC timestamp
        cursor.execute("""
            UPDATE candidate_auth 
            SET last_login = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
            WHERE id = %s
        """, (candidate[0],))
        
        # Create access token with role and candidate ID
        access_token = create_access_token(data={
            "sub": candidate[1],  # email
            "id": candidate[0],   # candidate ID
            "role": candidate[5]
        })
        logging.info(f"Access token created for {candidate[1]} with ID {candidate[0]}")
        
        conn.commit()
        return {"access_token": access_token, "token_type": "bearer"}
    
    except HTTPException as he:
        logging.error(f"HTTPException: {he.detail}")
        raise
    except Exception as e:
        logging.error(f"Exception in login: {str(e)}\n{traceback.format_exc()}")
        conn.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        cursor.close()
        conn.close()

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    logging.info(f"Validating token: {token}")
    payload = verify_token(token)
    logging.info(f"Token payload: {payload}")
    if payload is None:
        logging.warning("Token payload is None")
        raise credentials_exception
    
    email: str = payload.get("sub")
    candidate_id: str = payload.get("id")
    role: str = payload.get("role")
    
    if email is None or role is None:
        logging.warning("Email or role missing in token payload")
        raise credentials_exception
    
    # Return candidate ID if available, otherwise use email as fallback
    return {
        "sub": email,
        "id": candidate_id,
        "role": role
    } 