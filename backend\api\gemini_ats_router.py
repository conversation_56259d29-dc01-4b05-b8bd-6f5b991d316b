"""
Complete Gemini ATS Router - All JSON Endpoints
api/gemini_ats_router.py
"""

import os
import asyncio
import logging
import json
import re
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv
from pydantic_ai import Agent

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create dedicated ATS router
router = APIRouter(prefix="/api/ats", tags=["ats-analysis"])

# Configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
ATS_MODEL = "google-gla:gemini-2.5-flash-preview-05-20"  # Flash model only for cost optimization
DEFAULT_TEMPERATURE = 0.1  # Low temperature for consistent analysis

# Agent cache for performance
_ats_agent_cache: Dict[str, Agent] = {}

# ============================================================================
# COMPREHENSIVE PYDANTIC MODELS - ALL JSON
# ============================================================================

class ATSAnalysisRequest(BaseModel):
    resume_text: str = Field(..., min_length=50, description="Resume text content (minimum 50 characters)")
    job_description: Optional[str] = Field(None, description="Optional job description for targeted analysis")
    target_role: Optional[str] = Field(None, description="Target job role/position")
    analysis_depth: str = Field(default="standard", description="basic, standard, or comprehensive")
    industry: Optional[str] = Field(None, description="Target industry (e.g., tech, healthcare, finance)")
    experience_level: Optional[str] = Field(None, description="entry, mid, senior, executive")
    
    @validator('analysis_depth')
    def validate_analysis_depth(cls, v):
        if v not in ['basic', 'standard', 'comprehensive']:
            raise ValueError('analysis_depth must be basic, standard, or comprehensive')
        return v

class ATSBulkAnalysisRequest(BaseModel):
    resumes: List[str] = Field(..., max_items=10, description="List of resume texts (max 10)")
    job_description: Optional[str] = Field(None, description="Job description for comparison")
    target_role: Optional[str] = Field(None, description="Target role")

class ATSComparisonRequest(BaseModel):
    resume_a: str = Field(..., min_length=50, description="First resume text")
    resume_b: str = Field(..., min_length=50, description="Second resume text")
    job_description: Optional[str] = Field(None, description="Job description for comparison")
    comparison_criteria: List[str] = Field(default=["ats_score", "keywords", "experience", "skills"], description="Criteria to compare")

class QuickScoreRequest(BaseModel):
    resume_text: str = Field(..., min_length=50, description="Resume text content")

class KeywordOptimizeRequest(BaseModel):
    resume_text: str = Field(..., min_length=50, description="Resume text content")
    job_description: str = Field(..., min_length=10, description="Job description text")
    target_role: Optional[str] = Field(None, description="Target job role")

class ImprovementPlanRequest(BaseModel):
    current_resume: str = Field(..., min_length=50, description="Current resume text")
    target_score: int = Field(..., ge=0, le=100, description="Target ATS score")
    timeframe: str = Field(default="1 week", description="Timeline for improvements")

class IndustryAnalysisRequest(BaseModel):
    resume_text: str = Field(..., min_length=50, description="Resume text content")
    industry: str = Field(..., description="Target industry")
    role_level: str = Field(default="mid", description="Experience level: entry, mid, senior, executive")
    target_companies: Optional[List[str]] = Field(None, description="List of target companies")

class ReportRequest(BaseModel):
    resume_text: str = Field(..., min_length=50, description="Resume text content")
    job_description: Optional[str] = Field(None, description="Optional job description")
    report_format: str = Field(default="detailed", description="Report format: summary, detailed, executive")
    include_examples: bool = Field(default=True, description="Include examples in report")

class FileUploadRequest(BaseModel):
    resume_text: str = Field(..., min_length=50, description="Extracted resume text from uploaded file")
    job_description: Optional[str] = Field(None, description="Optional job description")
    target_role: Optional[str] = Field(None, description="Target job role")
    analysis_depth: str = Field(default="standard", description="Analysis depth level")

# Response Models
class ATSIssue(BaseModel):
    type: str = Field(..., description="error, warning, success, info")
    category: str = Field(..., description="formatting, content, keywords, structure, contact_info")
    text: str = Field(..., description="Issue description")
    severity: int = Field(..., ge=1, le=5, description="Severity level 1-5")
    fix_suggestion: str = Field(..., description="Specific suggestion to fix this issue")
    impact: str = Field(..., description="low, medium, high, critical")

class ATSKeywordAnalysis(BaseModel):
    found_keywords: List[str] = Field(default=[], description="Relevant keywords found in resume")
    missing_keywords: List[str] = Field(default=[], description="Important keywords missing from resume")
    keyword_density: float = Field(default=0.0, ge=0.0, le=1.0, description="Keyword density ratio")
    relevance_score: int = Field(default=0, ge=0, le=100, description="Keyword relevance score")
    technical_skills: List[str] = Field(default=[], description="Technical skills identified")
    soft_skills: List[str] = Field(default=[], description="Soft skills identified")
    industry_terms: List[str] = Field(default=[], description="Industry-specific terms found")

class ATSContactInfo(BaseModel):
    has_email: bool = Field(default=False)
    has_phone: bool = Field(default=False)
    has_linkedin: bool = Field(default=False)
    has_portfolio: bool = Field(default=False)
    email_format_valid: bool = Field(default=False)
    phone_format_valid: bool = Field(default=False)
    missing_elements: List[str] = Field(default=[])

class ATSStructureAnalysis(BaseModel):
    sections_found: List[str] = Field(default=[], description="Resume sections detected")
    sections_missing: List[str] = Field(default=[], description="Important sections missing")
    section_order_score: int = Field(default=0, ge=0, le=100, description="Section ordering score")
    formatting_consistency: int = Field(default=0, ge=0, le=100, description="Formatting consistency score")
    readability_score: int = Field(default=0, ge=0, le=100, description="Overall readability score")

class ATSJobMatch(BaseModel):
    overall_match_score: int = Field(default=0, ge=0, le=100, description="Overall job match percentage")
    keyword_match_score: int = Field(default=0, ge=0, le=100, description="Keyword match score")
    skills_match_score: int = Field(default=0, ge=0, le=100, description="Skills match score")
    experience_match_score: int = Field(default=0, ge=0, le=100, description="Experience relevance score")
    matching_keywords: List[str] = Field(default=[], description="Keywords found in both resume and job description")
    missing_critical_keywords: List[str] = Field(default=[], description="Critical keywords missing from resume")
    missing_preferred_keywords: List[str] = Field(default=[], description="Preferred keywords missing from resume")
    recommendations: List[str] = Field(default=[], description="Specific recommendations to improve match")
    competitive_edge: List[str] = Field(default=[], description="Unique strengths found in resume")

class ATSScoreBreakdown(BaseModel):
    overall_score: int = Field(..., ge=0, le=100, description="Overall ATS compatibility score")
    formatting_score: int = Field(..., ge=0, le=100, description="Formatting and structure score")
    content_score: int = Field(..., ge=0, le=100, description="Content quality score")
    keyword_score: int = Field(..., ge=0, le=100, description="Keyword optimization score")
    contact_score: int = Field(..., ge=0, le=100, description="Contact information score")
    experience_score: int = Field(..., ge=0, le=100, description="Experience section score")
    education_score: int = Field(..., ge=0, le=100, description="Education section score")
    skills_score: int = Field(..., ge=0, le=100, description="Skills section score")

class ATSAnalysisResponse(BaseModel):
    # Core scores
    scores: ATSScoreBreakdown
    overall_rating: str = Field(..., description="Excellent, Good, Fair, Poor, Critical")
    
    # Detailed analysis
    word_count: int = Field(..., ge=0)
    character_count: int = Field(..., ge=0)
    structure: ATSStructureAnalysis
    keywords: ATSKeywordAnalysis
    contact_info: ATSContactInfo
    issues: List[ATSIssue]
    suggestions: List[str] = Field(..., description="Prioritized improvement suggestions")
    
    # Job matching (if job description provided)
    job_match: Optional[ATSJobMatch] = None
    
    # Metadata
    processing_time: float = Field(..., description="Analysis processing time in seconds")
    model_used: str = Field(default=ATS_MODEL)
    analysis_depth: str = Field(..., description="Level of analysis performed")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

class ATSQuickScoreResponse(BaseModel):
    score: int = Field(..., ge=0, le=100)
    rating: str = Field(..., description="Excellent, Good, Fair, Poor, Critical")
    top_strength: str = Field(..., description="Best aspect of the resume")
    top_weakness: str = Field(..., description="Most critical issue to fix")
    quick_win: str = Field(..., description="Easiest improvement to make")
    processing_time: float
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

class ATSKeywordOptimizeResponse(BaseModel):
    critical_missing: List[str] = Field(default=[], description="Must-have keywords to add")
    important_missing: List[str] = Field(default=[], description="Important keywords to consider")
    nice_to_have: List[str] = Field(default=[], description="Additional keywords that could help")
    keyword_placement_strategy: List[str] = Field(default=[], description="Where to place keywords")
    natural_integration_tips: List[str] = Field(default=[], description="How to integrate keywords naturally")
    keyword_density_analysis: Dict[str, Any] = Field(default={}, description="Current keyword density metrics")

class ATSComparisonResponse(BaseModel):
    winner: str = Field(..., description="resume_a or resume_b or tie")
    score_difference: int = Field(..., description="Score difference between resumes")
    resume_a_score: int = Field(..., ge=0, le=100)
    resume_b_score: int = Field(..., ge=0, le=100)
    strengths_a: List[str] = Field(default=[])
    strengths_b: List[str] = Field(default=[])
    recommendations_a: List[str] = Field(default=[])
    recommendations_b: List[str] = Field(default=[])
    detailed_comparison: Dict[str, Any] = Field(default={})

class ATSBulkAnalysisResponse(BaseModel):
    total_resumes: int
    average_score: float
    best_resume_index: int
    worst_resume_index: int
    individual_scores: List[int]
    individual_ratings: List[str]
    summary_insights: List[str]
    processing_time: float
# ============================================================================
# COMPREHENSIVE AI PROMPTS
# ============================================================================

COMPREHENSIVE_ATS_PROMPT = """You are an expert ATS (Applicant Tracking System) resume analyzer with 10+ years of experience in recruitment technology and hiring optimization.

ANALYSIS FRAMEWORK:
1. **ATS Compatibility** (0-100): How well the resume performs in ATS systems
2. **Content Quality** (0-100): Relevance, achievements, quantified results, impact
3. **Structure & Formatting** (0-100): Section organization, consistency, readability
4. **Keyword Optimization** (0-100): Industry keywords, technical terms, skill relevance
5. **Contact Information** (0-100): Completeness and professional formatting
6. **Experience Relevance** (0-100): Job history alignment and progression
7. **Education Alignment** (0-100): Educational background relevance
8. **Skills Presentation** (0-100): Technical and soft skills organization

CRITICAL SECTION REQUIREMENTS:
- Missing any of these sections should result in automatic score reductions:
  - Education: -20 points minimum if completely missing (education_score cannot exceed 50)
  - Experience: -30 points minimum if completely missing (experience_score cannot exceed 40)
  - Skills: -15 points minimum if completely missing (skills_score cannot exceed 60)
  - Contact Information: -25 points minimum if incomplete (contact_score cannot exceed 50)
- Overall score cannot exceed 75 if any mandatory section is missing
- No single category should compensate for critical missing sections

INDUSTRY-SPECIFIC REQUIREMENTS:
- Academic/Research: Education section must be comprehensive (detailed degrees, research focus)
- Technical: Skills section must include specific technical competencies
- Executive: Experience section must demonstrate leadership progression
- Entry-level: Education section is especially critical and should be detailed

SCORING CRITERIA:
- 90-100: Excellent - ATS optimized, exceptional content, perfect structure, NO missing sections
- 80-89: Good - Minor improvements needed, strong overall performance, all critical sections present
- 70-79: Fair - Several areas need attention, decent foundation, may have minor section weaknesses
- 60-69: Poor - Significant improvements required, likely missing important content
- 0-59: Critical - Major overhaul needed, multiple critical sections missing or severely deficient

VALIDATION CHECKS:
- If sections_missing includes "education", education_score must be < 50
- If sections_missing includes "experience", experience_score must be < 50
- If sections_missing includes "skills", skills_score must be < 60
- If contact_info has multiple missing elements, contact_score must be < 60

ISSUE CATEGORIES:
- formatting: Layout, structure, consistency issues
- content: Missing information, weak descriptions, lack of quantification
- keywords: Missing industry terms, poor keyword density
- structure: Section problems, poor organization
- contact_info: Missing or improperly formatted contact details

RETURN EXACTLY THIS JSON FORMAT (no markdown, no additional text):
{
  "scores": {
    "overall_score": <int 0-100>,
    "formatting_score": <int 0-100>,
    "content_score": <int 0-100>,
    "keyword_score": <int 0-100>,
    "contact_score": <int 0-100>,
    "experience_score": <int 0-100>,
    "education_score": <int 0-100>,
    "skills_score": <int 0-100>
  },
  "overall_rating": "Excellent|Good|Fair|Poor|Critical",
  "word_count": <int>,
  "character_count": <int>,
  "structure": {
    "sections_found": [<list of detected sections>],
    "sections_missing": [<list of missing important sections>],
    "section_order_score": <int 0-100>,
    "formatting_consistency": <int 0-100>,
    "readability_score": <int 0-100>
  },
  "keywords": {
    "found_keywords": [<relevant keywords found>],
    "missing_keywords": [<important keywords to add>],
    "keyword_density": <float 0-1>,
    "relevance_score": <int 0-100>,
    "technical_skills": [<technical skills identified>],
    "soft_skills": [<soft skills identified>],
    "industry_terms": [<industry-specific terms found>]
  },
  "contact_info": {
    "has_email": <boolean>,
    "has_phone": <boolean>,
    "has_linkedin": <boolean>,
    "has_portfolio": <boolean>,
    "email_format_valid": <boolean>,
    "phone_format_valid": <boolean>,
    "missing_elements": [<missing contact elements>]
  },
  "issues": [
    {
      "type": "error|warning|success|info",
      "category": "formatting|content|keywords|structure|contact_info",
      "text": "<specific issue description>",
      "severity": <int 1-5>,
      "fix_suggestion": "<specific actionable fix>",
      "impact": "low|medium|high|critical"
    }
  ],
  "suggestions": [<prioritized list of specific improvements>]
}

Be thorough, specific, and actionable. Focus on practical improvements that will significantly impact ATS performance and hiring manager impression."""

JOB_MATCHING_PROMPT = """You are an expert at resume-to-job matching for ATS optimization and hiring success.

Analyze the resume against the job description and provide comprehensive matching insights.

RETURN EXACTLY THIS JSON FORMAT:
{
  "overall_match_score": <int 0-100>,
  "keyword_match_score": <int 0-100>,
  "skills_match_score": <int 0-100>,
  "experience_match_score": <int 0-100>,
  "matching_keywords": [<keywords found in both>],
  "missing_critical_keywords": [<must-have keywords from job description>],
  "missing_preferred_keywords": [<nice-to-have keywords from job description>],
  "recommendations": [<specific actionable recommendations>],
  "competitive_edge": [<unique strengths that make this candidate stand out>]
}

Focus on practical keyword integration and genuine qualification alignment."""

QUICK_SCORE_PROMPT = """Analyze this resume for ATS compatibility and provide a quick assessment.

RETURN EXACTLY THIS JSON FORMAT:
{
  "score": <int 0-100>,
  "rating": "Excellent|Good|Fair|Poor|Critical",
  "top_strength": "<best aspect of the resume>",
  "top_weakness": "<most critical issue>",
  "quick_win": "<easiest high-impact improvement>"
}

Be specific and actionable in your feedback."""

KEYWORD_OPTIMIZATION_PROMPT = """Analyze keyword optimization opportunities for this resume.

RETURN EXACTLY THIS JSON FORMAT:
{
  "critical_missing": [<must-add keywords>],
  "important_missing": [<should-add keywords>],
  "nice_to_have": [<could-add keywords>],
  "keyword_placement_strategy": [<where to place keywords>],
  "natural_integration_tips": [<how to integrate naturally>],
  "keyword_density_analysis": {
    "current_density": <float>,
    "optimal_range": "<percentage range>",
    "recommendation": "<density recommendation>"
  }
}

Focus on natural, authentic integration that maintains readability."""

COMPARISON_PROMPT = """Compare these two resumes across multiple criteria.

RETURN EXACTLY THIS JSON FORMAT:
{
  "winner": "resume_a|resume_b|tie",
  "score_difference": <int>,
  "resume_a_score": <int 0-100>,
  "resume_b_score": <int 0-100>,
  "strengths_a": [<unique strengths of resume A>],
  "strengths_b": [<unique strengths of resume B>],
  "recommendations_a": [<improvements for resume A>],
  "recommendations_b": [<improvements for resume B>],
  "detailed_comparison": {
    "formatting": {"winner": "resume_a|resume_b|tie", "notes": "<comparison notes>"},
    "content": {"winner": "resume_a|resume_b|tie", "notes": "<comparison notes>"},
    "keywords": {"winner": "resume_a|resume_b|tie", "notes": "<comparison notes>"},
    "experience": {"winner": "resume_a|resume_b|tie", "notes": "<comparison notes>"}
  }
}

Provide objective, detailed comparison with specific reasoning."""

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def get_ats_agent(prompt_type: str, custom_prompt: Optional[str] = None) -> Agent:
    """Get or create specialized ATS agent with caching"""
    if not GEMINI_API_KEY:
        raise HTTPException(status_code=503, detail="GEMINI_API_KEY not configured")
    
    # Prompt mapping
    prompts = {
        "comprehensive": COMPREHENSIVE_ATS_PROMPT,
        "job_match": JOB_MATCHING_PROMPT,
        "quick_score": QUICK_SCORE_PROMPT,
        "keyword_optimize": KEYWORD_OPTIMIZATION_PROMPT,
        "comparison": COMPARISON_PROMPT
    }
    
    system_prompt = custom_prompt or prompts.get(prompt_type, COMPREHENSIVE_ATS_PROMPT)
    cache_key = f"{prompt_type}_{hash(system_prompt)}"
    
    if cache_key not in _ats_agent_cache:
        try:
            agent = Agent(
                model=ATS_MODEL,
                system_prompt=system_prompt
            )
            _ats_agent_cache[cache_key] = agent
            logger.info(f"Created ATS agent: {prompt_type}")
        except Exception as e:
            logger.error(f"Failed to create ATS agent: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to initialize ATS agent: {str(e)}")
    
    return _ats_agent_cache[cache_key]

def parse_json_response(response_text: str, operation: str) -> Dict[str, Any]:
    """Parse JSON response with fallback handling"""
    try:
        return json.loads(response_text)
    except json.JSONDecodeError:
        # Try to extract JSON from response
        json_matches = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', response_text, re.DOTALL)
        if json_matches:
            try:
                return json.loads(json_matches[-1])  # Take the last/most complete JSON
            except json.JSONDecodeError:
                pass
        
        logger.error(f"Failed to parse JSON response for {operation}: {response_text[:500]}")
        raise HTTPException(status_code=500, detail=f"Failed to parse {operation} response")

def validate_resume_text(text: str) -> str:
    """Validate and clean resume text"""
    if not text or len(text.strip()) < 50:
        raise HTTPException(status_code=400, detail="Resume text must be at least 50 characters long")
    
    # Basic cleaning
    cleaned = text.strip()
    if len(cleaned) > 50000:  # Reasonable limit
        raise HTTPException(status_code=400, detail="Resume text too long (max 50,000 characters)")
    
    return cleaned

# ============================================================================
# MAIN ATS ANALYSIS ENDPOINTS - ALL JSON
# ============================================================================

@router.post("/analyze", response_model=ATSAnalysisResponse)
async def analyze_resume(request: ATSAnalysisRequest):
    """Comprehensive ATS resume analysis - JSON input"""
    start_time = asyncio.get_event_loop().time()
    
    try:
        # Validate input
        resume_text = validate_resume_text(request.resume_text)
        
        # Prepare analysis prompt with context
        context_additions = []
        if request.target_role:
            context_additions.append(f"TARGET ROLE: {request.target_role}")
        if request.industry:
            context_additions.append(f"TARGET INDUSTRY: {request.industry}")
        if request.experience_level:
            context_additions.append(f"EXPERIENCE LEVEL: {request.experience_level}")
        
        context = "\n".join(context_additions) if context_additions else ""
        
        # Get analysis agent
        agent = get_ats_agent("comprehensive")
        
        # Prepare input
        input_text = f"{context}\n\nRESUME TEXT:\n{resume_text}" if context else f"RESUME TEXT:\n{resume_text}"
        if request.job_description:
            input_text += f"\n\nJOB DESCRIPTION:\n{request.job_description}"
        
        # Run analysis
        logger.info(f"Running comprehensive ATS analysis (depth: {request.analysis_depth})")
        result = await agent.run(input_text)
        
        # Parse main analysis
        analysis_data = parse_json_response(result.data, "comprehensive analysis")
        
        # Job matching if job description provided
        job_match_data = None
        if request.job_description:
            job_agent = get_ats_agent("job_match")
            match_input = f"RESUME:\n{resume_text}\n\nJOB DESCRIPTION:\n{request.job_description}"
            match_result = await job_agent.run(match_input)
            job_match_data = parse_json_response(match_result.data, "job matching")
        
        processing_time = asyncio.get_event_loop().time() - start_time
        
        # Construct response
        response_data = {
            **analysis_data,
            "job_match": job_match_data,
            "processing_time": round(processing_time, 2),
            "analysis_depth": request.analysis_depth
        }
        
        return ATSAnalysisResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"ATS analysis error: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@router.post("/quick-score", response_model=ATSQuickScoreResponse)
async def quick_ats_score(request: QuickScoreRequest):
    """Quick ATS score for rapid feedback - JSON input"""
    start_time = asyncio.get_event_loop().time()
    
    try:
        resume_text = validate_resume_text(request.resume_text)
        
        agent = get_ats_agent("quick_score")
        result = await agent.run(resume_text)
        
        score_data = parse_json_response(result.data, "quick score")
        processing_time = asyncio.get_event_loop().time() - start_time
        
        return ATSQuickScoreResponse(
            **score_data,
            processing_time=round(processing_time, 2)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Quick score error: {e}")
        raise HTTPException(status_code=500, detail=f"Quick score failed: {str(e)}")

@router.post("/keyword-optimize", response_model=ATSKeywordOptimizeResponse)
async def optimize_keywords(request: KeywordOptimizeRequest):
    """Get detailed keyword optimization recommendations - JSON input"""
    try:
        resume_text = validate_resume_text(request.resume_text)
        
        if not request.job_description.strip():
            raise HTTPException(status_code=400, detail="Job description is required for keyword optimization")
        
        # Prepare context
        context = f"TARGET ROLE: {request.target_role}\n\n" if request.target_role else ""
        
        agent = get_ats_agent("keyword_optimize")
        input_text = f"{context}RESUME:\n{resume_text}\n\nJOB DESCRIPTION:\n{request.job_description}"
        
        result = await agent.run(input_text)
        keyword_data = parse_json_response(result.data, "keyword optimization")
        
        return ATSKeywordOptimizeResponse(**keyword_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Keyword optimization error: {e}")
        raise HTTPException(status_code=500, detail=f"Keyword optimization failed: {str(e)}")

@router.post("/compare", response_model=ATSComparisonResponse)
async def compare_resumes(request: ATSComparisonRequest):
    """Compare two resumes head-to-head - JSON input"""
    try:
        resume_a = validate_resume_text(request.resume_a)
        resume_b = validate_resume_text(request.resume_b)
        
        agent = get_ats_agent("comparison")
        
        input_text = f"RESUME A:\n{resume_a}\n\nRESUME B:\n{resume_b}"
        if request.job_description:
            input_text += f"\n\nJOB DESCRIPTION:\n{request.job_description}"
        
        input_text += f"\n\nCOMPARISON CRITERIA: {', '.join(request.comparison_criteria)}"
        
        result = await agent.run(input_text)
        comparison_data = parse_json_response(result.data, "resume comparison")
        
        return ATSComparisonResponse(**comparison_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Resume comparison error: {e}")
        raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")

@router.post("/bulk-analyze", response_model=ATSBulkAnalysisResponse)
async def bulk_analyze_resumes(request: ATSBulkAnalysisRequest):
    """Analyze multiple resumes in batch - JSON input"""
    start_time = asyncio.get_event_loop().time()
    
    try:
        if not request.resumes or len(request.resumes) == 0:
            raise HTTPException(status_code=400, detail="At least one resume is required")
        
        # Validate all resumes
        validated_resumes = [validate_resume_text(resume) for resume in request.resumes]
        
        agent = get_ats_agent("quick_score")  # Use quick score for bulk analysis
        
        scores = []
        ratings = []
        
        # Analyze each resume
        for i, resume in enumerate(validated_resumes):
            logger.info(f"Analyzing resume {i+1}/{len(validated_resumes)}")
            
            input_text = resume
            if request.job_description:
                input_text += f"\n\nJOB DESCRIPTION:\n{request.job_description}"
            
            result = await agent.run(input_text)
            score_data = parse_json_response(result.data, f"bulk analysis {i+1}")
            
            scores.append(score_data.get("score", 0))
            ratings.append(score_data.get("rating", "Unknown"))
        
        # Calculate summary statistics
        average_score = sum(scores) / len(scores) if scores else 0
        best_index = scores.index(max(scores)) if scores else 0
        worst_index = scores.index(min(scores)) if scores else 0
        
        # Generate insights
        insights = []
        if average_score >= 80:
            insights.append("Overall strong candidate pool with high ATS compatibility")
        elif average_score >= 60:
            insights.append("Decent candidate pool with room for optimization")
        else:
            insights.append("Candidate pool needs significant improvement for ATS compatibility")
        
        if max(scores) - min(scores) > 30:
            insights.append("Wide variation in resume quality - focus on standardizing best practices")
        
        processing_time = asyncio.get_event_loop().time() - start_time
        
        return ATSBulkAnalysisResponse(
            total_resumes=len(validated_resumes),
            average_score=round(average_score, 1),
            best_resume_index=best_index,
            worst_resume_index=worst_index,
            individual_scores=scores,
            individual_ratings=ratings,
            summary_insights=insights,
            processing_time=round(processing_time, 2)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Bulk analysis error: {e}")
        raise HTTPException(status_code=500, detail=f"Bulk analysis failed: {str(e)}")

@router.post("/ats-score-improvement")
async def ats_score_improvement_plan(request: ImprovementPlanRequest):
    """Generate a detailed improvement plan to reach target ATS score - JSON input"""
    try:
        current_resume = validate_resume_text(request.current_resume)
        
        if request.target_score < 0 or request.target_score > 100:
            raise HTTPException(status_code=400, detail="Target score must be between 0 and 100")
        
        improvement_prompt = f"""You are an expert resume coach specializing in ATS optimization.

Current situation: Analyze this resume and create a detailed improvement plan to reach an ATS score of {request.target_score} within {request.timeframe}.

Provide a structured improvement plan with:
1. Current score estimate
2. Gap analysis (what's preventing higher score)
3. Prioritized action items
4. Effort estimates for each improvement
5. Timeline for implementation
6. Expected score improvement for each change

RETURN EXACTLY THIS JSON FORMAT:
{{
  "current_estimated_score": <int 0-100>,
  "target_score": {request.target_score},
  "score_gap": <int>,
  "feasibility": "easy|moderate|challenging|difficult",
  "improvement_plan": [
    {{
      "priority": "critical|high|medium|low",
      "action": "<specific action to take>",
      "effort": "5 minutes|30 minutes|1 hour|2+ hours",
      "expected_improvement": <int points>,
      "category": "formatting|content|keywords|structure|contact_info",
      "instructions": "<detailed step-by-step instructions>"
    }}
  ],
  "timeline": {{
    "immediate": [<actions to do right now>],
    "day_1": [<actions for day 1>],
    "week_1": [<actions for week 1>],
    "ongoing": [<continuous improvements>]
  }},
  "success_metrics": [<how to measure improvement>],
  "estimated_final_score": <int 0-100>
}}"""

        agent = get_ats_agent("comprehensive", improvement_prompt)
        result = await agent.run(current_resume)
        
        improvement_data = parse_json_response(result.data, "improvement plan")
        
        return {
            **improvement_data,
            "timeframe": request.timeframe,
            "analysis_date": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Improvement plan error: {e}")
        raise HTTPException(status_code=500, detail=f"Improvement plan failed: {str(e)}")

@router.post("/industry-specific")
async def industry_specific_analysis(request: IndustryAnalysisRequest):
    """Industry-specific ATS analysis with tailored recommendations - JSON input"""
    try:
        resume_text = validate_resume_text(request.resume_text)
        
        # Industry-specific prompt
        industry_prompt = f"""You are an expert ATS analyzer specializing in the {request.industry} industry.

Analyze this resume for {request.role_level}-level positions in {request.industry}.

Consider industry-specific factors:
- Key skills and technologies valued in {request.industry}
- Common ATS systems used by {request.industry} companies
- Industry-specific terminology and keywords
- Career progression patterns typical in {request.industry}
- Certifications and qualifications valued in {request.industry}

{COMPREHENSIVE_ATS_PROMPT}

Focus especially on {request.industry}-specific optimization recommendations."""

        agent = get_ats_agent("comprehensive", industry_prompt)
        
        context = f"INDUSTRY: {request.industry}\nROLE LEVEL: {request.role_level}\n"
        if request.target_companies:
            context += f"TARGET COMPANIES: {', '.join(request.target_companies)}\n"
        
        input_text = f"{context}\nRESUME TEXT:\n{resume_text}"
        
        result = await agent.run(input_text)
        analysis_data = parse_json_response(result.data, "industry-specific analysis")
        
        return {
            **analysis_data,
            "industry": request.industry,
            "role_level": request.role_level,
            "target_companies": request.target_companies,
            "specialization": f"{request.industry} industry analysis"
        }
        
    except Exception as e:
        logger.error(f"Industry-specific analysis error: {e}")
        raise HTTPException(status_code=500, detail=f"Industry analysis failed: {str(e)}")

@router.post("/generate-report")
async def generate_detailed_report(request: ReportRequest):
    """Generate a comprehensive ATS analysis report - JSON input"""
    try:
        resume_text = validate_resume_text(request.resume_text)
        
        # Run comprehensive analysis
        analysis_request = ATSAnalysisRequest(
            resume_text=resume_text,
            job_description=request.job_description,
            analysis_depth="comprehensive"
        )
        
        analysis_result = await analyze_resume(analysis_request)
        
        # Generate formatted report
        report = {
            "report_metadata": {
                "generated_at": datetime.now().isoformat(),
                "report_type": request.report_format,
                "model_used": ATS_MODEL,
                "analysis_version": "1.0.0"
            },
            "executive_summary": {
                "overall_score": analysis_result.scores.overall_score,
                "rating": analysis_result.overall_rating,
                "key_strengths": [],
                "critical_issues": [],
                "top_recommendations": []
            },
            "detailed_analysis": analysis_result.dict(),
            "action_plan": {
                "immediate_actions": [],
                "short_term_goals": [],
                "long_term_improvements": []
            }
        }
        
        # Extract key insights for executive summary
        for issue in analysis_result.issues:
            if issue.type == "success" and issue.severity >= 4:
                report["executive_summary"]["key_strengths"].append(issue.text)
            elif issue.type == "error" or (issue.type == "warning" and issue.severity >= 4):
                report["executive_summary"]["critical_issues"].append(issue.text)
        
        report["executive_summary"]["top_recommendations"] = analysis_result.suggestions[:3]
        
        # Categorize action items
        for issue in analysis_result.issues:
            if issue.impact == "critical":
                report["action_plan"]["immediate_actions"].append(issue.fix_suggestion)
            elif issue.impact == "high":
                report["action_plan"]["short_term_goals"].append(issue.fix_suggestion)
            else:
                report["action_plan"]["long_term_improvements"].append(issue.fix_suggestion)
        
        return report
        
    except Exception as e:
        logger.error(f"Report generation error: {e}")
        raise HTTPException(status_code=500, detail=f"Report generation failed: {str(e)}")

# ============================================================================
# FILE UPLOAD ENDPOINTS - JSON BASED
# ============================================================================

@router.post("/upload-analyze")
async def upload_and_analyze(file: UploadFile = File(...)):
    """Upload resume file and return extracted text for client-side analysis"""
    try:
        # Validate file type
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")
        
        allowed_types = ['.txt', '.pdf', '.doc', '.docx']
        file_ext = os.path.splitext(file.filename)[1].lower()
        
        if file_ext not in allowed_types:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type: {file_ext}. Allowed: {', '.join(allowed_types)}"
            )
        
        # Read file content
        content = await file.read()
        
        # For now, assume text files (in production, you'd use document parsers)
        if file_ext == '.txt':
            resume_text = content.decode('utf-8')
        else:
            # Placeholder for document parsing
            raise HTTPException(
                status_code=501, 
                detail=f"Document parsing for {file_ext} files not yet implemented. Please use .txt files or convert to text format."
            )
        
        # Return extracted text for client to send to analysis endpoint
        return {
            "filename": file.filename,
            "file_type": file_ext,
            "extracted_text": resume_text,
            "character_count": len(resume_text),
            "word_count": len(resume_text.split()),
            "message": "File processed successfully. Use this extracted text for analysis."
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File upload error: {e}")
        raise HTTPException(status_code=500, detail=f"File processing failed: {str(e)}")

@router.post("/analyze-from-upload")
async def analyze_from_upload(request: FileUploadRequest):
    """Analyze resume from uploaded file text - JSON input"""
    try:
        # Use the main analysis endpoint
        analysis_request = ATSAnalysisRequest(
            resume_text=request.resume_text,
            job_description=request.job_description,
            target_role=request.target_role,
            analysis_depth=request.analysis_depth
        )
        
        return await analyze_resume(analysis_request)
        
    except Exception as e:
        logger.error(f"Upload analysis error: {e}")
        raise HTTPException(status_code=500, detail=f"Upload analysis failed: {str(e)}")

# ============================================================================
# STREAMING ENDPOINT - JSON BASED
# ============================================================================

@router.post("/analyze-stream")
async def analyze_resume_stream(request: ATSAnalysisRequest):
    """Stream ATS analysis results in real-time - JSON input"""
    try:
        resume_text = validate_resume_text(request.resume_text)
        
        async def stream_analysis():
            try:
                yield f"data: {json.dumps({'status': 'starting', 'message': 'Initializing ATS analysis...'})}\n\n"
                
                # Quick score first
                yield f"data: {json.dumps({'status': 'progress', 'message': 'Calculating quick score...', 'progress': 20})}\n\n"
                quick_agent = get_ats_agent("quick_score")
                quick_result = await quick_agent.run(resume_text)
                quick_data = parse_json_response(quick_result.data, "quick score stream")
                
                yield f"data: {json.dumps({'status': 'partial', 'type': 'quick_score', 'data': quick_data, 'progress': 40})}\n\n"
                
                # Full analysis
                yield f"data: {json.dumps({'status': 'progress', 'message': 'Running comprehensive analysis...', 'progress': 60})}\n\n"
                
                context = ""
                if request.target_role:
                    context += f"TARGET ROLE: {request.target_role}\n"
                if request.industry:
                    context += f"TARGET INDUSTRY: {request.industry}\n"
                
                agent = get_ats_agent("comprehensive")
                input_text = f"{context}\nRESUME TEXT:\n{resume_text}"
                
                result = await agent.run(input_text)
                analysis_data = parse_json_response(result.data, "comprehensive stream")
                
                yield f"data: {json.dumps({'status': 'partial', 'type': 'comprehensive', 'data': analysis_data, 'progress': 80})}\n\n"
                
                # Job matching if provided
                if request.job_description:
                    yield f"data: {json.dumps({'status': 'progress', 'message': 'Analyzing job match...', 'progress': 90})}\n\n"
                    
                    job_agent = get_ats_agent("job_match")
                    match_input = f"RESUME:\n{resume_text}\n\nJOB DESCRIPTION:\n{request.job_description}"
                    match_result = await job_agent.run(match_input)
                    job_data = parse_json_response(match_result.data, "job match stream")
                    
                    yield f"data: {json.dumps({'status': 'partial', 'type': 'job_match', 'data': job_data, 'progress': 95})}\n\n"
                
                yield f"data: {json.dumps({'status': 'complete', 'message': 'Analysis complete!', 'progress': 100})}\n\n"
                yield f"data: [DONE]\n\n"
                
            except Exception as e:
                yield f"data: {json.dumps({'status': 'error', 'message': str(e)})}\n\n"
        
        return StreamingResponse(
            stream_analysis(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*"
            }
        )
        
    except Exception as e:
        logger.error(f"Streaming analysis error: {e}")
        raise HTTPException(status_code=500, detail=f"Streaming analysis failed: {str(e)}")

# ============================================================================
# UTILITY AND MANAGEMENT ENDPOINTS
# ============================================================================

@router.get("/health")
async def ats_health_check():
    """Comprehensive health check for ATS analysis service"""
    try:
        if not GEMINI_API_KEY:
            return {
                "status": "unhealthy",
                "error": "GEMINI_API_KEY not configured",
                "timestamp": datetime.now().isoformat()
            }
        
        # Test with sample resume
        sample_resume = """John Smith
Software Engineer
<EMAIL> | (555) 123-4567

EXPERIENCE
Senior Software Developer at TechCorp (2022-2024)
• Developed React applications serving 100K+ users
• Implemented CI/CD pipelines reducing deployment time by 50%
• Led team of 3 developers using Agile methodologies

EDUCATION
Bachelor of Science in Computer Science
State University (2018-2022) - GPA: 3.8/4.0

SKILLS
• Programming: JavaScript, Python, Java, TypeScript
• Frameworks: React, Node.js, Express, Django
• Tools: Git, Docker, AWS, Jenkins"""

        # Test quick score endpoint
        agent = get_ats_agent("quick_score")
        start_time = asyncio.get_event_loop().time()
        result = await agent.run(sample_resume)
        response_time = asyncio.get_event_loop().time() - start_time
        
        # Parse response to ensure it's working
        try:
            test_data = parse_json_response(result.data, "health check")
            test_score = test_data.get("score", 0)
        except:
            test_score = "parsing_error"
        
        return {
            "status": "healthy",
            "service": "ATS Resume Analysis",
            "model": ATS_MODEL,
            "response_time": round(response_time, 2),
            "test_score": test_score,
            "endpoints_available": [
                "/analyze", "/quick-score", "/keyword-optimize", 
                "/compare", "/bulk-analyze", "/ats-score-improvement",
                "/industry-specific", "/generate-report", "/upload-analyze",
                "/analyze-stream"
            ],
            "cached_agents": len(_ats_agent_cache),
            "input_format": "JSON for all endpoints",
            "features": [
                "Comprehensive ATS analysis",
                "Job description matching", 
                "Keyword optimization",
                "Resume comparison",
                "Bulk analysis",
                "Improvement planning",
                "Industry-specific analysis",
                "Report generation",
                "File upload support",
                "Real-time streaming"
            ],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "model": ATS_MODEL,
            "timestamp": datetime.now().isoformat()
        }

@router.get("/stats")
async def get_ats_stats():
    """Get ATS service statistics and usage information"""
    return {
        "service_info": {
            "name": "Gemini ATS Resume Analyzer",
            "version": "2.0.0",
            "model": ATS_MODEL,
            "temperature": DEFAULT_TEMPERATURE,
            "input_format": "JSON only"
        },
        "capabilities": {
            "comprehensive_analysis": True,
            "job_matching": True,
            "keyword_optimization": True,
            "resume_comparison": True,
            "bulk_processing": True,
            "improvement_planning": True,
            "industry_specific_analysis": True,
            "report_generation": True,
            "file_upload": True,
            "streaming_analysis": True
        },
        "limits": {
            "max_resume_length": 50000,
            "max_bulk_resumes": 10,
            "min_resume_length": 50,
            "supported_file_types": [".txt", ".pdf", ".doc", ".docx"],
            "file_upload_note": "Currently supports .txt files only. PDF/Word parsing coming soon."
        },
        "performance": {
            "cached_agents": len(_ats_agent_cache),
            "typical_analysis_time": "2-5 seconds",
            "quick_score_time": "1-2 seconds",
            "bulk_analysis_time": "2-3 seconds per resume"
        },
        "cost_optimization": {
            "model_used": "Flash model only",
            "estimated_cost_per_analysis": "$0.001-0.003",
            "bulk_discount": "Shared context reduces per-resume cost"
        },
        "api_design": {
            "input_format": "JSON for all endpoints",
            "authentication": "None required (development)",
            "rate_limiting": "None (development)",
            "cors_enabled": True
        }
    }

@router.get("/sample-data")
async def get_sample_data():
    """Get sample resumes and job descriptions for testing"""
    return {
        "sample_resumes": {
            "software_engineer": """John Doe
Senior Software Engineer
<EMAIL> | (555) 123-4567 | linkedin.com/in/johndoe

PROFESSIONAL SUMMARY
Experienced software engineer with 5+ years developing scalable web applications using React, Node.js, and cloud technologies. Proven track record of delivering high-quality software solutions and leading cross-functional teams.

TECHNICAL SKILLS
• Programming Languages: JavaScript, TypeScript, Python, Java
• Frontend: React, Redux, HTML5, CSS3, Next.js, Vue.js
• Backend: Node.js, Express.js, Django, RESTful APIs, GraphQL
• Databases: PostgreSQL, MongoDB, Redis, MySQL
• Cloud & DevOps: AWS, Docker, Kubernetes, CI/CD, Jenkins
• Tools: Git, Jest, Webpack, Agile/Scrum

PROFESSIONAL EXPERIENCE

Senior Software Engineer | TechCorp Inc. | 2022 - Present
• Led development of customer-facing web application serving 100K+ daily active users
• Improved application performance by 40% through code optimization and caching strategies
• Mentored 3 junior developers and established coding standards for the team
• Collaborated with product managers and designers in Agile environment

Software Engineer | StartupXYZ | 2020 - 2022
• Built and maintained microservices architecture handling 1M+ API requests daily
• Implemented automated testing suite, reducing bug reports by 60%
• Contributed to architectural decisions for scalable cloud infrastructure
• Developed real-time data processing pipeline using React and WebSocket

Junior Developer | WebSolutions LLC | 2019 - 2020
• Created responsive web applications using React and modern JavaScript
• Integrated third-party APIs and payment processing systems
• Participated in code reviews and followed test-driven development practices

EDUCATION
Bachelor of Science in Computer Science
University of Technology | 2015 - 2019
GPA: 3.8/4.0 | Dean's List: 6 semesters

PROJECTS
E-Commerce Platform (2023)
• Full-stack application with React, Node.js, and PostgreSQL
• Implemented user authentication, payment processing, and admin dashboard
• Deployed on AWS with Docker containers and CI/CD pipeline

CERTIFICATIONS
• AWS Certified Developer Associate (2023)
• Google Cloud Professional Developer (2022)""",

            "marketing_manager": """Sarah Johnson
Marketing Manager
<EMAIL> | (555) 987-6543 | linkedin.com/in/sarahjohnson

PROFESSIONAL SUMMARY
Results-driven marketing professional with 6+ years of experience in digital marketing, brand management, and campaign optimization. Expertise in data-driven marketing strategies that increase brand awareness and drive revenue growth.

CORE COMPETENCIES
• Digital Marketing: SEO/SEM, Social Media Marketing, Email Marketing
• Analytics: Google Analytics, Adobe Analytics, Marketing Automation
• Content Strategy: Content Creation, Brand Messaging, Campaign Development
• Tools: HubSpot, Salesforce, Mailchimp, Hootsuite, Canva
• Skills: Project Management, A/B Testing, Lead Generation, CRM

PROFESSIONAL EXPERIENCE

Marketing Manager | GrowthCo | 2021 - Present
• Increased website traffic by 150% through comprehensive SEO strategy
• Managed $500K annual marketing budget across multiple channels
• Led team of 4 marketing specialists and coordinated cross-departmental initiatives
• Improved lead conversion rate by 35% through marketing automation implementation

Digital Marketing Specialist | BrandBuilder Inc. | 2019 - 2021
• Developed and executed social media campaigns reaching 2M+ impressions monthly
• Created content marketing strategy that generated 300% increase in organic traffic
• Managed PPC campaigns with average ROI of 250%
• Collaborated with sales team to optimize lead scoring and nurturing processes

EDUCATION
Bachelor of Arts in Marketing
State University | 2014 - 2018
Magna Cum Laude | Marketing Club President

CERTIFICATIONS
• Google Ads Certified (2023)
• HubSpot Content Marketing Certification (2022)
• Facebook Blueprint Certification (2021)""",

            "poor_example": """John
Developer
Email missing

Experience
Worked at company
Did programming
Made websites

Education
Went to college

Skills
Programming""",

            "excellent_example": """Michael Chen
Senior Data Scientist
<EMAIL> | (************* | linkedin.com/in/michaelchen | github.com/mchen

PROFESSIONAL SUMMARY
Senior Data Scientist with 7+ years of experience building machine learning models and data pipelines that drive business decisions. Expertise in Python, R, and cloud platforms with a track record of delivering $2M+ in measurable business value through advanced analytics and AI solutions.

TECHNICAL SKILLS
• Programming: Python, R, SQL, Scala, Java
• Machine Learning: Scikit-learn, TensorFlow, PyTorch, XGBoost
• Data Engineering: Spark, Airflow, Kafka, ETL pipelines
• Cloud Platforms: AWS (S3, EC2, SageMaker), GCP, Azure
• Databases: PostgreSQL, MongoDB, Redis, Snowflake
• Visualization: Tableau, Power BI, Matplotlib, Plotly

PROFESSIONAL EXPERIENCE

Senior Data Scientist | DataTech Solutions | 2021 - Present
• Built recommendation engine increasing user engagement by 45% and revenue by $1.2M annually
• Led cross-functional team of 5 engineers to deploy ML models serving 500K+ daily predictions
• Implemented A/B testing framework reducing experiment cycle time by 60%
• Mentored 2 junior data scientists and established ML best practices

Data Scientist | Analytics Pro | 2019 - 2021
• Developed churn prediction model achieving 89% accuracy, preventing $800K in lost revenue
• Created automated reporting dashboards used by 50+ stakeholders daily
• Optimized marketing spend allocation using attribution modeling, improving ROI by 25%

EDUCATION
Master of Science in Data Science
Carnegie Mellon University | 2017 - 2019
GPA: 3.9/4.0 | Thesis: "Deep Learning for Time Series Forecasting"

Bachelor of Science in Statistics
UC Berkeley | 2013 - 2017
Magna Cum Laude | Phi Beta Kappa

CERTIFICATIONS
• AWS Certified Machine Learning - Specialty (2023)
• Google Cloud Professional Data Engineer (2022)
• Tableau Desktop Specialist (2021)"""
        },
        
        "sample_job_descriptions": {
            "software_engineer": """Senior Software Engineer - Full Stack
TechCorp Inc.

We are seeking a skilled Senior Software Engineer to join our growing engineering team. You will be responsible for developing and maintaining our web applications using modern technologies.

Required Skills:
• 3+ years of experience with JavaScript, TypeScript
• Strong experience with React, Node.js
• Experience with cloud platforms (AWS, GCP, Azure)
• Knowledge of databases (PostgreSQL, MongoDB)
• Familiarity with CI/CD pipelines and DevOps practices
• Experience with Agile/Scrum methodologies

Preferred Skills:
• Experience with microservices architecture
• Knowledge of containerization (Docker, Kubernetes)
• Experience with testing frameworks (Jest, Cypress)
• Understanding of security best practices
• Bachelor's degree in Computer Science or related field

Responsibilities:
• Design and develop scalable web applications
• Collaborate with cross-functional teams
• Mentor junior developers
• Participate in code reviews and architectural decisions
• Implement automated testing and CI/CD pipelines""",

            "marketing_manager": """Marketing Manager - Digital Growth
GrowthCo

We are looking for an experienced Marketing Manager to lead our digital marketing efforts and drive customer acquisition and retention.

Required Qualifications:
• 4+ years of digital marketing experience
• Proven track record with SEO, SEM, and social media marketing
• Experience with marketing automation tools (HubSpot, Marketo)
• Strong analytical skills with Google Analytics, Adobe Analytics
• Budget management experience ($100K+ annual marketing budgets)
• Bachelor's degree in Marketing, Business, or related field

Preferred Qualifications:
• Experience in B2B SaaS marketing
• Knowledge of CRM systems (Salesforce)
• Content marketing and copywriting skills
• A/B testing and conversion optimization experience
• Project management certification

Key Responsibilities:
• Develop and execute comprehensive digital marketing strategies
• Manage multi-channel marketing campaigns
• Analyze campaign performance and optimize for ROI
• Lead and mentor marketing team members
• Collaborate with sales team on lead generation initiatives""",

            "data_scientist": """Senior Data Scientist
DataTech Solutions

Join our growing data science team to build machine learning solutions that drive business impact.

Required Skills:
• 5+ years of data science experience
• Expert-level Python and R programming
• Strong machine learning background (scikit-learn, TensorFlow, PyTorch)
• Experience with cloud platforms (AWS, GCP, Azure)
• SQL and database experience
• Statistical analysis and hypothesis testing

Preferred Skills:
• PhD in Data Science, Statistics, or related field
• Experience with big data tools (Spark, Hadoop)
• MLOps and model deployment experience
• A/B testing and experimentation
• Business acumen and stakeholder communication

Responsibilities:
• Build and deploy machine learning models
• Analyze large datasets to drive business insights
• Collaborate with engineering teams on implementation
• Present findings to executive leadership
• Mentor junior data scientists"""
        },
        
        "testing_tips": {
            "quick_tests": [
                "Start with health check to verify service",
                "Test quick-score with sample resume",
                "Try comprehensive analysis with job description",
                "Test keyword optimization with job-resume pair"
            ],
            "error_testing": [
                "Send resume with < 50 characters",
                "Send invalid target_score (> 100)",
                "Test with very long resume (> 50k chars)",
                "Try empty job description for keyword optimization"
            ],
            "json_examples": {
                "quick_score": {
                    "resume_text": "Use sample_resume from above"
                },
                "comprehensive": {
                    "resume_text": "Use sample_resume",
                    "job_description": "Use sample_job_description",
                    "target_role": "Software Engineer",
                    "analysis_depth": "comprehensive"
                },
                "keyword_optimize": {
                    "resume_text": "Use poor_example resume",
                    "job_description": "Use software_engineer job description",
                    "target_role": "Software Engineer"
                }
            }
        }
    }

@router.delete("/cache")
async def clear_ats_cache():
    """Clear the ATS agent cache"""
    global _ats_agent_cache
    cache_count = len(_ats_agent_cache)
    _ats_agent_cache.clear()
    
    return {
        "message": f"Cleared {cache_count} cached ATS agents",
        "cache_size": len(_ats_agent_cache),
        "timestamp": datetime.now().isoformat()
    }

@router.get("/prompts")
async def get_prompts_info():
    """Get information about the AI prompts used"""
    return {
        "prompts_available": {
            "comprehensive": "Full detailed ATS analysis with all metrics",
            "job_match": "Job description to resume matching analysis", 
            "quick_score": "Fast ATS compatibility scoring",
            "keyword_optimize": "Keyword optimization recommendations",
            "comparison": "Head-to-head resume comparison"
        },
        "prompt_characteristics": {
            "comprehensive": {
                "focus": "Complete ATS analysis with detailed scoring breakdown",
                "output_sections": ["scores", "structure", "keywords", "contact_info", "issues", "suggestions"],
                "typical_response_time": "3-5 seconds",
                "use_case": "Full resume optimization"
            },
            "quick_score": {
                "focus": "Rapid ATS score with key insights",
                "output_sections": ["score", "rating", "top_strength", "top_weakness", "quick_win"],
                "typical_response_time": "1-2 seconds", 
                "use_case": "Initial resume screening"
            },
            "keyword_optimize": {
                "focus": "Targeted keyword recommendations",
                "output_sections": ["critical_missing", "important_missing", "placement_strategy"],
                "typical_response_time": "2-3 seconds",
                "use_case": "Job-specific optimization"
            }
        },
        "input_format": "All endpoints accept JSON request bodies",
        "customization": {
            "temperature": DEFAULT_TEMPERATURE,
            "model": ATS_MODEL,
            "context_aware": True,
            "industry_specific": True,
            "role_specific": True
        }
    }

# Add router metadata
router.tags = ["ATS Resume Analysis"]
router.description = """
Comprehensive ATS (Applicant Tracking System) resume analysis service powered by Gemini AI.

**All endpoints accept JSON request bodies for modern API design.**

This service provides detailed analysis of resume compatibility with ATS systems, keyword optimization, 
job matching, and actionable recommendations for improvement.

Features:
- Comprehensive ATS scoring and analysis
- Job description matching and keyword optimization  
- Resume comparison and bulk analysis
- Industry-specific analysis
- Improvement planning and reporting
- File upload support (text extraction)
- Real-time streaming analysis
- JSON-only API design for frontend integration
"""