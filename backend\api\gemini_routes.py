"""
Dedicated Gemini Router using Pydantic AI
api/gemini_routes.py
"""

import os
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
from pydantic_ai import Agent

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/gemini", tags=["gemini-chat"])

# Environment Configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
DEFAULT_MODEL = os.getenv("GEMINI_DEFAULT_MODEL", "google-gla:gemini-2.5-flash-preview-05-20")
DEFAULT_TEMPERATURE = float(os.getenv("GEMINI_TEMPERATURE", "0.7"))
DEFAULT_MAX_TOKENS = int(os.getenv("GEMINI_MAX_TOKENS", "2048"))

# Available Gemini models (Updated to current models with correct format)
GEMINI_MODELS = [
     
    "google-gla:gemini-2.5-flash-preview-05-20",
    "google-gla:gemini-2.5-pro-preview-05-06"
]

# Pydantic Models
class GeminiChatRequest(BaseModel):
    message: str = Field(..., description="User message/question")
    model: str = Field(default=DEFAULT_MODEL, description="Gemini model to use")
    temperature: float = Field(default=DEFAULT_TEMPERATURE, ge=0.0, le=2.0)
    max_tokens: int = Field(default=DEFAULT_MAX_TOKENS, ge=1, le=8192)
    system_prompt: Optional[str] = Field(None, description="System prompt for the agent")

class GeminiChatResponse(BaseModel):
    response: str
    model: str
    timestamp: str
    processing_time: float
    tokens_used: Optional[int] = None
    cost_estimate: Optional[float] = None

class GeminiStreamRequest(BaseModel):
    message: str = Field(..., description="User message/question")
    model: str = Field(default=DEFAULT_MODEL, description="Gemini model to use")
    system_prompt: Optional[str] = Field(None, description="System prompt")

class GeminiModelInfo(BaseModel):
    name: str
    description: str
    input_cost_per_1m_tokens: Optional[float] = None
    output_cost_per_1m_tokens: Optional[float] = None
    context_window: Optional[int] = None

# Model information (Updated for Gemini 2.5 with correct keys)
GEMINI_MODEL_INFO = {
    "google-gla:gemini-2.5-flash-preview-05-20": GeminiModelInfo(
        name="google-gla:gemini-2.5-flash-preview-05-20",
        description="Latest fast, versatile model - preview version",
        input_cost_per_1m_tokens=0.075,
        output_cost_per_1m_tokens=0.30,
        context_window=1000000
    ),
    "google-gla:gemini-2.5-pro-preview-05-06": GeminiModelInfo(
        name="google-gla:gemini-2.5-pro-preview-05-06", 
        description="Latest advanced model for complex reasoning - preview version",
        input_cost_per_1m_tokens=1.25,
        output_cost_per_1m_tokens=5.00,
        context_window=2000000
    )
}

# Agent cache for performance
_agent_cache: Dict[str, Agent] = {}

def get_gemini_agent(model: str, system_prompt: Optional[str] = None, temperature: float = 0.7) -> Agent:
    """Get or create a Gemini agent with caching - Simplified Pydantic AI approach"""
    if not GEMINI_API_KEY:
        raise HTTPException(status_code=503, detail="GEMINI_API_KEY not configured")
    
    # Create cache key
    cache_key = f"{model}_{hash(system_prompt or '')}_{temperature}"
    
    if cache_key not in _agent_cache:
        # Default system prompt if none provided
        default_system = "You are a helpful AI assistant. Provide clear, accurate, and concise responses."
        final_system_prompt = system_prompt or default_system
        
        try:
            # Create agent using the simplified Pydantic AI syntax
            agent = Agent(
                model=model,  # e.g., 'google-gla:gemini-2.5-flash-preview-05-20'
                system_prompt=final_system_prompt
            )
            
            _agent_cache[cache_key] = agent
            logger.info(f"Created new Gemini agent: {model}")
            
        except Exception as e:
            logger.error(f"Failed to create Gemini agent: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to initialize Gemini agent: {str(e)}")
    
    return _agent_cache[cache_key]

def estimate_cost(input_tokens: int, output_tokens: int, model: str) -> Optional[float]:
    """Estimate the cost of the API call"""
    model_info = GEMINI_MODEL_INFO.get(model)
    if not model_info or not model_info.input_cost_per_1m_tokens:
        return None
    
    input_cost = (input_tokens / 1_000_000) * model_info.input_cost_per_1m_tokens
    output_cost = (output_tokens / 1_000_000) * model_info.output_cost_per_1m_tokens
    
    return round(input_cost + output_cost, 6)

@router.post("/chat", response_model=GeminiChatResponse)
async def gemini_chat(request: GeminiChatRequest):
    """Chat with Gemini using Pydantic AI"""
    start_time = asyncio.get_event_loop().time()
    
    try:
        # Validate model
        if request.model not in GEMINI_MODELS:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported model: {request.model}. Available: {GEMINI_MODELS}"
            )
        
        # Get agent
        agent = get_gemini_agent(
            model=request.model,
            system_prompt=request.system_prompt,
            temperature=request.temperature
        )
        
        # Run the agent - Use the correct Pydantic AI method
        logger.info(f"Running Gemini {request.model} with message: {request.message[:100]}...")
        
        # Use async run and access the data attribute
        result = await agent.run(request.message)
        
        processing_time = asyncio.get_event_loop().time() - start_time
        
        # Extract response using the correct attribute
        response_text = result.data
        
        # Estimate tokens and cost
        input_tokens = len(request.message.split())
        output_tokens = len(response_text.split())
        cost_estimate = estimate_cost(input_tokens, output_tokens, request.model)
        
        return GeminiChatResponse(
            response=response_text,
            model=request.model,
            timestamp=datetime.now().isoformat(),
            processing_time=round(processing_time, 2),
            tokens_used=input_tokens + output_tokens,
            cost_estimate=cost_estimate
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Gemini chat error: {e}")
        raise HTTPException(status_code=500, detail=f"Gemini chat failed: {str(e)}")

@router.post("/chat/stream")
async def gemini_stream_chat(request: GeminiStreamRequest):
    """Stream chat with Gemini"""
    try:
        # Get agent
        agent = get_gemini_agent(
            model=request.model,
            system_prompt=request.system_prompt
        )
        
        async def stream_response():
            try:
                # Stream the response
                async for chunk in agent.run_stream(request.message):
                    if hasattr(chunk, 'data') and chunk.data:
                        yield f"data: {chunk.data}\n\n"
                
                yield f"data: [DONE]\n\n"
                
            except Exception as e:
                yield f"data: Error: {str(e)}\n\n"
        
        return StreamingResponse(
            stream_response(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive"
            }
        )
    
    except Exception as e:
        logger.error(f"Gemini streaming error: {e}")
        raise HTTPException(status_code=500, detail=f"Streaming failed: {str(e)}")

@router.post("/quick")
async def gemini_quick_chat(
    message: str,
    model: str = "google-gla:gemini-2.5-flash-preview-05-20"  # Updated to correct format
):
    """Quick chat with Gemini for simple questions"""
    try:
        agent = get_gemini_agent(
            model=model,
            system_prompt="Be concise and direct. Provide brief, accurate answers."
        )
        
        start_time = asyncio.get_event_loop().time()
        result = await agent.run(message)
        processing_time = asyncio.get_event_loop().time() - start_time
        
        return {
            "response": result.data,
            "model": model,
            "processing_time": round(processing_time, 2),
            "timestamp": datetime.now().isoformat(),
            "mode": "quick"
        }
    
    except Exception as e:
        return {"error": str(e), "model": model}

@router.post("/analyze")
async def gemini_analyze(
    text: str,
    analysis_type: str = "general",
    model: str = "google-gla:gemini-2.5-pro-preview-05-06"  # Updated to correct format
):
    """Analyze text with specific prompts"""
    
    analysis_prompts = {
        "general": "Analyze the following text and provide key insights:",
        "sentiment": "Analyze the sentiment of the following text (positive, negative, neutral) and explain why:",
        "summary": "Provide a concise summary of the following text:",
        "financial": "Analyze this financial text and explain the key concepts, calculations, and implications:",
        "academic": "Provide an academic analysis of the following text, including key arguments and evidence:",
        "business": "Analyze this from a business perspective, including opportunities, risks, and recommendations:"
    }
    
    system_prompt = analysis_prompts.get(analysis_type, analysis_prompts["general"])
    
    try:
        agent = get_gemini_agent(model=model, system_prompt=system_prompt)
        result = await agent.run(text)
        
        return {
            "analysis": result.data,
            "analysis_type": analysis_type,
            "model": model,
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models")
async def get_gemini_models():
    """Get available Gemini models with details"""
    return {
        "available_models": list(GEMINI_MODEL_INFO.values()),
        "default_model": DEFAULT_MODEL,
        "total_models": len(GEMINI_MODELS)
    }

@router.get("/health")
async def gemini_health_check():
    """Health check for Gemini service"""
    try:
        if not GEMINI_API_KEY:
            return {
                "status": "unhealthy",
                "error": "GEMINI_API_KEY not configured",
                "timestamp": datetime.now().isoformat()
            }
        
        # Quick test with the fastest model
        agent = get_gemini_agent("google-gla:gemini-2.5-flash-preview-05-20", "Respond with 'OK' only.")
        
        start_time = asyncio.get_event_loop().time()
        result = await agent.run("Test")
        response_time = asyncio.get_event_loop().time() - start_time
        
        return {
            "status": "healthy",
            "gemini_connection": "ok",
            "response_time": round(response_time, 2),
            "test_response": result.data[:50],
            "available_models": len(GEMINI_MODELS),
            "cached_agents": len(_agent_cache),
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/usage")
async def get_usage_info():
    """Get usage and cost information"""
    return {
        "model_pricing": GEMINI_MODEL_INFO,
        "recommendations": {
            "fastest": "google-gla:gemini-2.5-flash-preview-05-20",
            "balanced": "google-gla:gemini-2.5-flash-preview-05-20", 
            "most_capable": "google-gla:gemini-2.5-pro-preview-05-06",
            "default": "google-gla:gemini-2.5-flash-preview-05-20"
        },
        "cost_tips": [
            "Use gemini-2.5-flash for most tasks (fastest and cost-effective)",
            "Use gemini-2.5-pro for complex reasoning and analysis", 
            "These are preview models - pricing may change",
            "Shorter prompts reduce costs"
        ]
    }

@router.delete("/cache")
async def clear_agent_cache():
    """Clear the agent cache (useful for development)"""
    global _agent_cache
    cache_count = len(_agent_cache)
    _agent_cache.clear()
    
    return {
        "message": f"Cleared {cache_count} cached agents",
        "timestamp": datetime.now().isoformat()
    }