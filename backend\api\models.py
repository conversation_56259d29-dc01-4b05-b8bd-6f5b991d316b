# api/models.py
from pydantic import BaseModel, EmailStr, Field
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

class AssessmentRequest(BaseModel):
    """Request model for career assessment API"""
    email: EmailStr = Field(..., description="Candidate's email address")

class StyleScores(BaseModel):
    """Model for style scores"""
    REFLECTIVE: Optional[int] = None
    RESERVED: Optional[int] = None
    STEADY: Optional[int] = None
    PRECISE: Optional[int] = None
    DIRECT: Optional[int] = None
    OUTGOING: Optional[int] = None
    DYNAMIC: Optional[int] = None
    PIONEERING: Optional[int] = None
    
    class Config:
        extra = "allow"  # Allow additional fields

class MotivatorScores(BaseModel):
    """Model for motivator scores"""
    INSTINCTIVE: Optional[int] = None
    INTELLECTUAL: Optional[int] = None
    SELFLESS: Optional[int] = None
    RESOURCEFUL: Optional[int] = None
    HARMONIOUS: Optional[int] = None
    OBJECTIVE: Optional[int] = None
    INTENTIONAL: Optional[int] = None
    ALTRUISTIC: Optional[int] = None
    COMMANDING: Optional[int] = None
    COLLABORATIVE: Optional[int] = None
    RECEPTIVE: Optional[int] = None
    STRUCTURED: Optional[int] = None
    
    class Config:
        extra = "allow"  # Allow additional fields

class GeneralDescription(BaseModel):
    description: str = Field(..., description="General description of the individual")

class Emotion(BaseModel):
    name: str = Field(..., description="Name of the emotion")
    description: str = Field(..., description="Description of the emotion")

class KeyEmotions(BaseModel):
    emotions: List[Emotion] = Field(..., description="Key emotions with descriptions")

class Strength(BaseModel):
    name: str = Field(..., description="Name of the strength")
    description: str = Field(..., description="Description of the strength")

class Strengths(BaseModel):
    strengths: List[Strength] = Field(..., description="List of individual strengths")
    critical_actions: List[str] = Field(..., description="Critical actions for success")

class Limitation(BaseModel):
    name: str = Field(..., description="Name of the limitation")
    description: str = Field(..., description="Description of the limitation")

class Limitations(BaseModel):
    limitations: List[Limitation] = Field(..., description="List of individual limitations")
    critical_causes: List[str] = Field(..., description="Critical causes of derailment")
    action_steps: List[str] = Field(..., description="Steps to overcome limitations")

class Communication(BaseModel):
    style: str = Field(..., description="Communication style description")

class Stressor(BaseModel):
    name: str = Field(..., description="Name of the stressor")
    description: str = Field(..., description="Description of the stressor")
    mitigation_steps: List[str] = Field(..., description="Steps to overcome the stressor")

class StressPerception(BaseModel):
    perception: str = Field(..., description="How others perceive the individual under stress")

class StudyManifestations(BaseModel):
    enablers: List[str] = Field(..., description="How strengths manifest in studies")
    derailers: List[str] = Field(..., description="How limitations manifest in studies")
    improvement_steps: List[str] = Field(..., description="Steps to overcome limitations in studies")

class SectionI(BaseModel):
    """Section I output model - About You"""
    general_description: GeneralDescription
    key_emotions: KeyEmotions
    strengths: Strengths
    limitations: Limitations
    communication: Communication
    stressors: List[Stressor]
    stress_perception: StressPerception
    study_manifestations: StudyManifestations

class WorkplaceManifestations(BaseModel):
    enablers: List[str] = Field(..., description="How strengths manifest in workplace")
    derailers: List[str] = Field(..., description="How limitations manifest in workplace")

class ImpactStrategies(BaseModel):
    academic: List[str] = Field(..., description="Strategies for high impact in academic settings")
    professional: List[str] = Field(..., description="Strategies for high impact in professional settings")

class CompetencyChallenge(BaseModel):
    challenges: List[str] = Field(..., description="Challenges related to the competency")
    improvements: List[str] = Field(..., description="Areas for improvement")

class CompetencyAssessment(BaseModel):
    networking: CompetencyChallenge = Field(..., description="Networking skills assessment")
    teamwork: CompetencyChallenge = Field(..., description="Teamwork assessment")
    conflict_handling: CompetencyChallenge = Field(..., description="Conflict handling assessment")
    time_management: CompetencyChallenge = Field(..., description="Time management assessment")

class IdealWorkEnvironment(BaseModel):
    description: str = Field(..., description="Description of ideal work environment")

class JobRole(BaseModel):
    title: str = Field(..., description="Job role title")

class CareerJustification(BaseModel):
    pros: List[str] = Field(..., description="Pros of the career")
    cons: List[str] = Field(..., description="Cons of the career")

class CareerAssessment(BaseModel):
    group_name: str = Field(..., description="Name of the career group")
    mba_specialization: List[str] = Field(..., description="Required MBA specialization")
    roles: List[JobRole] = Field(..., description="Top job roles")
    justification: CareerJustification = Field(..., description="Career justification")
    suitability: str = Field(..., description="Suitability category (Green/Orange/Red)")

class SpecializationRecommendation(BaseModel):
    career: str = Field(..., description="Career name")
    specialization: List[str] = Field(..., description="Recommended specializations")

class InterviewPreparation(BaseModel):
    strategies: List[str] = Field(..., description="Interview preparation strategies")

class ResumeOutline(BaseModel):
    content: str = Field(..., description="Resume outline content")

class ActivityItem(BaseModel):
    """Model for a detailed activity item with sub-activities"""
    title: str = Field(..., description="Main activity title")
    sub_activities: List[str] = Field(..., description="List of sub-activities or steps")

class ImplementationActivities(BaseModel):
    """Model for implementation activities section"""
    activities: List[Union[str, ActivityItem]] = Field(..., description="Top activities for next 30-60 days")

class SectionII(BaseModel):
    """Section II output model - The Future"""
    workplace_manifestations: WorkplaceManifestations
    impact_strategies: ImpactStrategies
    competency_assessment: CompetencyAssessment
    ideal_work_environment: IdealWorkEnvironment
    career_ranking: List[CareerAssessment]
    specialization_recommendations: List[SpecializationRecommendation]
    interview_preparation: InterviewPreparation
    resume_outline: ResumeOutline
    implementation_activities: ImplementationActivities

class AssessmentOutput(BaseModel):
    """Complete assessment output model"""
    individual_name: Optional[str] = Field(None, description="Name of the individual assessed")
    section_i: SectionI = Field(..., description="Section I - About You")
    section_ii: SectionII = Field(..., description="Section II - The Future")

class AssessmentResponse(BaseModel):
    """Response model for career assessment API"""
    email: EmailStr = Field(..., description="Candidate's email address")
    individual_name: str = Field(..., description="Candidate's name")
    assessment: Dict[str, Any] = Field(..., description="Complete assessment results")
    timestamp: str = Field(..., description="Timestamp of assessment generation")
    cached: Optional[bool] = Field(False, description="Whether the assessment was served from cache")
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "individual_name": "Jane Doe",
                "assessment": {
                    "section_i": {
                        "general_description": {"description": "You are a detail-oriented individual..."}
                    },
                    "section_ii": {
                        "workplace_manifestations": {
                            "enablers": ["Strong analytical abilities", "Attention to detail"],
                            "derailers": ["May overthink decisions", "Reluctance to speak up"]
                        }
                    }
                },
                "timestamp": "2023-04-15T14:30:45.123456",
                "cached": False
            }
        }

class StatsResponse(BaseModel):
    """Response model for API statistics"""
    provider_type: str = Field(..., description="Type of data provider being used")
    cache_enabled: bool = Field(..., description="Whether caching is enabled")
    cache_max_age_days: int = Field(..., description="Maximum age for cached assessments in days")
    total_assessments: Optional[int] = Field(None, description="Total number of assessments generated")
    unique_emails: Optional[int] = Field(None, description="Number of unique emails assessed")
    assessments_last_24h: Optional[int] = Field(None, description="Number of assessments in the last 24 hours")
    timestamp: str = Field(..., description="Timestamp of statistics generation")

class ErrorResponse(BaseModel):
    """Standard error response model"""
    error: str = Field(..., description="Error message")
    details: Optional[str] = Field(None, description="Additional error details")