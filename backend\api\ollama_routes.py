"""
Fixed Ollama FastAPI Routes - Syntax Error Corrected
"""

import os
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, AsyncGenerator
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
import requests
import json

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router for Ollama routes
router = APIRouter(prefix="/api/ollama", tags=["ollama"])

# Get configuration from environment variables
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
DEFAULT_MODEL = os.getenv("DEFAULT_OLLAMA_MODEL", "qwen3:8b")
DEFAULT_TEMPERATURE = float(os.getenv("OLLAMA_TEMPERATURE", "0.7"))
DEFAULT_MAX_TOKENS = int(os.getenv("OLLAMA_MAX_TOKENS", "2048"))

# Pydantic models for request/response
class ChatRequest(BaseModel):
    message: str = Field(..., description="User message/question")
    model: str = Field(default=DEFAULT_MODEL, description="Ollama model to use")
    temperature: float = Field(default=DEFAULT_TEMPERATURE, ge=0.0, le=2.0, description="Model temperature")
    max_tokens: int = Field(default=DEFAULT_MAX_TOKENS, ge=1, le=8192, description="Maximum tokens to generate")
    system_prompt: Optional[str] = Field(None, description="Custom system prompt")
    stream: bool = Field(default=False, description="Enable streaming response")

class ChatResponse(BaseModel):
    response: str
    model: str
    timestamp: str
    processing_time: float
    tokens_used: Optional[int] = None

class StreamChatRequest(BaseModel):
    message: str = Field(..., description="User message/question")
    model: str = Field(default=DEFAULT_MODEL, description="Ollama model to use")
    temperature: float = Field(default=DEFAULT_TEMPERATURE, ge=0.0, le=2.0)
    max_tokens: int = Field(default=DEFAULT_MAX_TOKENS, ge=1, le=8192)
    system_prompt: Optional[str] = Field(None, description="Custom system prompt")

class BatchChatRequest(BaseModel):
    messages: List[str] = Field(..., description="List of messages to process")
    model: str = Field(default=DEFAULT_MODEL, description="Ollama model to use")
    temperature: float = Field(default=DEFAULT_TEMPERATURE, ge=0.0, le=2.0)
    max_tokens: int = Field(default=DEFAULT_MAX_TOKENS, ge=1, le=8192)
    system_prompt: Optional[str] = Field(None, description="Custom system prompt")

class BatchChatResponse(BaseModel):
    responses: List[Dict[str, Any]]
    model: str
    timestamp: str
    total_processing_time: float

class ModelInfo(BaseModel):
    name: str
    status: str
    size: Optional[str] = None
    modified_at: Optional[str] = None
    family: Optional[str] = None
    parameter_size: Optional[str] = None

class ModelsResponse(BaseModel):
    available_models: List[ModelInfo]
    default_model: str

class ConversationRequest(BaseModel):
    messages: List[Dict[str, str]] = Field(..., description="Conversation history in OpenAI format")
    model: str = Field(default=DEFAULT_MODEL, description="Ollama model to use")
    temperature: float = Field(default=DEFAULT_TEMPERATURE, ge=0.0, le=2.0)
    max_tokens: int = Field(default=DEFAULT_MAX_TOKENS, ge=1, le=8192)

def format_prompt(message: str, system_prompt: Optional[str] = None) -> str:
    """Format the prompt with optional system prompt"""
    if system_prompt:
        return f"{system_prompt}\n\nQuestion: {message}\n\nResponse:"
    else:
        return f"You are a helpful AI assistant. Please provide a clear and informative response to the following question or request.\n\nQuestion: {message}\n\nResponse:"

def format_conversation(messages: List[Dict[str, str]]) -> str:
    """Format a conversation history into a single prompt"""
    formatted_messages = []
    
    for msg in messages:
        role = msg.get("role", "user")
        content = msg.get("content", "")
        
        if role == "system":
            formatted_messages.append(f"System: {content}")
        elif role == "user":
            formatted_messages.append(f"Human: {content}")
        elif role == "assistant":
            formatted_messages.append(f"Assistant: {content}")
    
    # Add the final prompt
    formatted_messages.append("Assistant:")
    
    return "\n\n".join(formatted_messages)

async def call_ollama_api(
    prompt: str, 
    model: str, 
    temperature: float = 0.7, 
    max_tokens: int = 2048,
    stream: bool = False
) -> str:
    """Make direct HTTP call to Ollama API"""
    url = f"{OLLAMA_BASE_URL}/api/generate"
    
    payload = {
        "model": model,
        "prompt": prompt,
        "stream": stream,
        "options": {
            "temperature": temperature,
            "num_predict": max_tokens
        }
    }
    
    try:
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            None,
            lambda: requests.post(url, json=payload, timeout=300)
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get("response", "").strip()
        else:
            error_msg = f"Ollama API error: {response.status_code}"
            if response.text:
                try:
                    error_data = response.json()
                    error_msg += f" - {error_data.get('error', response.text)}"
                except:
                    error_msg += f" - {response.text}"
            raise HTTPException(status_code=500, detail=error_msg)
            
    except requests.exceptions.ConnectionError:
        raise HTTPException(
            status_code=500, 
            detail="Cannot connect to Ollama. Make sure Ollama is running on localhost:11434"
        )
    except requests.exceptions.Timeout:
        raise HTTPException(status_code=500, detail="Ollama request timed out")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calling Ollama API: {str(e)}")

# Simple streaming without aiohttp (to avoid import issues)
@router.post("/chat/stream-simple")
async def simple_stream_chat(request: StreamChatRequest):
    """Simple streaming chat (no aiohttp dependency)"""
    try:
        prompt = format_prompt(request.message, request.system_prompt)
        
        async def simple_stream():
            url = f"{OLLAMA_BASE_URL}/api/generate"
            payload = {
                "model": request.model,
                "prompt": prompt,
                "stream": True,
                "options": {
                    "temperature": request.temperature,
                    "num_predict": request.max_tokens
                }
            }
            
            try:
                # Use requests in thread pool for streaming
                def make_request():
                    return requests.post(url, json=payload, stream=True, timeout=300)
                
                loop = asyncio.get_event_loop()
                response = await loop.run_in_executor(None, make_request)
                
                if response.status_code == 200:
                    for line in response.iter_lines():
                        if line:
                            try:
                                data = json.loads(line.decode('utf-8'))
                                if 'response' in data:
                                    yield f"data: {data['response']}\n\n"
                                if data.get('done', False):
                                    yield f"data: [DONE]\n\n"
                                    break
                            except json.JSONDecodeError:
                                continue
                else:
                    yield f"data: Error: {response.status_code}\n\n"
                    
            except Exception as e:
                yield f"data: Error: {str(e)}\n\n"
        
        return StreamingResponse(
            simple_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive"
            }
        )
    
    except Exception as e:
        logger.error(f"Error in simple streaming: {e}")
        raise HTTPException(status_code=500, detail=f"Simple streaming error: {str(e)}")

@router.post("/chat", response_model=ChatResponse)
async def chat_with_ollama(request: ChatRequest):
    """Chat with an Ollama model"""
    start_time = asyncio.get_event_loop().time()
    
    try:
        prompt = format_prompt(request.message, request.system_prompt)
        response = await call_ollama_api(
            prompt=prompt,
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            stream=request.stream
        )
        
        processing_time = asyncio.get_event_loop().time() - start_time
        
        # Estimate token usage (rough approximation)
        tokens_used = len(response.split()) + len(request.message.split())
        
        return ChatResponse(
            response=response,
            model=request.model,
            timestamp=datetime.now().isoformat(),
            processing_time=round(processing_time, 2),
            tokens_used=tokens_used
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/conversation")
async def conversation_with_ollama(request: ConversationRequest):
    """Handle multi-turn conversations in OpenAI-compatible format"""
    start_time = asyncio.get_event_loop().time()
    
    try:
        prompt = format_conversation(request.messages)
        response = await call_ollama_api(
            prompt=prompt,
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )
        
        processing_time = asyncio.get_event_loop().time() - start_time
        
        return {
            "response": response,
            "model": request.model,
            "timestamp": datetime.now().isoformat(),
            "processing_time": round(processing_time, 2),
            "conversation_length": len(request.messages)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in conversation endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/batch-chat", response_model=BatchChatResponse)
async def batch_chat_with_ollama(request: BatchChatRequest):
    """Process multiple messages in batch"""
    start_time = asyncio.get_event_loop().time()
    
    if len(request.messages) > 20:
        raise HTTPException(status_code=400, detail="Too many messages. Maximum 20 messages per batch.")
    
    try:
        # Process all messages concurrently
        tasks = []
        for msg in request.messages:
            prompt = format_prompt(msg, request.system_prompt)
            task = call_ollama_api(
                prompt=prompt,
                model=request.model,
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Format responses
        formatted_responses = []
        for i, (msg, resp) in enumerate(zip(request.messages, responses)):
            if isinstance(resp, Exception):
                formatted_responses.append({
                    "index": i,
                    "message": msg,
                    "response": f"Error: {str(resp)}",
                    "success": False
                })
            else:
                formatted_responses.append({
                    "index": i,
                    "message": msg,
                    "response": resp,
                    "success": True,
                    "tokens_used": len(resp.split()) + len(msg.split())
                })
        
        total_processing_time = asyncio.get_event_loop().time() - start_time
        
        return BatchChatResponse(
            responses=formatted_responses,
            model=request.model,
            timestamp=datetime.now().isoformat(),
            total_processing_time=round(total_processing_time, 2)
        )
    
    except Exception as e:
        logger.error(f"Error in batch chat endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/models", response_model=ModelsResponse)
async def get_available_models():
    """Get list of available Ollama models from your running instance"""
    try:
        url = f"{OLLAMA_BASE_URL}/api/tags"
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            None,
            lambda: requests.get(url, timeout=10)
        )
        
        if response.status_code == 200:
            data = response.json()
            models = []
            for model in data.get("models", []):
                # Convert size to human readable format
                size_bytes = model.get("size", 0)
                if size_bytes > 0:
                    size_gb = round(size_bytes / (1024**3), 1)
                    size_str = f"{size_gb} GB"
                else:
                    size_str = "Unknown"
                
                # Extract model family and parameter size from name
                model_name = model["name"]
                family = model_name.split(":")[0]
                
                # Try to extract parameter size
                parameter_size = "Unknown"
                if ":" in model_name:
                    tag = model_name.split(":")[1]
                    if any(char.isdigit() for char in tag):
                        parameter_size = tag
                
                models.append(ModelInfo(
                    name=model_name,
                    status="available",
                    size=size_str,
                    modified_at=model.get("modified_at", ""),
                    family=family,
                    parameter_size=parameter_size
                ))
            
            return ModelsResponse(
                available_models=models,
                default_model=DEFAULT_MODEL if models else "qwen3:8b"
            )
        else:
            raise HTTPException(status_code=500, detail=f"Failed to get models from Ollama: {response.status_code}")
    
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving models: {str(e)}")

@router.get("/health")
async def health_check():
    """Health check endpoint to verify Ollama connectivity"""
    try:
        url = f"{OLLAMA_BASE_URL}/api/tags"
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            None,
            lambda: requests.get(url, timeout=5)
        )
        
        if response.status_code == 200:
            data = response.json()
            model_count = len(data.get("models", []))
            
            return {
                "status": "healthy",
                "ollama_connection": "ok",
                "available_models": model_count,
                "default_model": DEFAULT_MODEL,
                "base_url": OLLAMA_BASE_URL,
                "method": "direct_http",
                "streaming_available": True,
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "status": "unhealthy",
                "ollama_connection": "failed",
                "error": f"Ollama returned status {response.status_code}",
                "timestamp": datetime.now().isoformat()
            }
    except Exception as e:
        return {
            "status": "unhealthy",
            "ollama_connection": "failed",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/info")
async def get_info():
    """Get information about the current setup"""
    return {
        "ollama_base_url": OLLAMA_BASE_URL,
        "default_model": DEFAULT_MODEL,
        "default_temperature": DEFAULT_TEMPERATURE,
        "default_max_tokens": DEFAULT_MAX_TOKENS,
        "method": "direct_http_api",
        "langchain_available": False,
        "streaming_available": True,
        "detected_models": ["phi4-reasoning:latest", "qwen3:8b", "phi4:latest"],
        "new_endpoints": [
            "/api/ollama/chat/stream-simple"
        ],
        "timestamp": datetime.now().isoformat()
    }

@router.post("/test-model")
async def test_model(model_name: str = "qwen3:8b"):
    """Test a specific model with a simple prompt"""
    try:
        test_prompt = "Hello! Please respond with 'Test successful' if you can understand this message."
        
        start_time = asyncio.get_event_loop().time()
        response = await call_ollama_api(
            prompt=test_prompt,
            model=model_name,
            temperature=0.1,
            max_tokens=50
        )
        processing_time = asyncio.get_event_loop().time() - start_time
        
        return {
            "model": model_name,
            "status": "success",
            "response": response,
            "processing_time": round(processing_time, 2),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "model": model_name,
            "status": "failed",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.post("/compare-models")
async def compare_models(
    message: str,
    models: List[str] = ["qwen3:8b", "phi4:latest", "phi4-reasoning:latest"],
    temperature: float = 0.7
):
    """Compare responses from multiple models for the same prompt"""
    try:
        tasks = []
        for model in models:
            prompt = format_prompt(message)
            task = call_ollama_api(
                prompt=prompt,
                model=model,
                temperature=temperature,
                max_tokens=DEFAULT_MAX_TOKENS
            )
            tasks.append(task)
        
        start_time = asyncio.get_event_loop().time()
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        processing_time = asyncio.get_event_loop().time() - start_time
        
        # Format comparison results
        comparisons = []
        for model, response in zip(models, responses):
            if isinstance(response, Exception):
                comparisons.append({
                    "model": model,
                    "response": f"Error: {str(response)}",
                    "success": False
                })
            else:
                comparisons.append({
                    "model": model,
                    "response": response,
                    "success": True,
                    "response_length": len(response)
                })
        
        return {
            "message": message,
            "comparisons": comparisons,
            "total_processing_time": round(processing_time, 2),
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"Error in model comparison: {e}")
        raise HTTPException(status_code=500, detail=f"Error comparing models: {str(e)}")

@router.get("/stats")
async def get_ollama_stats():
    """Get Ollama server statistics"""
    try:
        # Get basic info
        url = f"{OLLAMA_BASE_URL}/api/tags"
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            None,
            lambda: requests.get(url, timeout=5)
        )
        
        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])
            
            total_size = sum(model.get("size", 0) for model in models)
            total_size_gb = round(total_size / (1024**3), 1)
            
            model_families = {}
            for model in models:
                family = model["name"].split(":")[0]
                if family not in model_families:
                    model_families[family] = 0
                model_families[family] += 1
            
            return {
                "total_models": len(models),
                "total_size_gb": total_size_gb,
                "model_families": model_families,
                "models": [{"name": m["name"], "size_gb": round(m.get("size", 0) / (1024**3), 1)} for m in models],
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to get Ollama stats")
    
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving stats: {str(e)}")

@router.get("/models/memory-status")
async def get_memory_status():
    """Check which models are actually loaded in memory"""
    try:
        # Get running processes
        url = f"{OLLAMA_BASE_URL}/api/ps"
        loop = asyncio.get_event_loop()
        
        try:
            response = await loop.run_in_executor(
                None,
                lambda: requests.get(url, timeout=5)
            )
            
            if response.status_code == 200:
                running_models = response.json()
                
                return {
                    "loaded_models": running_models.get("models", []),
                    "total_loaded": len(running_models.get("models", [])),
                    "memory_info": "Models shown are currently loaded in RAM",
                    "status": "success"
                }
            else:
                return {
                    "status": "api_unavailable",
                    "message": "Ollama /api/ps endpoint not available in this version"
                }
        
        except Exception as api_error:
            return {
                "status": "error", 
                "error": str(api_error)
            }
    
    except Exception as e:
        logger.error(f"Error checking memory status: {e}")
        raise HTTPException(status_code=500, detail=f"Error checking memory: {str(e)}")

@router.get("/chat/stream-test")
async def test_streaming():
    """Test streaming functionality"""
    async def test_stream():
        for i in range(5):
            yield f"data: Test chunk {i+1} - {datetime.now().isoformat()}\n\n"
            await asyncio.sleep(1)
        yield f"data: [DONE]\n\n"
    
    return StreamingResponse(
        test_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive"
        }
    )

@router.get("/endpoints")
async def list_all_endpoints():
    """List all available endpoints"""
    return {
        "chat_endpoints": {
            "/api/ollama/chat": "Standard chat (may timeout for long responses)",
            "/api/ollama/chat/stream-simple": "🔥 STREAMING chat (solves timeout issues)",
            "/api/ollama/conversation": "Multi-turn conversations (OpenAI format)"
        },
        "model_endpoints": {
            "/api/ollama/models": "List available models",
            "/api/ollama/models/memory-status": "Check loaded models in RAM",
            "/api/ollama/test-model": "Test a specific model",
            "/api/ollama/compare-models": "Compare multiple models"
        },
        "system_endpoints": {
            "/api/ollama/health": "Health check",
            "/api/ollama/info": "System information", 
            "/api/ollama/stats": "Ollama statistics"
        },
        "batch_endpoints": {
            "/api/ollama/batch-chat": "Process multiple messages at once"
        },
        "test_endpoints": {
            "/api/ollama/chat/stream-test": "Test streaming functionality"
        },
        "recommended_for_timeout_issues": [
            "/api/ollama/chat/stream-simple"
        ],
        "timestamp": datetime.now().isoformat()
    }