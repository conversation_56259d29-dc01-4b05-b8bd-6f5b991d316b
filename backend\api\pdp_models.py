from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import date, datetime
from uuid import UUID
from enum import Enum

class PriorityLevel(str, Enum):
    LOW = "Low Priority"
    MEDIUM = "Medium Priority"
    HIGH = "High Priority"

class ProgressStatus(str, Enum):
    NOT_STARTED = "Not Started"
    IN_PROGRESS = "In Progress"
    COMPLETE = "Complete"

class PDPBase(BaseModel):
    """Base model for Personal Development Plan items"""
    goal: str = Field(..., description="The main development goal")
    tasks: str = Field(..., description="List of tasks to achieve the goal (rich text)")
    important_tasks: Optional[str] = Field(None, description="Important tasks that need special attention (rich text)")
    priority_level: PriorityLevel = Field(..., description="Priority level of the goal")
    time_to_complete: str = Field(..., description="Estimated time to complete (e.g., '2 hours per week')")
    target_completion_date: Optional[date] = Field(None, description="Target date for completing the goal")
    progress: ProgressStatus = Field(default=ProgressStatus.NOT_STARTED, description="Current progress status")

class PDPCreate(PDPBase):
    """Model for creating a new PDP item"""
    pass

class PDPUpdate(BaseModel):
    """Model for updating an existing PDP item"""
    goal: Optional[str] = None
    tasks: Optional[str] = None
    important_tasks: Optional[str] = None
    priority_level: Optional[PriorityLevel] = None
    time_to_complete: Optional[str] = None
    target_completion_date: Optional[date] = None
    progress: Optional[ProgressStatus] = None

class PDPInDB(PDPBase):
    """Model for PDP item in database"""
    id: UUID
    candidate_id: str
    date_created: datetime
    last_updated: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    is_active: bool = True

    class Config:
        orm_mode = True
        json_encoders = {
            UUID: lambda v: str(v),
        }

class PDPListResponse(BaseModel):
    """Response model for listing PDP items"""
    items: List[PDPInDB]
    total: int
    page: int = 1
    page_size: int = 10
