from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from typing import List, Optional
from uuid import UUID
import logging

# Use absolute imports to avoid relative import issues
from api.pdp_models import (
    PDPCreate, PDPUpdate, PDPInDB, PDPListResponse,
    PriorityLevel, ProgressStatus
)
from api.pdp_service import pdp_service
from api.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["Personal Development Plan"],
    responses={404: {"description": "Not found"}},
)

# Dependency to get the current user's ID
# Use the candidate ID from the token instead of the email
def get_current_user_id(current_user: dict = Depends(get_current_user)) -> str:
    """Get the current user's ID from the JWT token"""
    logger.debug(f"Current user: {current_user}")
    # Use the candidate ID if available, otherwise fall back to email
    candidate_id = current_user.get("id")
    if candidate_id:
        logger.debug(f"Using candidate ID: {candidate_id}")
        return candidate_id
    else:
        logger.debug(f"No candidate ID found, using email: {current_user['sub']}")
        return current_user["sub"]

# Uncomment this for testing without authentication
# def get_current_user_id() -> str:
#     """Return a fixed user ID for testing"""
#     logger.debug("Using fixed user ID for testing")
#     return "<EMAIL>"

@router.post(
    "/items",
    response_model=PDPInDB,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new PDP item"
)
async def create_pdp_item(
    pdp_data: PDPCreate,
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Create a new Personal Development Plan item for the current user
    """
    logger.info(f"Creating PDP item for user {current_user_id}")
    logger.debug(f"PDP data: {pdp_data.dict()}")
    
    try:
        result = await pdp_service.create_pdp(
            candidate_id=current_user_id,
            pdp_data=pdp_data,
            created_by=current_user_id
        )
        logger.info(f"Successfully created PDP item with ID {result.id}")
        return result
    except Exception as e:
        logger.error(f"Error creating PDP item: {str(e)}")
        # Log the full exception traceback
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create PDP item"
        )

@router.get(
    "/items",
    response_model=PDPListResponse,
    summary="List all PDP items for the current user"
)
async def list_pdp_items(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    priority: Optional[PriorityLevel] = Query(None, description="Filter by priority level"),
    progress: Optional[ProgressStatus] = Query(None, description="Filter by progress status"),
    current_user_id: str = Depends(get_current_user_id)
):
    """
    List all Personal Development Plan items for the current user with pagination and filtering
    """
    logger.info(f"Listing PDP items for user {current_user_id}")
    logger.debug(f"Query parameters: page={page}, page_size={page_size}, priority={priority}, progress={progress}")
    
    try:
        # Convert enum values to strings if they exist
        priority_value = priority.value if priority else None
        progress_value = progress.value if progress else None
        
        logger.debug(f"Calling pdp_service.list_pdps with: candidate_id={current_user_id}, page={page}, "
                   f"page_size={page_size}, priority={priority_value}, progress={progress_value}")
        
        result = await pdp_service.list_pdps(
            candidate_id=current_user_id,
            page=page,
            page_size=page_size,
            priority=priority_value,
            progress=progress_value
        )
        
        logger.info(f"Successfully retrieved {len(result['items'])} PDP items")
        logger.debug(f"Result: {result}")
        
        return result
    except Exception as e:
        logger.error(f"Error listing PDP items: {str(e)}")
        # Log the full exception traceback
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve PDP items"
        )

@router.get(
    "/items/{pdp_id}",
    response_model=PDPInDB,
    summary="Get a specific PDP item by ID"
)
async def get_pdp_item(
    pdp_id: UUID,
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Get a specific Personal Development Plan item by ID
    """
    logger.info(f"Getting PDP item {pdp_id} for user {current_user_id}")
    
    try:
        logger.debug(f"Calling pdp_service.get_pdp_by_id with: pdp_id={pdp_id}, candidate_id={current_user_id}")
        pdp_item = await pdp_service.get_pdp_by_id(pdp_id, current_user_id)
        
        if not pdp_item:
            logger.warning(f"PDP item {pdp_id} not found for user {current_user_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PDP item not found or access denied"
            )
            
        logger.info(f"Successfully retrieved PDP item {pdp_id}")
        logger.debug(f"PDP item: {pdp_item}")
        return pdp_item
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving PDP item {pdp_id}: {str(e)}")
        # Log the full exception traceback
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve PDP item"
        )

@router.put(
    "/items/{pdp_id}",
    response_model=PDPInDB,
    summary="Update a PDP item"
)
async def update_pdp_item(
    pdp_id: UUID,
    pdp_data: PDPUpdate,
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Update an existing Personal Development Plan item
    """
    try:
        updated_item = await pdp_service.update_pdp(
            pdp_id=pdp_id,
            candidate_id=current_user_id,
            pdp_data=pdp_data,
            updated_by=current_user_id
        )
        
        if not updated_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PDP item not found or access denied"
            )
            
        return updated_item
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating PDP item {pdp_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update PDP item"
        )

@router.delete(
    "/items/{pdp_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a PDP item"
)
async def delete_pdp_item(
    pdp_id: UUID,
    current_user_id: str = Depends(get_current_user_id)
):
    """
    Delete a Personal Development Plan item (soft delete)
    """
    try:
        success = await pdp_service.delete_pdp(pdp_id, current_user_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PDP item not found or access denied"
            )
        return JSONResponse(content=None, status_code=status.HTTP_204_NO_CONTENT)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting PDP item {pdp_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete PDP item"
        )
