import json
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime
from loguru import logger

from db_connection import get_db_connection
from api.pdp_models import PDPInDB, PDPCreate, PDPUpdate, PriorityLevel, ProgressStatus

# Configure logger to use loguru
logger.debug("Initializing PDPService module")

class PDPService:
    """Service for handling Personal Development Plan operations"""

    @staticmethod
    def _row_to_pdp(row: dict) -> PDPInDB:
        """Convert database row to PDPInDB model"""
        try:
            return PDPInDB(
                id=row['id'],
                candidate_id=row['candidate_id'],
                goal=row['goal'],
                tasks=row['tasks'],
                important_tasks=row.get('important_tasks'),
                priority_level=PriorityLevel(row['priority_level']),
                time_to_complete=row['time_to_complete'],
                target_completion_date=row.get('target_completion_date'),
                progress=ProgressStatus(row['progress']),
                date_created=row['date_created'],
                last_updated=row['last_updated'],
                created_by=row.get('created_by'),
                updated_by=row.get('updated_by'),
                is_active=row.get('is_active', True)
            )
        except Exception as e:
            logger.error(f"Error converting row to PDP model: {e}")
            raise

    async def create_pdp(self, candidate_id: str, pdp_data: PDPCreate, created_by: str = None) -> PDPInDB:
        """Create a new PDP item for a candidate"""
        conn = None
        try:
            # Log input parameters for debugging
            logger.debug(f"Creating PDP item for candidate {candidate_id}")
            logger.debug(f"PDP data: {pdp_data.dict()}")
            
            # First, check if the table exists
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Check if table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'candidate_pdp'
                );
            """)
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                logger.error("Table 'candidate_pdp' does not exist!")
                # Create the table if it doesn't exist
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS candidate_pdp (
                        id UUID PRIMARY KEY,
                        candidate_id VARCHAR(255) NOT NULL,
                        goal TEXT NOT NULL,
                        tasks TEXT NOT NULL,
                        important_tasks TEXT,
                        priority_level VARCHAR(50) NOT NULL,
                        time_to_complete VARCHAR(255) NOT NULL,
                        target_completion_date DATE,
                        progress VARCHAR(50) NOT NULL,
                        date_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_by VARCHAR(255),
                        updated_by VARCHAR(255),
                        is_active BOOLEAN DEFAULT TRUE
                    )
                """)
                conn.commit()
                logger.info("Created 'candidate_pdp' table")
            
            query = """
                INSERT INTO candidate_pdp (
                    id, candidate_id, goal, tasks, important_tasks, priority_level, 
                    time_to_complete, target_completion_date, progress, created_by, updated_by
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING *
            """
            
            pdp_id = str(uuid4())
            
            # Log the query and parameters for debugging
            params = (
                pdp_id,
                candidate_id,
                pdp_data.goal,
                pdp_data.tasks,
                pdp_data.important_tasks,
                pdp_data.priority_level.value,
                pdp_data.time_to_complete,
                pdp_data.target_completion_date,
                pdp_data.progress.value,
                created_by,
                created_by
            )
            logger.debug(f"Executing query: {query}")
            logger.debug(f"With parameters: {params}")
            
            cursor.execute(query, params)
            
            result = cursor.fetchone()
            if not result:
                logger.error("Insert succeeded but no row returned")
                raise Exception("Insert succeeded but no row returned")
                
            conn.commit()
            logger.info(f"Successfully created PDP item with ID {pdp_id}")
            
            # Convert result to dict with column names
            column_names = [desc[0] for desc in cursor.description]
            result_dict = dict(zip(column_names, result))
            
            return self._row_to_pdp(result_dict)
            
        except Exception as e:
            logger.error(f"Error creating PDP item: {str(e)}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()

    async def get_pdp_by_id(self, pdp_id: UUID, candidate_id: str) -> Optional[PDPInDB]:
        """Get a single PDP item by ID for a specific candidate"""
        conn = None
        try:
            logger.debug(f"Getting PDP item {pdp_id} for candidate {candidate_id}")
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = """
                SELECT * FROM candidate_pdp 
                WHERE id = %s AND candidate_id = %s AND is_active = TRUE
            """
            
            logger.debug(f"Executing query: {query}")
            logger.debug(f"With parameters: {(str(pdp_id), candidate_id)}")
            
            cursor.execute(query, (str(pdp_id), candidate_id))
            result = cursor.fetchone()
            
            if not result:
                logger.debug(f"No PDP item found with ID {pdp_id} for candidate {candidate_id}")
                return None
            
            # Convert result to dict with column names
            column_names = [desc[0] for desc in cursor.description]
            result_dict = dict(zip(column_names, result))
            
            logger.debug(f"Found PDP item: {result_dict}")
            return self._row_to_pdp(result_dict)
            
        except Exception as e:
            logger.error(f"Error getting PDP item {pdp_id}: {e}")
            raise
        finally:
            if conn:
                conn.close()

    async def list_pdps(
        self, 
        candidate_id: str, 
        page: int = 1, 
        page_size: int = 10,
        priority: Optional[str] = None,
        progress: Optional[str] = None
    ) -> Dict[str, Any]:
        """List all PDP items for a candidate with pagination and filtering"""
        conn = None
        try:
            logger.debug(f"Listing PDP items for candidate {candidate_id} (page={page}, page_size={page_size})")
            logger.debug(f"Filters: priority={priority}, progress={progress}")
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Check if table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'candidate_pdp'
                );
            """)
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                logger.warning("Table 'candidate_pdp' does not exist! Returning empty result.")
                return {
                    "items": [],
                    "total": 0,
                    "page": page,
                    "page_size": page_size
                }
            
            # Build the base query
            base_query = """
                SELECT * FROM candidate_pdp 
                WHERE candidate_id = %s AND is_active = TRUE
            """
            
            # Add filters if provided
            params = [candidate_id]
            conditions = []
            
            if priority:
                conditions.append("priority_level = %s")
                params.append(priority)
                
            if progress:
                conditions.append("progress = %s")
                params.append(progress)
            
            if conditions:
                base_query += " AND " + " AND ".join(conditions)
            
            # Add ordering and pagination
            base_query += " ORDER BY date_created DESC"
            
            logger.debug(f"Base query: {base_query}")
            logger.debug(f"Query parameters: {params}")
            
            # Get total count
            count_query = f"SELECT COUNT(*) FROM ({base_query}) AS count_query"
            cursor.execute(count_query, params)
            total = cursor.fetchone()[0]
            logger.debug(f"Total items: {total}")
            
            # Add pagination
            offset = (page - 1) * page_size
            paginated_query = f"{base_query} LIMIT %s OFFSET %s"
            params.extend([page_size, offset])
            
            logger.debug(f"Paginated query: {paginated_query}")
            logger.debug(f"Final parameters: {params}")
            
            cursor.execute(paginated_query, params)
            results = cursor.fetchall()
            
            # Convert to PDPInDB models
            items = []
            for row in results:
                # Convert row to dict with column names
                column_names = [desc[0] for desc in cursor.description]
                row_dict = dict(zip(column_names, row))
                items.append(self._row_to_pdp(row_dict))
            
            logger.debug(f"Found {len(items)} items")
            
            return {
                "items": items,
                "total": total,
                "page": page,
                "page_size": page_size
            }
            
        except Exception as e:
            logger.error(f"Error listing PDP items: {e}")
            raise
        finally:
            if conn:
                conn.close()

    async def update_pdp(
        self, 
        pdp_id: UUID, 
        candidate_id: str, 
        pdp_data: PDPUpdate, 
        updated_by: str = None
    ) -> Optional[PDPInDB]:
        """Update an existing PDP item"""
        conn = None
        try:
            logger.debug(f"Updating PDP item {pdp_id} for candidate {candidate_id}")
            logger.debug(f"Update data: {pdp_data.dict(exclude_unset=True)}")
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Check if table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'candidate_pdp'
                );
            """)
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                logger.error("Table 'candidate_pdp' does not exist!")
                return None
            
            # Get existing item to ensure it exists and belongs to the candidate
            existing = await self.get_pdp_by_id(pdp_id, candidate_id)
            if not existing:
                logger.warning(f"PDP item {pdp_id} not found for candidate {candidate_id}")
                return None
            
            # Build the update query dynamically based on provided fields
            update_fields = []
            params = []
            
            if pdp_data.goal is not None:
                update_fields.append("goal = %s")
                params.append(pdp_data.goal)
                
            if pdp_data.tasks is not None:
                update_fields.append("tasks = %s")
                params.append(pdp_data.tasks)
                
            if pdp_data.important_tasks is not None:
                update_fields.append("important_tasks = %s")
                params.append(pdp_data.important_tasks)
                
            if pdp_data.priority_level is not None:
                update_fields.append("priority_level = %s")
                params.append(pdp_data.priority_level.value)
                
            if pdp_data.time_to_complete is not None:
                update_fields.append("time_to_complete = %s")
                params.append(pdp_data.time_to_complete)
                
            if pdp_data.target_completion_date is not None:
                update_fields.append("target_completion_date = %s")
                params.append(pdp_data.target_completion_date)
                
            if pdp_data.progress is not None:
                update_fields.append("progress = %s")
                params.append(pdp_data.progress.value)
            
            # If no fields to update, return the existing item
            if not update_fields:
                return existing
            
            # Add updated_by and last_updated
            update_fields.append("updated_by = %s")
            params.append(updated_by)
            
            update_fields.append("last_updated = CURRENT_TIMESTAMP")
            
            # Add the WHERE clause
            params.extend([str(pdp_id), candidate_id])
            
            # Build and execute the update query
            update_query = f"""
                UPDATE candidate_pdp 
                SET {', '.join(update_fields)}
                WHERE id = %s AND candidate_id = %s
                RETURNING *
            """
            
            logger.debug(f"Update query: {update_query}")
            logger.debug(f"Update parameters: {params}")
            
            cursor.execute(update_query, params)
            
            if cursor.rowcount == 0:
                logger.warning(f"No rows affected when updating PDP item {pdp_id}")
                return None
                
            result = cursor.fetchone()
            if not result:
                logger.error(f"Update succeeded but no row returned for PDP item {pdp_id}")
                return None
                
            conn.commit()
            logger.info(f"Successfully updated PDP item {pdp_id}")
            
            # Convert result to dict with column names
            column_names = [desc[0] for desc in cursor.description]
            result_dict = dict(zip(column_names, result))
            
            return self._row_to_pdp(result_dict)
            
        except Exception as e:
            logger.error(f"Error updating PDP item {pdp_id}: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()

    async def delete_pdp(self, pdp_id: UUID, candidate_id: str) -> bool:
        """Soft delete a PDP item (set is_active = FALSE)"""
        conn = None
        try:
            logger.debug(f"Soft deleting PDP item {pdp_id} for candidate {candidate_id}")
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Check if table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'candidate_pdp'
                );
            """)
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                logger.error("Table 'candidate_pdp' does not exist!")
                return False
            
            # Check if the item exists and belongs to the candidate
            check_query = """
                SELECT id FROM candidate_pdp 
                WHERE id = %s AND candidate_id = %s AND is_active = TRUE
            """
            cursor.execute(check_query, (str(pdp_id), candidate_id))
            if cursor.fetchone() is None:
                logger.warning(f"PDP item {pdp_id} not found for candidate {candidate_id} or already deleted")
                return False
            
            query = """
                UPDATE candidate_pdp 
                SET is_active = FALSE, last_updated = CURRENT_TIMESTAMP
                WHERE id = %s AND candidate_id = %s
                RETURNING id
            """
            
            logger.debug(f"Delete query: {query}")
            logger.debug(f"Delete parameters: {(str(pdp_id), candidate_id)}")
            
            cursor.execute(query, (str(pdp_id), candidate_id))
            result = cursor.rowcount > 0
            conn.commit()
            
            if result:
                logger.info(f"Successfully soft deleted PDP item {pdp_id}")
            else:
                logger.warning(f"No rows affected when deleting PDP item {pdp_id}")
                
            return result
            
        except Exception as e:
            logger.error(f"Error deleting PDP item {pdp_id}: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()

# Create a singleton instance of the service
try:
    logger.info("Creating PDPService instance")
    pdp_service = PDPService()
    logger.info("PDPService instance created successfully")
except Exception as e:
    logger.error(f"Error creating PDPService instance: {str(e)}")
    import traceback
    logger.error(f"Traceback: {traceback.format_exc()}")
    # Create a dummy instance to avoid import errors
    pdp_service = PDPService()
