# api/routes.py

from datetime import datetime, timedelta
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, status
from loguru import logger
from datetime import datetime
import glob
import os

from api.models import AssessmentRequest, AssessmentResponse, ErrorResponse, StatsResponse
from api.services import AssessmentService
from config.config import CACHE_ENABLED, CACHE_MAX_AGE_DAYS, DATA_PROVIDER_TYPE
from data_providers import DataProviderFactory
from api.auth import get_current_user

router = APIRouter(prefix="/api/v1")

# Dependency to get assessment service
def get_assessment_service():
    return AssessmentService(
        cache_enabled=CACHE_ENABLED, 
        cache_max_age_days=CACHE_MAX_AGE_DAYS,
        provider_type=DATA_PROVIDER_TYPE
    )

# Dependency to get data provider
def get_data_provider():
    return DataProviderFactory.create_provider(DATA_PROVIDER_TYPE)

def admin_required(current_user=Depends(get_current_user)):
    if current_user["role"] != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    return current_user

@router.post(
    "/assessment",
    response_model=AssessmentResponse,
    responses={
        200: {"description": "Successful assessment generation"},
        400: {"model": ErrorResponse, "description": "Bad request"},
        500: {"model": ErrorResponse, "description": "Internal server error"}
    },
    tags=["Assessment"]
)
async def generate_assessment(
    request: AssessmentRequest,
    force_new: bool = Query(False, description="Force generation of a new assessment"),
    assessment_service: AssessmentService = Depends(get_assessment_service),
    current_user: str = Depends(get_current_user)
):
    """
    Generate a career assessment based on candidate's email
    
    This endpoint accepts a candidate's email and returns a comprehensive
    career assessment based on psychometric scores. By default, it will
    check for cached assessments first to save on API costs. Use the
    'force_new' parameter to bypass the cache.
    
    - If a cached assessment exists (within 30 days), it will be returned
    - If no cache exists or force_new=True, a new assessment will be generated
    
    All assessments are automatically saved.
    """
    try:
        logger.info(f"Received assessment request for email: {request.email} (force_new: {force_new})")
        
        # Process assessment
        assessment_result = await assessment_service.process_assessment_by_email(
            request.email, 
            force_new=force_new
        )
        
        # Check if it was served from cache
        if assessment_result.get("cached", False):
            logger.info(f"Served cached assessment for {request.email}")
        else:
            logger.info(f"Generated new assessment for {request.email}")
        
        return assessment_result
        
    except Exception as e:
        logger.error(f"Error generating assessment: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating assessment: {str(e)}"
        )

@router.get(
    "/assessment/history/{email}",
    response_model=List[AssessmentResponse],
    responses={
        200: {"description": "List of previous assessments"},
        404: {"model": ErrorResponse, "description": "No assessments found"},
        500: {"model": ErrorResponse, "description": "Internal server error"}
    },
    tags=["Assessment"]
)
async def get_assessment_history(
    email: str,
    data_provider = Depends(get_data_provider),
    current_user: str = Depends(get_current_user)
):
    """
    Retrieve assessment history for a candidate
    
    This endpoint returns all previous assessment results for the given email.
    """
    try:
        logger.info(f"Retrieving assessment history for email: {email}")
        
        # Get assessments
        assessments = await data_provider.get_assessment_history(email)
        
        if not assessments:
            logger.warning(f"No assessments found for email: {email}")
            raise HTTPException(
                status_code=404,
                detail=f"No assessments found for email: {email}"
            )
        
        logger.info(f"Found {len(assessments)} assessments for {email}")
        
        return assessments
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving assessment history: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving assessment history: {str(e)}"
        )

@router.delete(
    "/assessment/cache/{email}",
    responses={
        200: {"description": "Cache cleared"},
        404: {"model": ErrorResponse, "description": "No assessments found"},
        500: {"model": ErrorResponse, "description": "Internal server error"}
    },
    tags=["Assessment"]
)
async def clear_assessment_cache(
    email: str,
    data_provider = Depends(get_data_provider),
    current_user: str = Depends(get_current_user)
):
    """
    Clear cached assessments for a candidate
    
    This endpoint deletes all cached assessments for the given email.
    """
    try:
        logger.info(f"Clearing assessment cache for email: {email}")
        
        # Clear assessments
        deleted_count = await data_provider.clear_assessments(email)
        
        if deleted_count == 0:
            logger.warning(f"No cached assessments found for email: {email}")
            raise HTTPException(
                status_code=404,
                detail=f"No cached assessments found for email: {email}"
            )
        
        logger.info(f"Deleted {deleted_count} cached assessments for {email}")
        
        return {"message": f"Deleted {deleted_count} cached assessments for {email}"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing assessment cache: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error clearing assessment cache: {str(e)}"
        )

@router.get(
    "/stats",
    response_model=StatsResponse,
    responses={
        200: {"description": "API usage statistics"},
        500: {"model": ErrorResponse, "description": "Internal server error"}
    },
    tags=["Admin"]
)
async def get_api_stats(
    current_user: dict = Depends(admin_required)
):
    """
    Get API usage statistics
    
    This endpoint returns statistics about API usage, including cache hits/misses.
    """
    try:
        logger.info("Retrieving API statistics")
        
        # Count total assessments
        all_assessments = glob.glob("output/api_responses/*.json")
        total_count = len(all_assessments)
        
        # Count unique emails
        unique_emails = set()
        for file in all_assessments:
            filename = os.path.basename(file)
            if "_at_" in filename:
                email_part = filename.split("_")[0] + "@" + filename.split("_at_")[1].split("_")[0]
                unique_emails.add(email_part)
        
        # Count assessments in last 24h
        last_24h_count = 0
        cutoff_time = datetime.now() - timedelta(days=1)
        
        for file in all_assessments:
            try:
                # Get file modification time
                mod_time = datetime.fromtimestamp(os.path.getmtime(file))
                if mod_time > cutoff_time:
                    last_24h_count += 1
            except Exception:
                pass
        
        return {
            "provider_type": DATA_PROVIDER_TYPE,
            "cache_enabled": CACHE_ENABLED,
            "cache_max_age_days": CACHE_MAX_AGE_DAYS,
            "total_assessments": total_count,
            "unique_emails": len(unique_emails),
            "assessments_last_24h": last_24h_count,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error retrieving API statistics: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving API statistics: {str(e)}"
        )