# api/services.py
from typing import Dict, Any, Optional
from datetime import datetime
from loguru import logger

from main import CareerAssessmentSystem
from data_providers import DataProvider, DataProviderFactory
from config.config import DATA_PROVIDER_TYPE

class AssessmentService:
    """Service that connects the API to the core assessment functionality"""
    
    def __init__(self, cache_enabled: bool = True, cache_max_age_days: int = 30, provider_type: str = DATA_PROVIDER_TYPE):
        """
        Initialize the assessment service
        
        Args:
            cache_enabled (bool): Whether to use caching
            cache_max_age_days (int): Maximum age for cached assessments in days
            provider_type (str): Type of data provider to use
        """
        self.assessment_system = CareerAssessmentSystem()
        self.cache_enabled = cache_enabled
        self.cache_max_age_days = cache_max_age_days
        
        # Create data provider
        self.data_provider = DataProviderFactory.create_provider(provider_type)
        
        logger.info(f"Assessment Service initialized. Cache enabled: {cache_enabled}, Cache max age: {cache_max_age_days} days")
    
    async def process_assessment_by_email(self, email: str, force_new: bool = False) -> Dict[str, Any]:
        """
        Process a career assessment based on candidate's email
        
        Args:
            email (str): Candidate's email address
            force_new (bool): Force generation of a new assessment, ignoring cache
            
        Returns:
            Dict[str, Any]: Assessment results
            
        Raises:
            Exception: If assessment processing fails
        """
        try:
            logger.info(f"Processing assessment for email: {email} (force_new: {force_new})")
            
            # Check cache if enabled and not forcing new
            if self.cache_enabled and not force_new:
                cached_assessment = await self.data_provider.get_assessment(email, self.cache_max_age_days)
                if cached_assessment:
                    logger.info(f"Using cached assessment for {email}")
                    # Mark as cached
                    cached_assessment["cached"] = True
                    return cached_assessment
            
            # Get candidate information from data provider
            name, gender, style_scores, motivator_scores = await self.data_provider.get_candidate_info(email)
            
            logger.info(f"Retrieved candidate info: {name} ({gender})")
            
            # Prepare assessment input
            assessment_input = {
                "individual_name": name,
                "gender": gender,
                "style_scores": style_scores,
                "motivator_scores": motivator_scores
            }
            
            # Process the assessment
            assessment_result = self.assessment_system.process_assessment(assessment_input)
            
            if "error" in assessment_result:
                logger.error(f"Assessment error: {assessment_result['error']}")
                raise Exception(assessment_result["error"])
                
            logger.success(f"Successfully processed assessment for {name}")
            
            # Prepare response
            response = {
                "email": email,
                "individual_name": name,
                "assessment": assessment_result,
                "timestamp": datetime.now().isoformat(),
                "cached": False
            }
            
            # Save response
            await self.data_provider.save_assessment(email, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing assessment for {email}: {str(e)}")
            raise