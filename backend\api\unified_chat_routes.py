"""
Unified AI Chat Routes - Supports <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> via environment variables
"""

import os
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, AsyncGenerator
import asyncio
import logging
from datetime import datetime
from dotenv import load_dotenv
import requests
import json

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/chat", tags=["unified-chat"])

# Environment Configuration
DEFAULT_PROVIDER = os.getenv("DEFAULT_AI_PROVIDER", "ollama")  # ollama, gemini, openai, claude
DEFAULT_MODEL = os.getenv("DEFAULT_AI_MODEL", "qwen3:8b")
DEFAULT_TEMPERATURE = float(os.getenv("AI_TEMPERATURE", "0.7"))
DEFAULT_MAX_TOKENS = int(os.getenv("AI_MAX_TOKENS", "2048"))

# API Keys
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY") 
CLAUDE_API_KEY = os.getenv("CLAUDE_API_KEY")

# Ollama Configuration
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

# Provider Model Mappings
PROVIDER_MODELS = {
    "ollama": ["qwen3:8b", "phi4:latest", "phi4-reasoning:latest"],
    "gemini": ["gemini-1.5-flash", "gemini-1.5-pro", "gemini-2.0-flash-exp"],
    "openai": ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo", "gpt-4-turbo"],
    "claude": ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307", "claude-3-opus-20240229"]
}

# Pydantic Models
class UnifiedChatRequest(BaseModel):
    message: str = Field(..., description="User message/question")
    provider: str = Field(default=DEFAULT_PROVIDER, description="AI provider: ollama, gemini, openai, claude")
    model: str = Field(default=DEFAULT_MODEL, description="Model to use")
    temperature: float = Field(default=DEFAULT_TEMPERATURE, ge=0.0, le=2.0)
    max_tokens: int = Field(default=DEFAULT_MAX_TOKENS, ge=1, le=8192)
    system_prompt: Optional[str] = Field(None, description="System prompt")
    stream: bool = Field(default=False, description="Enable streaming")

class UnifiedChatResponse(BaseModel):
    response: str
    provider: str
    model: str
    timestamp: str
    processing_time: float
    tokens_used: Optional[int] = None

class ProviderInfo(BaseModel):
    name: str
    status: str
    available_models: List[str]
    requires_api_key: bool
    api_key_configured: bool

class ProvidersResponse(BaseModel):
    providers: List[ProviderInfo]
    default_provider: str
    current_config: Dict[str, Any]

# Provider Classes
class OllamaProvider:
    @staticmethod
    async def chat(message: str, model: str, temperature: float, max_tokens: int, system_prompt: Optional[str] = None) -> str:
        url = f"{OLLAMA_BASE_URL}/api/generate"
        
        if system_prompt:
            prompt = f"{system_prompt}\n\nQuestion: {message}\n\nResponse:"
        else:
            prompt = f"You are a helpful AI assistant.\n\nQuestion: {message}\n\nResponse:"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": temperature,
                "num_predict": max_tokens
            }
        }
        
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(url, json=payload, timeout=120)
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "").strip()
            else:
                raise Exception(f"Ollama API error: {response.status_code}")
        except Exception as e:
            raise Exception(f"Ollama error: {str(e)}")

    @staticmethod
    def is_available() -> bool:
        try:
            response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False

class GeminiProvider:
    @staticmethod
    async def chat(message: str, model: str, temperature: float, max_tokens: int, system_prompt: Optional[str] = None) -> str:
        if not GEMINI_API_KEY:
            raise Exception("GEMINI_API_KEY not configured")
        
        url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={GEMINI_API_KEY}"
        
        # Prepare messages
        parts = []
        if system_prompt:
            parts.append({"text": f"System: {system_prompt}\n\nUser: {message}"})
        else:
            parts.append({"text": message})
        
        payload = {
            "contents": [{"parts": parts}],
            "generationConfig": {
                "temperature": temperature,
                "maxOutputTokens": max_tokens,
                "topP": 0.95,
                "topK": 40
            }
        }
        
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(url, json=payload, timeout=120)
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["candidates"][0]["content"]["parts"][0]["text"].strip()
            else:
                error_data = response.json() if response.text else {}
                raise Exception(f"Gemini API error: {response.status_code} - {error_data}")
        except Exception as e:
            raise Exception(f"Gemini error: {str(e)}")

    @staticmethod
    def is_available() -> bool:
        return bool(GEMINI_API_KEY)

class OpenAIProvider:
    @staticmethod
    async def chat(message: str, model: str, temperature: float, max_tokens: int, system_prompt: Optional[str] = None) -> str:
        if not OPENAI_API_KEY:
            raise Exception("OPENAI_API_KEY not configured")
        
        url = "https://api.openai.com/v1/chat/completions"
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": message})
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        headers = {
            "Authorization": f"Bearer {OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(url, json=payload, headers=headers, timeout=120)
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"].strip()
            else:
                error_data = response.json() if response.text else {}
                raise Exception(f"OpenAI API error: {response.status_code} - {error_data}")
        except Exception as e:
            raise Exception(f"OpenAI error: {str(e)}")

    @staticmethod
    def is_available() -> bool:
        return bool(OPENAI_API_KEY)

class ClaudeProvider:
    @staticmethod
    async def chat(message: str, model: str, temperature: float, max_tokens: int, system_prompt: Optional[str] = None) -> str:
        if not CLAUDE_API_KEY:
            raise Exception("CLAUDE_API_KEY not configured")
        
        url = "https://api.anthropic.com/v1/messages"
        
        messages = [{"role": "user", "content": message}]
        
        payload = {
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "messages": messages
        }
        
        if system_prompt:
            payload["system"] = system_prompt
        
        headers = {
            "x-api-key": CLAUDE_API_KEY,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(url, json=payload, headers=headers, timeout=120)
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["content"][0]["text"].strip()
            else:
                error_data = response.json() if response.text else {}
                raise Exception(f"Claude API error: {response.status_code} - {error_data}")
        except Exception as e:
            raise Exception(f"Claude error: {str(e)}")

    @staticmethod
    def is_available() -> bool:
        return bool(CLAUDE_API_KEY)

# Provider Factory
PROVIDERS = {
    "ollama": OllamaProvider,
    "gemini": GeminiProvider,
    "openai": OpenAIProvider,
    "claude": ClaudeProvider
}

# Routes
@router.post("/", response_model=UnifiedChatResponse)
async def unified_chat(request: UnifiedChatRequest):
    """Unified chat endpoint supporting multiple AI providers"""
    start_time = asyncio.get_event_loop().time()
    
    try:
        # Validate provider
        if request.provider not in PROVIDERS:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported provider: {request.provider}. Available: {list(PROVIDERS.keys())}"
            )
        
        # Get provider class
        provider_class = PROVIDERS[request.provider]
        
        # Check if provider is available
        if not provider_class.is_available():
            raise HTTPException(
                status_code=503,
                detail=f"Provider {request.provider} is not available. Check configuration."
            )
        
        # Validate model for provider
        available_models = PROVIDER_MODELS.get(request.provider, [])
        if available_models and request.model not in available_models:
            logger.warning(f"Model {request.model} not in known models for {request.provider}, proceeding anyway")
        
        # Make the request
        response_text = await provider_class.chat(
            message=request.message,
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            system_prompt=request.system_prompt
        )
        
        processing_time = asyncio.get_event_loop().time() - start_time
        
        # Estimate token usage (rough)
        tokens_used = len(response_text.split()) + len(request.message.split())
        
        return UnifiedChatResponse(
            response=response_text,
            provider=request.provider,
            model=request.model,
            timestamp=datetime.now().isoformat(),
            processing_time=round(processing_time, 2),
            tokens_used=tokens_used
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in unified chat: {e}")
        raise HTTPException(status_code=500, detail=f"Chat error: {str(e)}")

@router.get("/providers", response_model=ProvidersResponse)
async def get_providers():
    """Get information about all available providers"""
    providers = []
    
    for name, provider_class in PROVIDERS.items():
        api_key_required = name != "ollama"
        api_key_configured = False
        
        if name == "gemini":
            api_key_configured = bool(GEMINI_API_KEY)
        elif name == "openai":
            api_key_configured = bool(OPENAI_API_KEY)
        elif name == "claude":
            api_key_configured = bool(CLAUDE_API_KEY)
        else:  # ollama
            api_key_configured = True
        
        status = "available" if provider_class.is_available() else "unavailable"
        
        providers.append(ProviderInfo(
            name=name,
            status=status,
            available_models=PROVIDER_MODELS.get(name, []),
            requires_api_key=api_key_required,
            api_key_configured=api_key_configured
        ))
    
    return ProvidersResponse(
        providers=providers,
        default_provider=DEFAULT_PROVIDER,
        current_config={
            "default_model": DEFAULT_MODEL,
            "default_temperature": DEFAULT_TEMPERATURE,
            "default_max_tokens": DEFAULT_MAX_TOKENS,
            "ollama_url": OLLAMA_BASE_URL
        }
    )

@router.post("/quick")
async def quick_chat(
    message: str,
    provider: str = DEFAULT_PROVIDER,
    model: str = DEFAULT_MODEL
):
    """Quick chat endpoint with minimal configuration"""
    try:
        request = UnifiedChatRequest(
            message=message,
            provider=provider,
            model=model,
            max_tokens=200  # Quick response
        )
        return await unified_chat(request)
    except Exception as e:
        return {"error": str(e), "provider": provider, "model": model}

@router.post("/compare")
async def compare_providers(
    message: str,
    providers: List[str] = ["ollama", "gemini"],
    models: Optional[Dict[str, str]] = None
):
    """Compare responses from multiple providers"""
    start_time = asyncio.get_event_loop().time()
    
    if not models:
        models = {
            "ollama": "qwen3:8b",
            "gemini": "gemini-1.5-flash",
            "openai": "gpt-4o-mini",
            "claude": "claude-3-haiku-20240307"
        }
    
    results = []
    
    for provider in providers:
        if provider not in PROVIDERS:
            results.append({
                "provider": provider,
                "model": models.get(provider, "unknown"),
                "response": f"Error: Unknown provider {provider}",
                "success": False
            })
            continue
        
        try:
            model = models.get(provider, DEFAULT_MODEL)
            request = UnifiedChatRequest(
                message=message,
                provider=provider,
                model=model,
                max_tokens=300
            )
            response = await unified_chat(request)
            
            results.append({
                "provider": provider,
                "model": model,
                "response": response.response,
                "processing_time": response.processing_time,
                "success": True
            })
        except Exception as e:
            results.append({
                "provider": provider,
                "model": models.get(provider, "unknown"),
                "response": f"Error: {str(e)}",
                "success": False
            })
    
    total_time = asyncio.get_event_loop().time() - start_time
    
    return {
        "message": message,
        "comparisons": results,
        "total_processing_time": round(total_time, 2),
        "timestamp": datetime.now().isoformat()
    }

@router.get("/health")
async def health_check():
    """Health check for all providers"""
    provider_status = {}
    
    for name, provider_class in PROVIDERS.items():
        try:
            status = "healthy" if provider_class.is_available() else "unavailable"
            provider_status[name] = {
                "status": status,
                "api_key_configured": (
                    bool(GEMINI_API_KEY) if name == "gemini" else
                    bool(OPENAI_API_KEY) if name == "openai" else
                    bool(CLAUDE_API_KEY) if name == "claude" else
                    True  # ollama
                )
            }
        except Exception as e:
            provider_status[name] = {
                "status": "error",
                "error": str(e)
            }
    
    return {
        "status": "healthy",
        "providers": provider_status,
        "default_provider": DEFAULT_PROVIDER,
        "timestamp": datetime.now().isoformat()
    }

@router.get("/config")
async def get_config():
    """Get current configuration"""
    return {
        "default_provider": DEFAULT_PROVIDER,
        "default_model": DEFAULT_MODEL,
        "default_temperature": DEFAULT_TEMPERATURE,
        "default_max_tokens": DEFAULT_MAX_TOKENS,
        "available_providers": list(PROVIDERS.keys()),
        "api_keys_configured": {
            "gemini": bool(GEMINI_API_KEY),
            "openai": bool(OPENAI_API_KEY),
            "claude": bool(CLAUDE_API_KEY)
        },
        "ollama_url": OLLAMA_BASE_URL,
        "provider_models": PROVIDER_MODELS
    }