# config/config.py
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Claude API configuration
CLAUDE_API_KEY = os.getenv("ANTHROPIC_API_KEY")
# CLAUDE_MODEL = "claude-3-7-sonnet-20250219"  # Latest Claude 3.7 model
CLAUDE_MODEL = "claude-sonnet-4-20250514"  # Latest Claude 3.5 model
# Application configuration
MAX_TOKENS = 20000  # Reduced to a more reliable size that should still be sufficient
TEMPERATURE = 0.1
JSON_MODE = False  # Disable JSON mode and use system instructions instead

# Cache configuration
CACHE_ENABLED = os.getenv("CACHE_ENABLED", "true").lower() in ("true", "1", "yes")
CACHE_MAX_AGE_DAYS = int(os.getenv("CACHE_MAX_AGE_DAYS", "30"))

# Data provider configuration
DATA_PROVIDER_TYPE = os.getenv("DATA_PROVIDER_TYPE", "postgres")  # Use PostgreSQL by default
DB_CONNECTION_STRING = os.getenv("DB_CONNECTION_STRING", "")