# data/motivator_descriptors.py

MOTIVATOR_DESCRIPTORS = """
MOTIVATORS - why a person does what they do; it defines and identifies what motivates people. It measures the motivation (and strength) behind behaviors, using 6 different motivators. NOTE SCORE SPECTRUM, (Primary motivators, rank 1-4; Situational motivators, rank 5-8; Indifferent motivators, rank 9-12, situations demanding these motivators could cause an adverse impact on the individual)

KNOWLEDGE - A drive for knowledge and learning.
INSTINCTIVE - Driven by utilizing past experiences, intuition and seeking specific knowledge when necessary
Example:
- I only learn what is necessary to do my job well. 
- I will put in the necessary time to learn what I want to know. To do this, I need access to well-organized resources like a knowledge-base or past project briefs, it will help me be efficient and save time; and I can solve problems on my own, without asking others. This way I can maintain a face pace and schedule to my work.
Stress Factors - might become frustrated with others who need more information in a high-pressure situation.

INTELLECTUAL - Driven by opportunities to learn, acquire knowledge and the discovery of truth
Example:
- I want to know everything there is to know about a subject. 
- I will spend vast amounts of time and energy to learn. 
- Continuous Learning is what drives me. If I do not have continuous learning opportunities, I will stagnate in my role, my productivity will drop and I could experience burnout.
Stress Factors - avoid acting until they've gathered all the information they can. Considering every possible outcome can lead to worrying and confusion.

UTILITY - A drive for practicality, value and ROI.
SELFLESS - Driven by completing tasks for the sake of completion, with little expectation of personal return
Example:
- Getting the task done is important, doesn't matter if I receive reward and recognition. 
- I tend to focus on the greater good versus a return on the investment of my resources of time and effort and access to people.
Stress Factors - In difficult situations, can have a hard time adapting. Their focus hones in on task completion can lead to missed deadlines.

RESOURCEFUL - Driven by practical results, maximizing both efficiency and returns for their investments of time, talent, energy and resources
Example:
- The results I deliver must match the talent, effort and time I put into the job. 
- I will become frustrated and disengaged with my work, if my talent, effort and time is wasted on a regular basis.
Stress Factors - fall back on viewing everything through a lens of efficiency, overemphasize profit over people. 

SURROUNDINGS - A drive for beauty and creative expression.
OBJECTIVE - Driven by the functionality and objectivity of their surroundings
Example:
- I am not distracted in environments filled with chaos and have the ability to view everything in pieces and focus on one piece at a time. 
- I prefer to break a whole project or idea down into measurable parts and look at them independently of one another.
Stress Factors - stop seeing the bigger picture because they're too focused on the functional details, not the larger effect and response.
Their actions can lack cohesion and overall understanding of the experience of others and they have a hard time understanding what the issue is— the work is done in their eyes. 

HARMONIOUS - Driven by the experience, subjective viewpoints and balance in their surroundings.
Example:
- I feel a high level of satisfaction when I can create harmony and tranquillity in all aspects of my life. I look at the big picture. 
- I like to complete projects in order to see the full picture seamlessly evolving over time.
Stress Factors - need for balance, results in getting caught in details that don't matter when a deadline is approaching. Easily frustrated by a perceived lack of cohesion on the part of others involved, as they don't 'zoom out' and see the bigger picture.

OTHERS - A drive for compassion and helping others.
INTENTIONAL - Driven to assist others for a specific purpose, not just for the sake of being helpful or supportive.
Example:
- I see the world and its people as part of the toolset I have to accomplish my own goals. So, I am selective about who, when, why and how much I am willing to give of myself to others. 
- I care about others but on my own terms and I can be fiercely loyal and protective of those I care about. 
- For my work I want KPIs against which I am measured and recognized.
Stress Factors - narrow down their scope and focus on helping others only if they see a personal benefit. 
Under pressure, don't want to help those who will not help themselves! They aren't looking for compromise, which can result in them looking for problems and conflicts instead.

ALTRUISTIC - Driven to assist others for the satisfaction of being helpful and supportive.
Example:
- I have a keen sense of noticing and responding to what others need. 
- I believe everyone should have the opportunity to be the best they can be and will generously share my time, talent and resources, without expecting anything in return. 
- I prefer to avoid conflict and will create a win-win outcome for all concerned.
Altruistic is people oriented versus Selfless which is task oriented.
Stress Factors - begin to worry and question if they're doing enough good in their role and through their work for others. Altruistic people will try to seek out meaning through helping others in each project and can become distressed if they can't find it. 

POWER - A drive for uniqueness, status and to lead.
COLLABORATIVE - Driven by being in a supporting role and contributing with little need for individual recognition.
Example:
- I am driven by working on team projects, because I feel included and connected the team. 
- I go with the flow. 
- I am a team player. I believe in a cooperative community of people working towards the same goal. 
- I focus on my contribution rather than advancing my position and enjoy working behind the scenes and getting things done.
Stress Factors - default is to help the group succeed and move forward, but they might agree under pressure even if they know otherwise.
In the worst-case scenario, this can result in them following a leader or cause to their own detriment or pressure when to act against their best interests.

COMMANDING - Driven by status, recognition and control over personal freedom.
Example:
- I am driven to create winning strategies, and my passion is to create an enduring legacy through my work. 
- I am not necessarily aggressive or direct, but I want to be the "go-to" person for those around me, irrespective of the problem. 
- I like to work with a plan in place and I am not afraid of taking charge if a situation demands it.
Stress Factors - When people with a Commanding Motivator are under pressure, they can become very protective of themselves and their goals and plans. Commanding people are highly driven by the concept of controlling their own destiny and don't want to take orders or devote their time to another person's cause in heightened situations.

METHODOLOGIES - A drive for unity, order and a system for living.
RECEPTIVE - Driven by new ideas, methods and opportunities that fall outside a defined system for living.
Example:
- I like to challenge the status quo and find new ways to complete routine tasks, set my own path and direct my actions. 
- I consider myself an innovator, because I value new ideas, technology and work hard to stay creative in the latest industry or education trends. 
Stress Factors - Under stress, can get stubborn when forced by the idea that 'we should just do it the way it's always been done', can reject that notion and chase the thrill of a new method or approach, even if it's not the right call.

STRUCTURED - Driven by traditional approaches, proven methods and a defined system for living.
Example:
- I work well when I am contributing to a well-defined system and approach. 
- If I show you my work, I expect you to do the same. 
- I live by the mantra "if it's not broken, don't fix it". 
- I don't like to pivot my way of working quickly and tend to not respond well to frequent change.
Stress Factors - In extreme situations, can become reliant on their principles and beliefs to guide their actions and decisions, and can become adamant and protective of those beliefs as they try to keep things in check or in their control.
"""