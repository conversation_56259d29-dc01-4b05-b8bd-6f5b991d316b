# data_providers/base_provider.py
from abc import ABC, abstractmethod
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional, List

class DataProvider(ABC):
    """Abstract base class for data providers"""
    
    @abstractmethod
    async def get_candidate_info(self, identifier: str) -> Tuple[str, str, Dict[str, int], Dict[str, int]]:
        """
        Get candidate information by identifier (email, ID, etc.)
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            
        Returns:
            Tuple containing:
            - name (str): Candidate's name
            - gender (str): Candidate's gender
            - style_scores (Dict[str, int]): Style scores
            - motivator_scores (Dict[str, int]): Motivator scores
            
        Raises:
            Exception: If candidate not found or error occurs
        """
        pass
    
    @abstractmethod
    async def save_assessment(self, identifier: str, data: Dict[str, Any]) -> str:
        """
        Save assessment for a candidate
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            data (Dict[str, Any]): Assessment data
            
        Returns:
            str: Reference to the saved assessment (e.g., file path, record ID)
            
        Raises:
            Exception: If saving fails
        """
        pass
    
    @abstractmethod
    async def get_assessment(self, identifier: str, max_age_days: int = 30) -> Optional[Dict[str, Any]]:
        """
        Get the latest assessment for a candidate, if within the age limit
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            max_age_days (int): Maximum age in days
            
        Returns:
            Optional[Dict[str, Any]]: Assessment data or None if not found or too old
        """
        pass
    
    @abstractmethod
    async def get_assessment_history(self, identifier: str) -> List[Dict[str, Any]]:
        """
        Get all assessments for a candidate
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            
        Returns:
            List[Dict[str, Any]]: List of assessments, sorted by date (newest first)
        """
        pass
    
    @abstractmethod
    async def clear_assessments(self, identifier: str) -> int:
        """
        Clear all assessments for a candidate
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            
        Returns:
            int: Number of assessments deleted
        """
        pass