# data_providers/factory.py
from typing import Optional
from loguru import logger

from data_providers.base_provider import DataProvider
from data_providers.file_provider import FileDataProvider
from data_providers.db_provider import DatabaseDataProvider
from data_providers.pg_provider import PostgreSQLDataProvider

class DataProviderFactory:
    """Factory for creating data providers"""
    
    @staticmethod
    def create_provider(provider_type: str = "file", **kwargs) -> DataProvider:
        """
        Create a data provider of the specified type
        
        Args:
            provider_type (str): Type of provider ('file', 'database', or 'postgres')
            **kwargs: Additional arguments for the provider
            
        Returns:
            DataProvider: Instance of the requested provider
            
        Raises:
            ValueError: If the provider type is unknown
        """
        if provider_type.lower() == "file":
            logger.info("Creating file-based data provider")
            return FileDataProvider(**kwargs)
        elif provider_type.lower() in ("db", "database"):
            logger.info("Creating database data provider")
            return DatabaseDataProvider(**kwargs)
        elif provider_type.lower() in ("pg", "postgres", "postgresql"):
            logger.info("Creating PostgreSQL data provider")
            return PostgreSQLDataProvider(**kwargs)
        else:
            raise ValueError(f"Unknown provider type: {provider_type}")