# data_providers/file_provider.py
import os
import json
import glob
from typing import Dict, <PERSON>, Tu<PERSON>, Optional, List
from datetime import datetime, timedelta
from loguru import logger

from data_providers.base_provider import DataProvider
from dummy.score_generator import DummyScoreGenerator
from student_data import get_student_data_by_email, convert_to_assessment_format

class FileDataProvider(DataProvider):
    """File-based implementation of DataProvider"""
    
    def __init__(self, base_dir: str = "output"):
        """
        Initialize the file-based data provider
        
        Args:
            base_dir (str): Base directory for storing files
        """
        self.base_dir = base_dir
        self.assessments_dir = os.path.join(base_dir, "api_responses")
        
        # Create directories if they don't exist
        os.makedirs(self.assessments_dir, exist_ok=True)
        
        # Initialize score generator (temporary)
        self.score_generator = DummyScoreGenerator()
    
    async def get_candidate_info(self, identifier: str) -> Tuple[str, str, Dict[str, int], Dict[str, int]]:
        """
        Get candidate information by identifier (tries database first, falls back to dummy)
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            
        Returns:
            Tuple containing candidate information
        """
        try:
            # Try to get info from database first
            student_data = get_student_data_by_email(identifier)
            
            if student_data:
                # Use real name from database
                name, gender, style_scores, motivator_scores = convert_to_assessment_format(student_data)
                
                if name:
                    # If we have scores from the database, use them, otherwise get dummy scores
                    if not style_scores or not motivator_scores:
                        _, _, style_scores, motivator_scores = self.score_generator.get_candidate_info(identifier)
                    
                    return name, gender, style_scores, motivator_scores
            
            # Fall back to dummy generator if database query failed or returned no data
            return self.score_generator.get_candidate_info(identifier)
            
        except Exception as e:
            logger.error(f"Error getting real data, falling back to dummy: {str(e)}")
            # Fall back to dummy generator
            return self.score_generator.get_candidate_info(identifier)
    
    async def save_assessment(self, identifier: str, data: Dict[str, Any]) -> str:
        """
        Save assessment to a file
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            data (Dict[str, Any]): Assessment data
            
        Returns:
            str: Path to the saved file
        """
        try:
            # Ensure the data has a timestamp
            if "timestamp" not in data:
                data["timestamp"] = datetime.now().isoformat()
                
            # Sanitize identifier for filename
            safe_id = identifier.replace("@", "_at_").replace(".", "_")
            
            # Create timestamp-based filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{safe_id}_{timestamp}.json"
            
            # Create full path
            filepath = os.path.join(self.assessments_dir, filename)
            
            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Assessment saved to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving assessment: {str(e)}")
            raise
    
    async def get_assessment(self, identifier: str, max_age_days: int = 30) -> Optional[Dict[str, Any]]:
        """
        Get the latest assessment for a candidate, if within the age limit
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            max_age_days (int): Maximum age in days
            
        Returns:
            Optional[Dict[str, Any]]: Assessment data or None if not found or too old
        """
        try:
            assessments = await self.get_assessment_history(identifier)
            
            if not assessments:
                return None
            
            # Get the latest assessment
            latest = assessments[0]  # Assessments are already sorted newest first
            
            # Check if the assessment is within the age limit
            if "timestamp" in latest:
                try:
                    assessment_time = datetime.fromisoformat(latest["timestamp"])
                    age_limit = datetime.now() - timedelta(days=max_age_days)
                    
                    if assessment_time < age_limit:
                        logger.info(f"Latest assessment for {identifier} is too old ({assessment_time.isoformat()})")
                        return None
                        
                except (ValueError, TypeError) as e:
                    logger.warning(f"Could not parse timestamp in assessment: {str(e)}")
                    # Continue anyway, returning the latest
            
            return latest
            
        except Exception as e:
            logger.error(f"Error getting assessment: {str(e)}")
            return None
    
    async def get_assessment_history(self, identifier: str) -> List[Dict[str, Any]]:
        """
        Get all assessments for a candidate
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            
        Returns:
            List[Dict[str, Any]]: List of assessments, sorted by date (newest first)
        """
        try:
            # Sanitize identifier for filename
            safe_id = identifier.replace("@", "_at_").replace(".", "_")
            
            # Get all matching files
            pattern = os.path.join(self.assessments_dir, f"{safe_id}_*.json")
            matching_files = glob.glob(pattern)
            
            results = []
            for file_path in matching_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        results.append(data)
                except Exception as e:
                    logger.error(f"Error loading assessment from {file_path}: {str(e)}")
            
            # Sort by timestamp (newest first)
            results.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting assessment history: {str(e)}")
            return []
    
    async def clear_assessments(self, identifier: str) -> int:
        """
        Clear all assessments for a candidate
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            
        Returns:
            int: Number of assessments deleted
        """
        try:
            # Sanitize identifier for filename
            safe_id = identifier.replace("@", "_at_").replace(".", "_")
            
            # Get all matching files
            pattern = os.path.join(self.assessments_dir, f"{safe_id}_*.json")
            matching_files = glob.glob(pattern)
            
            # Delete files
            deleted_count = 0
            for file_path in matching_files:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"Error deleting file {file_path}: {str(e)}")
            
            logger.info(f"Deleted {deleted_count}/{len(matching_files)} assessments for {identifier}")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error clearing assessments: {str(e)}")
            return 0