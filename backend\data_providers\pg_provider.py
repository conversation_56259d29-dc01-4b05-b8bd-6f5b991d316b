from typing import Dict, Any, <PERSON><PERSON>, Optional, List
from datetime import datetime, timed<PERSON>ta
from loguru import logger

from data_providers.base_provider import DataProvider
from student_data import get_student_data_by_email, convert_to_assessment_format

class PostgreSQLDataProvider(DataProvider):
    """PostgreSQL database implementation of DataProvider"""
    
    def __init__(self, **kwargs):
        """
        Initialize the PostgreSQL data provider
        
        Args:
            **kwargs: Additional initialization parameters
        """
        # Initialize any needed properties
        self.base_dir = kwargs.get('base_dir', 'output')
        self.assessments_dir = f"{self.base_dir}/api_responses"
        
        logger.info("PostgreSQL data provider initialized")
    
    async def get_candidate_info(self, identifier: str) -> Tuple[str, str, Dict[str, int], Dict[str, int]]:
        """
        Get candidate information by identifier (email)
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            
        Returns:
            Tuple containing (name, gender, style_scores, motivator_scores)
        """
        try:
            # Get student data from database
            student_data = get_student_data_by_email(identifier)
            
            if not student_data:
                logger.warning(f"No data found for candidate with email: {identifier}")
                # Return a reasonable default or None
                return None, None, None, None
            
            # Convert the data to the format expected by the system
            name, gender, style_scores, motivator_scores = convert_to_assessment_format(student_data)
            
            if not name:
                logger.warning(f"Missing name for candidate: {identifier}")
                return None, None, None, None
            
            # Initialize with empty dictionaries if they're None
            style_scores = style_scores or {}
            motivator_scores = motivator_scores or {}
            
            # Set a default gender if not provided
            gender = gender or 'male'
            
            if not style_scores and not motivator_scores:
                logger.warning(f"No assessment scores found for candidate: {identifier}")
            else:
                logger.info(f"Successfully retrieved data for candidate: {name} ({gender})")
            
            return name, gender, style_scores, motivator_scores
        
        except Exception as e:
            logger.error(f"Error retrieving candidate info: {str(e)}")
            return None, None, None, None
    
    async def save_assessment(self, identifier: str, data: Dict[str, Any]) -> str:
        """
        Save assessment to a file (using FileDataProvider functionality)
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            data (Dict[str, Any]): Assessment data
            
        Returns:
            str: Path to the saved file
        """
        try:
            # Ensure the data has a timestamp
            if "timestamp" not in data:
                data["timestamp"] = datetime.now().isoformat()
                
            # Sanitize identifier for filename
            safe_id = identifier.replace("@", "_at_").replace(".", "_")
            
            # Create timestamp-based filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{safe_id}_{timestamp}.json"
            
            # Create full path
            import os
            os.makedirs(self.assessments_dir, exist_ok=True)
            filepath = os.path.join(self.assessments_dir, filename)
            
            # Save to file
            import json
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Assessment saved to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving assessment: {str(e)}")
            raise
    
    async def get_assessment(self, identifier: str, max_age_days: int = 30) -> Optional[Dict[str, Any]]:
        """
        Get the latest assessment for a candidate, if within the age limit
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            max_age_days (int): Maximum age in days
            
        Returns:
            Optional[Dict[str, Any]]: Assessment data or None if not found or too old
        """
        try:
            assessments = await self.get_assessment_history(identifier)
            
            if not assessments:
                return None
            
            # Get the latest assessment
            latest = assessments[0]  # Assessments are already sorted newest first
            
            # Check if the assessment is within the age limit
            if "timestamp" in latest:
                try:
                    assessment_time = datetime.fromisoformat(latest["timestamp"])
                    age_limit = datetime.now() - timedelta(days=max_age_days)
                    
                    if assessment_time < age_limit:
                        logger.info(f"Latest assessment for {identifier} is too old ({assessment_time.isoformat()})")
                        return None
                        
                except (ValueError, TypeError) as e:
                    logger.warning(f"Could not parse timestamp in assessment: {str(e)}")
                    # Continue anyway, returning the latest
            
            return latest
            
        except Exception as e:
            logger.error(f"Error getting assessment: {str(e)}")
            return None
    
    async def get_assessment_history(self, identifier: str) -> List[Dict[str, Any]]:
        """
        Get all assessments for a candidate (using file-based storage for now)
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            
        Returns:
            List[Dict[str, Any]]: List of assessments, sorted by date (newest first)
        """
        try:
            # Sanitize identifier for filename
            safe_id = identifier.replace("@", "_at_").replace(".", "_")
            
            # Get all matching files
            import glob
            import os
            pattern = os.path.join(self.assessments_dir, f"{safe_id}_*.json")
            matching_files = glob.glob(pattern)
            
            results = []
            for file_path in matching_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        import json
                        data = json.load(f)
                        results.append(data)
                except Exception as e:
                    logger.error(f"Error loading assessment from {file_path}: {str(e)}")
            
            # Sort by timestamp (newest first)
            results.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting assessment history: {str(e)}")
            return []
    
    async def clear_assessments(self, identifier: str) -> int:
        """
        Clear all assessments for a candidate
        
        Args:
            identifier (str): Candidate identifier (e.g., email)
            
        Returns:
            int: Number of assessments deleted
        """
        try:
            # Sanitize identifier for filename
            safe_id = identifier.replace("@", "_at_").replace(".", "_")
            
            # Get all matching files
            import glob
            import os
            pattern = os.path.join(self.assessments_dir, f"{safe_id}_*.json")
            matching_files = glob.glob(pattern)
            
            # Delete files
            deleted_count = 0
            for file_path in matching_files:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"Error deleting file {file_path}: {str(e)}")
            
            logger.info(f"Deleted {deleted_count}/{len(matching_files)} assessments for {identifier}")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error clearing assessments: {str(e)}")
            return 0 