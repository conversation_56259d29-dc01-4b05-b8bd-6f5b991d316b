# db_connection.py
import os
import psycopg2
from dotenv import load_dotenv
from loguru import logger
import traceback

# Load environment variables
load_dotenv()

# Log database connection parameters (without password)
def log_db_params():
    """Log database connection parameters for debugging"""
    logger.debug(f"Database connection parameters:")
    logger.debug(f"  Host: {os.getenv('PG_DB_HOST')}")
    logger.debug(f"  User: {os.getenv('PG_DB_USER')}")
    logger.debug(f"  Port: {os.getenv('PG_DB_PORT')}")
    logger.debug(f"  Database: {os.getenv('PG_DB_NAME')}")

def get_db_connection():
    """Create and return a connection to the PostgreSQL database"""
    try:
        logger.debug("Attempting to connect to database...")
        log_db_params()
        
        conn = psycopg2.connect(
            host=os.getenv("PG_DB_HOST"),
            user=os.getenv("PG_DB_USER"),
            password=os.getenv("PG_DB_PASSWORD"),
            port=os.getenv("PG_DB_PORT"),
            database=os.getenv("PG_DB_NAME")
        )
        
        logger.debug("Database connection established successfully")
        return conn
    except Exception as e:
        logger.error(f"Database connection error: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise