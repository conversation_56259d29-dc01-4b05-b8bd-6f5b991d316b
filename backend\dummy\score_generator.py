# dummy/score_generator.py
import random
from typing import Dict, Tuple

class DummyScoreGenerator:
    """
    Temporary class to generate dummy psychometric scores based on email.
    This will be replaced with actual database fetching in the future.
    """
    
    @staticmethod
    def get_candidate_info(email: str) -> Tuple[str, str, Dict[str, int], Dict[str, int]]:
        """
        Generate dummy candidate information based on email
        
        Args:
            email (str): Candidate's email address
            
        Returns:
            Tuple containing:
            - name (str): Generated name
            - gender (str): Generated gender
            - style_scores (Dict[str, int]): Generated style scores
            - motivator_scores (Dict[str, int]): Generated motivator scores
        """
        # Extract name parts from email
        name_part = email.split('@')[0]
        parts = name_part.split('.')
        
        if len(parts) >= 2:
            first_name = parts[0].capitalize()
            last_name = parts[1].capitalize()
        else:
            first_name = name_part.capitalize()
            last_name = "<PERSON>"  # Default last name
            
        full_name = f"{first_name} {last_name}"
        
        # Determine gender (very simplified - in reality, would come from user profile)
        # This is just for demo purposes
        common_female_names = ['mary', 'linda', 'susan', 'sarah', 'jessica', 'emily']
        if any(name.lower() in first_name.lower() for name in common_female_names):
            gender = "female"
        else:
            gender = "male"  # Default for demo
        
        # Generate dummy style scores
        style_scores = {
            "REFLECTIVE": random.randint(60, 100),
            "RESERVED": random.randint(60, 100),
            "STEADY": random.randint(60, 100),
            "PRECISE": random.randint(60, 100),
        }
        
        # Add additional styles with lower probability
        additional_styles = ["DIRECT", "OUTGOING", "DYNAMIC", "PIONEERING"]
        for style in additional_styles:
            if random.random() < 0.5:  # 50% chance to include each additional style
                style_scores[style] = random.randint(60, 100)
        
        # Generate dummy motivator scores
        motivator_scores = {
            "INSTINCTIVE": random.randint(5, 95),
            "INTELLECTUAL": random.randint(5, 95),
            "SELFLESS": random.randint(5, 95),
            "RESOURCEFUL": random.randint(5, 95),
            "HARMONIOUS": random.randint(5, 95),
            "INTENTIONAL": random.randint(5, 95),
            "ALTRUISTIC": random.randint(5, 95),
            "COMMANDING": random.randint(5, 95),
            "COLLABORATIVE": random.randint(5, 95),
            "RECEPTIVE": random.randint(5, 95),
            "STRUCTURED": random.randint(5, 95),
        }
        
        return full_name, gender, style_scores, motivator_scores