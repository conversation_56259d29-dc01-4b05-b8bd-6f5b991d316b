2025-05-16 15:55:11.069 | ERROR    | __main__:check_jobs_loop:2430 - Error in check_jobs_loop: name 'check_scheduled_jobs' is not defined
2025-05-16 15:56:08.518 | ERROR    | __main__:check_jobs_loop:2430 - Error in check_jobs_loop: name 'check_scheduled_jobs' is not defined
2025-05-16 15:56:51.402 | ERROR    | __main__:check_jobs_loop:2430 - Error in check_jobs_loop: name 'check_scheduled_jobs' is not defined
2025-05-16 16:00:27.879 | INFO     | __main__:log_message:138 - Fetching source schema...
2025-05-16 16:00:28.099 | ERROR    | __main__:log_message:134 - Error getting schema objects: connection to server at "*************", port 5432 failed: FATAL:  database "tcs_ion_backup_feb18" does not exist

2025-05-16 16:00:28.099 | INFO     | __main__:log_message:138 - Fetching target schema...
2025-05-16 16:00:28.170 | ERROR    | __main__:log_message:134 - <PERSON><PERSON>r getting schema objects: connection to server at "localhost" (::1), port 5432 failed: FATAL:  password authentication failed for user "postgres"

2025-05-16 16:00:28.170 | INFO     | __main__:log_message:138 - Comparing schemas...
2025-05-16 16:00:28.170 | INFO     | __main__:log_message:138 - Schema comparison completed
2025-05-16 16:01:00.922 | INFO     | __main__:log_message:138 - Fetching source schema...
2025-05-16 16:01:01.126 | ERROR    | __main__:log_message:134 - Error getting schema objects: connection to server at "*************", port 5432 failed: FATAL:  database "tcs_ion_backup_feb18" does not exist

2025-05-16 16:01:01.126 | INFO     | __main__:log_message:138 - Fetching target schema...
2025-05-16 16:01:01.179 | ERROR    | __main__:log_message:134 - Error getting schema objects: connection to server at "localhost" (::1), port 5432 failed: FATAL:  password authentication failed for user "postgres"

2025-05-16 16:01:01.184 | INFO     | __main__:log_message:138 - Comparing schemas...
2025-05-16 16:01:01.184 | INFO     | __main__:log_message:138 - Schema comparison completed
2025-05-16 16:02:49.351 | INFO     | __main__:log_message:138 - Testing database connections...
2025-05-16 16:02:49.614 | INFO     | __main__:log_message:138 - Successfully connected to tcs_ion_backup_18feb on *************:5432
2025-05-16 16:02:49.615 | INFO     | __main__:log_message:138 - Server version: PostgreSQL 13.9 (Ubuntu 13.9-1.pgdg20.04+1) on aarch64-unknown-linux-gnu, compiled by gcc (Ubuntu 9.4.0-1ubuntu1~20.04.1) 9.4.0, 64-bit
2025-05-16 16:02:49.662 | ERROR    | __main__:log_message:134 - Failed to connect to tcs_ion_backup_16may2025 on localhost:5432
2025-05-16 16:02:49.664 | ERROR    | __main__:log_message:134 - Error: connection to server at "localhost" (::1), port 5432 failed: FATAL:  password authentication failed for user "postgres"

2025-05-16 16:02:49.664 | INFO     | __main__:log_message:138 - Target database doesn't exist. Attempting to create it...
2025-05-16 16:02:49.704 | ERROR    | __main__:log_message:134 - Error creating database: connection to server at "localhost" (::1), port 5432 failed: FATAL:  password authentication failed for user "postgres"

2025-05-16 16:02:49.707 | ERROR    | __main__:log_message:134 - Cannot proceed: Failed to create target database
2025-05-16 16:04:21.100 | INFO     | __main__:log_message:138 - Testing database connections...
2025-05-16 16:04:21.369 | INFO     | __main__:log_message:138 - Successfully connected to tcs_ion_backup_18feb on *************:5432
2025-05-16 16:04:21.369 | INFO     | __main__:log_message:138 - Server version: PostgreSQL 13.9 (Ubuntu 13.9-1.pgdg20.04+1) on aarch64-unknown-linux-gnu, compiled by gcc (Ubuntu 9.4.0-1ubuntu1~20.04.1) 9.4.0, 64-bit
2025-05-16 16:04:21.450 | ERROR    | __main__:log_message:134 - Failed to connect to tcs_ion_backup_16may2025 on localhost:5432
2025-05-16 16:04:21.450 | ERROR    | __main__:log_message:134 - Error: connection to server at "localhost" (::1), port 5432 failed: FATAL:  database "tcs_ion_backup_16may2025" does not exist

2025-05-16 16:04:21.450 | INFO     | __main__:log_message:138 - Target database doesn't exist. Attempting to create it...
2025-05-16 16:04:21.515 | INFO     | __main__:log_message:138 - Target PostgreSQL server version: PostgreSQL 17.4 on x86_64-windows, compiled by msvc-19.42.34436, 64-bit
2025-05-16 16:04:21.519 | INFO     | __main__:log_message:138 - Target server default encoding: UTF8
2025-05-16 16:04:21.519 | INFO     | __main__:log_message:138 - Creating database 'tcs_ion_backup_16may2025' with encoding 'UTF8'...
2025-05-16 16:04:21.809 | INFO     | __main__:log_message:138 - Database 'tcs_ion_backup_16may2025' created successfully.
2025-05-16 16:04:21.809 | INFO     | __main__:log_message:138 - Getting source database statistics for verification...
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Source database has 17 tables in schema 'public'
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.assessments_backup3: 11526 rows
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.registered_participants: 7602 rows
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.assessments: 5662 rows
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.assessments_backup: 5644 rows
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.assessments_backup2: 5630 rows
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.country: 253 rows
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.lsi_statements: 45 rows
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.states: 41 rows
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.disc_development_stmts: 12 rows
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.products: 11 rows
2025-05-16 16:04:22.919 | INFO     | __main__:log_message:138 - Table public.institutions: 7 rows
2025-05-16 16:04:22.929 | INFO     | __main__:log_message:138 - Table public.counsellors: 7 rows
2025-05-16 16:04:22.929 | INFO     | __main__:log_message:138 - Table public.users: 4 rows
2025-05-16 16:04:22.929 | INFO     | __main__:log_message:138 - Table public.channel_partner: 3 rows
2025-05-16 16:04:22.929 | INFO     | __main__:log_message:138 - Table public.playbook_statements: 0 rows
2025-05-16 16:04:22.933 | INFO     | __main__:log_message:138 - Table public.career_database: 37 rows
2025-05-16 16:04:22.933 | INFO     | __main__:log_message:138 - Table public.assessments_assigned: 0 rows
2025-05-16 16:04:22.939 | INFO     | __main__:log_message:138 - Exporting data from tcs_ion_backup_18feb schema public...
2025-05-16 16:04:22.944 | INFO     | __main__:log_message:138 - Starting: Export database
2025-05-16 16:04:23.055 | WARNING  | __main__:log_message:136 - 'PGPASSWORD' is not recognized as an internal or external command,
operable program or batch file.

2025-05-16 16:04:23.056 | ERROR    | __main__:log_message:134 - Error: Export database failed with exit code 1
2025-05-16 16:07:20.253 | INFO     | __main__:log_message:138 - Testing database connections...
2025-05-16 16:07:20.524 | INFO     | __main__:log_message:138 - Successfully connected to tcs_ion_backup_18feb on *************:5432
2025-05-16 16:07:20.524 | INFO     | __main__:log_message:138 - Server version: PostgreSQL 13.9 (Ubuntu 13.9-1.pgdg20.04+1) on aarch64-unknown-linux-gnu, compiled by gcc (Ubuntu 9.4.0-1ubuntu1~20.04.1) 9.4.0, 64-bit
2025-05-16 16:07:20.579 | INFO     | __main__:log_message:138 - Successfully connected to tcs_ion_backup_16may2025 on localhost:5432
2025-05-16 16:07:20.579 | INFO     | __main__:log_message:138 - Server version: PostgreSQL 17.4 on x86_64-windows, compiled by msvc-19.42.34436, 64-bit
2025-05-16 16:07:20.579 | INFO     | __main__:log_message:138 - Getting source database statistics for verification...
2025-05-16 16:07:21.646 | INFO     | __main__:log_message:138 - Source database has 17 tables in schema 'public'
2025-05-16 16:07:21.647 | INFO     | __main__:log_message:138 - Table public.assessments_backup3: 11526 rows
2025-05-16 16:07:21.647 | INFO     | __main__:log_message:138 - Table public.registered_participants: 7602 rows
2025-05-16 16:07:21.647 | INFO     | __main__:log_message:138 - Table public.assessments: 5662 rows
2025-05-16 16:07:21.648 | INFO     | __main__:log_message:138 - Table public.assessments_backup: 5644 rows
2025-05-16 16:07:21.648 | INFO     | __main__:log_message:138 - Table public.assessments_backup2: 5630 rows
2025-05-16 16:07:21.648 | INFO     | __main__:log_message:138 - Table public.country: 253 rows
2025-05-16 16:07:21.648 | INFO     | __main__:log_message:138 - Table public.lsi_statements: 45 rows
2025-05-16 16:07:21.649 | INFO     | __main__:log_message:138 - Table public.states: 41 rows
2025-05-16 16:07:21.650 | INFO     | __main__:log_message:138 - Table public.disc_development_stmts: 12 rows
2025-05-16 16:07:21.650 | INFO     | __main__:log_message:138 - Table public.products: 11 rows
2025-05-16 16:07:21.651 | INFO     | __main__:log_message:138 - Table public.institutions: 7 rows
2025-05-16 16:07:21.652 | INFO     | __main__:log_message:138 - Table public.counsellors: 7 rows
2025-05-16 16:07:21.653 | INFO     | __main__:log_message:138 - Table public.users: 4 rows
2025-05-16 16:07:21.655 | INFO     | __main__:log_message:138 - Table public.channel_partner: 3 rows
2025-05-16 16:07:21.655 | INFO     | __main__:log_message:138 - Table public.playbook_statements: 0 rows
2025-05-16 16:07:21.656 | INFO     | __main__:log_message:138 - Table public.career_database: 37 rows
2025-05-16 16:07:21.656 | INFO     | __main__:log_message:138 - Table public.assessments_assigned: 0 rows
2025-05-16 16:07:21.658 | INFO     | __main__:log_message:138 - Exporting data from tcs_ion_backup_18feb schema public...
2025-05-16 16:07:21.658 | INFO     | __main__:log_message:138 - Starting: Export database
2025-05-16 16:07:59.279 | INFO     | __main__:log_message:138 - Completed: Export database
2025-05-16 16:07:59.279 | INFO     | __main__:log_message:138 - Export completed in 37.62 seconds
2025-05-16 16:07:59.279 | INFO     | __main__:log_message:138 - Backup file: C:\Users\<USER>\AppData\Local\Temp\tcs_ion_backup_18feb_public_20250516_160720.dump
2025-05-16 16:07:59.279 | INFO     | __main__:log_message:138 - Importing data to tcs_ion_backup_16may2025...
2025-05-16 16:07:59.279 | INFO     | __main__:log_message:138 - Starting: Import database
2025-05-16 16:08:01.729 | WARNING  | __main__:log_message:136 - pg_restore: error: could not execute query: ERROR:  schema "public" already exists
Command was: CREATE SCHEMA public;


pg_restore: error: could not execute query: ERROR:  function public.uuid_generate_v4() does not exist
LINE 2:     id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                            ^
HINT:  No function matches the given name and argument types. You might need to add explicit type casts.
Command was: CREATE TABLE public.playbook_statements (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    assessment_id text,
    product_code text,
    counsellor_email text,
    counselling_date timestamp with time zone,
    statements jsonb,
    created_by text,
    last_modified_by text,
    created_at timestamp with time zone,
    last_modified_at timestamp with time zone DEFAULT now() NOT NULL
);


pg_restore: error: could not execute query: ERROR:  relation "public.playbook_statements" does not exist
Command was: COPY public.playbook_statements (id, assessment_id, product_code, counsellor_email, counselling_date, statements, created_by, last_modified_by, created_at, last_modified_at) FROM stdin;
pg_restore: error: could not execute query: ERROR:  relation "public.playbook_statements" does not exist
Command was: ALTER TABLE ONLY public.playbook_statements
    ADD CONSTRAINT playbook_statements_assessment_id_fkey FOREIGN KEY (assessment_id) REFERENCES public.assessments(assessment_id);


pg_restore: error: could not execute query: ERROR:  relation "public.playbook_statements" does not exist
Command was: ALTER TABLE ONLY public.playbook_statements
    ADD CONSTRAINT playbook_statements_counsellor_email_fkey FOREIGN KEY (counsellor_email) REFERENCES public.counsellors(email);


pg_restore: error: could not execute query: ERROR:  relation "public.playbook_statements" does not exist
Command was: ALTER TABLE ONLY public.playbook_statements
    ADD CONSTRAINT playbook_statements_product_code_fkey FOREIGN KEY (product_code) REFERENCES public.products(code);


pg_restore: error: could not execute query: ERROR:  relation "public.playbook_statements" does not exist
Command was: ALTER TABLE ONLY public.playbook_statements
    ADD CONSTRAINT playbook_statements_pkey PRIMARY KEY (id);


pg_restore: error: could not execute query: ERROR:  role "tcsion" does not exist
Command was: GRANT SELECT,INSERT,UPDATE ON TABLE public.assessments TO tcsion;


pg_restore:pg_restore:  error: could not execute query: ERROR:  role "tcsion" does not exist
Command was: GRANT SELECT,INSERT,UPDATE ON TABLE public.assessments_backup TO tcsion;


error: could not execute query: ERROR:  role "tcsion" does not exist
Command was: GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE public.career_database TO tcsion;


pg_restore: pg_restore: error: could not execute query: ERROR:  role "tcsion" does not exist
Command was: GRANT SELECT ON TABLE public.channel_partner TO tcsion;


error: could not execute query: ERROR:  role "tcsion" does not exist
Command was: GRANT SELECT ON TABLE public.country TO tcsion;


pg_restore: error: could not execute query: ERROR:  role "tcsion" does not exist
Command was: GRANT SELECT,INSERT,REFERENCES,DELETE,TRIGGER,TRUNCATE,UPDATE ON TABLE public.disc_development_stmts TO tcsion;


pg_restore: error: could not execute query: ERROR:  role "tcsion" does not exist
Command was: GRANT SELECT,INSERT,UPDATE ON TABLE public.lsi_statements TO tcsion;


pg_restore: error: could not execute query: ERROR:  role "tcsion" does not exist
Command was: GRANT SELECT ON TABLE public.states TO tcsion;


pg_restore: warning: errors ignored on restore: 28

2025-05-16 16:08:01.729 | ERROR    | __main__:log_message:134 - Error: Import database failed with exit code 1
2025-05-16 16:09:13.269 | INFO     | __main__:log_message:138 - Testing database connections...
2025-05-16 16:09:13.528 | INFO     | __main__:log_message:138 - Successfully connected to tcs_ion_backup_18feb on *************:5432
2025-05-16 16:09:13.528 | INFO     | __main__:log_message:138 - Server version: PostgreSQL 13.9 (Ubuntu 13.9-1.pgdg20.04+1) on aarch64-unknown-linux-gnu, compiled by gcc (Ubuntu 9.4.0-1ubuntu1~20.04.1) 9.4.0, 64-bit
2025-05-16 16:09:13.583 | INFO     | __main__:log_message:138 - Successfully connected to tcs_ion_backup_16may2025 on localhost:5432
2025-05-16 16:09:13.584 | INFO     | __main__:log_message:138 - Server version: PostgreSQL 17.4 on x86_64-windows, compiled by msvc-19.42.34436, 64-bit
2025-05-16 16:09:13.584 | INFO     | __main__:log_message:138 - Setting up target database prerequisites...
2025-05-16 16:09:13.634 | INFO     | __main__:log_message:138 - Created tcsion role
2025-05-16 16:09:13.791 | INFO     | __main__:log_message:138 - Created uuid-ossp extension
2025-05-16 16:09:13.791 | INFO     | __main__:log_message:138 - Getting source database statistics for verification...
2025-05-16 16:09:14.839 | INFO     | __main__:log_message:138 - Source database has 17 tables in schema 'public'
2025-05-16 16:09:14.839 | INFO     | __main__:log_message:138 - Table public.assessments_backup3: 11526 rows
2025-05-16 16:09:14.839 | INFO     | __main__:log_message:138 - Table public.registered_participants: 7602 rows
2025-05-16 16:09:14.839 | INFO     | __main__:log_message:138 - Table public.assessments: 5662 rows
2025-05-16 16:09:14.839 | INFO     | __main__:log_message:138 - Table public.assessments_backup: 5644 rows
2025-05-16 16:09:14.839 | INFO     | __main__:log_message:138 - Table public.assessments_backup2: 5630 rows
2025-05-16 16:09:14.839 | INFO     | __main__:log_message:138 - Table public.country: 253 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.lsi_statements: 45 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.states: 41 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.disc_development_stmts: 12 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.products: 11 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.institutions: 7 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.counsellors: 7 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.users: 4 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.channel_partner: 3 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.playbook_statements: 0 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.career_database: 37 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Table public.assessments_assigned: 0 rows
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Exporting data from tcs_ion_backup_18feb schema public...
2025-05-16 16:09:14.842 | INFO     | __main__:log_message:138 - Starting: Export database
2025-05-16 16:09:51.119 | INFO     | __main__:log_message:138 - Completed: Export database
2025-05-16 16:09:51.119 | INFO     | __main__:log_message:138 - Export completed in 36.28 seconds
2025-05-16 16:09:51.119 | INFO     | __main__:log_message:138 - Backup file: C:\Users\<USER>\AppData\Local\Temp\tcs_ion_backup_18feb_public_20250516_160913.dump
2025-05-16 16:09:51.119 | INFO     | __main__:log_message:138 - Importing data to tcs_ion_backup_16may2025...
2025-05-16 16:09:51.119 | INFO     | __main__:log_message:138 - Starting: Import database
