# main.py
import json
import os
import re
from pydantic import ValidationError
from langchain.prompts import PromptTemplate
from prompts.templates import create_assessment_prompt, JSON_FORMAT_INSTRUCTIONS
from models.input_models import AssessmentInput
from models.output_models import AssessmentOutput, ActivityItem
from services.claude_service import ClaudeService
from utils.formatter import (
    format_style_scores,
    format_motivator_scores,
    format_mba_specializations,
    format_career_groups,
    format_mba_mapping
)
from data.style_descriptors import STYLE_DESCRIPTORS
from data.motivator_descriptors import MOTIVATOR_DESCRIPTORS
from data.competency_descriptors import COMPETENCY_DESCRIPTORS
from data.mba_data import MBA_SPECIALIZATIONS, MBA_MAPPING, CAREER_GROUPS
from config.config import CLAUDE_API_KEY
from loguru import logger
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from api.auth import router as auth_router

class CareerAssessmentSystem:
    """Main class for the career assessment system"""
    
    def __init__(self, api_key=None):
        """Initialize the career assessment system"""
        self.llm_service = ClaudeService(api_key=api_key)
        self.prompt_template = create_assessment_prompt()
        
    def process_activity_format(self, activities):
        """
        Process activities list to convert from string format to structured format
        
        Args:
            activities (list): List of activity strings
            
        Returns:
            list: Processed activities in structured format where possible
        """
        result = []
        current_title = None
        current_subs = []
        
        for activity in activities:
            # Check if this is a title
            title_match = re.match(r"^TITLE:\s*(.+)$", activity)
            sub_match = re.match(r"^SUB:\s*(.+)$", activity)
            
            if title_match:
                # If we have a previous title with subs, add it to results
                if current_title and current_subs:
                    result.append(ActivityItem(
                        title=current_title,
                        sub_activities=current_subs
                    ))
                # Start a new title
                current_title = title_match.group(1).strip()
                current_subs = []
            elif sub_match and current_title:
                # Add a sub-activity to the current title
                current_subs.append(sub_match.group(1).strip())
            else:
                # Just a regular activity, add as is
                result.append(activity)
        
        # Add the last title if there is one
        if current_title and current_subs:
            result.append(ActivityItem(
                title=current_title,
                sub_activities=current_subs
            ))
            
        return result
    
    def process_assessment(self, assessment_input_dict: dict) -> dict:
        """
        Process an assessment and generate a career report
        
        Args:
            assessment_input_dict (dict): Assessment input data
            
        Returns:
            dict: The assessment output
        """
        try:
            # Validate input data with Pydantic
            assessment_input = AssessmentInput(**assessment_input_dict)
            
            # Format input data for the prompt
            formatted_style_scores = format_style_scores(assessment_input)
            formatted_motivator_scores = format_motivator_scores(assessment_input)
            formatted_mba_specializations = format_mba_specializations(MBA_SPECIALIZATIONS)
            formatted_career_groups = format_career_groups(CAREER_GROUPS)
            formatted_mba_mapping = format_mba_mapping(MBA_MAPPING)
            
            # Create the prompt
            prompt = self.prompt_template.format(
                individual_name=assessment_input.individual_name,
                gender=assessment_input.gender,
                style_scores=formatted_style_scores,
                motivator_scores=formatted_motivator_scores,
                style_descriptors=STYLE_DESCRIPTORS,
                motivator_descriptors=MOTIVATOR_DESCRIPTORS,
                competency_descriptors=COMPETENCY_DESCRIPTORS,
                mba_specializations=formatted_mba_specializations,
                career_groups_json=formatted_career_groups,
                mba_mapping_json=formatted_mba_mapping,
                json_format_instructions=JSON_FORMAT_INSTRUCTIONS
            )
            
            # Generate the response
            response = self.llm_service.generate_response(prompt)
            
            # Parse the JSON response
            parsed_response = self.llm_service.parse_json_response(response)
            
            # Post-process the implementation activities if they exist
            if (parsed_response and 
                "section_ii" in parsed_response and 
                "implementation_activities" in parsed_response["section_ii"] and
                "activities" in parsed_response["section_ii"]["implementation_activities"]):
                
                activities = parsed_response["section_ii"]["implementation_activities"]["activities"]
                
                # Process activities to convert string format to structured format
                try:
                    processed_activities = self.process_activity_format(activities)
                    parsed_response["section_ii"]["implementation_activities"]["activities"] = processed_activities
                except Exception as e:
                    logger.warning(f"Error processing activities format: {str(e)}")
                    # Keep original activities on error
            
            # Validate the output with Pydantic
            validated_output = AssessmentOutput(**parsed_response)
            
            # Return as dict but remove the individual_name field
            output_dict = validated_output.dict()
            if "individual_name" in output_dict:
                del output_dict["individual_name"]  # Remove the redundant name
            
            return output_dict
            
        except ValidationError as e:
            logger.error(f"Validation error: {str(e)}")
            return {"error": f"Validation error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error processing assessment: {str(e)}")
            return {"error": f"Error processing assessment: {str(e)}"}
    
    def save_assessment(self, assessment_output: dict, output_file: str):
        """
        Save the assessment output to a file
        
        Args:
            assessment_output (dict): The assessment output
            output_file (str): The file to save to
        """
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            # Save to file
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(assessment_output, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Assessment saved to {output_file}")
        except Exception as e:
            logger.error(f"Error saving assessment: {str(e)}")

# Example usage
if __name__ == "__main__":
    # Sample assessment data
    sample_data = {
        "individual_name": "Dia Rautela",
        "gender": "female",
        "style_scores": {
            "REFLECTIVE": 93,
            "RESERVED": 72,
            "STEADY": 86,
            "PRECISE": 95
        },
        "motivator_scores": {
            "INSTINCTIVE": 8,
            "INTELLECTUAL": 79,
            "SELFLESS": 46,
            "RESOURCEFUL": 33,
            "HARMONIOUS": 86,
            "INTENTIONAL": 69,
            "ALTRUISTIC": 12,
            "COMMANDING": 19,
            "COLLABORATIVE": 61,
            "RECEPTIVE": 65,
            "STRUCTURED": 19
        }
    }
    
    try:
        # Create and use the assessment system
        system = CareerAssessmentSystem()
        result = system.process_assessment(sample_data)
        
        # Save the result
        output_dir = "output/batch_assessments"
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, "dia_rautela_assessment.json")
        system.save_assessment(result, output_file)
        
        print(f"Assessment for {sample_data['individual_name']} completed and saved to {output_file}")
    except Exception as e:
        print(f"Error: {str(e)}")

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include authentication routes
app.include_router(auth_router, prefix="/auth", tags=["authentication"])