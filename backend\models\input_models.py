# models/input_models.py
from pydantic import BaseModel, <PERSON>
from typing import Dict, Optional, Literal

class StyleScores(BaseModel):
    """Model for style scores"""
    REFLECTIVE: Optional[int] = None
    RESERVED: Optional[int] = None
    STEADY: Optional[int] = None
    PRECISE: Optional[int] = None
    DIRECT: Optional[int] = None
    OUTGOING: Optional[int] = None
    DYNAMIC: Optional[int] = None
    PIONEERING: Optional[int] = None
    
    class Config:
        extra = "allow"  # Allow additional fields

class MotivatorScores(BaseModel):
    """Model for motivator scores"""
    INSTINCTIVE: Optional[int] = None
    INTELLECTUAL: Optional[int] = None
    SELFLESS: Optional[int] = None
    RESOURCEFUL: Optional[int] = None
    HARMONIOUS: Optional[int] = None
    OBJECTIVE: Optional[int] = None
    INTENTIONAL: Optional[int] = None
    ALTRUISTIC: Optional[int] = None
    COMMANDING: Optional[int] = None
    COLLABORATIVE: Optional[int] = None
    RECEPTIVE: Optional[int] = None
    STRUCTURED: Optional[int] = None
    
    class Config:
        extra = "allow"  # Allow additional fields

class AssessmentInput(BaseModel):
    """Input data model for assessment processing"""
    individual_name: str = Field(..., description="Name of the individual being assessed")
    gender: Literal["male", "female", "other"] = Field(..., description="Gender of the individual")
    style_scores: StyleScores = Field(..., description="Style scores from the assessment")
    motivator_scores: MotivatorScores = Field(..., description="Motivator scores from the assessment")