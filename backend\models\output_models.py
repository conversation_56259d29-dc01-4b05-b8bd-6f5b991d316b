# models/output_models.py
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union

class GeneralDescription(BaseModel):
    description: str = Field(..., description="General description of the individual")

class Emotion(BaseModel):
    name: str = Field(..., description="Name of the emotion")
    description: str = Field(..., description="Description of the emotion")

class KeyEmotions(BaseModel):
    emotions: List[Emotion] = Field(..., description="Key emotions with descriptions")

class Strength(BaseModel):
    name: str = Field(..., description="Name of the strength")
    description: str = Field(..., description="Description of the strength")

class Strengths(BaseModel):
    strengths: List[Strength] = Field(..., description="List of individual strengths")
    critical_actions: List[str] = Field(..., description="Critical actions for success")

class Limitation(BaseModel):
    name: str = Field(..., description="Name of the limitation")
    description: str = Field(..., description="Description of the limitation")

class Limitations(BaseModel):
    limitations: List[Limitation] = Field(..., description="List of individual limitations")
    critical_causes: List[str] = Field(..., description="Critical causes of derailment")
    action_steps: List[str] = Field(..., description="Steps to overcome limitations")

class Communication(BaseModel):
    style: str = Field(..., description="Communication style description")

class Stressor(BaseModel):
    name: str = Field(..., description="Name of the stressor")
    description: str = Field(..., description="Description of the stressor")
    mitigation_steps: List[str] = Field(..., description="Steps to overcome the stressor")

class StressPerception(BaseModel):
    perception: str = Field(..., description="How others perceive the individual under stress")

class StudyManifestations(BaseModel):
    enablers: List[str] = Field(..., description="How strengths manifest in studies")
    derailers: List[str] = Field(..., description="How limitations manifest in studies")
    improvement_steps: List[str] = Field(..., description="Steps to overcome limitations in studies")

class SectionI(BaseModel):
    """Section I output model - About You"""
    general_description: GeneralDescription
    key_emotions: KeyEmotions
    strengths: Strengths
    limitations: Limitations
    communication: Communication
    stressors: List[Stressor]
    stress_perception: StressPerception
    study_manifestations: StudyManifestations

class WorkplaceManifestations(BaseModel):
    enablers: List[str] = Field(..., description="How strengths manifest in workplace")
    derailers: List[str] = Field(..., description="How limitations manifest in workplace")

class ImpactStrategies(BaseModel):
    academic: List[str] = Field(..., description="Strategies for high impact in academic settings")
    professional: List[str] = Field(..., description="Strategies for high impact in professional settings")

class CompetencyChallenge(BaseModel):
    challenges: List[str] = Field(..., description="Challenges related to the competency")
    improvements: List[str] = Field(..., description="Areas for improvement")

class CompetencyAssessment(BaseModel):
    networking: CompetencyChallenge = Field(..., description="Networking skills assessment")
    teamwork: CompetencyChallenge = Field(..., description="Teamwork assessment")
    conflict_handling: CompetencyChallenge = Field(..., description="Conflict handling assessment")
    time_management: CompetencyChallenge = Field(..., description="Time management assessment")

class IdealWorkEnvironment(BaseModel):
    description: str = Field(..., description="Description of ideal work environment")

class JobRole(BaseModel):
    title: str = Field(..., description="Job role title")

class CareerJustification(BaseModel):
    pros: List[str] = Field(..., description="Pros of the career")
    cons: List[str] = Field(..., description="Cons of the career")

class CareerAssessment(BaseModel):
    group_name: str = Field(..., description="Name of the career group")
    mba_specialization: List[str] = Field(..., description="Required MBA specialization")
    roles: List[JobRole] = Field(..., description="Top job roles")
    justification: CareerJustification = Field(..., description="Career justification")
    suitability: str = Field(..., description="Suitability category (Green/Orange/Red)")

class SpecializationRecommendation(BaseModel):
    specialization: str = Field(..., description="Specialization name")
    justification: str = Field(..., description="Justification for the specialization")

class InterviewPreparation(BaseModel):
    strategies: List[str] = Field(..., description="Interview preparation strategies")

class ResumeOutline(BaseModel):
    content: str = Field(..., description="Resume outline content")

class ActivityItem(BaseModel):
    """Model for a detailed activity item with sub-activities"""
    title: str = Field(..., description="Main activity title")
    sub_activities: List[str] = Field(..., description="List of sub-activities or steps")

class ImplementationActivities(BaseModel):
    """Model for implementation activities section"""
    activities: List[Union[str, ActivityItem]] = Field(..., description="Top activities for next 30-60 days")

class SectionII(BaseModel):
    """Section II output model - The Future"""
    workplace_manifestations: WorkplaceManifestations
    impact_strategies: ImpactStrategies
    competency_assessment: CompetencyAssessment
    ideal_work_environment: IdealWorkEnvironment
    career_ranking: List[CareerAssessment]
    specialization_recommendations: List[SpecializationRecommendation]
    interview_preparation: InterviewPreparation
    resume_outline: ResumeOutline
    implementation_activities: ImplementationActivities

class AssessmentOutput(BaseModel):
    """Complete assessment output model"""
    individual_name: Optional[str] = Field(None, description="Name of the individual assessed")
    section_i: SectionI = Field(..., description="Section I - About You")
    section_ii: SectionII = Field(..., description="Section II - The Future")