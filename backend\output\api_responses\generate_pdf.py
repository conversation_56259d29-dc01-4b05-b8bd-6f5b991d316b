import json
import os
from datetime import datetime
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, black, white
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle
from reportlab.platypus.frames import Frame
from reportlab.platypus.doctemplate import PageTemplate, BaseDocTemplate
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
from reportlab.pdfgen import canvas
from reportlab.lib import colors

class AssessmentPDFGenerator:
    def __init__(self):
        self.blue_color = HexColor('#3793F7')
        self.orange_color = HexColor('#FF7733')
        self.light_blue = HexColor('#E8F4FD')
        self.gray_color = HexColor('#666666')
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Set up custom paragraph styles"""
        
        # Main heading style with gradient effect
        self.styles.add(ParagraphStyle(
            name='CustomMainHeading',
            parent=self.styles['Heading1'],
            fontSize=20,
            textColor=self.blue_color,
            spaceAfter=10,
            spaceBefore=6,
            fontName='Helvetica-Bold',
            borderPadding=(6, 10),
            backColor=HexColor('#F8FBFF'),
            borderColor=self.blue_color,
            borderWidth=0.5,
            borderRadius=3
        ))
        
        # Section heading style
        self.styles.add(ParagraphStyle(
            name='CustomSectionHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            textColor=self.blue_color,
            spaceAfter=6,
            spaceBefore=8,
            fontName='Helvetica-Bold',
            leftIndent=8,
            borderPadding=(3, 6),
            backColor=HexColor('#F0F8FF')
        ))
        
        # Subsection heading style
        self.styles.add(ParagraphStyle(
            name='CustomSubsectionHeading',
            parent=self.styles['Heading3'],
            fontSize=12,
            textColor=self.blue_color,
            spaceAfter=3,
            spaceBefore=6,
            fontName='Helvetica-Bold',
            leftIndent=12
        ))
        
        # Orange subsection for limitations/stressors
        self.styles.add(ParagraphStyle(
            name='CustomOrangeSubsectionHeading',
            parent=self.styles['Heading3'],
            fontSize=12,
            textColor=self.orange_color,
            spaceAfter=3,
            spaceBefore=6,
            fontName='Helvetica-Bold',
            leftIndent=12
        ))
        
        # Compact body text style
        self.styles.add(ParagraphStyle(
            name='CustomBodyText',
            parent=self.styles['Normal'],
            fontSize=10,
            leading=12,
            alignment=TA_JUSTIFY,
            spaceAfter=4,
            fontName='Helvetica',
            leftIndent=12,
            rightIndent=8
        ))
        
        # Compact bullet point style
        self.styles.add(ParagraphStyle(
            name='CustomBulletText',
            parent=self.styles['Normal'],
            fontSize=9,
            leading=11,
            leftIndent=20,
            bulletIndent=12,
            spaceAfter=2,
            fontName='Helvetica',
            rightIndent=8
        ))
        
        # Intro text style with background
        self.styles.add(ParagraphStyle(
            name='CustomIntroText',
            parent=self.styles['Normal'],
            fontSize=10,
            leading=12,
            alignment=TA_JUSTIFY,
            spaceAfter=6,
            fontName='Helvetica-Oblique',
            leftIndent=8,
            rightIndent=8,
            borderPadding=(4, 8),
            backColor=HexColor('#FAFBFC'),
            borderColor=HexColor('#E0E8F0'),
            borderWidth=0.5
        ))

    def create_header_footer(self, canvas, doc):
        """Add stylish header and footer to each page"""
        canvas.saveState()
        
        # Calculate page dimensions
        page_width = doc.pagesize[0]
        page_height = doc.pagesize[1]
        
        # Stylish gradient header
        canvas.setFillColor(self.blue_color)
        canvas.rect(0, page_height - 50, page_width, 50, fill=1)
        
        # Add subtle gradient effect
        canvas.setFillColor(HexColor('#5BA3F8'))
        canvas.rect(0, page_height - 30, page_width, 30, fill=1)
        
        # Header text with shadow effect
        canvas.setFillColor(HexColor('#333333'))
        canvas.setFont('Helvetica-Bold', 16)
        canvas.drawString(52, page_height - 25, "TalentMetrix Assessment Report")
        canvas.setFillColor(white)
        canvas.drawString(50, page_height - 27, "TalentMetrix Assessment Report")
        
        # Decorative line
        canvas.setStrokeColor(HexColor('#E0E8F0'))
        canvas.setLineWidth(1)
        canvas.line(50, page_height - 55, page_width - 50, page_height - 55)
        
        # Stylish footer with background
        canvas.setFillColor(HexColor('#F8FBFF'))
        canvas.rect(40, 20, page_width - 80, 25, fill=1)
        canvas.setStrokeColor(HexColor('#E0E8F0'))
        canvas.rect(40, 20, page_width - 80, 25, fill=0)
        
        canvas.setFillColor(self.gray_color)
        canvas.setFont('Helvetica', 9)
        canvas.drawString(50, 32, f"Generated: {datetime.now().strftime('%B %d, %Y')}")
        canvas.drawRightString(page_width - 50, 32, f"Page {canvas.getPageNumber()}")
        
        canvas.restoreState()

    def add_cover_page(self, story, data):
        """Add a stylish cover page to the PDF"""
        story.append(Spacer(1, 1.2*inch))
        
        # Stylish title with background box
        title_style = ParagraphStyle('TitleStyle',
                                   parent=self.styles['Normal'],
                                   fontSize=26,
                                   fontName='Helvetica-Bold',
                                   textColor=white,
                                   alignment=TA_CENTER,
                                   borderPadding=(12, 16),
                                   backColor=self.blue_color,
                                   borderRadius=6)
        title = Paragraph("TalentMetrix Assessment Report", title_style)
        story.append(title)
        story.append(Spacer(1, 0.25*inch))
        
        # Individual name with stylish formatting
        name = data.get('individual_name', 'Assessment Report')
        name_style = ParagraphStyle('NameStyle', 
                                  parent=self.styles['Normal'],
                                  fontSize=20,
                                  fontName='Helvetica-Bold',
                                  textColor=self.blue_color,
                                  alignment=TA_CENTER,
                                  borderPadding=(8, 12),
                                  backColor=HexColor('#F0F8FF'),
                                  borderColor=self.blue_color,
                                  borderWidth=1,
                                  spaceAfter=16)
        name_para = Paragraph(name, name_style)
        story.append(name_para)
        story.append(Spacer(1, 0.6*inch))
        
        # Assessment date with icon-like styling
        timestamp = data.get('timestamp', '')
        if timestamp:
            try:
                date_obj = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                date_str = date_obj.strftime('%B %d, %Y')
            except:
                date_str = timestamp
        else:
            date_str = datetime.now().strftime('%B %d, %Y')
            
        date_style = ParagraphStyle('DateStyle',
                                  parent=self.styles['Normal'],
                                  fontSize=12,
                                  fontName='Helvetica',
                                  textColor=self.gray_color,
                                  alignment=TA_CENTER,
                                  borderPadding=(6, 10),
                                  backColor=HexColor('#FAFBFC'),
                                  borderColor=HexColor('#E0E8F0'),
                                  borderWidth=0.5)
        date_para = Paragraph(f"Assessment Date: {date_str}", date_style)
        story.append(date_para)
        
        story.append(PageBreak())

    def add_section_1_1(self, story, section_data):
        """Add Section 1.1 - Understand Yourself"""
        story.append(Paragraph("1.1 Understand Yourself", self.styles['CustomMainHeading']))
        
        intro_text = """This section provides a comprehensive overview of your natural behavioral tendencies—how you typically think, feel, and act. Understanding these patterns will help you navigate your personal and professional life more effectively."""
        
        story.append(Paragraph(intro_text, self.styles['CustomIntroText']))
        story.append(Spacer(1, 8))
        
        # General Description
        general_desc = section_data.get('general_description', {}).get('description', '')
        if general_desc:
            story.append(Paragraph("Your Behavioral Profile", self.styles['CustomSectionHeading']))
            
            # Split description into sentences for better formatting
            sentences = [s.strip() for s in general_desc.split('.') if s.strip()]
            for sentence in sentences:
                if sentence:
                    bullet_text = f"• {sentence}."
                    story.append(Paragraph(bullet_text, self.styles['CustomBulletText']))
            
            story.append(Spacer(1, 8))

    def add_section_1_2(self, story, section_data):
        """Add Section 1.2 - Strengths & Limitations"""
        story.append(Paragraph("1.2 Strengths & Limitations", self.styles['CustomMainHeading']))
        
        intro_text = """This section provides insights into the key strengths that shape your approach to academics, work, and relationships, and areas where you may face challenges. Your strengths highlight your natural abilities and behavioural tendencies that contribute to your success, while your limitations indicate potential areas that may require conscious effort to improve."""
        
        story.append(Paragraph(intro_text, self.styles['CustomIntroText']))
        story.append(Spacer(1, 8))
        
        # Strengths
        strengths_data = section_data.get('strengths', {})
        strengths = strengths_data.get('strengths', [])
        
        if strengths:
            story.append(Paragraph("Your Key Strengths", self.styles['CustomSectionHeading']))
            
            for strength in strengths:
                name = strength.get('name', '')
                description = strength.get('description', '')
                
                if name and description:
                    story.append(Paragraph(f"<b>{name}</b>", self.styles['CustomSubsectionHeading']))
                    story.append(Paragraph(description, self.styles['CustomBodyText']))
                    story.append(Spacer(1, 4))
            
            # Critical Actions in a compact format
            critical_actions = strengths_data.get('critical_actions', [])
            if critical_actions:
                story.append(Paragraph("Critical Actions", self.styles['CustomSubsectionHeading']))
                for i, action in enumerate(critical_actions, 1):
                    story.append(Paragraph(f"{i}. {action}", self.styles['CustomBulletText']))
                story.append(Spacer(1, 6))

        # Limitations
        limitations_data = section_data.get('limitations', {})
        limitations = limitations_data.get('limitations', [])
        
        if limitations:
            story.append(Paragraph("Areas for Development", self.styles['CustomSectionHeading']))
            
            for limitation in limitations:
                name = limitation.get('name', '')
                description = limitation.get('description', '')
                
                if name and description:
                    story.append(Paragraph(f"<b>{name}</b>", self.styles['CustomOrangeSubsectionHeading']))
                    story.append(Paragraph(description, self.styles['CustomBodyText']))
                    story.append(Spacer(1, 4))
            
            # Action Steps in compact format
            action_steps = limitations_data.get('action_steps', [])
            if action_steps:
                story.append(Paragraph("Action Steps", self.styles['CustomOrangeSubsectionHeading']))
                for i, step in enumerate(action_steps, 1):
                    story.append(Paragraph(f"{i}. {step}", self.styles['CustomBulletText']))
                story.append(Spacer(1, 6))

    def add_section_1_3(self, story, section_data):
        """Add Section 1.3 - Communication Styles"""
        story.append(Paragraph("1.3 Communication Styles", self.styles['CustomMainHeading']))
        
        intro_text = """This section explores how you naturally express yourself, engage in conversations, and interpret information in various settings. Understanding your communication style can help you build stronger relationships and enhance collaboration."""
        
        story.append(Paragraph(intro_text, self.styles['CustomIntroText']))
        story.append(Spacer(1, 8))
        
        # Communication Style
        communication_style = section_data.get('communication', {}).get('style', '')
        if communication_style:
            story.append(Paragraph("Your Communication Style", self.styles['CustomSectionHeading']))
            
            # Split communication style into sentences for better formatting
            sentences = [s.strip() for s in communication_style.split('.') if s.strip()]
            for sentence in sentences:
                if sentence:
                    bullet_text = f"• {sentence}."
                    story.append(Paragraph(bullet_text, self.styles['CustomBulletText']))
            
            story.append(Spacer(1, 8))

    def add_section_1_4(self, story, section_data):
        """Add Section 1.4 - Your Emotional Patterns And Responses"""
        story.append(Paragraph("1.4 Emotional Patterns & Responses", self.styles['CustomMainHeading']))
        
        intro_text = """This section explores your emotions and identifies key stress factors that may influence your well-being. Understanding these triggers can help you develop strategies for emotional balance and maintaining a positive mindset."""
        
        story.append(Paragraph(intro_text, self.styles['CustomIntroText']))
        story.append(Spacer(1, 8))
        
        # Key Emotions in compact format
        emotions = section_data.get('key_emotions', {}).get('emotions', [])
        if emotions:
            story.append(Paragraph("Key Emotional Patterns", self.styles['CustomSectionHeading']))
            
            for emotion in emotions:
                name = emotion.get('name', '')
                description = emotion.get('description', '')
                
                if name and description:
                    compact_emotion_style = ParagraphStyle('CompactEmotion',
                                                         parent=self.styles['CustomBodyText'],
                                                         spaceAfter=2,
                                                         leftIndent=16)
                    story.append(Paragraph(f"<b>{name}:</b> {description}", compact_emotion_style))
            story.append(Spacer(1, 6))
        
        # Stressors in compact format with mitigation
        stressors = section_data.get('stressors', [])
        if stressors:
            story.append(Paragraph("Stress Triggers & Solutions", self.styles['CustomSectionHeading']))
            
            for stressor in stressors:
                name = stressor.get('name', '')
                description = stressor.get('description', '')
                mitigation_steps = stressor.get('mitigation_steps', [])
                
                if name and description:
                    story.append(Paragraph(f"<b>{name}</b>", self.styles['CustomOrangeSubsectionHeading']))
                    story.append(Paragraph(description, self.styles['CustomBodyText']))
                    
                    if mitigation_steps:
                        mitigation_compact_style = ParagraphStyle('MitigationCompact',
                                                                parent=self.styles['CustomSubsectionHeading'],
                                                                fontSize=10,
                                                                spaceAfter=2,
                                                                spaceBefore=2)
                        story.append(Paragraph("<b>Solutions:</b>", mitigation_compact_style))
                        for i, step in enumerate(mitigation_steps[:2], 1):
                            story.append(Paragraph(f"{i}. {step}", self.styles['CustomBulletText']))
                    
                    story.append(Spacer(1, 4))

    def add_section_1_5(self, story, section_data):
        """Add Section 1.5 - How Others Perceive You"""
        story.append(Paragraph("1.5 How Others Perceive You", self.styles['CustomMainHeading']))
        
        intro_text = """This section provides information on how others may see your behaviour under certain stressful conditions. Understanding this will help you present yourself in a more confident, calm and professional manner."""
        
        story.append(Paragraph(intro_text, self.styles['CustomIntroText']))
        story.append(Spacer(1, 8))
        
        # Stress Perception
        stress_perception = section_data.get('stress_perception', {}).get('perception', '')
        if stress_perception:
            story.append(Paragraph("External Perception Under Stress", self.styles['CustomSectionHeading']))
            
            # Split perception into sentences for better formatting
            sentences = [s.strip() for s in stress_perception.split('.') if s.strip()]
            for sentence in sentences:
                if sentence:
                    bullet_text = f"• {sentence}."
                    story.append(Paragraph(bullet_text, self.styles['CustomBulletText']))
            
            story.append(Spacer(1, 8))

    def generate_pdf(self, json_file_path, output_path=None):
        """Generate PDF from JSON assessment data"""
        # Load JSON data
        try:
            with open(json_file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
        except FileNotFoundError:
            raise FileNotFoundError(f"Could not find the file '{json_file_path}'")
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON format in '{json_file_path}'")
        
        # Set output path
        if output_path is None:
            name = data.get('individual_name', 'assessment').replace(' ', '_')
            output_path = f"{name}_assessment_report.pdf"
        
        # Create PDF document with optimized margins
        doc = SimpleDocTemplate(
            output_path,
            pagesize=letter,
            rightMargin=50,
            leftMargin=50,
            topMargin=70,
            bottomMargin=50
        )
        
        # Build content
        story = []
        
        # Add cover page
        self.add_cover_page(story, data)
        
        # Get assessment sections
        assessment = data.get('assessment', {})
        section_i = assessment.get('section_i', {})
        
        # Add all sections 1.1 through 1.5 - each section starts on a new page
        self.add_section_1_1(story, section_i)
        story.append(PageBreak())
        
        self.add_section_1_2(story, section_i)
        story.append(PageBreak())
        
        self.add_section_1_3(story, section_i)
        story.append(PageBreak())
        
        self.add_section_1_4(story, section_i)
        story.append(PageBreak())
        
        self.add_section_1_5(story, section_i)
        
        # Compact final footer text with styling
        final_footer_text = """By understanding your natural behaviours, communication style, emotional patterns, and how others perceive you under stress, you gain valuable self-awareness. This foundation empowers you to navigate challenges, build stronger relationships, and present yourself effectively in both personal and professional settings."""
        
        footer_style = ParagraphStyle('FooterStyle',
                                    parent=self.styles['CustomBodyText'],
                                    fontSize=9,
                                    leading=10,
                                    alignment=TA_JUSTIFY,
                                    borderPadding=(6, 10),
                                    backColor=HexColor('#F8FBFF'),
                                    borderColor=self.blue_color,
                                    borderWidth=0.5,
                                    fontName='Helvetica-Oblique')
        
        story.append(Spacer(1, 12))
        story.append(Paragraph("<b>Key Takeaway</b>", self.styles['CustomSectionHeading']))
        story.append(Paragraph(final_footer_text, footer_style))
        
        # Build PDF with header/footer
        doc.build(story, onFirstPage=self.create_header_footer, 
                 onLaterPages=self.create_header_footer)
        
        print(f"PDF generated successfully: {output_path}")
        return output_path

def main():
    """Main function to generate PDF"""
    # Initialize the generator
    generator = AssessmentPDFGenerator()
    
    # Example usage - replace with your JSON file path
    json_file = 'simplesample1_at_talentmetrix_com_20250523_134430.json'
    
    try:
        # Generate PDF
        output_file = generator.generate_pdf(json_file)
        print(f"Assessment PDF created: {output_file}")
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Please make sure the JSON file exists and the path is correct.")
    except ValueError as e:
        print(f"Error: {e}")
    except Exception as e:
        print(f"Error generating PDF: {str(e)}")

if __name__ == "__main__":
    main()