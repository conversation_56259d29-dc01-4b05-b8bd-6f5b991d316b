import streamlit as st
import os
import sys
import subprocess
import time
import psycopg2
import tempfile
import threading
import pandas as pd
import json
import base64
import hashlib
import uuid
import datetime
import schedule
import pytz
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import yaml
import plotly.graph_objects as go
from io import StringIO
from loguru import logger
import queue
import threading

# Configure logging
log_file_path = os.path.join("logs", "migration.log")
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

logger.remove()  # Remove default handler
logger.add(sys.stderr, level="INFO")  # Add stderr handler
logger.add(
    log_file_path,
    rotation="500 MB",
    retention="10 days",
    level="DEBUG",
    backtrace=True,
    diagnose=True
)

# Thread-safe log queue for background threads
log_queue = queue.Queue()

# Thread-safe job storage
JOBS_FILE = "scheduled_jobs.json"
jobs_lock = threading.Lock()

def save_jobs(jobs):
    """Save jobs to persistent storage"""
    with jobs_lock:
        with open(JOBS_FILE, 'w') as f:
            json.dump(jobs, f)

def load_jobs():
    """Load jobs from persistent storage"""
    with jobs_lock:
        try:
            if os.path.exists(JOBS_FILE):
                with open(JOBS_FILE, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading jobs: {e}")
    return []

# Page configuration
st.set_page_config(
    page_title="PostgreSQL Migration Tool",
    page_icon="🐘",
    layout="wide",
    initial_sidebar_state="expanded",
)

# Initialize key session state variables
if 'logs' not in st.session_state:
    st.session_state.logs = []
if 'migration_complete' not in st.session_state:
    st.session_state.migration_complete = False
if 'verification_results' not in st.session_state:
    st.session_state.verification_results = None
if 'migration_running' not in st.session_state:
    st.session_state.migration_running = False
if 'schema_comparison' not in st.session_state:
    st.session_state.schema_comparison = None
if 'saved_configs' not in st.session_state:
    # Try to load from file
    try:
        if os.path.exists('saved_configs.json'):
            with open('saved_configs.json', 'r') as f:
                st.session_state.saved_configs = json.load(f)
        else:
            st.session_state.saved_configs = {}
    except Exception:
        st.session_state.saved_configs = {}
if 'encryption_key' not in st.session_state:
    # Create or load encryption key
    key_file = '.encryption_key'
    if os.path.exists(key_file):
        with open(key_file, 'rb') as f:
            st.session_state.encryption_key = f.read()
    else:
        # Generate a key and save it
        st.session_state.encryption_key = Fernet.generate_key()
        with open(key_file, 'wb') as f:
            f.write(st.session_state.encryption_key)
        # Set secure permissions
        os.chmod(key_file, 0o600)
if 'compare_results' not in st.session_state:
    st.session_state.compare_results = None

# Add this near the top of the file with other session state initializations
if '_needs_rerun' not in st.session_state:
    st.session_state._needs_rerun = False

# Process any pending logs from background threads
def process_log_queue():
    """Process logs from the queue and add them to session state"""
    while not log_queue.empty():
        try:
            log_entry = log_queue.get_nowait()
            st.session_state.logs.append(log_entry)
        except queue.Empty:
            break

def log_message(message, level="INFO"):
    """Log a message to both logger and session state"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = {
        "timestamp": timestamp,
        "message": message,
        "level": level
    }
    
    # Log to file using loguru
    if level == "ERROR":
        logger.error(message)
    elif level == "WARNING":
        logger.warning(message)
    else:
        logger.info(message)
    
    # Add to queue for session state
    log_queue.put(log_entry)
    
    # If we're in the main thread, process the queue immediately
    if threading.current_thread() is threading.main_thread():
        process_log_queue()
    else:
        # For background threads, trigger a rerun
        if st.session_state.migration_running:
            # Use a non-blocking way to trigger UI updates
            st.session_state._needs_rerun = True

def test_connection(host, port, dbname, user, password):
    """Test connection to a PostgreSQL database"""
    try:
        conn_string = f"host={host} port={port} dbname={dbname} user={user} password={password}"
        conn = psycopg2.connect(conn_string)
        cursor = conn.cursor()
        
        # Get PostgreSQL version
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        log_message(f"Successfully connected to {dbname} on {host}:{port}")
        log_message(f"Server version: {version}")
        return True, version
    except Exception as e:
        log_message(f"Failed to connect to {dbname} on {host}:{port}", "ERROR")
        log_message(f"Error: {str(e)}", "ERROR")
        return False, str(e)

def create_database_if_not_exists(host, port, user, password, dbname, encoding='UTF8'):
    """Create the target database if it doesn't exist with proper encoding"""
    try:
        # Connect to postgres database to create new database
        conn_string = f"host={host} port={port} dbname=postgres user={user} password={password}"
        conn = psycopg2.connect(conn_string)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check server version
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        log_message(f"Target PostgreSQL server version: {version}")
        
        # Get default encoding
        cursor.execute("SHOW server_encoding")
        server_encoding = cursor.fetchone()[0]
        log_message(f"Target server default encoding: {server_encoding}")
        
        # Check if database exists
        cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{dbname}'")
        exists = cursor.fetchone()
        
        if not exists:
            log_message(f"Creating database '{dbname}' with encoding '{encoding}'...")
            try:
                cursor.execute(f"CREATE DATABASE {dbname} WITH ENCODING '{encoding}' TEMPLATE template0")
                log_message(f"Database '{dbname}' created successfully.")
            except Exception as e:
                log_message(f"Error with custom settings, trying defaults: {str(e)}", "WARNING")
                cursor.execute(f"CREATE DATABASE {dbname}")
                log_message(f"Database '{dbname}' created with default settings.")
        else:
            log_message(f"Database '{dbname}' already exists.")
            
            # Get database encoding
            cursor.execute(f"SELECT pg_encoding_to_char(encoding) FROM pg_database WHERE datname = '{dbname}'")
            db_encoding = cursor.fetchone()[0]
            log_message(f"Existing database encoding: {db_encoding}")
        
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        log_message(f"Error creating database: {str(e)}", "ERROR")
        return False

def run_command(command, description, show_output=False):
    """Run a command and log its output"""
    log_message(f"Starting: {description}")
    start_time = time.time()
    
    try:
        # Handle Windows environment variables
        if os.name == 'nt':
            # Extract PGPASSWORD if present
            pgpassword = None
            if "PGPASSWORD=" in command:
                pgpassword = command.split("PGPASSWORD='")[1].split("'")[0]
                command = command.replace(f"PGPASSWORD='{pgpassword}' ", "")
            
            # Create environment with PGPASSWORD if needed
            env = os.environ.copy()
            if pgpassword:
                env['PGPASSWORD'] = pgpassword
            
            # Run the command with the modified environment
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True,
                env=env,
                text=True
            )
        else:
            # Original behavior for Unix-like systems
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True,
                text=True
            )
        
        # Read output in real-time
        while True:
            output = process.stdout.readline()
            error = process.stderr.readline()
            
            if output:
                if show_output:
                    log_message(output.strip())
                else:
                    # For pg_dump/pg_restore, show progress
                    if "processing item" in output.lower() or "restoring" in output.lower():
                        log_message(output.strip())
            
            if error:
                if "warning" in error.lower():
                    log_message(error.strip(), "WARNING")
                else:
                    log_message(error.strip(), "ERROR")
            
            # Check if process has finished
            if process.poll() is not None:
                break
            
            # Small delay to prevent CPU overuse
            time.sleep(0.1)
        
        # Get any remaining output
        remaining_output, remaining_error = process.communicate()
        
        if remaining_output and show_output:
            log_message(remaining_output.strip())
        if remaining_error:
            if "warning" in remaining_error.lower():
                log_message(remaining_error.strip(), "WARNING")
            else:
                log_message(remaining_error.strip(), "ERROR")
        
        duration = time.time() - start_time
        if process.returncode == 0:
            log_message(f"Completed: {description}")
            log_message(f"{description} completed in {duration:.2f} seconds")
            return True
        else:
            log_message(f"Error: {description} failed with exit code {process.returncode}", "ERROR")
            return False
            
    except Exception as e:
        duration = time.time() - start_time
        log_message(f"Error: {description} failed: {str(e)}", "ERROR")
        return False

def count_tables(host, port, dbname, user, password, schema):
    """Count tables in a schema"""
    try:
        conn_string = f"host={host} port={port} dbname={dbname} user={user} password={password}"
        conn = psycopg2.connect(conn_string)
        cursor = conn.cursor()
        
        cursor.execute(f"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '{schema}'")
        count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        return count
    except Exception as e:
        log_message(f"Error counting tables: {str(e)}", "ERROR")
        return -1

def get_table_row_counts(host, port, dbname, user, password, schema, table_limit=50):
    """Get row counts for tables in a schema"""
    try:
        conn_string = f"host={host} port={port} dbname={dbname} user={user} password={password}"
        conn = psycopg2.connect(conn_string)
        cursor = conn.cursor()
        
        # Get list of tables with row counts
        cursor.execute(f"""
            SELECT 
                table_name, 
                (SELECT reltuples::bigint FROM pg_class c JOIN pg_namespace n ON n.oid = c.relnamespace 
                 WHERE n.nspname = '{schema}' AND c.relname = table_name) AS estimate_count
            FROM information_schema.tables 
            WHERE table_schema = '{schema}' AND table_type = 'BASE TABLE'
            ORDER BY estimate_count DESC
            LIMIT {table_limit}
        """)
        
        tables = cursor.fetchall()
        
        result = {}
        for table_name, estimate_count in tables:
            # Get exact count for verification
            cursor.execute(f"SELECT COUNT(*) FROM {schema}.{table_name}")
            exact_count = cursor.fetchone()[0]
            result[table_name] = exact_count
        
        cursor.close()
        conn.close()
        
        return result
    except Exception as e:
        log_message(f"Error getting table row counts: {str(e)}", "ERROR")
        return {}

def get_table_list(host, port, dbname, user, password, schema):
    """Get list of tables in a schema"""
    try:
        conn_string = f"host={host} port={port} dbname={dbname} user={user} password={password}"
        conn = psycopg2.connect(conn_string)
        cursor = conn.cursor()
        
        cursor.execute(f"""
            SELECT table_name
            FROM information_schema.tables 
            WHERE table_schema = '{schema}' AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """)
        
        tables = [row[0] for row in cursor.fetchall()]
        
        cursor.close()
        conn.close()
        
        return tables
    except Exception as e:
        log_message(f"Error getting table list: {str(e)}", "ERROR")
        return []

def get_schema_objects(host, port, dbname, user, password, schema):
    """Get detailed information about all objects in a schema"""
    try:
        conn_string = f"host={host} port={port} dbname={dbname} user={user} password={password}"
        conn = psycopg2.connect(conn_string)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Get tables
        cursor.execute(f"""
            SELECT 
                table_name
            FROM 
                information_schema.tables 
            WHERE 
                table_schema = '{schema}' 
                AND table_type = 'BASE TABLE'
            ORDER BY 
                table_name
        """)
        tables = [row['table_name'] for row in cursor.fetchall()]
        
        # Get table structure
        table_details = {}
        for table in tables:
            # Get columns
            cursor.execute(f"""
                SELECT 
                    column_name, 
                    data_type, 
                    character_maximum_length, 
                    numeric_precision, 
                    numeric_scale, 
                    is_nullable
                FROM 
                    information_schema.columns 
                WHERE 
                    table_schema = '{schema}' 
                    AND table_name = '{table}'
                ORDER BY 
                    ordinal_position
            """)
            columns = [dict(row) for row in cursor.fetchall()]
            
            # Get indexes
            cursor.execute(f"""
                SELECT 
                    indexname,
                    indexdef
                FROM 
                    pg_indexes
                WHERE 
                    schemaname = '{schema}' 
                    AND tablename = '{table}'
                ORDER BY 
                    indexname
            """)
            indexes = [dict(row) for row in cursor.fetchall()]
            
            # Get constraints
            cursor.execute(f"""
                SELECT 
                    tc.constraint_name,
                    tc.constraint_type,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM 
                    information_schema.table_constraints tc
                LEFT JOIN 
                    information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                LEFT JOIN 
                    information_schema.constraint_column_usage ccu
                    ON tc.constraint_name = ccu.constraint_name
                WHERE 
                    tc.table_schema = '{schema}' 
                    AND tc.table_name = '{table}'
                ORDER BY 
                    tc.constraint_name
            """)
            constraints = [dict(row) for row in cursor.fetchall()]
            
            table_details[table] = {
                'columns': columns,
                'indexes': indexes,
                'constraints': constraints
            }
        
        # Get views
        cursor.execute(f"""
            SELECT 
                table_name AS view_name,
                view_definition
            FROM 
                information_schema.views
            WHERE 
                table_schema = '{schema}'
            ORDER BY 
                table_name
        """)
        views = [dict(row) for row in cursor.fetchall()]
        
        # Get functions
        cursor.execute(f"""
            SELECT 
                p.proname AS function_name,
                pg_get_function_identity_arguments(p.oid) AS arguments
            FROM 
                pg_proc p
            JOIN 
                pg_namespace n ON p.pronamespace = n.oid
            WHERE 
                n.nspname = '{schema}'
            ORDER BY 
                p.proname
        """)
        functions = [dict(row) for row in cursor.fetchall()]
        
        # Get sequences
        cursor.execute(f"""
            SELECT 
                sequence_name
            FROM 
                information_schema.sequences
            WHERE 
                sequence_schema = '{schema}'
            ORDER BY 
                sequence_name
        """)
        sequences = [row['sequence_name'] for row in cursor.fetchall()]
        
        cursor.close()
        conn.close()
        
        return {
            'tables': table_details,
            'views': views,
            'functions': functions,
            'sequences': sequences
        }
    except Exception as e:
        log_message(f"Error getting schema objects: {str(e)}", "ERROR")
        return None

def compare_schemas(source_schema, target_schema):
    """Compare two schema structures and return differences"""
    if not source_schema or not target_schema:
        return None
        
    differences = {
        'missing_tables': [],
        'extra_tables': [],
        'table_differences': {},
        'missing_views': [],
        'extra_views': [],
        'missing_functions': [],
        'extra_functions': [],
        'missing_sequences': [],
        'extra_sequences': []
    }
    
    # Compare tables
    source_tables = set(source_schema['tables'].keys())
    target_tables = set(target_schema['tables'].keys())
    
    differences['missing_tables'] = list(source_tables - target_tables)
    differences['extra_tables'] = list(target_tables - source_tables)
    
    # Compare common tables
    common_tables = source_tables.intersection(target_tables)
    for table in common_tables:
        table_diff = {}
        
        # Compare columns
        source_columns = {col['column_name']: col for col in source_schema['tables'][table]['columns']}
        target_columns = {col['column_name']: col for col in target_schema['tables'][table]['columns']}
        
        source_column_names = set(source_columns.keys())
        target_column_names = set(target_columns.keys())
        
        missing_columns = list(source_column_names - target_column_names)
        extra_columns = list(target_column_names - source_column_names)
        
        # Check for differences in common columns
        common_columns = source_column_names.intersection(target_column_names)
        column_differences = []
        
        for col_name in common_columns:
            source_col = source_columns[col_name]
            target_col = target_columns[col_name]
            
            # Check for differences in column properties
            col_diffs = []
            for prop in ['data_type', 'character_maximum_length', 'numeric_precision', 'numeric_scale', 'is_nullable']:
                if source_col.get(prop) != target_col.get(prop):
                    col_diffs.append({
                        'property': prop,
                        'source_value': source_col.get(prop),
                        'target_value': target_col.get(prop)
                    })
            
            if col_diffs:
                column_differences.append({
                    'column_name': col_name,
                    'differences': col_diffs
                })
        
        if missing_columns or extra_columns or column_differences:
            table_diff['columns'] = {
                'missing': missing_columns,
                'extra': extra_columns,
                'differences': column_differences
            }
        
        # Compare indexes
        source_indexes = {idx['indexname']: idx for idx in source_schema['tables'][table]['indexes']}
        target_indexes = {idx['indexname']: idx for idx in target_schema['tables'][table]['indexes']}
        
        source_index_names = set(source_indexes.keys())
        target_index_names = set(target_indexes.keys())
        
        missing_indexes = list(source_index_names - target_index_names)
        extra_indexes = list(target_index_names - source_index_names)
        
        if missing_indexes or extra_indexes:
            table_diff['indexes'] = {
                'missing': missing_indexes,
                'extra': extra_indexes
            }
        
        # Compare constraints
        source_constraints = {con['constraint_name']: con for con in source_schema['tables'][table]['constraints']}
        target_constraints = {con['constraint_name']: con for con in target_schema['tables'][table]['constraints']}
        
        source_constraint_names = set(source_constraints.keys())
        target_constraint_names = set(target_constraints.keys())
        
        missing_constraints = list(source_constraint_names - target_constraint_names)
        extra_constraints = list(target_constraint_names - source_constraint_names)
        
        if missing_constraints or extra_constraints:
            table_diff['constraints'] = {
                'missing': missing_constraints,
                'extra': extra_constraints
            }
        
        if table_diff:
            differences['table_differences'][table] = table_diff
    
    # Compare views
    source_views = {view['view_name']: view for view in source_schema['views']}
    target_views = {view['view_name']: view for view in target_schema['views']}
    
    source_view_names = set(source_views.keys())
    target_view_names = set(target_views.keys())
    
    differences['missing_views'] = list(source_view_names - target_view_names)
    differences['extra_views'] = list(target_view_names - source_view_names)
    
    # Compare functions
    source_functions = {func['function_name'] + func['arguments']: func for func in source_schema['functions']}
    target_functions = {func['function_name'] + func['arguments']: func for func in target_schema['functions']}
    
    source_function_names = set(source_functions.keys())
    target_function_names = set(target_functions.keys())
    
    differences['missing_functions'] = list(source_function_names - target_function_names)
    differences['extra_functions'] = list(target_function_names - source_function_names)
    
    # Compare sequences
    source_sequences = set(source_schema['sequences'])
    target_sequences = set(target_schema['sequences'])
    
    differences['missing_sequences'] = list(source_sequences - target_sequences)
    differences['extra_sequences'] = list(target_sequences - source_sequences)
    
    return differences

def create_required_extensions(host, port, dbname, user, password):
    """Create required extensions in the target database"""
    try:
        conn_string = f"host={host} port={port} dbname={dbname} user={user} password={password}"
        conn = psycopg2.connect(conn_string)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create uuid-ossp extension if it doesn't exist
        cursor.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";")
        log_message("Created uuid-ossp extension")
        
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        log_message(f"Error creating extensions: {str(e)}", "ERROR")
        return False

def create_required_roles(host, port, user, password):
    """Create required roles in the target database"""
    try:
        # Connect to postgres database to create roles
        conn_string = f"host={host} port={port} dbname=postgres user={user} password={password}"
        conn = psycopg2.connect(conn_string)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create tcsion role if it doesn't exist
        cursor.execute("DO $$ BEGIN CREATE ROLE tcsion; EXCEPTION WHEN duplicate_object THEN null; END $$;")
        log_message("Created tcsion role")
        
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        log_message(f"Error creating roles: {str(e)}", "ERROR")
        return False

def migrate_database_thread(config, job_id=None):
    """Run the migration process in a separate thread"""
    try:
        st.session_state.migration_running = True
        
        # Create timestamp for backup files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = config.get('output_dir') or tempfile.gettempdir()
        os.makedirs(backup_dir, exist_ok=True)
        
        backup_file = os.path.join(backup_dir, f"{config['source_db']}_{config['schema']}_{timestamp}.dump")
        
        # Step 1: Test connections
        log_message("Testing database connections...")
        source_ok, source_version = test_connection(
            config['source_host'], config['source_port'], config['source_db'], 
            config['source_user'], config['source_password']
        )
        
        if not source_ok:
            log_message("Cannot proceed: Unable to connect to source database", "ERROR")
            st.session_state.migration_running = False
            return False
        
        target_ok, target_version = test_connection(
            config['target_host'], config['target_port'], config['target_db'], 
            config['target_user'], config['target_password']
        )
        
        if not target_ok:
            if config['create_target_db']:
                log_message("Target database doesn't exist. Attempting to create it...")
                if not create_database_if_not_exists(
                    config['target_host'], config['target_port'], config['target_user'], 
                    config['target_password'], config['target_db']
                ):
                    log_message("Cannot proceed: Failed to create target database", "ERROR")
                    st.session_state.migration_running = False
                    return False
            else:
                log_message("Cannot proceed: Unable to connect to target database", "ERROR")
                st.session_state.migration_running = False
                return False
        
        # Step 1.5: Create required roles and extensions
        log_message("Setting up target database prerequisites...")
        if not create_required_roles(
            config['target_host'], config['target_port'],
            config['target_user'], config['target_password']
        ):
            log_message("Warning: Failed to create required roles, continuing anyway", "WARNING")
        
        if not create_required_extensions(
            config['target_host'], config['target_port'], config['target_db'],
            config['target_user'], config['target_password']
        ):
            log_message("Cannot proceed: Failed to create required extensions", "ERROR")
            st.session_state.migration_running = False
            return False
        
        # Step 2: Get source database statistics for later verification
        verification_data = {}
        if config['verify']:
            log_message("Getting source database statistics for verification...")
            source_table_count = count_tables(
                config['source_host'], config['source_port'], config['source_db'], 
                config['source_user'], config['source_password'], config['schema']
            )
            
            source_row_counts = get_table_row_counts(
                config['source_host'], config['source_port'], config['source_db'], 
                config['source_user'], config['source_password'], config['schema']
            )
            
            verification_data['source_table_count'] = source_table_count
            verification_data['source_row_counts'] = source_row_counts
            
            log_message(f"Source database has {source_table_count} tables in schema '{config['schema']}'")
            for table, count in source_row_counts.items():
                log_message(f"Table {config['schema']}.{table}: {count} rows")
        
        # Step 3: Schema comparison if requested
        if config.get('compare_schema', False):
            log_message("Comparing source and target schemas...")
            
            source_schema = get_schema_objects(
                config['source_host'], config['source_port'], config['source_db'], 
                config['source_user'], config['source_password'], config['schema']
            )
            
            # Check if target database and schema exist
            try:
                target_schema = get_schema_objects(
                    config['target_host'], config['target_port'], config['target_db'], 
                    config['target_user'], config['target_password'], config['schema']
                )
            except Exception:
                target_schema = {'tables': {}, 'views': [], 'functions': [], 'sequences': []}
            
            schema_differences = compare_schemas(source_schema, target_schema)
            st.session_state.schema_comparison = schema_differences
            
            log_message("Schema comparison completed")
            
            if config.get('schema_compare_only', False):
                log_message("Schema comparison only mode - stopping here as requested")
                st.session_state.migration_running = False
                return True
        
        # Step 4: Export the schema (if requested)
        if config['schema_only']:
            schema_file = os.path.join(backup_dir, f"{config['source_db']}_{config['schema']}_{timestamp}_schema.sql")
            
            dump_schema_cmd = (
                f"PGPASSWORD='{config['source_password']}' pg_dump "
                f"-h {config['source_host']} -p {config['source_port']} -U {config['source_user']} "
                f"-d {config['source_db']} -n {config['schema']} --schema-only -f {schema_file}"
            )
            
            if not run_command(dump_schema_cmd, "Export schema only", config['verbose']):
                st.session_state.migration_running = False
                return False
            
            log_message(f"Schema exported to {schema_file}")
            
            if config['schema_only_no_data']:
                # Import just the schema
                restore_schema_cmd = (
                    f"PGPASSWORD='{config['target_password']}' psql "
                    f"-h {config['target_host']} -p {config['target_port']} -U {config['target_user']} "
                    f"-d {config['target_db']} -f {schema_file}"
                )
                
                if not run_command(restore_schema_cmd, "Import schema to target database", config['verbose']):
                    st.session_state.migration_running = False
                    return False
                
                log_message("Schema migration completed successfully.")
                st.session_state.migration_running = False
                st.session_state.migration_complete = True
                return True
        
        # Step 5: Full export
        log_message(f"Exporting data from {config['source_db']} schema {config['schema']}...")
        
        dump_cmd = (
            f"PGPASSWORD='{config['source_password']}' pg_dump "
            f"-h {config['source_host']} -p {config['source_port']} -U {config['source_user']} "
            f"-d {config['source_db']} -n {config['schema']} "
        )
        
        # Add table filters if specified
        if config['tables']:
            tables = config['tables'].split(',')
            for table in tables:
                table = table.strip()
                if table:
                    dump_cmd += f"-t {config['schema']}.{table} "
        
        # Add format and compression options
        if config['format'] == 'custom':
            dump_cmd += f"-F c -Z {config['compress']} "
        elif config['format'] == 'directory':
            dump_cmd += "-F d "
        elif config['format'] == 'tar':
            dump_cmd += "-F t "
        
        # Add output file
        dump_cmd += f"-f {backup_file}"
        
        # Run the export command
        start_time = time.time()
        if not run_command(dump_cmd, "Export database", config['verbose']):
            st.session_state.migration_running = False
            return False
        
        export_duration = time.time() - start_time
        log_message(f"Export completed in {export_duration:.2f} seconds")
        log_message(f"Backup file: {backup_file}")
        
        # Step 6: Import to target database
        log_message(f"Importing data to {config['target_db']}...")
        
        if config['format'] == 'plain':
            # For plain format, use psql
            restore_cmd = (
                f"PGPASSWORD='{config['target_password']}' psql "
                f"-h {config['target_host']} -p {config['target_port']} -U {config['target_user']} "
                f"-d {config['target_db']} -f {backup_file}"
            )
        else:
            # For other formats, use pg_restore
            restore_cmd = (
                f"PGPASSWORD='{config['target_password']}' pg_restore "
                f"-h {config['target_host']} -p {config['target_port']} -U {config['target_user']} "
                f"-d {config['target_db']} "
            )
            
            # Add parallel workers if specified
            if config['jobs'] > 1:
                restore_cmd += f"-j {config['jobs']} "
            
            # Add other options
            if config['clean']:
                restore_cmd += "--clean "
            
            if config['no_owner']:
                restore_cmd += "--no-owner "
            
            if config['no_privileges']:
                restore_cmd += "--no-privileges "
            else:
                # Add --role option to handle role-based permissions
                restore_cmd += f"--role={config['target_user']} "
            
            # Add input file
            restore_cmd += f"{backup_file}"
        
        # Run the import command
        start_time = time.time()
        if not run_command(restore_cmd, "Import database", config['verbose']):
            st.session_state.migration_running = False
            return False
        
        import_duration = time.time() - start_time
        log_message(f"Import completed in {import_duration:.2f} seconds")
        
        # Step 7: Verify migration if requested
        verification_results = None
        if config['verify']:
            log_message("Verifying migration...")
            verification_results = {
                'table_count_match': False,
                'row_counts': [],
                'issues': 0
            }
            
            target_table_count = count_tables(
                config['target_host'], config['target_port'], config['target_db'], 
                config['target_user'], config['target_password'], config['schema']
            )
            
            verification_results['source_table_count'] = verification_data['source_table_count']
            verification_results['target_table_count'] = target_table_count
            
            if target_table_count != verification_data['source_table_count']:
                log_message(
                    f"Verification warning: Source has {verification_data['source_table_count']} tables, "
                    f"but target has {target_table_count} tables", 
                    "WARNING"
                )
                verification_results['table_count_match'] = False
            else:
                log_message(f"Table count verified: {target_table_count} tables in schema '{config['schema']}'")
                verification_results['table_count_match'] = True
            
            target_row_counts = get_table_row_counts(
                config['target_host'], config['target_port'], config['target_db'], 
                config['target_user'], config['target_password'], config['schema']
            )
            
            row_count_mismatches = 0
            for table, source_count in verification_data['source_row_counts'].items():
                row_data = {
                    'table': table,
                    'source_count': source_count,
                    'target_count': target_row_counts.get(table, 'Not found'),
                    'match': False
                }
                
                if table in target_row_counts:
                    target_count = target_row_counts[table]
                    if source_count != target_count:
                        log_message(
                            f"Row count mismatch in {config['schema']}.{table}: Source={source_count}, Target={target_count}", 
                            "WARNING"
                        )
                        row_count_mismatches += 1
                    else:
                        log_message(f"Row count verified for {config['schema']}.{table}: {source_count} rows")
                        row_data['match'] = True
                else:
                    log_message(f"Table {config['schema']}.{table} not found in target database", "WARNING")
                    row_count_mismatches += 1
                
                verification_results['row_counts'].append(row_data)
            
            verification_results['issues'] = row_count_mismatches
            
            if row_count_mismatches > 0:
                log_message(f"Verification found {row_count_mismatches} row count mismatches", "WARNING")
            else:
                log_message("All row counts verified successfully")
            
            st.session_state.verification_results = verification_results
        
        # Step 8: Run ANALYZE on target database
        if config['analyze']:
            log_message("Running ANALYZE on target database...")
            
            analyze_cmd = (
                f"PGPASSWORD='{config['target_password']}' psql "
                f"-h {config['target_host']} -p {config['target_port']} -U {config['target_user']} "
                f"-d {config['target_db']} -c 'ANALYZE'"
            )
            
            if not run_command(analyze_cmd, "Analyze database", config['verbose']):
                log_message("Warning: ANALYZE command failed but migration was successful", "WARNING")
        
        # Step 9: Clean up if requested
        if not config['keep_backup']:
            log_message(f"Removing backup file: {backup_file}")
            try:
                os.remove(backup_file)
            except Exception as e:
                log_message(f"Warning: Failed to remove backup file: {str(e)}", "WARNING")
        
        total_duration = export_duration + import_duration
        log_message(f"Migration completed successfully in {total_duration:.2f} seconds")
        st.session_state.migration_complete = True
        st.session_state.migration_running = False
        
        return True
    except Exception as e:
        log_message(f"Unexpected error during migration: {str(e)}", "ERROR")
        st.session_state.migration_running = False
        
        return False

def encrypt_password(password):
    """Encrypt a password using the application encryption key"""
    f = Fernet(st.session_state.encryption_key)
    return f.encrypt(password.encode()).decode()

def decrypt_password(encrypted_password):
    """Decrypt a password using the application encryption key"""
    f = Fernet(st.session_state.encryption_key)
    return f.decrypt(encrypted_password.encode()).decode()

def mask_password(password):
    """Return a masked version of the password for display"""
    if not password:
        return ""
    if len(password) <= 3:
        return "*" * len(password)
    return password[0] + "*" * (len(password) - 2) + password[-1]

def get_env_var(var_name, default=None):
    """Get an environment variable, with a default"""
    return os.environ.get(var_name, default)

def save_configuration(name, config):
    """Save a configuration profile"""
    # Encrypt passwords before saving
    secure_config = config.copy()
    secure_config['source_password'] = encrypt_password(config['source_password'])
    secure_config['target_password'] = encrypt_password(config['target_password'])
    
    st.session_state.saved_configs[name] = secure_config
    
    # Save to file
    try:
        with open('saved_configs.json', 'w') as f:
            json.dump(st.session_state.saved_configs, f)
        log_message(f"Configuration '{name}' saved")
        return True
    except Exception as e:
        log_message(f"Error saving configuration: {str(e)}", "ERROR")
        return False

def load_configuration(name):
    """Load a saved configuration profile"""
    if name not in st.session_state.saved_configs:
        log_message(f"Configuration '{name}' not found", "ERROR")
        return None
    
    # Decrypt passwords
    config = st.session_state.saved_configs[name].copy()
    try:
        config['source_password'] = decrypt_password(config['source_password'])
        config['target_password'] = decrypt_password(config['target_password'])
    except Exception as e:
        log_message(f"Error decrypting passwords: {str(e)}", "ERROR")
        # In case of decryption error, return with empty passwords
        config['source_password'] = ""
        config['target_password'] = ""
    
    return config

def delete_configuration(name):
    """Delete a saved configuration profile"""
    if name not in st.session_state.saved_configs:
        log_message(f"Configuration '{name}' not found", "ERROR")
        return False
    
    del st.session_state.saved_configs[name]
    
    # Save to file
    try:
        with open('saved_configs.json', 'w') as f:
            json.dump(st.session_state.saved_configs, f)
        log_message(f"Configuration '{name}' deleted")
        return True
    except Exception as e:
        log_message(f"Error saving configuration: {str(e)}", "ERROR")
        return False

# Custom CSS to improve appearance
st.markdown("""
<style>
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
    }
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
    }
    .warning-message {
        background-color: #fff3cd;
        color: #856404;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
    }
    .info-message {
        background-color: #d1ecf1;
        color: #0c5460;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
    }
    .log-container {
        background-color: #f8f9fa;
        color: #212529;
        padding: 10px;
        border-radius: 5px;
        max-height: 400px;
        overflow-y: auto;
        font-family: monospace;
        margin-bottom: 10px;
    }
    .scheduler-card {
        background-color: white;
        border-radius: 5px;
        padding: 15px;
        box-shadow: 0 0 5px rgba(0,0,0,0.1);
        margin-bottom: 10px;
    }
    .config-card {
        background-color: white;
        border-radius: 5px;
        padding: 15px;
        box-shadow: 0 0 5px rgba(0,0,0,0.1);
        margin-bottom: 10px;
        position: relative;
    }
    .delete-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        color: #dc3545;
        cursor: pointer;
    }
    .stTabs [data-baseweb="tab-panel"] {
        padding-top: 1rem;
    }
    .diff-table {
        font-family: monospace;
    }
    .diff-added {
        background-color: #d4edda;
    }
    .diff-removed {
        background-color: #f8d7da;
    }
    .diff-changed {
        background-color: #fff3cd;
    }
    .diff-unchanged {
        background-color: #f8f9fa;
    }
</style>
""", unsafe_allow_html=True)

# ------------------- Main Application UI -------------------

st.title("🐘 PostgreSQL Database Migration Tool")
st.subheader("Easily migrate databases between PostgreSQL instances")

# Create tabs
tabs = st.tabs([
   "Migration", 
   "Schema Comparison", 
   "Task Scheduler", 
   "Saved Configurations",
   "Verification Results",
   "Table Browser"
])

# Migration Tab
with tabs[0]:
   with st.form("migration_form"):
       st.subheader("Database Connection Details")
       
       col1, col2 = st.columns(2)
       
       with col1:
           st.markdown("### Source Database (PostgreSQL 13)")
           source_host = st.text_input("Source Host", "remote-host")
           source_port = st.text_input("Source Port", "5432")
           source_db = st.text_input("Source Database", "remote_db")
           source_user = st.text_input("Source Username", "remote_user")
           source_password = st.text_input("Source Password", "remote_pass", type="password")
           
       with col2:
           st.markdown("### Target Database (PostgreSQL 17)")
           target_host = st.text_input("Target Host", "localhost")
           target_port = st.text_input("Target Port", "5432")
           target_db = st.text_input("Target Database", "local_db")
           target_user = st.text_input("Target Username", "local_user")
           target_password = st.text_input("Target Password", "local_pass", type="password")
       
       st.markdown("### Migration Options")
       col1, col2 = st.columns(2)
       
       with col1:
           schema = st.text_input("Schema Name", "public")
           tables = st.text_input("Tables to Migrate (comma-separated, empty for all)", "")
           
           format_options = ["custom", "plain", "directory", "tar"]
           format_option = st.selectbox("Backup Format", format_options, index=0)
           
           compress_level = st.slider("Compression Level (0-9)", 0, 9, 6)
           jobs = st.slider("Parallel Jobs for Restore", 1, 8, 2)
       
       with col2:
           st.markdown("#### Options")
           schema_only = st.checkbox("Export Schema Only First", False)
           schema_only_no_data = st.checkbox("Schema Only (No Data)", False)
           verify = st.checkbox("Verify Migration", True)
           analyze = st.checkbox("Run ANALYZE After Migration", True)
           create_target_db = st.checkbox("Create Target DB If Missing", True)
           clean = st.checkbox("Clean Objects Before Creating", False)
           no_owner = st.checkbox("No Owner", True)
           no_privileges = st.checkbox("No Privileges", False)
           keep_backup = st.checkbox("Keep Backup File", False)
           verbose = st.checkbox("Verbose Output", False)
           compare_schema = st.checkbox("Compare Schema First", True)
           
           # Add credential source options
           st.markdown("#### Security")
           cred_source = st.radio(
               "Credential Source", 
               ["Form Input", "Environment Variables"], 
               index=0
           )
       
       output_dir = st.text_input("Output Directory (empty for temp)", "")
       
       # Add save configuration option
       save_config = st.checkbox("Save this configuration for future use")
       config_name = st.text_input("Configuration Name", "My Config") if save_config else ""
       
       submit = st.form_submit_button("Start Migration")
       
       if submit:
           # Clear previous results
           st.session_state.logs = []
           st.session_state.migration_complete = False
           st.session_state.verification_results = None
           st.session_state.schema_comparison = None
           
           # Handle credential source
           if cred_source == "Environment Variables":
               source_password = get_env_var(f"PG_SOURCE_PASSWORD", source_password)
               target_password = get_env_var(f"PG_TARGET_PASSWORD", target_password)
           
           # Configure migration options
           config = {
               'source_host': source_host,
               'source_port': source_port,
               'source_db': source_db,
               'source_user': source_user,
               'source_password': source_password,
               'target_host': target_host,
               'target_port': target_port,
               'target_db': target_db,
               'target_user': target_user,
               'target_password': target_password,
               'schema': schema,
               'tables': tables,
               'format': format_option,
               'compress': compress_level,
               'jobs': jobs,
               'schema_only': schema_only,
               'schema_only_no_data': schema_only_no_data,
               'verify': verify,
               'analyze': analyze,
               'create_target_db': create_target_db,
               'clean': clean,
               'no_owner': no_owner,
               'no_privileges': no_privileges,
               'keep_backup': keep_backup,
               'verbose': verbose,
               'output_dir': output_dir,
               'compare_schema': compare_schema
           }
           
           # Save configuration if requested
           if save_config and config_name:
               save_configuration(config_name, config)
           
           # Start migration in a separate thread
           migration_thread = threading.Thread(target=migrate_database_thread, args=(config,))
           migration_thread.start()
           st.session_state.migration_running = True
   
   # Show progress during migration
   if st.session_state.migration_running:
       st.markdown('<div class="info-message">Migration is running... Please wait.</div>', unsafe_allow_html=True)
       
       # Create a placeholder for the progress bar
       progress_placeholder = st.empty()
       progress_bar = progress_placeholder.progress(0)
       
       # Create a placeholder for logs
       log_placeholder = st.empty()
       
       # Update progress based on log entries
       if len(st.session_state.logs) > 0:
           # Estimate progress based on known stages
           progress_value = 0
           for log in st.session_state.logs:
               msg = log['message'].lower()
               if "testing database connections" in msg:
                   progress_value = 0.05
               elif "setting up target database prerequisites" in msg:
                   progress_value = 0.1
               elif "comparing source and target schemas" in msg:
                   progress_value = 0.15
               elif "schema comparison completed" in msg:
                   progress_value = 0.2
               elif "exporting data" in msg:
                   progress_value = 0.3
               elif "export completed" in msg:
                   progress_value = 0.5
               elif "importing data" in msg:
                   progress_value = 0.6
               elif "import completed" in msg:
                   progress_value = 0.8
               elif "verifying" in msg:
                   progress_value = 0.9
               elif "analyze" in msg:
                   progress_value = 0.95
               elif "migration completed successfully" in msg:
                   progress_value = 1.0
           
           progress_bar.progress(progress_value)
           
           # Display logs in real-time
           log_content = ""
           for log in st.session_state.logs:
               timestamp = log['timestamp']
               message = log['message']
               level = log['level']
               
               if level == "ERROR":
                   log_content += f'<span style="color: #dc3545;">[{timestamp}] ERROR: {message}</span>\n'
               elif level == "WARNING":
                   log_content += f'<span style="color: #ffc107;">[{timestamp}] WARNING: {message}</span>\n'
               else:
                   log_content += f'[{timestamp}] {message}\n'
           
           log_placeholder.markdown(f'<div class="log-container">{log_content}</div>', unsafe_allow_html=True)
           
           # Auto-rerun while migration is running
           if st.session_state._needs_rerun:
               st.session_state._needs_rerun = False
               time.sleep(1)  # Small delay to prevent too frequent updates
               st.experimental_rerun()
   
   # Show success message when complete
   if st.session_state.migration_complete:
       st.markdown('<div class="success-message">Migration completed successfully!</div>', unsafe_allow_html=True)
   
   # Display logs (for when migration is not running)
   if not st.session_state.migration_running:
       st.subheader("Migration Logs")
       
       log_content = ""
       for log in st.session_state.logs:
           timestamp = log['timestamp']
           message = log['message']
           level = log['level']
           
           if level == "ERROR":
               log_content += f'<span style="color: #dc3545;">[{timestamp}] ERROR: {message}</span>\n'
           elif level == "WARNING":
               log_content += f'<span style="color: #ffc107;">[{timestamp}] WARNING: {message}</span>\n'
           else:
               log_content += f'[{timestamp}] {message}\n'
       
       st.markdown(f'<div class="log-container">{log_content}</div>', unsafe_allow_html=True)
       
       # Export logs button
       if st.session_state.logs:
           log_text = "\n".join([f"[{log['timestamp']}] {log['level']}: {log['message']}" for log in st.session_state.logs])
           st.download_button(
               label="Export Logs", 
               data=log_text, 
               file_name=f"migration_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
               mime="text/plain"
           )

# Schema Comparison Tab
with tabs[1]:
   st.subheader("Schema Comparison")
   
   # Schema comparison form
   with st.form("schema_comparison_form"):
       st.markdown("### Database Connection Details")
       
       col1, col2 = st.columns(2)
       
       with col1:
           st.markdown("### Source Database")
           sc_source_host = st.text_input("Source Host", "remote-host", key="sc_source_host")
           sc_source_port = st.text_input("Source Port", "5432", key="sc_source_port")
           sc_source_db = st.text_input("Source Database", "remote_db", key="sc_source_db")
           sc_source_user = st.text_input("Source Username", "remote_user", key="sc_source_user")
           sc_source_password = st.text_input("Source Password", "remote_pass", type="password", key="sc_source_password")
           
       with col2:
           st.markdown("### Target Database")
           sc_target_host = st.text_input("Target Host", "localhost", key="sc_target_host")
           sc_target_port = st.text_input("Target Port", "5432", key="sc_target_port")
           sc_target_db = st.text_input("Target Database", "local_db", key="sc_target_db")
           sc_target_user = st.text_input("Target Username", "local_user", key="sc_target_user")
           sc_target_password = st.text_input("Target Password", "local_pass", type="password", key="sc_target_password")
       
       sc_schema = st.text_input("Schema Name", "public", key="sc_schema")
       
       # Add credential source options
       cred_source = st.radio(
           "Credential Source", 
           ["Form Input", "Environment Variables"], 
           index=0,
           key="sc_cred_source"
       )
       
       compare_button = st.form_submit_button("Compare Schemas")
       
       if compare_button:
           # Clear previous results
           st.session_state.logs = []
           st.session_state.schema_comparison = None
           
           # Handle credential source
           if cred_source == "Environment Variables":
               sc_source_password = get_env_var(f"PG_SOURCE_PASSWORD", sc_source_password)
               sc_target_password = get_env_var(f"PG_TARGET_PASSWORD", sc_target_password)
           
           try:
               # Get schemas
               log_message("Fetching source schema...")
               source_schema = get_schema_objects(
                   sc_source_host, sc_source_port, sc_source_db, 
                   sc_source_user, sc_source_password, sc_schema
               )
               
               log_message("Fetching target schema...")
               try:
                   target_schema = get_schema_objects(
                       sc_target_host, sc_target_port, sc_target_db, 
                       sc_target_user, sc_target_password, sc_schema
                   )
               except Exception as e:
                   st.error(f"Failed to connect to target database: {str(e)}")
                   target_schema = {'tables': {}, 'views': [], 'functions': [], 'sequences': []}
               
               # Compare schemas
               log_message("Comparing schemas...")
               schema_differences = compare_schemas(source_schema, target_schema)
               st.session_state.schema_comparison = schema_differences
               log_message("Schema comparison completed")
               
           except Exception as e:
               st.error(f"Error comparing schemas: {str(e)}")
   
   # Display schema comparison results
   if st.session_state.schema_comparison:
       diff = st.session_state.schema_comparison
       
       # Missing and extra tables
       col1, col2 = st.columns(2)
       
       with col1:
           st.markdown("### Tables in Source but not in Target")
           if diff['missing_tables']:
               for table in diff['missing_tables']:
                   st.markdown(f'<div class="diff-removed">➖ {table}</div>', unsafe_allow_html=True)
           else:
               st.markdown("No missing tables")
       
       with col2:
           st.markdown("### Tables in Target but not in Source")
           if diff['extra_tables']:
               for table in diff['extra_tables']:
                   st.markdown(f'<div class="diff-added">➕ {table}</div>', unsafe_allow_html=True)
           else:
               st.markdown("No extra tables")
       
       # Table differences
       if diff['table_differences']:
           st.markdown("### Table Structure Differences")
           
           for table, table_diff in diff['table_differences'].items():
               with st.expander(f"Table: {table}"):
                   # Column differences
                   if 'columns' in table_diff:
                       col_diff = table_diff['columns']
                       
                       st.markdown("#### Column Differences")
                       
                       if col_diff.get('missing'):
                           st.markdown("##### Columns in source but not in target:")
                           for col in col_diff['missing']:
                               st.markdown(f'<div class="diff-removed">➖ {col}</div>', unsafe_allow_html=True)
                       
                       if col_diff.get('extra'):
                           st.markdown("##### Columns in target but not in source:")
                           for col in col_diff['extra']:
                               st.markdown(f'<div class="diff-added">➕ {col}</div>', unsafe_allow_html=True)
                       
                       if col_diff.get('differences'):
                           st.markdown("##### Columns with different definitions:")
                           for col_diff_item in col_diff['differences']:
                               col_name = col_diff_item['column_name']
                               st.markdown(f'<div class="diff-changed">🔄 {col_name}</div>', unsafe_allow_html=True)
                               
                               # Create a table of differences
                               diff_data = []
                               for prop_diff in col_diff_item['differences']:
                                   diff_data.append({
                                       'Property': prop_diff['property'],
                                       'Source Value': prop_diff['source_value'],
                                       'Target Value': prop_diff['target_value']
                                   })
                               
                               if diff_data:
                                   st.table(pd.DataFrame(diff_data))
                   
                   # Index differences
                   if 'indexes' in table_diff:
                       idx_diff = table_diff['indexes']
                       
                       st.markdown("#### Index Differences")
                       
                       if idx_diff.get('missing'):
                           st.markdown("##### Indexes in source but not in target:")
                           for idx in idx_diff['missing']:
                               st.markdown("##### Indexes in source but not in target:")
                           for idx in idx_diff['missing']:
                               st.markdown(f'<div class="diff-removed">➖ {idx}</div>', unsafe_allow_html=True)
                       
                       if idx_diff.get('extra'):
                           st.markdown("##### Indexes in target but not in source:")
                           for idx in idx_diff['extra']:
                               st.markdown(f'<div class="diff-added">➕ {idx}</div>', unsafe_allow_html=True)
                   
                   # Constraint differences
                   if 'constraints' in table_diff:
                       con_diff = table_diff['constraints']
                       
                       st.markdown("#### Constraint Differences")
                       
                       if con_diff.get('missing'):
                           st.markdown("##### Constraints in source but not in target:")
                           for con in con_diff['missing']:
                               st.markdown(f'<div class="diff-removed">➖ {con}</div>', unsafe_allow_html=True)
                       
                       if con_diff.get('extra'):
                           st.markdown("##### Constraints in target but not in source:")
                           for con in con_diff['extra']:
                               st.markdown(f'<div class="diff-added">➕ {con}</div>', unsafe_allow_html=True)
       
       # View differences
       col1, col2 = st.columns(2)
       
       with col1:
           st.markdown("### Views in Source but not in Target")
           if diff['missing_views']:
               for view in diff['missing_views']:
                   st.markdown(f'<div class="diff-removed">➖ {view}</div>', unsafe_allow_html=True)
           else:
               st.markdown("No missing views")
       
       with col2:
           st.markdown("### Views in Target but not in Source")
           if diff['extra_views']:
               for view in diff['extra_views']:
                   st.markdown(f'<div class="diff-added">➕ {view}</div>', unsafe_allow_html=True)
           else:
               st.markdown("No extra views")
       
       # Function differences
       col1, col2 = st.columns(2)
       
       with col1:
           st.markdown("### Functions in Source but not in Target")
           if diff['missing_functions']:
               for func in diff['missing_functions']:
                   st.markdown(f'<div class="diff-removed">➖ {func}</div>', unsafe_allow_html=True)
           else:
               st.markdown("No missing functions")
       
       with col2:
           st.markdown("### Functions in Target but not in Source")
           if diff['extra_functions']:
               for func in diff['extra_functions']:
                   st.markdown(f'<div class="diff-added">➕ {func}</div>', unsafe_allow_html=True)
           else:
               st.markdown("No extra functions")
       
       # Sequence differences
       col1, col2 = st.columns(2)
       
       with col1:
           st.markdown("### Sequences in Source but not in Target")
           if diff['missing_sequences']:
               for seq in diff['missing_sequences']:
                   st.markdown(f'<div class="diff-removed">➖ {seq}</div>', unsafe_allow_html=True)
           else:
               st.markdown("No missing sequences")
       
       with col2:
           st.markdown("### Sequences in Target but not in Source")
           if diff['extra_sequences']:
               for seq in diff['extra_sequences']:
                   st.markdown(f'<div class="diff-added">➕ {seq}</div>', unsafe_allow_html=True)
           else:
               st.markdown("No extra sequences")
       
       # Generate migration script
       st.markdown("### Generate Migration Script")
       
       if st.button("Generate Script to Fix Differences"):
           # Start with creating missing tables
           script_lines = []
           
           if diff['missing_tables']:
               script_lines.append("-- Create missing tables")
               script_lines.append("-- Use pg_dump to generate the full CREATE TABLE statements")
               for table in diff['missing_tables']:
                   script_lines.append(f"-- pg_dump -h {sc_source_host} -p {sc_source_port} -U {sc_source_user} -d {sc_source_db} -n {sc_schema} -t {sc_schema}.{table} --schema-only -f {table}.sql")
               
               script_lines.append("")
           
           # Handle table structure differences
           if diff['table_differences']:
               script_lines.append("-- Fix table structure differences")
               
               for table, table_diff in diff['table_differences'].items():
                   # Column differences
                   if 'columns' in table_diff:
                       col_diff = table_diff['columns']
                       
                       if col_diff.get('missing'):
                           script_lines.append(f"-- Add missing columns to {table}")
                           for col in col_diff['missing']:
                               script_lines.append(f"-- ALTER TABLE {sc_schema}.{table} ADD COLUMN {col} data_type;  -- Replace data_type with actual type")
                           
                           script_lines.append("")
                       
                       if col_diff.get('differences'):
                           script_lines.append(f"-- Fix column differences in {table}")
                           for col_diff_item in col_diff['differences']:
                               col_name = col_diff_item['column_name']
                               script_lines.append(f"-- ALTER TABLE {sc_schema}.{table} ALTER COLUMN {col_name} TYPE new_type;  -- Replace new_type with appropriate type")
                               script_lines.append(f"-- ALTER TABLE {sc_schema}.{table} ALTER COLUMN {col_name} SET DEFAULT new_default;  -- If default value is different")
                               script_lines.append(f"-- ALTER TABLE {sc_schema}.{table} ALTER COLUMN {col_name} SET NOT NULL;  -- If nullability needs to be changed")
                           
                           script_lines.append("")
                   
                   # Index differences
                   if 'indexes' in table_diff and table_diff['indexes'].get('missing'):
                       script_lines.append(f"-- Add missing indexes to {table}")
                       script_lines.append(f"-- Use pg_dump to get the exact index definitions")
                       for idx in table_diff['indexes']['missing']:
                           script_lines.append(f"-- CREATE INDEX {idx} ON {sc_schema}.{table} (column);  -- Replace with actual column(s)")
                       
                       script_lines.append("")
                   
                   # Constraint differences
                   if 'constraints' in table_diff and table_diff['constraints'].get('missing'):
                       script_lines.append(f"-- Add missing constraints to {table}")
                       for con in table_diff['constraints']['missing']:
                           script_lines.append(f"-- ALTER TABLE {sc_schema}.{table} ADD CONSTRAINT {con} ...;  -- Add appropriate constraint definition")
                       
                       script_lines.append("")
           
           # Handle missing views
           if diff['missing_views']:
               script_lines.append("-- Create missing views")
               script_lines.append("-- Use pg_dump to get the exact view definitions")
               for view in diff['missing_views']:
                   script_lines.append(f"-- CREATE OR REPLACE VIEW {sc_schema}.{view} AS SELECT ...;  -- Replace with actual view definition")
               
               script_lines.append("")
           
           # Handle missing functions
           if diff['missing_functions']:
               script_lines.append("-- Create missing functions")
               script_lines.append("-- Use pg_dump to get the exact function definitions")
               for func in diff['missing_functions']:
                   script_lines.append(f"-- CREATE OR REPLACE FUNCTION {sc_schema}.{func.split('(')[0]}(...) RETURNS ... AS $$ ... $$;  -- Replace with actual function")
               
               script_lines.append("")
           
           # Handle missing sequences
           if diff['missing_sequences']:
               script_lines.append("-- Create missing sequences")
               for seq in diff['missing_sequences']:
                   script_lines.append(f"-- CREATE SEQUENCE {sc_schema}.{seq};")
               
               script_lines.append("")
           
           migration_script = "\n".join(script_lines)
           
           st.code(migration_script, language="sql")
           
           # Add download button
           st.download_button(
               label="Download Migration Script",
               data=migration_script,
               file_name=f"migration_script_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql",
               mime="text/plain"
           )
       
       # Export comparison results
       st.markdown("### Export Comparison Results")
       
       # Convert comparison to YAML for better readability
       yaml_comparison = yaml.dump(diff, default_flow_style=False)
       
       st.download_button(
           label="Export Comparison Results",
           data=yaml_comparison,
           file_name=f"schema_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml",
           mime="text/yaml"
       )

# Task Scheduler Tab
with tabs[2]:
    st.subheader("Schedule Migration")
    
    # Load saved configurations
    saved_configs = st.session_state.saved_configs
    
    if saved_configs:
        # Configuration selection
        config_name = st.selectbox(
            "Select Configuration",
            list(saved_configs.keys()),
            key="scheduler_config"
        )
        
        if config_name:
            config = saved_configs[config_name]
            
            # Show configuration details
            st.markdown("### Selected Configuration")
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("#### Source Database")
                st.text(f"Host: {config['source_host']}")
                st.text(f"Port: {config['source_port']}")
                st.text(f"Database: {config['source_db']}")
                st.text(f"User: {config['source_user']}")
            
            with col2:
                st.markdown("#### Target Database")
                st.text(f"Host: {config['target_host']}")
                st.text(f"Port: {config['target_port']}")
                st.text(f"Database: {config['target_db']}")
                st.text(f"User: {config['target_user']}")
            
            st.markdown("#### Options")
            st.text(f"Schema: {config['schema']}")
            st.text(f"Format: {config['format']}")
            st.text(f"Compression: {config['compress']}")
            st.text(f"Jobs: {config['jobs']}")
            
            # --- Wrap scheduling options and submit button in a form ---
            with st.form("scheduler_form"):
                st.markdown("### Schedule Options")
                schedule_type = st.radio("Schedule Type", ["One-time", "Recurring"])
                if schedule_type == "One-time":
                    schedule_date = st.date_input("Date", datetime.now().date())
                    schedule_time = st.time_input("Time", datetime.now().time())
                    # Combine date and time
                    schedule_datetime = datetime.combine(schedule_date, schedule_time)
                    # Show local timezone
                    local_tz = time.tzname[0]
                    st.info(f"The migration will run at {schedule_datetime.strftime('%Y-%m-%d %H:%M:%S')} {local_tz}")
                else:
                    recurring_type = st.selectbox("Frequency", ["Daily", "Weekly", "Monthly"])
                    if recurring_type == "Daily":
                        recurring_time = st.time_input("Time", datetime.now().time())
                        st.info(f"The migration will run daily at {recurring_time.strftime('%H:%M:%S')}")
                    elif recurring_type == "Weekly":
                        weekday = st.selectbox("Day of Week", ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"])
                        recurring_time = st.time_input("Time", datetime.now().time())
                        st.info(f"The migration will run every {weekday} at {recurring_time.strftime('%H:%M:%S')}")
                    else:  # Monthly
                        day_of_month = st.slider("Day of Month", 1, 28, 1)
                        recurring_time = st.time_input("Time", datetime.now().time())
                        st.info(f"The migration will run on day {day_of_month} of each month at {recurring_time.strftime('%H:%M:%S')}")
                schedule_submit = st.form_submit_button("Schedule Migration")
                if schedule_submit:
                    # Schedule the migration
                    job_id = schedule_migration(config, schedule_datetime)
                    st.success(f"Migration scheduled successfully! Job ID: {job_id}")
                    time.sleep(0.5)  # Short delay for UI update
                    st.experimental_rerun()
    else:
        st.warning("No saved configurations found. Please save a configuration first.")
    
    # Display scheduled jobs
    st.subheader("Scheduled Jobs")
    jobs = load_jobs()
    
    if jobs:
        for job in jobs:
            with st.container():
                col1, col2, col3 = st.columns([3, 1, 1])
                
                with col1:
                    st.markdown(f"""
                    **Job ID:** {job['id']}  
                    **Status:** {job['status']}  
                    **Scheduled for:** {datetime.fromisoformat(job['schedule_time']).strftime('%Y-%m-%d %H:%M:%S')}  
                    **Created:** {datetime.fromisoformat(job['created_at']).strftime('%Y-%m-%d %H:%M:%S')}
                    """)
                
                with col2:
                    if job['status'] == 'SCHEDULED':
                        if st.button("Cancel", key=f"cancel_{job['id']}"):
                            # Remove the job
                            jobs = [j for j in jobs if j['id'] != job['id']]
                            save_jobs(jobs)
                            st.success(f"Job {job['id']} cancelled")
                            time.sleep(0.5)  # Short delay for UI update
                            st.experimental_rerun()
                
                with col3:
                    if job['status'] in ['COMPLETED', 'FAILED']:
                        if st.button("Delete", key=f"delete_{job['id']}"):
                            # Remove the job
                            jobs = [j for j in jobs if j['id'] != job['id']]
                            save_jobs(jobs)
                            st.success(f"Job {job['id']} deleted")
                            time.sleep(0.5)  # Short delay for UI update
                            st.experimental_rerun()
                
                st.markdown("---")
    else:
        st.info("No scheduled jobs")

# Saved Configurations Tab
with tabs[3]:
   st.subheader("Saved Migration Configurations")
   
   # Configuration management form
   with st.form("config_management_form"):
       st.markdown("### Save Current Configuration")
       
       config_name = st.text_input("Configuration Name", "New Config")
       
       col1, col2 = st.columns(2)
       
       with col1:
           source_host_save = st.text_input("Source Host", "remote-host", key="source_host_save")
           source_port_save = st.text_input("Source Port", "5432", key="source_port_save")
           source_db_save = st.text_input("Source Database", "remote_db", key="source_db_save")
           source_user_save = st.text_input("Source Username", "remote_user", key="source_user_save")
           source_password_save = st.text_input("Source Password", "remote_pass", type="password", key="source_password_save")
           
       with col2:
           target_host_save = st.text_input("Target Host", "localhost", key="target_host_save")
           target_port_save = st.text_input("Target Port", "5432", key="target_port_save")
           target_db_save = st.text_input("Target Database", "local_db", key="target_db_save")
           target_user_save = st.text_input("Target Username", "local_user", key="target_user_save")
           target_password_save = st.text_input("Target Password", "local_pass", type="password", key="target_password_save")
       
       schema_save = st.text_input("Schema Name", "public", key="schema_save")
       
       # Advanced options (collapsible)
       with st.expander("Advanced Options"):
           tables_save = st.text_input("Tables (comma-separated)", "", key="tables_save")
           format_save = st.selectbox("Backup Format", ["custom", "plain", "directory", "tar"], index=0, key="format_save")
           compress_save = st.slider("Compression Level", 0, 9, 6, key="compress_save")
           jobs_save = st.slider("Parallel Jobs", 1, 8, 2, key="jobs_save")
           
           col1, col2 = st.columns(2)
           with col1:
               schema_only_save = st.checkbox("Export Schema Only", False, key="schema_only_save")
               verify_save = st.checkbox("Verify Migration", True, key="verify_save")
               analyze_save = st.checkbox("Run ANALYZE", True, key="analyze_save")
               create_target_db_save = st.checkbox("Create Target DB", True, key="create_target_db_save")
           
           with col2:
               clean_save = st.checkbox("Clean Objects", False, key="clean_save")
               no_owner_save = st.checkbox("No Owner", True, key="no_owner_save")
               no_privileges_save = st.checkbox("No Privileges", False, key="no_privileges_save")
               keep_backup_save = st.checkbox("Keep Backup", False, key="keep_backup_save")
       
       save_config_button = st.form_submit_button("Save Configuration")
       
       if save_config_button:
           # Create configuration
           config = {
               'source_host': source_host_save,
               'source_port': source_port_save,
               'source_db': source_db_save,
               'source_user': source_user_save,
               'source_password': source_password_save,
               'target_host': target_host_save,
               'target_port': target_port_save,
               'target_db': target_db_save,
               'target_user': target_user_save,
               'target_password': target_password_save,
               'schema': schema_save,
               'tables': tables_save,
               'format': format_save,
               'compress': compress_save,
               'jobs': jobs_save,
               'schema_only': schema_only_save,
               'schema_only_no_data': False,
               'verify': verify_save,
               'analyze': analyze_save,
               'create_target_db': create_target_db_save,
               'clean': clean_save,
               'no_owner': no_owner_save,
               'no_privileges': no_privileges_save,
               'keep_backup': keep_backup_save,
               'verbose': False,
               'output_dir': ''
           }
           
           # Save it
           if save_configuration(config_name, config):
               st.success(f"Configuration '{config_name}' saved successfully")
           else:
               st.error(f"Failed to save configuration '{config_name}'")
   
   # Display saved configurations
   st.subheader("Saved Configurations")
   
   saved_configs = st.session_state.saved_configs
   if saved_configs:
       # Create a card for each configuration
       for name in saved_configs:
           config = saved_configs[name]
           
           # Try to decrypt passwords for display masking
           try:
               source_pass = decrypt_password(config['source_password'])
               target_pass = decrypt_password(config['target_password'])
               # Mask passwords for display
               source_pass_masked = mask_password(source_pass)
               target_pass_masked = mask_password(target_pass)
           except:
               source_pass_masked = "******"
               target_pass_masked = "******"
           
           with st.container():
               st.markdown(f"""
               <div class="config-card">
                   <h4>{name}</h4>
                   <p><strong>Schema:</strong> {config['schema']}</p>
                   <p><strong>Source:</strong> {config['source_host']}:{config['source_port']}/{config['source_db']} (User: {config['source_user']}, Pass: {source_pass_masked})</p>
                   <p><strong>Target:</strong> {config['target_host']}:{config['target_port']}/{config['target_db']} (User: {config['target_user']}, Pass: {target_pass_masked})</p>
               </div>
               """, unsafe_allow_html=True)
               
               # Action buttons
               col1, col2, col3 = st.columns(3)
               with col1:
                   if st.button(f"Load {name}", key=f"load_{name}"):
                       loaded_config = load_configuration(name)
                       if loaded_config:
                           # Store in session state for use in migration tab
                           st.session_state.loaded_config = loaded_config
                           st.session_state.loaded_config_name = name
                           st.success(f"Configuration '{name}' loaded successfully")
               with col2:
                   if st.button(f"Use in Migration {name}", key=f"use_{name}"):
                       loaded_config = load_configuration(name)
                       if loaded_config:
                           # Start migration with this config
                           migration_thread = threading.Thread(target=migrate_database_thread, args=(loaded_config,))
                           migration_thread.start()
                           st.session_state.migration_running = True
                           st.session_state.logs = []
                           st.success(f"Started migration using configuration '{name}'")
                           time.sleep(0.5)  # Short delay for UI update
                           st.experimental_rerun()
               with col3:
                   if st.button(f"Delete {name}", key=f"delete_{name}"):
                       if delete_configuration(name):
                           st.success(f"Configuration '{name}' deleted successfully")
                           time.sleep(0.5)  # Short delay for UI update
                           st.experimental_rerun()
                       else:
                           st.error(f"Failed to delete configuration '{name}'")
   else:
       st.info("No saved configurations")
   
   # Export/Import configurations
   st.subheader("Export/Import Configurations")
   
   col1, col2 = st.columns(2)
   
   with col1:
       if saved_configs:
           # Export configurations
           export_configs = {name: {k: v for k, v in config.items() if k != 'source_password' and k != 'target_password'} 
                            for name, config in saved_configs.items()}
           export_data = json.dumps(export_configs, indent=2)
           
           st.download_button(
               label="Export Configurations",
               data=export_data,
               file_name=f"pg_migration_configs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
               mime="application/json"
           )
   
   with col2:
       # Import configurations
       uploaded_file = st.file_uploader("Import Configurations", type="json")
       if uploaded_file is not None:
           try:
               import_configs = json.load(uploaded_file)
               
               # Validate the imported data
               if not isinstance(import_configs, dict):
                   st.error("Invalid configuration file format")
               else:
                   for name, config in import_configs.items():
                       # Prompt for passwords since they're not exported
                       with st.expander(f"Set passwords for '{name}'"):
                           source_password = st.text_input(f"Source Password for {name}", type="password", key=f"import_source_pass_{name}")
                           target_password = st.text_input(f"Target Password for {name}", type="password", key=f"import_target_pass_{name}")
                           
                           if st.button(f"Save {name}", key=f"import_save_{name}"):
                               config['source_password'] = source_password
                               config['target_password'] = target_password
                               
                               if save_configuration(name, config):
                                   st.success(f"Configuration '{name}' imported successfully")
                               else:
                                   st.error(f"Failed to import configuration '{name}'")
           except Exception as e:
               st.error(f"Error importing configurations: {str(e)}")

# Verification Results Tab
with tabs[4]:
   if st.session_state.verification_results:
       results = st.session_state.verification_results
       
       st.subheader("Migration Verification Results")
       
       # Table count comparison
       table_count_match = results['table_count_match']
       source_count = results['source_table_count']
       target_count = results['target_table_count']
       
       if table_count_match:
           st.markdown(f'<div class="success-message">✅ Table count match: {source_count} tables</div>', unsafe_allow_html=True)
       else:
           st.markdown(
               f'<div class="error-message">❌ Table count mismatch: Source has {source_count} tables, '
               f'but target has {target_count} tables</div>', 
               unsafe_allow_html=True
           )
       
       # Row count comparison overview
       st.subheader("Row Count Verification")
       
       if results['issues'] == 0:
           st.markdown(f'<div class="success-message">✅ All row counts match</div>', unsafe_allow_html=True)
       else:
           st.markdown(
               f'<div class="warning-message">⚠️ Found {results["issues"]} row count mismatches</div>',
               unsafe_allow_html=True
           )
       
       # Create a DataFrame for row counts
       row_data = []
       for table_data in results['row_counts']:
           row_data.append({
               'Table': table_data['table'],
               'Source Rows': table_data['source_count'],
               'Target Rows': table_data['target_count'],
               'Match': '✅' if table_data['match'] else '❌'
           })
       
       if row_data:
           # Create a filtered view showing only mismatches if there are any
           if results['issues'] > 0:
               show_all = st.checkbox("Show All Tables", value=False)
               if not show_all:
                   row_data = [row for row in row_data if row['Match'] == '❌']
           
           # Sort by table name
           row_data = sorted(row_data, key=lambda x: x['Table'])
           
           # Create a DataFrame and display
           row_df = pd.DataFrame(row_data)
           st.dataframe(row_df, use_container_width=True)
           
           # Add visualization
           st.subheader("Data Volume Comparison")
           
           # Prepare data for the chart
           chart_data = pd.DataFrame(row_data)
           chart_data = chart_data.head(20)  # Limit to top 20 tables for readability
           
           # Create a bar chart
           fig = go.Figure(data=[
               go.Bar(name='Source', x=chart_data['Table'], y=chart_data['Source Rows'], marker_color='rgba(58, 71, 80, 0.6)'),
               go.Bar(name='Target', x=chart_data['Table'], y=chart_data['Target Rows'], marker_color='rgba(246, 78, 139, 0.6)')
           ])
           
           # Update layout
           fig.update_layout(
               title='Row Count Comparison (Top 20 Tables)',
               xaxis_title='Table',
               yaxis_title='Number of Rows',
               barmode='group',
               height=500
           )
           
           st.plotly_chart(fig, use_container_width=True)
           
           # Add export option
           csv = row_df.to_csv(index=False).encode('utf-8')
           st.download_button(
               label="Export Row Count Verification",
               data=csv,
               file_name=f"row_count_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
               mime="text/csv"
           )
   else:
       st.info("No verification results available. Run a migration with verification enabled to see results here.")

# Table Browser Tab
with tabs[5]:
   st.subheader("Database Table Browser")
   
   # Connection details
   col1, col2 = st.columns(2)
   
   with col1:
       browser_db_type = st.radio("Database", ["Source", "Target"])
       
       if browser_db_type == "Source":
           browser_host = st.text_input("Host", "remote-host", key="browser_source_host")
           browser_port = st.text_input("Port", "5432", key="browser_source_port")
           browser_db = st.text_input("Database", "remote_db", key="browser_source_db")
           browser_user = st.text_input("Username", "remote_user", key="browser_source_user")
           browser_password = st.text_input("Password", "remote_pass", type="password", key="browser_source_password")
       else:
           browser_host = st.text_input("Host", "localhost", key="browser_target_host")
           browser_port = st.text_input("Port", "5432", key="browser_target_port")
           browser_db = st.text_input("Database", "local_db", key="browser_target_db")
           browser_user = st.text_input("Username", "local_user", key="browser_target_user")
           browser_password = st.text_input("Password", "local_pass", type="password", key="browser_target_password")
   
   with col2:
       browser_schema = st.text_input("Schema", "public", key="browser_schema")
       
       # Add credential source options
       browser_cred_source = st.radio(
           "Credential Source", 
           ["Form Input", "Environment Variables"], 
           index=0,
           key="browser_cred_source"
       )
       
       if browser_cred_source == "Environment Variables":
           browser_password = get_env_var(f"PG_{'SOURCE' if browser_db_type == 'Source' else 'TARGET'}_PASSWORD", browser_password)
       
       # Test connection button
       if st.button("Test Connection", key="browser_test_connection"):
           success, message = test_connection(
               browser_host, browser_port, browser_db, browser_user, browser_password
           )
           if success:
               st.success(message)
           else:
               st.error(message)
   
   # Browse tables
   if st.button("Load Tables", key="browser_load_tables"):
       try:
           tables = get_table_list(
               browser_host, browser_port, browser_db, browser_user, browser_password, browser_schema
           )
           
           if tables:
               st.session_state.browser_tables = tables
               st.success(f"Found {len(tables)} tables in schema '{browser_schema}'")
           else:
               st.warning(f"No tables found in schema '{browser_schema}'")
       except Exception as e:
           st.error(f"Error loading tables: {str(e)}")
   
   # Display table list with tabs for different views
   if hasattr(st.session_state, 'browser_tables') and st.session_state.browser_tables:
       browser_view_tabs = st.tabs(["Table Explorer", "Row Counts", "Custom Query"])
       
       # Table Explorer tab
       with browser_view_tabs[0]:
           selected_table = st.selectbox(
               "Select a table to browse", 
               st.session_state.browser_tables,
               key="browser_selected_table"
           )
           
           limit = st.slider("Number of Rows", 5, 1000, 100, key="browser_row_limit")
           offset = st.number_input("Offset", 0, 10000, 0, key="browser_offset")
           
           # Column selection
           try:
               conn_string = f"host={browser_host} port={browser_port} dbname={browser_db} user={browser_user} password={browser_password}"
               conn = psycopg2.connect(conn_string)
               cursor = conn.cursor()
               
               # Get columns
               cursor.execute(f"""
                   SELECT column_name, data_type
                   FROM information_schema.columns 
                   WHERE table_schema = %s AND table_name = %s
                   ORDER BY ordinal_position
               """, (browser_schema, selected_table))
               
               columns = cursor.fetchall()
               column_names = [col[0] for col in columns]
               
               # Allow column selection
               selected_columns = st.multiselect(
                   "Select columns to display (empty for all)", 
                   column_names,
                   key="browser_selected_columns"
               )
               
               # Add WHERE clause option
               filter_column = st.selectbox(
                   "Filter by column (optional)", 
                   ["None"] + column_names,
                   key="browser_filter_column"
               )
               
               filter_value = ""
               if filter_column != "None":
                   filter_value = st.text_input(
                       f"Filter value for {filter_column}", 
                       key="browser_filter_value"
                   )
               
               # Add ORDER BY option
               order_by_column = st.selectbox(
                   "Order by column", 
                   column_names,
                   key="browser_order_by_column"
               )
               
               order_direction = st.radio(
                   "Order direction", 
                   ["ASC", "DESC"],
                   key="browser_order_direction"
               )
               
               if st.button("Browse Data", key="browser_browse_button"):
                   # Build the query
                   cols_str = ", ".join(selected_columns) if selected_columns else "*"
                   query = f"SELECT {cols_str} FROM {browser_schema}.{selected_table}"
                   
                   # Add WHERE clause if filter is specified
                   if filter_column != "None" and filter_value:
                       query += f" WHERE {filter_column} = %s"
                   
                   # Add ORDER BY
                   query += f" ORDER BY {order_by_column} {order_direction}"
                   
                   # Add LIMIT and OFFSET
                   query += f" LIMIT {limit} OFFSET {offset}"
                   
                   # Execute the query
                   try:
                       if filter_column != "None" and filter_value:
                           cursor.execute(query, (filter_value,))
                       else:
                           cursor.execute(query)
                       
                       rows = cursor.fetchall()
                       
                       # Get the actual columns returned
                       result_columns = [desc[0] for desc in cursor.description]
                       
                       # Create DataFrame
                       data_df = pd.DataFrame(rows, columns=result_columns)
                       
                       # Show results
                       st.subheader(f"Data from {browser_schema}.{selected_table}")
                       
                       if not data_df.empty:
                           st.dataframe(data_df, use_container_width=True)
                           
                           # Add export option
                           csv = data_df.to_csv(index=False).encode('utf-8')
                           st.download_button(
                               label="Export Data to CSV",
                               data=csv,
                               file_name=f"{browser_schema}_{selected_table}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                               mime="text/csv"
                           )
                       else:
                           st.info("No data found matching the criteria")
                   except Exception as e:
                       st.error(f"Error executing query: {str(e)}")
               
               cursor.close()
               conn.close()
           except Exception as e:
               st.error(f"Error connecting to database: {str(e)}")
       
       # Row Counts tab
       with browser_view_tabs[1]:
           if st.button("Get Row Counts", key="browser_row_counts_button"):
               try:
                   conn_string = f"host={browser_host} port={browser_port} dbname={browser_db} user={browser_user} password={browser_password}"
                   conn = psycopg2.connect(conn_string)
                   cursor = conn.cursor()
                   
                   table_data = []
                   for table in st.session_state.browser_tables:
                       # Get row count
                       cursor.execute(f"SELECT COUNT(*) FROM {browser_schema}.{table}")
                       row_count = cursor.fetchone()[0]
                       
                       # Get column count
                       cursor.execute(f"""
                           SELECT COUNT(*) 
                           FROM information_schema.columns 
                           WHERE table_schema = %s AND table_name = %s
                       """, (browser_schema, table))
                       column_count = cursor.fetchone()[0]
                       
                       # Get table size
                       cursor.execute(f"""
                           SELECT pg_size_pretty(pg_total_relation_size(%s))
                       """, (f"{browser_schema}.{table}",))
                       size = cursor.fetchone()[0]
                       
                       # Get index count
                       cursor.execute(f"""
                           SELECT COUNT(*) 
                           FROM pg_indexes
                           WHERE schemaname = %s AND tablename = %s
                       """, (browser_schema, table))
                       index_count = cursor.fetchone()[0]
                       
                       table_data.append({
                           'Table Name': table,
                           'Rows': row_count,
                           'Columns': column_count,
                           'Indexes': index_count,
                           'Size': size
                       })
                   
                   cursor.close()
                   conn.close()
                   
                   # Store in session state
                   st.session_state.browser_table_stats = table_data
                   
                   # Display as dataframe
                   if table_data:
                       st.subheader("Table Statistics")
                       stats_df = pd.DataFrame(table_data)
                       st.dataframe(stats_df, use_container_width=True)
                       
                       # Add visualization
                       st.subheader("Table Size Visualization")
                       
                       # Sort by row count and take top 20
                       top_tables = sorted(table_data, key=lambda x: x['Rows'], reverse=True)[:20]
                       
                       # Create a bar chart for row counts
                       fig = go.Figure([
                           go.Bar(
                               x=[t['Table Name'] for t in top_tables],
                               y=[t['Rows'] for t in top_tables],
                               name='Row Count'
                           )
                       ])
                       
                       fig.update_layout(
                           title='Top 20 Tables by Row Count',
                           xaxis_title='Table',
                           yaxis_title='Number of Rows',
                           height=500
                       )
                       
                       st.plotly_chart(fig, use_container_width=True)
                       
                       # Add export option
                       csv = stats_df.to_csv(index=False).encode('utf-8')
                       st.download_button(
                           label="Export Table Statistics",
                           data=csv,
                           file_name=f"table_statistics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                           mime="text/csv"
                       )
               except Exception as e:
                   st.error(f"Error getting table statistics: {str(e)}")
           
           # Display stored stats if available
           elif hasattr(st.session_state, 'browser_table_stats'):
               st.subheader("Table Statistics")
               stats_df = pd.DataFrame(st.session_state.browser_table_stats)
               st.dataframe(stats_df, use_container_width=True)
               
               # Visualization
               st.subheader("Table Size Visualization")
               
               # Sort by row count and take top 20
               top_tables = sorted(st.session_state.browser_table_stats, key=lambda x: x['Rows'], reverse=True)[:20]
               
               # Create a bar chart for row counts
               fig = go.Figure([
                   go.Bar(
                       x=[t['Table Name'] for t in top_tables],
                       y=[t['Rows'] for t in top_tables],
                       name='Row Count'
                   )
               ])
               
               fig.update_layout(
                   title='Top 20 Tables by Row Count',
                   xaxis_title='Table',
                   yaxis_title='Number of Rows',
                   height=500
               )
               
               st.plotly_chart(fig, use_container_width=True)
       
       # Custom Query tab
       with browser_view_tabs[2]:
           st.subheader("Custom SQL Query")
           
           query = st.text_area(
               "Enter SQL Query", 
               f"SELECT * FROM {browser_schema}.{st.session_state.browser_tables[0]} LIMIT 100;",
               height=150,
               key="browser_custom_query"
           )
           
           if st.button("Execute Query", key="browser_execute_query"):
               try:
                   conn_string = f"host={browser_host} port={browser_port} dbname={browser_db} user={browser_user} password={browser_password}"
                   conn = psycopg2.connect(conn_string)
                   cursor = conn.cursor()
                   
                   # Check if query is a SELECT statement or other type
                   is_select = query.strip().upper().startswith("SELECT")
                   
                   start_time = time.time()
                   cursor.execute(query)
                   
                   if is_select:
                       rows = cursor.fetchall()
                       end_time = time.time()
                       duration = end_time - start_time
                       
                       # Get the columns
                       result_columns = [desc[0] for desc in cursor.description]
                       
                       # Create DataFrame
                       result_df = pd.DataFrame(rows, columns=result_columns)
                       
                       # Show results
                       st.subheader("Query Results")
                       st.info(f"Query executed in {duration:.4f} seconds, returned {len(rows)} rows")
                       
                       if not result_df.empty:
                           st.dataframe(result_df, use_container_width=True)
                           
                           # Add export option
                           csv = result_df.to_csv(index=False).encode('utf-8')
                           st.download_button(
                               label="Export Results to CSV",
                               data=csv,
                               file_name=f"query_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                               mime="text/csv"
                           )
                       else:
                           st.info("Query returned no results")
                   else:
                       # For non-SELECT queries, show how many rows were affected
                       rowcount = cursor.rowcount
                       end_time = time.time()
                       duration = end_time - start_time
                       
                       st.success(f"Query executed successfully in {duration:.4f} seconds")
                       st.info(f"Rows affected: {rowcount if rowcount >= 0 else 'unknown'}")
                   
                   conn.commit()
                   cursor.close()
                   conn.close()
               except Exception as e:
                   st.error(f"Error executing query: {str(e)}")
   else:
       st.info("Click 'Load Tables' to browse the database tables")

# Process any pending logs at the start of each rerun
process_log_queue()

def check_scheduled_jobs():
    """Check for scheduled jobs that are due and start them if needed."""
    jobs = load_jobs()
    now = datetime.now().isoformat()
    updated = False
    for job in jobs:
        # Only process jobs that are scheduled and due
        if job.get('status') == 'SCHEDULED' and job.get('schedule_time') <= now:
            # Start the migration in a new thread
            config = job.get('config')
            job['status'] = 'RUNNING'
            job['started_at'] = datetime.now().isoformat()
            updated = True
            def run_and_update(job_id):
                result = migrate_database_thread(config, job_id=job_id)
                jobs2 = load_jobs()
                for j in jobs2:
                    if j['id'] == job_id:
                        j['status'] = 'COMPLETED' if result else 'FAILED'
                        j['finished_at'] = datetime.now().isoformat()
                        break
                save_jobs(jobs2)
            threading.Thread(target=run_and_update, args=(job['id'],), daemon=True).start()
    if updated:
        save_jobs(jobs)

# Background job for checking scheduled migrations
if not hasattr(st, '_check_jobs_scheduled'):
    st._check_jobs_scheduled = True
    
    def check_jobs_loop():
        while True:
            try:
                check_scheduled_jobs()
                # Process any pending logs
                process_log_queue()
            except Exception as e:
                logger.error(f"Error in check_jobs_loop: {e}")
            time.sleep(60)  # Check every minute
    
    # Start the job checker in a background thread
    checker_thread = threading.Thread(target=check_jobs_loop, daemon=True)
    checker_thread.start()

# Add a footer
st.markdown("---")
st.markdown(
   "Made with ❤️ by Streamlit PostgreSQL Migration Tool. This tool uses pg_dump and pg_restore to migrate "
   "data between PostgreSQL databases."
)