# process_candidates.py
import os
import json
from main import CareerAssessmentSystem
from loguru import logger

# Configure logging
if not os.path.exists("logs"):
    os.makedirs("logs")
logger.add("logs/batch_process.log", rotation="100 MB", retention="10 days")

# List of candidates
candidates = [
    {
        "individual_name": "<PERSON><PERSON>",
        "gender": "female",
        "style_scores": {
            "REFLECTIVE": 93,
            "RESERVED": 72,
            "STEADY": 86,
            "PRECISE": 95
        },
        "motivator_scores": {
            "INSTINCTIVE": 8,
            "INTELLECTUAL": 79,
            "SELFLESS": 46,
            "RESOURCEFUL": 33,
            "HARMONIOUS": 86,
            "INTENTIONAL": 69,
            "ALTRUISTIC": 12,
            "COMMANDING": 19,
            "COLLABORATIVE": 61,
            "RECEPTIVE": 65,
            "STRUCTURED": 19
        }
    },
    {
        "individual_name": "<PERSON>",
        "gender": "male",
        "style_scores": {
            "REFLECTIVE": 65,
            "DIRECT": 85,
            "STEADY": 55,
            "PRECISE": 78
        },
        "motivator_scores": {
            "INSTINCTIVE": 65,
            "INTELLECTUAL": 45,
            "SELFLESS": 58,
            "RESOURCEFUL": 72,
            "HARMONIOUS": 42,
            "INTENTIONAL": 81,
            "ALTRUISTIC": 38,
            "COMMANDING": 70,
            "COLLABORATIVE": 45,
            "RECEPTIVE": 36,
            "STRUCTURED": 62
        }
    }
    # Add more candidates as needed
]

def process_batch():
    """Process a batch of candidates"""
    # Create output directory if it doesn't exist
    output_dir = "output/batch_assessments"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Process each candidate
    system = CareerAssessmentSystem()
    for i, candidate in enumerate(candidates):
        try:
            logger.info(f"Processing candidate {i+1}/{len(candidates)}: {candidate['individual_name']}")
            result = system.process_assessment(candidate)
            
            # Save the result
            filename = os.path.join(output_dir, f"{candidate['individual_name'].lower().replace(' ', '_')}_assessment.json")
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
                
            logger.success(f"Assessment completed and saved to {filename}")
            
        except Exception as e:
            logger.error(f"Error processing {candidate['individual_name']}: {str(e)}")

if __name__ == "__main__":
    logger.info(f"Starting batch processing of {len(candidates)} candidates")
    process_batch()
    logger.info("Batch processing completed")