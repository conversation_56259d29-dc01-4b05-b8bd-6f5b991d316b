# prompts/templates.py
from langchain.prompts import PromptTemplate

# JSON output instructions for <PERSON>FORMAT_INSTRUCTIONS = """
You must respond with a valid JSON object that follows this structure exactly:
{
  "individual_name": "string",
  "section_i": {
    "general_description": {
      "description": "string"
    },
    "key_emotions": {
      "emotions": [
        {"name": "string", "description": "string"}
      ]
    },
    "strengths": {
      "strengths": [
        {"name": "string", "description": "string"}
      ],
      "critical_actions": ["string"]
    },
    "limitations": {
      "limitations": [
        {"name": "string", "description": "string"}
      ],
      "critical_causes": ["string"],
      "action_steps": ["string"]
    },
    "communication": {
      "style": "string"
    },
    "stressors": [
      {
        "name": "string",
        "description": "string",
        "mitigation_steps": ["string"]
      }
    ],
    "stress_perception": {
      "perception": "string"
    },
    "study_manifestations": {
      "enablers": ["string"],
      "derailers": ["string"],
      "improvement_steps": ["string"]
    }
  },
  "section_ii": {
    "workplace_manifestations": {
      "enablers": ["string"],
      "derailers": ["string"]
    },
    "impact_strategies": {
      "academic": ["string"],
      "professional": ["string"]
    },
    "competency_assessment": {
      "networking": {
        "challenges": ["string"],
        "improvements": ["string"]
      },
      "teamwork": {
        "challenges": ["string"],
        "improvements": ["string"]
      },
      "conflict_handling": {
        "challenges": ["string"],
        "improvements": ["string"]
      },
      "time_management": {
        "challenges": ["string"],
        "improvements": ["string"]
      }
    },
    "ideal_work_environment": {
      "description": "string"
    },
    "career_ranking": [
      {
        "group_name": "string",
        "mba_specialization": ["string"],
        "roles": [
          {"title": "string"}
        ],
        "justification": {
          "pros": ["string"],
          "cons": ["string"]
        },
        "suitability": "string"
      }
    ],
    "specialization_recommendations": [
      {
        "specialization": "string",
        "justification": "string"
      }
    ],
    "interview_preparation": {
      "strategies": ["string"]
    },
    "resume_outline": {
      "content": "string"
    },
    "implementation_activities": {
      "activities": ["string"]
    }
  }
}

CRITICAL VALIDATION REQUIREMENTS:
1. Every field shown above is REQUIRED - do not omit any fields even if information is limited
2. Follow the exact structure with ALL fields in section_i and section_ii
3. Ensure all JSON is properly formatted with correct nesting, quotes, brackets and braces
4. Do not truncate your response - complete the entire JSON object with all necessary closing braces
5. Validate your JSON response is complete before submitting
6. If any required field is unknown, provide a reasonable default rather than omitting it

Ensure all JSON keys and the structure match EXACTLY what is shown above. Do not add, remove, or modify any keys.
"""

# Main prompt template
ASSESSMENT_PROMPT_TEMPLATE = """
# Psychometric Assessment Career Analysis for {individual_name}

## IMPORTANT INSTRUCTIONS
- Provide detailed and comprehensive analysis
- Include thorough descriptions and explanations
- Ensure your JSON response is complete and well-formed

## Basic Information
Gender: {gender}

## Style Scores
{style_scores}

## Motivator Scores
{motivator_scores}

## Reference Information

### Style Descriptors
{style_descriptors}

### Motivator Descriptors
{motivator_descriptors}

### Competency Descriptors
{competency_descriptors}

### MBA Specializations
{mba_specializations}

### Career Groups
{career_groups_json}

### MBA Mapping
{mba_mapping_json}

## Analysis Instructions

### SECTION I | ABOUT YOU

Using the Style and Motivator scores of the individual, respond to the following information request. Do not include the scores in your response. Report in Second-Person. Report in very easy to understand English, for students who have a basic reading comprehension of the language. Do not use technical terms or names of the Behaviors and Motivators.

1. Give a General Description of the individual, based on the Style and Motivator descriptors. Consider all styles and Motivators.
2. List and describe the key Emotions of the individual considering all styles.

3. Describe the Strengths of the individual, each strength in a maximum of two sentences. Considering the combination of the Styles and Motivators scores, what specific actions are required by the individual to enable their success. Give the TOP 3 Critical actions to be undertaken by the individual, as bullet points. If there is a conflict between scores of Styles and Motivators, evaluate both very carefully and synthesize the response with all nuances being considered.

4. Describe the Limitations of the individual, each Limitation in a maximum of 2 sentences. Considering the combination of the Styles and Motivators scores, what causes him/her to get derailed from the path to success. Give the TOP 3 Critical causes, as bullet points. If there is a conflict between scores of Styles and Motivators, evaluate both very carefully and synthesize the response with all nuances being considered. Provide specific and actionable steps, that the individual should take to overcome the limitations.

5. How does this individual Communicate. Considering the combination of the Styles and Motivators scores, explain in detail


6. List the key areas of Stress for this individual. Limit to TOP 3. Provide specific and actionable steps, the individual should take to overcome each of the stressors.

7. How do others Perceive this individual under Stress.

8. How do the Enablers (Strengths) and Derailers (Limitations) manifest in the individual's studies. Give the statements, as bullet points. Provide specific and actionable steps, the individual should take to overcome each of the limitations.


### SECTION II | THE FUTURE

Using the Style and Motivator scores of the individual, respond to the following information request. Report in Second-Person. Report in very easy to understand English, for students who have a basic reading comprehension of the language.


1. How would the Enablers (Strengths) and Derailers (Limitations) manifest in the individual's future workplace. Give the statements, as bullet points. 

2. What should this individual do to make a high impact in academic settings. Give bullet points.


3. What should this individual do to make a high impact in professional work settings. Give bullet points.

4. Describe the individual on the following competencies. What are the potential challenges and areas of Improvement, for each competency, in bullet points. 

A.	Networking Skills

B.	Teamwork

C.	Conflict Handling

D.	Time Management


5. What is the most ideal Work Environment for this individual.

6. Rank all the careers in order of suitability with best suited being number 1 and least suited being the last(select from the list of given career groups only) and help the individual understand the why of each of the careers suits them or does not suit them in a maximum of 3 sentences each.  Do not include the scores in the response. For each career group, indicate the MBA specialization required . Include a maximum of 3 job roles for career group. For each career group provide detailed justification in bulleted list including pros and cons.Indicate Green for best suited , Red for least suited and Orange for in between.Give the logic in the end how you categorized them into these categories.

7. Recommend all the suitable MBA specializations (select from mba_specializations only) the individual should opt for in his/her management studies. For each specilaization provide detailed justification in one sentence.

8. How should the individual prepare for his/her campus placement interview, given his Limitations and Communication style.

9. Build a brief Resume Outline for this individual, basis his/her critical strengths, linked to each career.

10. Advise the top 4-5 activities students can repeatedly implement in the next 30-60 days - from the critical actions for success. For each activity, include a clear title followed by 2-3 specific sub-activities or steps, all as separate entries in the activities list. Format each entry like: "TITLE: Create a personal knowledge management system" followed by its sub-activities starting with "SUB: " such as "SUB: Set up a digital system to organize materials".

{json_format_instructions}
"""

def create_assessment_prompt():
    """Creates the LangChain PromptTemplate for assessment"""
    prompt = PromptTemplate(
        template=ASSESSMENT_PROMPT_TEMPLATE,
        input_variables=[
            "individual_name",
            "gender",
            "style_scores",
            "motivator_scores",
            "style_descriptors",
            "motivator_descriptors",
            "competency_descriptors",
            "mba_specializations",
            "career_groups_json",
            "mba_mapping_json",
            "json_format_instructions"
        ]
    )
    return prompt