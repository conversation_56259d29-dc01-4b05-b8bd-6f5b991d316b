# run_api.py
import os
import uvicorn
from api.app import app
from loguru import logger

# Ensure output directories exist
os.makedirs("output/api_responses", exist_ok=True)
os.makedirs("output/batch_assessments", exist_ok=True)

if __name__ == "__main__":
    logger.info("Starting Career Assessment API server")
    logger.info(f"API responses will be saved to: {os.path.abspath('output/api_responses')}")
    uvicorn.run(
        "api.app:app",
        host="0.0.0.0",
        port=8000,
        reload=True  # Enable auto-reload during development
    )