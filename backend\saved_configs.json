{"TM_DB_PRODUCCTION_AWS": {"source_host": "*************", "source_port": "5432", "source_db": "tcs_ion", "source_user": "tm_admin", "source_password": "gAAAAABoJwrRvTwWrpgJ9PzzjIg0OsBGWt4vgt2Wo3vXOsvRShBn4jpYQkIYmnIh-0Jfw6lZq__J-iUC18573l3KLAFR37qwzg==", "target_host": "localhost", "target_port": "5432", "target_db": "local_db", "target_user": "local_user", "target_password": "gAAAAABoJwrRvQAfXYk4cO4FhlOnh2yVOSUf1swmqN441htq40LB0dITs-eS8Eg9VB3mb6VpFIT2Y5BZuF1lawXO0NJD2WdhKQ==", "schema": "public", "tables": "", "format": "custom", "compress": 6, "jobs": 2, "schema_only": false, "schema_only_no_data": false, "verify": true, "analyze": true, "create_target_db": true, "clean": false, "no_owner": true, "no_privileges": false, "keep_backup": false, "verbose": false, "output_dir": ""}, "My Config": {"source_host": "*************", "source_port": "5432", "source_db": "tcs_ion_backup_feb18", "source_user": "postgres", "source_password": "gAAAAABoJw-kjnUMT7NigicQmTSgQP3bDwGF5yP-846XzBTqERRoNKlv5feYmA9LxXWKsy2067iMu984RaS_kz2ogQvJpWcR5g==", "target_host": "localhost", "target_port": "5432", "target_db": "tcs_ion_backup_may162025", "target_user": "postgres", "target_password": "gAAAAABoJw-kz63lUDGEfHV1O6K3zXOWhE5i-wZSLe86LoBjFu7dl8NMLR3jkHd6QtpvzaBK76fcUejKsnBSsigRtpIDpX9F2g==", "schema": "public", "tables": "", "format": "custom", "compress": 6, "jobs": 2, "schema_only": false, "schema_only_no_data": true, "verify": true, "analyze": false, "create_target_db": true, "clean": false, "no_owner": true, "no_privileges": false, "keep_backup": false, "verbose": true, "output_dir": "", "compare_schema": true}}