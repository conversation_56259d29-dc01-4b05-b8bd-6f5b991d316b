# services/claude_service.py
import json
from typing import Dict, Any, Optional
from anthropic import Anthropic
from config.config import CLA<PERSON>DE_API_KEY, CLAUDE_MODEL, MAX_TOKENS, TEMPERATURE, JSON_MODE
from services.llm_service import LLMService
from loguru import logger

class ClaudeService(LLMService):
    """Service for interacting with Claude API"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Claude service
        
        Args:
            api_key (str, optional): API key for <PERSON>. If not provided, uses the key from config
        """
        self.api_key = api_key or CLAUDE_API_KEY
        self.client = Anthropic(api_key=self.api_key)
        self.model = CLAUDE_MODEL
        self.max_tokens = 15000  # Reduced to a more reliable size that should still be sufficient
        self.temperature = TEMPERATURE
        self.json_mode = False  # Disable JSON mode to use system prompt instead
        
    def generate_response(self, prompt: str) -> str:
        """
        Generate a response from <PERSON> using streaming
        
        Args:
            prompt (str): The prompt to send to <PERSON>
            
        Returns:
            str: The generated response
        """
        try:
            logger.info(f"Generating response with {self.model}, max_tokens={self.max_tokens}")
            
            # Use streaming for long responses
            with self.client.messages.stream(
                model=self.model,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                system="You are a career assessment expert. Analyze psychometric scores and provide detailed career advice in JSON format. Include comprehensive explanations and detailed insights in your analysis. Your response MUST include ALL required fields in the output schema. Make sure your JSON is complete, valid, and properly formatted. Every field in section_i and section_ii is REQUIRED - do not omit any fields even if information is limited.",
                messages=[{"role": "user", "content": prompt}]
            ) as stream:
                # Collect all pieces of the response
                full_response = ""
                for text in stream.text_stream:
                    full_response += text
                    
            logger.info(f"Response generated successfully, length: {len(full_response)} characters")
            return full_response
            
        except Exception as e:
            logger.error(f"Error generating response from Claude: {str(e)}")
            raise Exception(f"Error generating response from Claude: {str(e)}")
    
    def parse_json_response(self, response: str) -> Dict[str, Any]:
        """
        Parse JSON response from Claude
        
        Args:
            response (str): The response from Claude
            
        Returns:
            Dict[str, Any]: The parsed JSON
        """
        try:
            # Clean up the response
            if response.startswith("```json"):
                response = response.strip("```json").strip("```").strip()
            elif response.startswith("```"):
                response = response.strip("```").strip()
            
            # Try to fix incomplete JSON if needed
            if not self._is_valid_json(response):
                logger.warning("Received incomplete JSON, attempting to fix")
                response = self._fix_incomplete_json(response)
            
            return json.loads(response)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response: {str(e)}\nResponse: {response[:500]}...")
            
            # Try a more aggressive JSON repair approach
            try:
                cleaned_response = self._advanced_json_repair(response)
                return json.loads(cleaned_response)
            except Exception as repair_error:
                logger.error(f"Advanced JSON repair failed: {str(repair_error)}")
            
            # If all else fails, create a minimal valid response
            fallback_response = {
                "individual_name": "Parse Error",
                "section_i": {
                    "general_description": {"description": "Error parsing response from Claude. Please try again."}
                },
                "section_ii": {
                    "workplace_manifestations": {"enablers": [], "derailers": []}
                }
            }
            return fallback_response
    
    def _is_valid_json(self, json_str: str) -> bool:
        """Check if a string is valid JSON"""
        try:
            json.loads(json_str)
            return True
        except:
            return False
    
    def _fix_incomplete_json(self, json_str: str) -> str:
        """
        Attempt to fix incomplete JSON by closing brackets
        
        This is a basic implementation that tries to balance brackets.
        For a more robust solution, a proper JSON parser would be needed.
        """
        # Count open and close braces
        open_braces = json_str.count('{')
        close_braces = json_str.count('}')
        
        # Add missing closing braces
        if open_braces > close_braces:
            json_str += '}' * (open_braces - close_braces)
        
        # Count open and close brackets
        open_brackets = json_str.count('[')
        close_brackets = json_str.count(']')
        
        # Add missing closing brackets
        if open_brackets > close_brackets:
            json_str += ']' * (open_brackets - close_brackets)
        
        return json_str
    
    def _advanced_json_repair(self, json_str: str) -> str:
        """More sophisticated JSON repair for complex cases"""
        logger.warning("Attempting advanced JSON repair")
        
        # Check if we have the required sections at minimum
        required_sections = [
            '"section_i":', 
            '"section_ii":'
        ]
        
        # Check for section_i subsections
        section_i_fields = [
            '"general_description":', 
            '"key_emotions":', 
            '"strengths":', 
            '"limitations":', 
            '"communication":', 
            '"stressors":', 
            '"stress_perception":', 
            '"study_manifestations":'
        ]
        
        # Check for section_ii subsections
        section_ii_fields = [
            '"workplace_manifestations":', 
            '"impact_strategies":', 
            '"competency_assessment":', 
            '"ideal_work_environment":', 
            '"career_ranking":', 
            '"specialization_recommendations":', 
            '"interview_preparation":', 
            '"resume_outline":', 
            '"implementation_activities":'
        ]
        
        # Check if the response is severely truncated
        missing_sections = [section for section in required_sections if section not in json_str]
        missing_section_i_fields = [field for field in section_i_fields if field not in json_str]
        missing_section_ii_fields = [field for field in section_ii_fields if field not in json_str]
        
        # If essential parts are missing, the JSON is too severely truncated to repair
        if missing_sections or len(missing_section_i_fields) > 4 or len(missing_section_ii_fields) > 4:
            logger.error("JSON is too severely truncated to repair - missing essential sections")
            
            # Create a minimal placeholder response with error message
            placeholder = {
                "individual_name": "Response Error",
                "section_i": {
                    "general_description": {
                        "description": "Error: The AI response was truncated or malformed. Please try again."
                    },
                    "key_emotions": {
                        "emotions": [
                            {"name": "Error", "description": "Response truncated or malformed"}
                        ]
                    },
                    "strengths": {
                        "strengths": [
                            {"name": "Error", "description": "Response truncated or malformed"}
                        ],
                        "critical_actions": ["Try again with a different approach"]
                    },
                    "limitations": {
                        "limitations": [
                            {"name": "Error", "description": "Response truncated or malformed"}
                        ],
                        "critical_causes": ["Response truncation"]
                    },
                    "communication": {
                        "style": "Error: The AI response was truncated or malformed. Please try again."
                    },
                    "stressors": [
                        {"stressor": "Error", "description": "Response truncated or malformed"}
                    ],
                    "stress_perception": {
                        "perception": "Error: The AI response was truncated or malformed. Please try again.",
                        "action_steps": ["Try again with a different approach"]
                    },
                    "study_manifestations": {
                        "enablers": ["Error: Response truncated"],
                        "derailers": ["Error: Response truncated"]
                    }
                },
                "section_ii": {
                    "workplace_manifestations": {
                        "enablers": ["Error: Response truncated"],
                        "derailers": ["Error: Response truncated"]
                    },
                    "impact_strategies": {
                        "academic": ["Error: Response truncated"],
                        "professional": ["Error: Response truncated"]
                    },
                    "competency_assessment": {
                        "networking": {"strength": "Low", "challenge": "Error: Response truncated"},
                        "teamwork": {"strength": "Low", "challenge": "Error: Response truncated"},
                        "conflict_handling": {"strength": "Low", "challenge": "Error: Response truncated"},
                        "time_management": {"strength": "Low", "challenge": "Error: Response truncated"}
                    },
                    "ideal_work_environment": {
                        "characteristics": ["Error: Response truncated"],
                        "management_style": "Error: Response truncated",
                        "work_culture": "Error: Response truncated"
                    },
                    "career_ranking": [
                        {
                            "group_name": "ERROR",
                            "mba_specialization": ["ERROR"],
                            "roles": [{"title": "Error: Response truncated"}],
                            "justification": {
                                "pros": ["Error: Response truncated"],
                                "cons": ["Error: Response truncated"]
                            },
                            "suitability": "Red"
                        }
                    ],
                    "specialization_recommendations": [
                        {
                            "specialization": "ERROR",
                            "justification": "Error: Response truncated",
                            "priority": "Low"
                        }
                    ],
                    "interview_preparation": {
                        "strengths_evidence": ["Error: Response truncated"],
                        "limitations_mitigation": ["Error: Response truncated"],
                        "questions_to_ask": ["Error: Response truncated"]
                    },
                    "resume_outline": {
                        "professional_summary": "Error: The AI response was truncated or malformed. Please try again.",
                        "skills_to_highlight": ["Error: Response truncated"],
                        "achievements_to_highlight": ["Error: Response truncated"]
                    },
                    "implementation_activities": {
                        "activities": [
                            {
                                "title": "Try Again",
                                "description": "Error: The AI response was truncated or malformed. Please try again.",
                                "timeline": "Immediate"
                            }
                        ]
                    }
                }
            }
            return json.dumps(placeholder)
            
        # Try to identify where the JSON was cut off
        last_complete_object_end = json_str.rfind("},")
        if last_complete_object_end > 0:
            # Cut the string at the last complete object and add closing structures
            truncated_json = json_str[:last_complete_object_end+1]
            
            # Count open structures that need to be closed
            open_braces = truncated_json.count('{')
            close_braces = truncated_json.count('}')
            open_brackets = truncated_json.count('[')
            close_brackets = truncated_json.count(']')
            
            # Add missing closing brackets and braces
            if open_brackets > close_brackets:
                truncated_json += ']' * (open_brackets - close_brackets)
            if open_braces > close_braces:
                truncated_json += '}' * (open_braces - close_braces)
                
            return truncated_json
            
        # If we can't find a clear truncation point, fall back to basic repair
        return self._fix_incomplete_json(json_str)