# services/llm_service.py
from abc import ABC, abstractmethod

class LLMService(ABC):
    """Abstract base class for LLM services"""
    
    @abstractmethod
    def generate_response(self, prompt: str) -> str:
        """
        Generate a response from the LLM
        
        Args:
            prompt (str): The prompt to send to the LLM
            
        Returns:
            str: The generated response
        """
        pass