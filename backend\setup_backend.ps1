# setup_backend.ps1
# <PERSON><PERSON><PERSON> to setup the career assessment backend project structure

# Create main directories
$directories = @(
    ".\api",
    ".\config",
    ".\data",
    ".\data_providers",
    ".\dummy",
    ".\logs",
    ".\models",
    ".\output\api_responses",
    ".\output\batch_assessments",
    ".\prompts",
    ".\services",
    ".\utils"
)

# Create directories
foreach ($dir in $directories) {
    if (-not (Test-Path -Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "Created directory: $dir"
    } else {
        Write-Host "Directory already exists: $dir"
    }
}

# Create __init__.py files in each package directory
$init_files = @(
    ".\api\__init__.py",
    ".\config\__init__.py",
    ".\data\__init__.py",
    ".\data_providers\__init__.py",
    ".\dummy\__init__.py",
    ".\models\__init__.py",
    ".\prompts\__init__.py",
    ".\services\__init__.py",
    ".\utils\__init__.py"
)

foreach ($file in $init_files) {
    if (-not (Test-Path -Path $file)) {
        New-Item -ItemType File -Path $file -Force
        Write-Host "Created file: $file"
    } else {
        Write-Host "File already exists: $file"
    }
}

# Create all the required files
$files = @(
    ".\api\app.py",
    ".\api\models.py",
    ".\api\routes.py",
    ".\api\services.py",
    ".\config\config.py",
    ".\data\style_descriptors.py",
    ".\data\motivator_descriptors.py",
    ".\data\competency_descriptors.py",
    ".\data\mba_data.py",
    ".\data_providers\base_provider.py",
    ".\data_providers\file_provider.py",
    ".\data_providers\db_provider.py",
    ".\data_providers\factory.py",
    ".\dummy\score_generator.py",
    ".\models\input_models.py",
    ".\models\output_models.py",
    ".\prompts\templates.py",
    ".\services\llm_service.py",
    ".\services\claude_service.py",
    ".\utils\formatter.py",
    ".\main.py",
    ".\process_candidates.py",
    ".\run_api.py"
)

foreach ($file in $files) {
    if (-not (Test-Path -Path $file)) {
        New-Item -ItemType File -Path $file -Force
        Write-Host "Created file: $file"
    } else {
        Write-Host "File already exists: $file"
    }
}

# Create .env file
$env_content = @"
ANTHROPIC_API_KEY=your_api_key_here
CACHE_ENABLED=true
CACHE_MAX_AGE_DAYS=30
DATA_PROVIDER_TYPE=file
"@

if (-not (Test-Path -Path ".\.env")) {
    Set-Content -Path ".\.env" -Value $env_content
    Write-Host "Created .env file"
} else {
    Write-Host ".env file already exists"
}

# Create requirements.txt
$requirements_content = @"
langchain>=0.1.0
langchain-anthropic>=0.0.10
anthropic>=0.8.0
pydantic>=2.5.0
python-dotenv>=1.0.0
fastapi>=0.109.0
uvicorn[standard]>=0.27.0
loguru>=0.7.0
email-validator>=2.1.0
"@

if (-not (Test-Path -Path ".\requirements.txt")) {
    Set-Content -Path ".\requirements.txt" -Value $requirements_content
    Write-Host "Created requirements.txt file"
} else {
    Write-Host "requirements.txt file already exists"
}

Write-Host "`nBackend project structure setup complete!"
Write-Host "`nNext steps:"
Write-Host "1. Create a virtual environment with: uv venv"
Write-Host "2. Activate the virtual environment with: .\.venv\Scripts\activate"
Write-Host "3. Install dependencies with: uv pip install -r requirements.txt"
Write-Host "4. Add your Anthropic API key to the .env file"
Write-Host "5. Paste the code to each file"
Write-Host "6. Run the API server with: python run_api.py"