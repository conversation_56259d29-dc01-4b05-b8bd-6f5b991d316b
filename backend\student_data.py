# student_data.py
import json
import psycopg2
from psycopg2.extras import RealDictCursor
from db_connection import get_db_connection
from loguru import logger

def ensure_object(data):
    """Ensure data is a parsed JSON object"""
    if data is None:
        return None
    if isinstance(data, str):
        try:
            return json.loads(data)
        except json.JSONDecodeError:
            logger.error(f"Failed to parse JSON data: {data[:50]}...")
            return None
    return data

def format_behavioral_factor(factor):
    """Format a behavioral factor into a readable string"""
    if not factor:
        return None
    
    try:
        score = factor['natural_score_right'] if factor['natural_score_right'] is not None else factor['natural_score_left']
        score_label = 'right' if factor['natural_score_right'] is not None else 'left'
        
        if score_label == 'right':
            return f"{factor['left_label']} vs. {factor['right_label']} ({score})"
        else:
            return f"{factor['right_label']} vs. {factor['left_label']} ({score})"
    except Exception as e:
        logger.error(f"Error formatting behavioral factor: {str(e)}")
        return None

def format_motivational_continuum(factor1, factor2):
    """Format a motivational continuum pair into a readable string"""
    if not factor1 or not factor2:
        return None
    
    try:
        return f"{factor1['name']} ({factor1['score']}) vs. {factor2['name']} ({factor2['score']})"
    except Exception as e:
        logger.error(f"Error formatting motivational continuum: {str(e)}")
        return None

def process_raw_data(raw_data):
    """Process raw data from the database into a structured format"""
    # Process behavioral factors
    behavioral_factors = None
    if raw_data.get('behavioral_factors_raw'):
        behavioral_factors = {
            'Problems & Challenges': format_behavioral_factor(raw_data['behavioral_factors_raw'][0]),
            'People & Contacts': format_behavioral_factor(raw_data['behavioral_factors_raw'][1]),
            'Pace & Consistency': format_behavioral_factor(raw_data['behavioral_factors_raw'][2]),
            'Procedures & Constraints': format_behavioral_factor(raw_data['behavioral_factors_raw'][3])
        }
    
    # Process motivational continuum
    motivational_continuum = None
    if raw_data.get('motivational_continuum_raw'):
        motivational_forces = raw_data['motivational_continuum_raw']
        
        def find_force(name):
            for force in motivational_forces:
                if force.get('name') == name:
                    return force
            return None
        
        motivational_continuum = {
            'KNOWLEDGE': format_motivational_continuum(
                find_force("Intellectual"),
                find_force("Instinctive")
            ),
            'UTILITY': format_motivational_continuum(
                find_force("Resourceful"),
                find_force("Selfless")
            ),
            'SURROUNDINGS': format_motivational_continuum(
                find_force("Harmonious"),
                find_force("Objective")
            ),
            'OTHERS': format_motivational_continuum(
                find_force("Altruistic"),
                find_force("Intentional")
            ),
            'POWER': format_motivational_continuum(
                find_force("Commanding"),
                find_force("Collaborative")
            ),
            'METHODOLOGIES': format_motivational_continuum(
                find_force("Structured"),
                find_force("Receptive")
            )
        }
    
    # Return only the requested data
    return {
        'name': raw_data.get('name'),
        'gender': raw_data.get('gender'),
        'behavioral_factors': behavioral_factors,
        'motivational_continuum': motivational_continuum
    }

def get_student_data_by_email(email):
    """Retrieve student data from the database by email"""
    query = """
      SELECT 
        candidate_firstname || ' ' || candidate_lastname AS name,
        gender,
        tti_report_responses->'report'->'sections'->11->'rows' AS behavioral_factors_raw,
        tti_report_responses->'report'->'sections'->12->'driving_forces' AS motivational_continuum_raw
      FROM 
        assessments
      WHERE 
        candidate_email = %s;
    """
    
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute(query, (email,))
            result = cur.fetchone()
            
            if result:
                raw_data = dict(result)
                
                # Ensure JSON data is parsed correctly
                raw_data['behavioral_factors_raw'] = ensure_object(raw_data['behavioral_factors_raw'])
                raw_data['motivational_continuum_raw'] = ensure_object(raw_data['motivational_continuum_raw'])
                
                # Process the raw data and return
                return process_raw_data(raw_data)
            else:
                logger.warning(f"No data found for email: {email}")
                return None
    except Exception as e:
        logger.error(f"Error fetching student data: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

# Convert the behavioral factors and motivational continuum to the format expected by the system
def convert_to_assessment_format(student_data):
    """
    Convert the student data from database format to the format expected by the assessment system
    
    Args:
        student_data (dict): Student data from the database
        
    Returns:
        tuple: (name, gender, style_scores, motivator_scores)
    """
    if not student_data:
        return None, None, None, None
        
    name = student_data.get('name', '')
    gender = student_data.get('gender', '').lower()
    
    # Default to male if gender is not specified
    if not gender or gender not in ['male', 'female']:
        gender = 'male'
    
    # Convert behavioral factors to style scores
    style_scores = {}
    behavioral_factors = student_data.get('behavioral_factors', {})
    
    if behavioral_factors:
        # Helper function to extract score and determine which trait it belongs to
        def parse_factor(factor_string, trait1, trait2):
            if not factor_string:
                return None, None
                
            try:
                # Extract the score
                score = int(factor_string.split('(')[1].split(')')[0])
                
                # Determine which trait the score belongs to
                parts = factor_string.split(' vs. ')
                if '(' in parts[0]:  # First trait has the score
                    return trait1, score
                else:  # Second trait has the score
                    return trait2, score
            except Exception as e:
                logger.error(f"Error parsing factor {factor_string}: {str(e)}")
                return None, None
        
        # Problems & Challenges: maps to DIRECT or REFLECTIVE
        if 'Problems & Challenges' in behavioral_factors:
            trait, score = parse_factor(
                behavioral_factors['Problems & Challenges'],
                'DIRECT', 'REFLECTIVE'
            )
            if trait and score is not None:
                style_scores[trait] = score
        
        # People & Contacts: maps to OUTGOING or RESERVED
        if 'People & Contacts' in behavioral_factors:
            trait, score = parse_factor(
                behavioral_factors['People & Contacts'],
                'OUTGOING', 'RESERVED'
            )
            if trait and score is not None:
                style_scores[trait] = score
        
        # Pace & Consistency: maps to DYNAMIC or STEADY
        if 'Pace & Consistency' in behavioral_factors:
            trait, score = parse_factor(
                behavioral_factors['Pace & Consistency'],
                'DYNAMIC', 'STEADY'
            )
            if trait and score is not None:
                style_scores[trait] = score
        
        # Procedures & Constraints: maps to PIONEERING or PRECISE
        if 'Procedures & Constraints' in behavioral_factors:
            trait, score = parse_factor(
                behavioral_factors['Procedures & Constraints'],
                'PIONEERING', 'PRECISE'
            )
            if trait and score is not None:
                style_scores[trait] = score
    
    # Convert motivational continuum to motivator scores
    motivator_scores = {}
    motivational_continuum = student_data.get('motivational_continuum', {})
    
    # Helper function to extract scores from motivational continuum
    def parse_continuum(continuum_string):
        if not continuum_string:
            return {}
            
        result = {}
        try:
            # Split by "vs."
            parts = continuum_string.split(' vs. ')
            
            # Extract first trait and score
            trait1_parts = parts[0].split('(')
            trait1_name = trait1_parts[0].strip()
            if len(trait1_parts) > 1 and ')' in trait1_parts[1]:
                trait1_score = int(trait1_parts[1].split(')')[0])
                result[trait1_name.upper()] = trait1_score
            
            # Extract second trait and score
            trait2_parts = parts[1].split('(')
            trait2_name = trait2_parts[0].strip()
            if len(trait2_parts) > 1 and ')' in trait2_parts[1]:
                trait2_score = int(trait2_parts[1].split(')')[0])
                result[trait2_name.upper()] = trait2_score
                
        except Exception as e:
            logger.error(f"Error parsing continuum {continuum_string}: {str(e)}")
            
        return result
    
    if motivational_continuum:
        # Knowledge
        if 'KNOWLEDGE' in motivational_continuum:
            knowledge_scores = parse_continuum(motivational_continuum['KNOWLEDGE'])
            motivator_scores.update(knowledge_scores)
        
        # Utility
        if 'UTILITY' in motivational_continuum:
            utility_scores = parse_continuum(motivational_continuum['UTILITY'])
            motivator_scores.update(utility_scores)
        
        # Surroundings
        if 'SURROUNDINGS' in motivational_continuum:
            surroundings_scores = parse_continuum(motivational_continuum['SURROUNDINGS'])
            motivator_scores.update(surroundings_scores)
        
        # Others
        if 'OTHERS' in motivational_continuum:
            others_scores = parse_continuum(motivational_continuum['OTHERS'])
            motivator_scores.update(others_scores)
        
        # Power
        if 'POWER' in motivational_continuum:
            power_scores = parse_continuum(motivational_continuum['POWER'])
            motivator_scores.update(power_scores)
        
        # Methodologies
        if 'METHODOLOGIES' in motivational_continuum:
            methodologies_scores = parse_continuum(motivational_continuum['METHODOLOGIES'])
            motivator_scores.update(methodologies_scores)
    
    return name, gender, style_scores, motivator_scores

# Example usage
if __name__ == "__main__":
    student_email = '<EMAIL>'
    result = get_student_data_by_email(student_email)
    print(json.dumps(result, indent=2))
    
    name, gender, style_scores, motivator_scores = convert_to_assessment_format(result)
    print(f"Name: {name}")
    print(f"Gender: {gender}")
    print(f"Style Scores: {json.dumps(style_scores, indent=2)}")
    print(f"Motivator Scores: {json.dumps(motivator_scores, indent=2)}") 