# test_imports.py
# Run this to find the correct import

import sys
print("Testing different import methods...\n")

# Test 1: Standard import
try:
    import pydantic_ai
    print("✅ SUCCESS: import pydantic_ai")
    print(f"   Location: {pydantic_ai.__file__}")
    print(f"   Contents: {dir(pydantic_ai)}")
except ImportError as e:
    print(f"❌ FAILED: import pydantic_ai - {e}")

# Test 2: Slim version
try:
    import pydantic_ai_slim
    print("✅ SUCCESS: import pydantic_ai_slim")
    print(f"   Location: {pydantic_ai_slim.__file__}")
    print(f"   Contents: {dir(pydantic_ai_slim)}")
except ImportError as e:
    print(f"❌ FAILED: import pydantic_ai_slim - {e}")

# Test 3: Try importing Agent directly from different places
agent_import_attempts = [
    ("from pydantic_ai import Agent", "pydantic_ai"),
    ("from pydantic_ai_slim import Agent", "pydantic_ai_slim"), 
    ("from pydantic_ai.agent import Agent", "pydantic_ai.agent"),
    ("from pydantic_ai_slim.agent import Agent", "pydantic_ai_slim.agent")
]

print("\nTesting Agent imports:")
for attempt, source in agent_import_attempts:
    try:
        exec(attempt)
        print(f"✅ SUCCESS: {attempt}")
    except ImportError as e:
        print(f"❌ FAILED: {attempt} - {e}")

# Test 4: Check what packages are actually available
print("\nChecking site-packages for pydantic-related packages:")
import site
import os

for site_path in site.getsitepackages():
    try:
        packages = os.listdir(site_path)
        pydantic_packages = [p for p in packages if 'pydantic' in p.lower()]
        if pydantic_packages:
            print(f"Site-packages in {site_path}:")
            for pkg in pydantic_packages:
                print(f"  - {pkg}")
    except:
        pass

print("\nTry running this script to see what works!")