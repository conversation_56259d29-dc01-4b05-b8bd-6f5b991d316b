# test_pg_provider.py
import asyncio
import json
from loguru import logger
from dotenv import load_dotenv
import sys

# Add better error formatting
import traceback

# Configure logger to output to console
logger.remove()
logger.add(sys.stderr, level="INFO")

# Make sure environment variables are loaded
load_dotenv()

# Import the functions we want to test
from student_data import get_student_data_by_email, convert_to_assessment_format
from data_providers.pg_provider import PostgreSQLDataProvider

async def test_direct_fetch(email):
    """Test direct fetching from the database"""
    logger.info(f"Testing direct fetch for email: {email}")
    
    # Get student data directly using the function
    raw_data = get_student_data_by_email(email)
    
    if raw_data:
        logger.info("Raw data successfully retrieved")
        logger.info(f"Name: {raw_data.get('name')}")
        logger.info(f"Gender: {raw_data.get('gender')}")
        
        # Print behavioral factors
        bf = raw_data.get('behavioral_factors', {})
        if bf:
            logger.info("Behavioral Factors:")
            for factor, value in bf.items():
                logger.info(f"  {factor}: {value}")
        else:
            logger.warning("No behavioral factors found")
        
        # Print motivational continuum
        mc = raw_data.get('motivational_continuum', {})
        if mc:
            logger.info("Motivational Continuum:")
            for factor, value in mc.items():
                logger.info(f"  {factor}: {value}")
        else:
            logger.warning("No motivational continuum found")
        
        # Convert to assessment format
        name, gender, style_scores, motivator_scores = convert_to_assessment_format(raw_data)
        
        logger.info(f"Converted data - Name: {name}, Gender: {gender}")
        logger.info(f"Style Scores: {json.dumps(style_scores, indent=2)}")
        logger.info(f"Motivator Scores: {json.dumps(motivator_scores, indent=2)}")
        
        return True
    else:
        logger.error(f"No data found for email: {email}")
        return False

async def test_provider(email):
    """Test fetching using the PostgreSQL provider"""
    logger.info(f"Testing PostgreSQL provider for email: {email}")
    
    # Create provider
    provider = PostgreSQLDataProvider()
    
    # Get candidate info
    name, gender, style_scores, motivator_scores = await provider.get_candidate_info(email)
    
    if name and style_scores and motivator_scores:
        logger.info(f"Successfully retrieved data using provider")
        logger.info(f"Name: {name}")
        logger.info(f"Gender: {gender}")
        logger.info(f"Style Scores: {json.dumps(style_scores, indent=2)}")
        logger.info(f"Motivator Scores: {json.dumps(motivator_scores, indent=2)}")
        return True
    else:
        logger.error(f"Failed to retrieve data using provider")
        return False

async def main():
    """Main test function"""
    
    # Test email - replace with a real email that exists in your database
    test_email = input("Enter an email to test (should exist in your database): ").strip()
    
    if not test_email:
        logger.error("No email provided, using <EMAIL>")
        test_email = "<EMAIL>"
    
    try:
        # Test direct fetch
        logger.info("=== Testing Direct Database Fetch ===")
        direct_result = await test_direct_fetch(test_email)
        
        logger.info("\n=== Testing Provider Implementation ===")
        provider_result = await test_provider(test_email)
        
        # Summary
        logger.info("\n=== Test Results ===")
        logger.info(f"Direct fetch: {'SUCCESS' if direct_result else 'FAILED'}")
        logger.info(f"Provider: {'SUCCESS' if provider_result else 'FAILED'}")
        
        if direct_result and provider_result:
            logger.info("All tests passed! PostgreSQL data fetching is working correctly.")
        else:
            logger.error("Some tests failed. Check the logs for details.")
    
    except Exception as e:
        logger.error(f"An error occurred during testing: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    # Run the async test functions
    asyncio.run(main()) 