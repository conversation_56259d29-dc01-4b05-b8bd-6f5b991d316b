import bcrypt
import jwt
from datetime import datetime, timedelta, timezone
from typing import Optional
import os
from dotenv import load_dotenv

load_dotenv()

# Get JWT secret from environment variable or use a default for development
JWT_SECRET = os.getenv("JWT_SECRET", "your-secret-key-for-development")
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 1440  # 24 hours

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password.encode('utf-8'))

def get_password_hash(password: str) -> str:
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET, algorithm=JWT_ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    try:
        # Make sure we're using the same secret and algorithm as when creating tokens
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError as e:
        print(f"JWT expired: {e}")
        return None
    except jwt.InvalidSignatureError as e:
        print(f"JWT invalid signature: {e} - This usually means the token was tampered with or signed with a different secret")
        return None
    except jwt.DecodeError as e:
        print(f"JWT decode error: {e} - This usually means the token is malformed")
        return None
    except jwt.PyJWTError as e:
        print(f"JWT other error: {e}")
        return None