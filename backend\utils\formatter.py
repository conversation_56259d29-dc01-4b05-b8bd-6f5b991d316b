# utils/formatter.py
import json
from typing import Dict, Any
from models.input_models import AssessmentInput
from data.style_descriptors import STYLE_DESCRIPTORS
from data.motivator_descriptors import MOTIVATOR_DESCRIPTORS
from data.competency_descriptors import COMPETENCY_DESCRIPTORS
from data.mba_data import MBA_SPECIALIZATIONS, MBA_MAPPING, CAREER_GROUPS

def format_style_scores(assessment_input: AssessmentInput) -> str:
    """Format style scores as a string"""
    formatted = ""
    for style, score in assessment_input.style_scores.dict(exclude_none=True).items():
        formatted += f"* {style}: {score}/100\n"
    return formatted

def format_motivator_scores(assessment_input: AssessmentInput) -> str:
    """Format motivator scores as a string"""
    formatted = ""
    for motivator, score in assessment_input.motivator_scores.dict(exclude_none=True).items():
        formatted += f"* {motivator}: {score}/100\n"
    return formatted

def format_mba_specializations(specializations: list) -> str:
    """Format MBA specializations as a string"""
    return ", ".join(specializations)

def format_career_groups(career_groups: Dict[str, Any]) -> str:
    """Convert career groups to a pretty JSON string"""
    return json.dumps(career_groups, indent=2)

def format_mba_mapping(mba_mapping: Dict[str, Any]) -> str:
    """Convert MBA mapping to a pretty JSON string"""
    return json.dumps(mba_mapping, indent=2)