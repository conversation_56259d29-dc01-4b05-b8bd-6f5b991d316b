services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai_report_backend
    # For "EC2-like" local testing, backend port is not exposed directly.
    # Uncomment to test API directly from host during dev:
    # ports:
    #   - "127.0.0.1:8000:8000"
    networks:
      - app_network
    restart: unless-stopped
    # Provide .env file for runtime configuration
    env_file:
      - ./backend/.env # Path relative to docker-compose.yml
    # Handle .encryption_key via volume mount (recommended for sensitive files)
    # volumes:
    #   - ./backend/.encryption_key:/app/.encryption_key:ro
    # For persistent logs/output (optional):
    #   - ai_report_backend_logs:/app/logs
    #   - ai_report_backend_output:/app/output

  frontend:
    # ... (frontend configuration remains the same) ...
    build:
      context: ./frontend_v2
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_API_URL: http://backend:8000
    container_name: ai_report_frontend
    ports:
      - "127.0.0.1:3008:3008"
    depends_on:
      - backend
    environment:
      NEXT_PUBLIC_API_URL: http://backend:8000
    networks:
      - app_network
    restart: unless-stopped

networks:
  app_network:
    driver: bridge

# Optional named volumes for backend logs/output if you uncommented them above
# volumes:
#   ai_report_backend_logs:
#   ai_report_backend_output: