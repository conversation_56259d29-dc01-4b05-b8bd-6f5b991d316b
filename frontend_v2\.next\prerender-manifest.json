{"version": 4, "routes": {"/favicon.ico": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "image/x-icon", "x-next-cache-tags": "_N_T_/layout,_N_T_/favicon.ico/layout,_N_T_/favicon.ico/route,_N_T_/favicon.ico"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/favicon.ico", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "e336841c626e553568e5a6867d39b8bd", "previewModeSigningKey": "863f0fa35af7780c69cff0de1a69bd02819d133118c62fe3df8fa66296809d43", "previewModeEncryptionKey": "cb99202f9dcad104e0e86b05ec1d0cb9cd60cc622c5f0e0dee5abbfc833c9dc6"}}