{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/1_1_understand_yourself", "regex": "^/1_1_understand_yourself(?:/)?$", "routeKeys": {}, "namedRegex": "^/1_1_understand_yourself(?:/)?$"}, {"page": "/1_2_strengths_limitations", "regex": "^/1_2_strengths_limitations(?:/)?$", "routeKeys": {}, "namedRegex": "^/1_2_strengths_limitations(?:/)?$"}, {"page": "/1_3_communication_styles", "regex": "^/1_3_communication_styles(?:/)?$", "routeKeys": {}, "namedRegex": "^/1_3_communication_styles(?:/)?$"}, {"page": "/1_4_emotional_patterns", "regex": "^/1_4_emotional_patterns(?:/)?$", "routeKeys": {}, "namedRegex": "^/1_4_emotional_patterns(?:/)?$"}, {"page": "/1_5_how_others_percive_you", "regex": "^/1_5_how_others_percive_you(?:/)?$", "routeKeys": {}, "namedRegex": "^/1_5_how_others_percive_you(?:/)?$"}, {"page": "/2_1_making_impact", "regex": "^/2_1_making_impact(?:/)?$", "routeKeys": {}, "namedRegex": "^/2_1_making_impact(?:/)?$"}, {"page": "/2_2_academic_impact", "regex": "^/2_2_academic_impact(?:/)?$", "routeKeys": {}, "namedRegex": "^/2_2_academic_impact(?:/)?$"}, {"page": "/2_3_professional_impact", "regex": "^/2_3_professional_impact(?:/)?$", "routeKeys": {}, "namedRegex": "^/2_3_professional_impact(?:/)?$"}, {"page": "/2_4_key_competencies", "regex": "^/2_4_key_competencies(?:/)?$", "routeKeys": {}, "namedRegex": "^/2_4_key_competencies(?:/)?$"}, {"page": "/2_5_work_environment", "regex": "^/2_5_work_environment(?:/)?$", "routeKeys": {}, "namedRegex": "^/2_5_work_environment(?:/)?$"}, {"page": "/3_1_career_options_for_you", "regex": "^/3_1_career_options_for_you(?:/)?$", "routeKeys": {}, "namedRegex": "^/3_1_career_options_for_you(?:/)?$"}, {"page": "/3_1_career_path", "regex": "^/3_1_career_path(?:/)?$", "routeKeys": {}, "namedRegex": "^/3_1_career_path(?:/)?$"}, {"page": "/3_2_recommended_careers", "regex": "^/3_2_recommended_careers(?:/)?$", "routeKeys": {}, "namedRegex": "^/3_2_recommended_careers(?:/)?$"}, {"page": "/3_3_recommended_specializations", "regex": "^/3_3_recommended_specializations(?:/)?$", "routeKeys": {}, "namedRegex": "^/3_3_recommended_specializations(?:/)?$"}, {"page": "/3_4_build_your_resume", "regex": "^/3_4_build_your_resume(?:/)?$", "routeKeys": {}, "namedRegex": "^/3_4_build_your_resume(?:/)?$"}, {"page": "/3_5_prepare_for_interviews", "regex": "^/3_5_prepare_for_interviews(?:/)?$", "routeKeys": {}, "namedRegex": "^/3_5_prepare_for_interviews(?:/)?$"}, {"page": "/4_1_embark_on_success_path", "regex": "^/4_1_embark_on_success_path(?:/)?$", "routeKeys": {}, "namedRegex": "^/4_1_embark_on_success_path(?:/)?$"}, {"page": "/4_2_swod_analysis", "regex": "^/4_2_swod_analysis(?:/)?$", "routeKeys": {}, "namedRegex": "^/4_2_swod_analysis(?:/)?$"}, {"page": "/4_3_top_activities", "regex": "^/4_3_top_activities(?:/)?$", "routeKeys": {}, "namedRegex": "^/4_3_top_activities(?:/)?$"}, {"page": "/4_4_personal_development_plan", "regex": "^/4_4_personal_development_plan(?:/)?$", "routeKeys": {}, "namedRegex": "^/4_4_personal_development_plan(?:/)?$"}, {"page": "/4_5_conclusion", "regex": "^/4_5_conclusion(?:/)?$", "routeKeys": {}, "namedRegex": "^/4_5_conclusion(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/landingpage_v2", "regex": "^/landingpage_v2(?:/)?$", "routeKeys": {}, "namedRegex": "^/landingpage_v2(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}