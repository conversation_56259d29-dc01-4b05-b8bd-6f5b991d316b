(()=>{var e={};e.id=169,e.ids=[169],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},14339:(e,t,r)=>{Promise.resolve().then(r.bind(r,15766))},15766:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\TM\\\\ai_report_01062025\\\\frontend_v2\\\\app\\\\(with-sidebar)\\\\2_1_making_impact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\2_1_making_impact\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27067:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var i=r(65239),s=r(48088),n=r(88170),o=r.n(n),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["(with-sidebar)",{children:["2_1_making_impact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,15766)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\2_1_making_impact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,35363)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,87707)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\2_1_making_impact\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(with-sidebar)/2_1_making_impact/page",pathname:"/2_1_making_impact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44884:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var i=r(60687),s=r(76180),n=r.n(s);r(43210);var o=r(85814),a=r.n(o),l=r(24934),d=r(30474),c=r(64626);function u(){return(0,i.jsxs)("div",{className:"jsx-485dec7556801de8 w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1]",children:[(0,i.jsx)(n(),{id:"485dec7556801de8",children:'.card.jsx-485dec7556801de8{-webkit-border-radius:16px;-moz-border-radius:16px;border-radius:16px;padding:24px;margin-bottom:24px;position:relative;-webkit-box-shadow:0 2px 6px rgba(0,0,0,.1);-moz-box-shadow:0 2px 6px rgba(0,0,0,.1);box-shadow:0 2px 6px rgba(0,0,0,.1);background-color:white;position:relative;overflow:hidden;max-width:980px;margin-left:auto;margin-right:auto}.card.jsx-485dec7556801de8::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:-webkit-linear-gradient(315deg,rgba(55,147,247,.7)0%,rgba(55,147,247,.2)10%,rgba(55,147,247,.05)20%,rgba(55,147,247,0)30%);background:-moz-linear-gradient(315deg,rgba(55,147,247,.7)0%,rgba(55,147,247,.2)10%,rgba(55,147,247,.05)20%,rgba(55,147,247,0)30%);background:-o-linear-gradient(315deg,rgba(55,147,247,.7)0%,rgba(55,147,247,.2)10%,rgba(55,147,247,.05)20%,rgba(55,147,247,0)30%);background:linear-gradient(135deg,rgba(55,147,247,.7)0%,rgba(55,147,247,.2)10%,rgba(55,147,247,.05)20%,rgba(55,147,247,0)30%);-webkit-border-radius:16px;-moz-border-radius:16px;border-radius:16px;z-index:0;pointer-events:none}.card-content.jsx-485dec7556801de8{position:relative;z-index:1}.info-icon.jsx-485dec7556801de8{position:absolute;top:20px;right:20px;width:24px;height:24px;background-color:#3793F7;color:white;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;font-style:italic;font-weight:bold;z-index:2}.bullet-list.jsx-485dec7556801de8{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px}.bullet-item.jsx-485dec7556801de8{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;line-height:1.5}.bullet.jsx-485dec7556801de8{color:#3793F7;font-size:18px;margin-right:10px;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}'}),(0,i.jsxs)("div",{className:"jsx-485dec7556801de8 w-full max-w-[1100px] mx-auto px-5 pt-8 relative",children:[(0,i.jsxs)("div",{className:"jsx-485dec7556801de8 flex flex-col md:flex-row relative mb-10 mt-5",children:[(0,i.jsx)("div",{className:"jsx-485dec7556801de8 flex-none w-full md:w-[400px] lg:w-[400px] relative",children:(0,i.jsx)("div",{className:"jsx-485dec7556801de8 relative w-full h-[320px]",children:(0,i.jsx)(d.default,{src:"/2.1TM.svg",alt:"Making Impact Illustration",fill:!0,style:{objectFit:"contain"},priority:!0})})}),(0,i.jsxs)("div",{className:"jsx-485dec7556801de8 flex-1 md:pl-10 mt-4 md:mt-0",children:[(0,i.jsx)("h1",{className:"jsx-485dec7556801de8 text-[3rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[3rem]",children:"2.1 How You can make an Impact"}),(0,i.jsx)("p",{className:"jsx-485dec7556801de8 mb-6 leading-relaxed",children:"This segment examines how your strengths, limitations, and stressors influence your academic and professional performance. This segment also highlights key competencies for workplace success and identifies the work environment where you are most likely to thrive."})]})]}),(0,i.jsx)("div",{className:"jsx-485dec7556801de8 flex justify-center w-full my-8",children:(0,i.jsx)(a(),{href:"/2_2_academic_impact",children:(0,i.jsxs)(l.$,{variant:"outline",className:"rounded-full border-2 border-gray-800 text-gray-900 hover:bg-gray-100 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer",style:{boxShadow:"0 2px 8px rgba(55,147,247,0.10)"},children:["Continue",(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",className:"jsx-485dec7556801de8 ml-2 group-hover:translate-x-1 transition-transform duration-150",children:[(0,i.jsx)("path",{d:"M5 12h14",className:"jsx-485dec7556801de8"}),(0,i.jsx)("path",{d:"M12 5l7 7-7 7",className:"jsx-485dec7556801de8"})]})]})})}),(0,i.jsx)(c.A,{})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,t,r)=>{"use strict";r(56397);var i=r(43210),s=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),n="undefined"!=typeof process&&process.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,r=t.name,i=void 0===r?"stylesheet":r,s=t.optimizeForSpeed,a=void 0===s?n:s;l(o(i),"`name` must be a string"),this._name=i,this._deletedRulePlaceholder="#"+i+"-deleted-rule____{}",l("boolean"==typeof a,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=a,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){return l(o(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},r.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(i){n||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},r.deleteRule=function(e){this._serverSheet.deleteRule(e)},r.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},r.cssRules=function(){return this._serverSheet.cssRules},r.makeStyleTag=function(e,t,r){t&&l(o(t),"makeStyleTag accepts only strings as second parameter");var i=document.createElement("style");this._nonce&&i.setAttribute("nonce",this._nonce),i.type="text/css",i.setAttribute("data-"+e,""),t&&i.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return r?s.insertBefore(i,r):s.appendChild(i),i},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},c={};function u(e,t){if(!t)return"jsx-"+e;var r=String(t),i=e+r;return c[i]||(c[i]="jsx-"+d(e+"-"+r)),c[i]}function p(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return c[r]||(c[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[r]}var h=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,i=void 0===r?null:r,s=t.optimizeForSpeed,n=void 0!==s&&s;this._sheet=i||new a({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),i&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var r=this.getIdAndRules(e),i=r.styleId,s=r.rules;if(i in this._instancesCounts){this._instancesCounts[i]+=1;return}var n=s.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[i]=n,this._instancesCounts[i]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var i=this._fromServer&&this._fromServer[r];i?(i.parentNode.removeChild(i),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],i=e[1];return s.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:i}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,i=e.id;if(r){var s=u(i,r);return{styleId:s,rules:Array.isArray(t)?t.map(function(e){return p(s,e)}):[p(s,t)]}}return{styleId:u(i),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=i.createContext(null);m.displayName="StyleSheetContext";s.default.useInsertionEffect||s.default.useLayoutEffect;var f=void 0;function x(e){var t=f||i.useContext(m);return t&&t.add(e),null}x.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=x},76180:(e,t,r)=>{"use strict";e.exports=r(75913).style},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79995:(e,t,r)=>{Promise.resolve().then(r.bind(r,44884))},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[243,854,256,658,611,793,74],()=>r(27067));module.exports=i})();