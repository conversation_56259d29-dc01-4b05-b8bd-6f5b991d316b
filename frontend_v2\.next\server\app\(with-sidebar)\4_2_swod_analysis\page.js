(()=>{var e={};e.id=638,e.ids=[638],e.modules={2033:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(60687);r(43210);var n=r(85814),i=r.n(n),o=r(24934);let a=function({text:e,href:t,onClick:r,className:n="",style:a}){let l=(0,s.jsxs)(s.Fragment,{children:[e,(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 group-hover:translate-x-1 transition-transform duration-150",children:[(0,s.jsx)("path",{d:"M5 12h14"}),(0,s.jsx)("path",{d:"M12 5l7 7-7 7"})]})]}),d="rounded-full border-2 border-gray-800 dark:border-gray-200 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer "+(n||"");return(0,s.jsx)("div",{className:"flex justify-center w-full my-8",children:t?(0,s.jsx)(o.$,{asChild:!0,variant:"outline",className:d,style:a,onClick:r,children:(0,s.jsx)(i(),{href:t,children:l})}):(0,s.jsx)(o.$,{variant:"outline",className:d,style:a,onClick:r,children:l})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4021:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\TM\\\\ai_report_01062025\\\\frontend_v2\\\\app\\\\(with-sidebar)\\\\4_2_swod_analysis\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\4_2_swod_analysis\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32842:(e,t,r)=>{Promise.resolve().then(r.bind(r,4021))},33873:e=>{"use strict";e.exports=require("path")},47763:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(60687),n=r(76180),i=r.n(n),o=r(43210),a=r(30474),l=r(93947),d=r(2033),c=r(64626);let u=({cardId:e,type:t,icon:r,title:n,description:i,expandedContent:l,color:d,bgGradient:c})=>{let[u,p]=(0,o.useState)(!1),h={position:"relative",borderRadius:"16px",padding:"24px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",backgroundColor:"white",overflow:"hidden",display:"flex",flexDirection:"column",minHeight:u?"fit-content":"320px",height:u?"fit-content":"320px",transition:"all 0.3s ease",background:`linear-gradient(135deg, ${c.from} 0%, ${c.via} 20%, transparent 40%), white`},m={marginTop:"auto",display:"inline-flex",alignItems:"center",justifyContent:"center",padding:"8px 16px",backgroundColor:"transparent",border:`2px solid ${d}`,color:d,borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer",transition:"opacity 0.2s ease",height:"36px",minWidth:"120px"},f=e=>({__html:e.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"")});return(0,s.jsx)("div",{style:h,"data-card-id":e,"data-expanded":u,children:(0,s.jsxs)("div",{style:{position:"relative",zIndex:10,display:"flex",flexDirection:"column",flex:1},children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px",gap:"16px"},children:[(0,s.jsx)("div",{style:{width:"50px",height:"50px",position:"relative",flexShrink:0},children:(0,s.jsx)(a.default,{src:r,alt:`${n} Icon`,fill:!0,style:{objectFit:"contain"}})}),(0,s.jsx)("h2",{style:{fontSize:"24px",fontWeight:"600",color:"#374151",margin:0},children:n})]}),(0,s.jsx)("p",{style:{fontSize:"16px",lineHeight:"1.6",color:"#4B5563",marginBottom:"20px"},children:i}),(0,s.jsx)("button",{style:m,onClick:e=>{e.preventDefault(),e.stopPropagation(),p(e=>!e)},type:"button","aria-expanded":u,"aria-label":`${u?"Hide":"Show"} more ${n.toLowerCase()} details`,onMouseEnter:e=>e.currentTarget.style.opacity="0.8",onMouseLeave:e=>e.currentTarget.style.opacity="1",children:u?"Show Less":"Show More"}),(0,s.jsx)("div",{style:{overflow:"hidden",transition:"all 0.3s ease-in-out",maxHeight:u?"2000px":"0px",opacity:+!!u,marginTop:u?"20px":"0px",paddingTop:u?"8px":"0px"},children:(0,s.jsx)("ul",{style:{display:"flex",flexDirection:"column",gap:"12px",listStyle:"none",padding:0,margin:0},children:l.map((r,n)=>(0,s.jsxs)("li",{style:{display:"flex",alignItems:"flex-start",lineHeight:"1.6"},children:[(0,s.jsx)("span",{style:{color:d,fontSize:"18px",marginRight:"12px",flexShrink:0},children:"•"}),(0,s.jsx)("span",{dangerouslySetInnerHTML:f(r)})]},`${e}-${t}-${n}`))})})]})})};function p(){let{assessmentData:e,loading:t,error:r}=(0,l.U)();if(t)return(0,s.jsx)("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"50vh"},children:(0,s.jsxs)("div",{style:{textAlign:"center"},children:[(0,s.jsx)("p",{style:{fontSize:"1.25rem",color:"#3793F7",marginBottom:"8px"},children:"Loading your SWOD analysis..."}),(0,s.jsx)("div",{style:{width:"48px",height:"48px",border:"4px solid #f3f4f6",borderTop:"4px solid #3793F7",borderRadius:"50%",animation:"spin 1s linear infinite",margin:"0 auto"}})]})});let n=e?.assessment?.section_i?.strengths?.strengths||[],o=n.length>0?n[0].name:"No Data Available",a=n.length>0?n[0].description:"No strengths data could be fetched from the assessment",p=e?.assessment?.section_i?.study_manifestations?.enablers||[],h=p.length>0?p[0]:"No opportunities data could be fetched from the assessment",m=e?.assessment?.section_i?.strengths?.critical_actions||[],f=e?.assessment?.section_i?.stressors?.length>0&&e?.assessment?.section_i?.stressors[0]?.mitigation_steps||[],g=e?.assessment?.section_ii?.impact_strategies?.academic||[],x=m.length>0?m[0]:f.length>0?f[0]:g.length>0?g[0]:"No workarounds data could be fetched from the assessment",y=e?.assessment?.section_i?.study_manifestations?.derailers||[],v=e?.assessment?.section_i?.limitations?.action_steps||[],_=e?.assessment?.section_ii?.competency_assessment?.time_management?.improvements||[],b=y.length>0?y[0]:v.length>0?v[0]:_.length>0?_[0]:"No development areas data could be fetched from the assessment",S=[...n.slice(1).map(e=>`<strong>${e.name}:</strong> ${e.description}`)],j=[...m.slice(1),...f.slice(1),...g.slice(1)],w=[...p.slice(1)],C=[...y.slice(1),...v.slice(1),..._.slice(1)],k={width:"100%",marginTop:"-32px",marginLeft:"auto",marginRight:"auto",paddingBottom:"0",position:"relative",backgroundColor:"#f1f1f1",padding:"16px"},z={width:"100%",maxWidth:"1100px",margin:"0 auto",padding:"32px 20px 0"},R={fontSize:"2.8rem",fontWeight:"300",color:"#3793F7",marginBottom:"40px",textAlign:"center"};return r?(0,s.jsx)("div",{style:k,children:(0,s.jsxs)("div",{style:z,children:[(0,s.jsx)("h1",{style:R,children:"4.2 SWOD Analysis"}),(0,s.jsx)("div",{style:{textAlign:"center",padding:"2rem",backgroundColor:"#fef2f2",borderRadius:"8px",border:"1px solid #fecaca"},children:(0,s.jsx)("p",{style:{color:"#dc2626",fontSize:"1.125rem"},children:"Unable to load your SWOD analysis. Please try refreshing the page."})})]})}):(0,s.jsxs)("div",{style:k,className:"jsx-ff161281ed666c63",children:[(0,s.jsx)(i(),{id:"ff161281ed666c63",children:"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"}),(0,s.jsxs)("div",{style:z,className:"jsx-ff161281ed666c63",children:[(0,s.jsx)("h1",{style:R,className:"jsx-ff161281ed666c63",children:"4.2 SWOD Analysis"}),(0,s.jsx)("p",{style:{marginBottom:"2.5rem",lineHeight:"1.625",maxWidth:"none"},className:"jsx-ff161281ed666c63",children:"Your SWOD is a summarization of your Strengths, Workarounds, Development Areas and Opportunities, that have been presented in the various segments and sections of your Kaleidoscope."}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(2, 1fr)",gap:"2rem",marginBottom:"2.5rem",alignItems:"flex-start"},className:"jsx-ff161281ed666c63",children:[(0,s.jsx)(u,{cardId:"strengths-001",type:"strengths",icon:"/StrengthTM.svg",title:"Strengths",description:`${o}: ${a}`,expandedContent:S,color:"#6ec850",bgGradient:{from:"rgba(110,200,80,0.7)",via:"rgba(110,200,80,0.2)"}}),(0,s.jsx)(u,{cardId:"workarounds-002",type:"workarounds",icon:"/WorkaroundsTM.svg",title:"Workarounds",description:x,expandedContent:j,color:"#ff9933",bgGradient:{from:"rgba(255,153,51,0.7)",via:"rgba(255,153,51,0.2)"}}),(0,s.jsx)(u,{cardId:"opportunities-003",type:"opportunities",icon:"/OpportunitiesTM.svg",title:"Opportunities",description:h,expandedContent:w,color:"#3793F7",bgGradient:{from:"rgba(55,147,247,0.7)",via:"rgba(55,147,247,0.2)"}}),(0,s.jsx)(u,{cardId:"development-004",type:"development",icon:"/Dev AreasTM.svg",title:"Development Areas",description:b,expandedContent:C,color:"#8a67e2",bgGradient:{from:"rgba(138,103,226,0.7)",via:"rgba(138,103,226,0.2)"}})]}),(0,s.jsx)(d.A,{text:"CONTINUE",href:"/4_4_conclusion"}),(0,s.jsx)(c.A,{})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,t,r)=>{"use strict";r(56397);var s=r(43210),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),i="undefined"!=typeof process&&process.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,n=t.optimizeForSpeed,a=void 0===n?i:n;l(o(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",l("boolean"==typeof a,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=a,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){return l(o(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},r.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},r.deleteRule=function(e){this._serverSheet.deleteRule(e)},r.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},r.cssRules=function(){return this._serverSheet.cssRules},r.makeStyleTag=function(e,t,r){t&&l(o(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return r?n.insertBefore(s,r):n.appendChild(s),s},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},c={};function u(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return c[s]||(c[s]="jsx-"+d(e+"-"+r)),c[s]}function p(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return c[r]||(c[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[r]}var h=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,n=t.optimizeForSpeed,i=void 0!==n&&n;this._sheet=s||new a({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),s&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var r=this.getIdAndRules(e),s=r.styleId,n=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var i=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=i,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return n.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var n=u(s,r);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return p(n,e)}):[p(n,t)]}}return{styleId:u(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=s.createContext(null);m.displayName="StyleSheetContext";n.default.useInsertionEffect||n.default.useLayoutEffect;var f=void 0;function g(e){var t=f||s.useContext(m);return t&&t.add(e),null}g.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=g},76180:(e,t,r)=>{"use strict";e.exports=r(75913).style},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94453:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),n=r(48088),i=r(88170),o=r.n(i),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["(with-sidebar)",{children:["4_2_swod_analysis",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4021)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\4_2_swod_analysis\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,35363)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,87707)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\4_2_swod_analysis\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(with-sidebar)/4_2_swod_analysis/page",pathname:"/4_2_swod_analysis",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},95890:(e,t,r)=>{Promise.resolve().then(r.bind(r,47763))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,854,256,658,611,793,74],()=>r(94453));module.exports=s})();