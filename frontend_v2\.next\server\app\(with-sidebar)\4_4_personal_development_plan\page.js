(()=>{var e={};e.id=404,e.ids=[404],e.modules={2033:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(60687);r(43210);var a=r(85814),i=r.n(a),n=r(24934);let l=function({text:e,href:t,onClick:r,className:a="",style:l}){let o=(0,s.jsxs)(s.Fragment,{children:[e,(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 group-hover:translate-x-1 transition-transform duration-150",children:[(0,s.jsx)("path",{d:"M5 12h14"}),(0,s.jsx)("path",{d:"M12 5l7 7-7 7"})]})]}),d="rounded-full border-2 border-gray-800 dark:border-gray-200 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer "+(a||"");return(0,s.jsx)("div",{className:"flex justify-center w-full my-8",children:t?(0,s.jsx)(n.$,{asChild:!0,variant:"outline",className:d,style:l,onClick:r,children:(0,s.jsx)(i(),{href:t,children:o})}):(0,s.jsx)(n.$,{variant:"outline",className:d,style:l,onClick:r,children:o})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6645:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(60687);r(43210);var a=r(96241);let i={blue:{gradient:"linear-gradient(135deg, rgba(55,147,247,0.5) 0%, rgba(55,147,247,0.05) 30%, transparent 100%)",iconBg:"#3793F7"},orange:{gradient:"linear-gradient(135deg, rgba(255,168,0,0.5) 0%, rgba(255,168,0,0.05) 30%, transparent 100%)",iconBg:"#FFA800"}},n=({children:e,className:t="",style:r={},infoIcon:n=!1,infoIconContent:l="i",color:o="blue"})=>{let d=i[o]||i.blue;return(0,s.jsxs)("div",{className:(0,a.cn)("relative rounded-2xl p-6 mb-6 shadow-md bg-white dark:bg-gray-800 overflow-hidden max-w-[980px] mx-auto text-gray-900 dark:text-gray-100",t),style:r,children:[(0,s.jsx)("div",{style:{position:"absolute",inset:0,zIndex:0,pointerEvents:"none",borderRadius:"1rem",background:d.gradient}}),n&&(0,s.jsx)("div",{className:"absolute top-5 right-5 w-6 h-6 text-white rounded-full flex items-center justify-center italic font-bold z-10",style:{background:d.iconBg},children:l}),(0,s.jsx)("div",{className:"relative z-[1]",children:e})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24479:(e,t,r)=>{"use strict";r.d(t,{nD:()=>W,ub:()=>K,As:()=>B,$m:()=>J});var s=r(60687),a=r(43210),i=r(11273),n=r(72031),l=r(98599),o=r(70569),d=r(65551),c=r(3416),m=r(95682),p=r(96963),u=r(43),x="Accordion",h=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[g,y,f]=(0,n.N)(x),[v,b]=(0,i.A)(x,[f,m.z3]),j=(0,m.z3)(),k=a.forwardRef((e,t)=>{let{type:r,...a}=e;return(0,s.jsx)(g.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,s.jsx)(T,{...a,ref:t}):(0,s.jsx)(S,{...a,ref:t})})});k.displayName=x;var[N,w]=v(x),[_,C]=v(x,{collapsible:!1}),S=a.forwardRef((e,t)=>{let{value:r,defaultValue:i,onValueChange:n=()=>{},collapsible:l=!1,...o}=e,[c,m]=(0,d.i)({prop:r,defaultProp:i??"",onChange:n,caller:x});return(0,s.jsx)(N,{scope:e.__scopeAccordion,value:a.useMemo(()=>c?[c]:[],[c]),onItemOpen:m,onItemClose:a.useCallback(()=>l&&m(""),[l,m]),children:(0,s.jsx)(_,{scope:e.__scopeAccordion,collapsible:l,children:(0,s.jsx)(z,{...o,ref:t})})})}),T=a.forwardRef((e,t)=>{let{value:r,defaultValue:i,onValueChange:n=()=>{},...l}=e,[o,c]=(0,d.i)({prop:r,defaultProp:i??[],onChange:n,caller:x}),m=a.useCallback(e=>c((t=[])=>[...t,e]),[c]),p=a.useCallback(e=>c((t=[])=>t.filter(t=>t!==e)),[c]);return(0,s.jsx)(N,{scope:e.__scopeAccordion,value:o,onItemOpen:m,onItemClose:p,children:(0,s.jsx)(_,{scope:e.__scopeAccordion,collapsible:!0,children:(0,s.jsx)(z,{...l,ref:t})})})}),[A,P]=v(x),z=a.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:i,dir:n,orientation:d="vertical",...m}=e,p=a.useRef(null),x=(0,l.s)(p,t),f=y(r),v="ltr"===(0,u.jH)(n),b=(0,o.m)(e.onKeyDown,e=>{if(!h.includes(e.key))return;let t=e.target,r=f().filter(e=>!e.ref.current?.disabled),s=r.findIndex(e=>e.ref.current===t),a=r.length;if(-1===s)return;e.preventDefault();let i=s,n=a-1,l=()=>{(i=s+1)>n&&(i=0)},o=()=>{(i=s-1)<0&&(i=n)};switch(e.key){case"Home":i=0;break;case"End":i=n;break;case"ArrowRight":"horizontal"===d&&(v?l():o());break;case"ArrowDown":"vertical"===d&&l();break;case"ArrowLeft":"horizontal"===d&&(v?o():l());break;case"ArrowUp":"vertical"===d&&o()}let c=i%a;r[c].ref.current?.focus()});return(0,s.jsx)(A,{scope:r,disabled:i,direction:n,orientation:d,children:(0,s.jsx)(g.Slot,{scope:r,children:(0,s.jsx)(c.sG.div,{...m,"data-orientation":d,ref:x,onKeyDown:i?void 0:b})})})}),L="AccordionItem",[M,D]=v(L),I=a.forwardRef((e,t)=>{let{__scopeAccordion:r,value:a,...i}=e,n=P(L,r),l=w(L,r),o=j(r),d=(0,p.B)(),c=a&&l.value.includes(a)||!1,u=n.disabled||e.disabled;return(0,s.jsx)(M,{scope:r,open:c,disabled:u,triggerId:d,children:(0,s.jsx)(m.bL,{"data-orientation":n.orientation,"data-state":H(c),...o,...i,ref:t,disabled:u,open:c,onOpenChange:e=>{e?l.onItemOpen(a):l.onItemClose(a)}})})});I.displayName=L;var R="AccordionHeader",E=a.forwardRef((e,t)=>{let{__scopeAccordion:r,...a}=e,i=P(x,r),n=D(R,r);return(0,s.jsx)(c.sG.h3,{"data-orientation":i.orientation,"data-state":H(n.open),"data-disabled":n.disabled?"":void 0,...a,ref:t})});E.displayName=R;var F="AccordionTrigger",O=a.forwardRef((e,t)=>{let{__scopeAccordion:r,...a}=e,i=P(x,r),n=D(F,r),l=C(F,r),o=j(r);return(0,s.jsx)(g.ItemSlot,{scope:r,children:(0,s.jsx)(m.l9,{"aria-disabled":n.open&&!l.collapsible||void 0,"data-orientation":i.orientation,id:n.triggerId,...o,...a,ref:t})})});O.displayName=F;var q="AccordionContent",$=a.forwardRef((e,t)=>{let{__scopeAccordion:r,...a}=e,i=P(x,r),n=D(q,r),l=j(r);return(0,s.jsx)(m.UC,{role:"region","aria-labelledby":n.triggerId,"data-orientation":i.orientation,...l,...a,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function H(e){return e?"open":"closed"}$.displayName=q;var U=r(78272),G=r(96241);function W({...e}){return(0,s.jsx)(k,{"data-slot":"accordion",...e})}function B({className:e,...t}){return(0,s.jsx)(I,{"data-slot":"accordion-item",className:(0,G.cn)("border-b last:border-b-0",e),...t})}function J({className:e,children:t,...r}){return(0,s.jsx)(E,{className:"flex",children:(0,s.jsxs)(O,{"data-slot":"accordion-trigger",className:(0,G.cn)("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",e),...r,children:[t,(0,s.jsx)(U.A,{className:"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"})]})})}function K({className:e,children:t,...r}){return(0,s.jsx)($,{"data-slot":"accordion-content",className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",...r,children:(0,s.jsx)("div",{className:(0,G.cn)("pt-0 pb-4",e),children:t})})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39390:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var s=r(60687);r(43210);var a=r(78148),i=r(96241);function n({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},42664:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>T});var s=r(60687),a=r(76180),i=r.n(a),n=r(43210),l=r(93947),o=r(2033),d=r(6645),c=r(64626),m=r(24934),p=r(68988),u=r(39390),x=r(63974),h=r(24479),g=r(92241),y=r(62688);let f=(0,y.A)("bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]]),v=(0,y.A)("italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]]),b=(0,y.A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]),j=(0,y.A)("list-ordered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]]),k=(0,y.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),N=(0,y.A)("rotate-cw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]),w=(0,y.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),_=(0,y.A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),C=(0,y.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),S=({content:e,onChange:t,placeholder:r,id:a})=>{let l=(0,n.useRef)(null),[o,d]=(0,n.useState)(!1),[c,p]=(0,n.useState)(e);(0,n.useEffect)(()=>{l.current&&(l.current.innerHTML=e||"",p(e||""),u())},[]),(0,n.useEffect)(()=>{l.current&&e!==c&&(l.current.innerHTML=e||"",p(e||""),u())},[e,c]);let u=()=>{l.current&&l.current.querySelectorAll("ul, ol").forEach(e=>{"UL"===e.tagName?(e.style.listStyleType="disc",e.style.marginLeft="20px",e.style.paddingLeft="0px"):"OL"===e.tagName&&(e.style.listStyleType="decimal",e.style.marginLeft="20px",e.style.paddingLeft="0px")})},x=()=>{if(l.current){let e=l.current.innerHTML;p(e),t(e)}},h=(e,t)=>{if(l.current){if(l.current.focus(),"insertUnorderedList"===e||"insertOrderedList"===e){let r=window.getSelection();r&&r.rangeCount>0&&(document.execCommand(e,!1,t),setTimeout(()=>{l.current&&(l.current.querySelectorAll("ul, ol").forEach(e=>{"UL"===e.tagName?(e.style.listStyleType="disc",e.style.marginLeft="20px",e.style.paddingLeft="0px"):"OL"===e.tagName&&(e.style.listStyleType="decimal",e.style.marginLeft="20px",e.style.paddingLeft="0px")}),x())},10))}else document.execCommand(e,!1,t);x()}},g=e=>{try{if(l.current&&l.current.contains(document.activeElement))return document.queryCommandState(e);return!1}catch{return!1}};return(0,s.jsxs)("div",{className:i().dynamic([["9e5cf363cf309452",[a,a,a]]])+" border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800",children:[(0,s.jsxs)("div",{className:i().dynamic([["9e5cf363cf309452",[a,a,a]]])+" border-b border-gray-300 dark:border-gray-600 p-2 flex items-center gap-1 bg-gray-50 dark:bg-gray-700 rounded-t-md",children:[(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("bold"),className:`h-8 w-8 p-0 ${g("bold")?"bg-gray-200 dark:bg-gray-600":""}`,children:(0,s.jsx)(f,{className:"h-4 w-4"})}),(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("italic"),className:`h-8 w-8 p-0 ${g("italic")?"bg-gray-200 dark:bg-gray-600":""}`,children:(0,s.jsx)(v,{className:"h-4 w-4"})}),(0,s.jsx)("div",{className:i().dynamic([["9e5cf363cf309452",[a,a,a]]])+" w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("insertUnorderedList"),className:`h-8 w-8 p-0 ${g("insertUnorderedList")?"bg-gray-200 dark:bg-gray-600":""}`,children:(0,s.jsx)(b,{className:"h-4 w-4"})}),(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("insertOrderedList"),className:`h-8 w-8 p-0 ${g("insertOrderedList")?"bg-gray-200 dark:bg-gray-600":""}`,children:(0,s.jsx)(j,{className:"h-4 w-4"})}),(0,s.jsx)("div",{className:i().dynamic([["9e5cf363cf309452",[a,a,a]]])+" w-px h-6 bg-gray-300 dark:border-gray-600 mx-1"}),(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("undo"),className:"h-8 w-8 p-0",children:(0,s.jsx)(k,{className:"h-4 w-4"})}),(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("redo"),className:"h-8 w-8 p-0",children:(0,s.jsx)(N,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:i().dynamic([["9e5cf363cf309452",[a,a,a]]])+" relative",children:[(0,s.jsx)("div",{ref:l,contentEditable:!0,onInput:x,onKeyDown:e=>{if("Enter"===e.key){let t=window.getSelection();if(t&&t.rangeCount>0){let r=t.getRangeAt(0),s=r.startContainer.parentElement?.closest("li");s&&(e.preventDefault(),s.textContent?.trim()===""?document.execCommand("outdent"):(document.execCommand("insertHTML",!1,"<br>"),document.execCommand("insertHTML",!1,"</li><li>")),x())}}},onFocus:()=>d(!0),onBlur:()=>d(!1),style:{wordBreak:"break-word",overflowWrap:"break-word",direction:"ltr",textAlign:"left",unicodeBidi:"embed"},suppressContentEditableWarning:!0,"data-editor-id":a,className:i().dynamic([["9e5cf363cf309452",[a,a,a]]])+" min-h-[100px] p-3 text-gray-900 dark:text-gray-100 focus:outline-none"}),!o&&(!c||""===c.trim()||"<br>"===c)&&(0,s.jsx)("div",{className:i().dynamic([["9e5cf363cf309452",[a,a,a]]])+" absolute top-3 left-3 text-gray-400 dark:text-gray-500 pointer-events-none",children:r})]}),(0,s.jsx)(i(),{id:"9e5cf363cf309452",dynamic:[a,a,a],children:`div[data-editor-id="${a}"].__jsx-style-dynamic-selector ul.__jsx-style-dynamic-selector{list-style-type:disc!important;margin-left:20px!important;padding-left:0px!important}div[data-editor-id="${a}"].__jsx-style-dynamic-selector ol.__jsx-style-dynamic-selector{list-style-type:decimal!important;margin-left:20px!important;padding-left:0px!important}div[data-editor-id="${a}"].__jsx-style-dynamic-selector li.__jsx-style-dynamic-selector{margin-bottom:4px!important;display:list-item!important}`})]})};function T(){let{assessmentData:e,loading:t,error:r}=(0,l.U)(),[a,y]=(0,n.useState)(!1),[f,v]=(0,n.useState)(null),[b,j]=(0,n.useState)([]),[k,N]=(0,n.useState)(0),[T,A]=(0,n.useState)(!0),[P,z]=(0,n.useState)(null),[L,M]=(0,n.useState)({goal:"",tasks:"",importantTasks:"",difficultyLevel:"",timeToComplete:"",progress:"Not Started"}),D=async e=>{if(confirm("Are you sure you want to delete this development item?"))try{A(!0),z(null);let t=await fetch(`/api/pdp?id=${e}`,{method:"DELETE"});if(!t.ok)throw Error(`Error deleting PDP item: ${t.status}`);j(t=>t.filter(t=>t.id!==e))}catch(e){console.error("Error deleting PDP item:",e),z("Failed to delete development item. Please try again.")}finally{A(!1)}},I=(e,t)=>{M(r=>({...r,[e]:t}))},R=e=>{console.log("Opening edit modal for item:",e),N(Date.now());let t={goal:e.goal||"",tasks:e.tasks||"",importantTasks:e.importantTasks||"",difficultyLevel:e.difficultyLevel,timeToComplete:e.timeToComplete,progress:e.progress};M(t),v(e),console.log("Setting form data for edit:",t),setTimeout(()=>{y(!0)},50)},E=async e=>{e.preventDefault(),A(!0),z(null);try{if(f){let e={goal:L.goal,tasks:L.tasks,important_tasks:L.importantTasks,priority_level:L.difficultyLevel,time_to_complete:L.timeToComplete,progress:L.progress},t=await fetch(`/api/pdp?id=${f.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:f.id,...e})});if(!t.ok)throw Error(`Error updating PDP item: ${t.status}`);let r=await t.json(),s={id:r.id,goal:r.goal,tasks:r.tasks,importantTasks:r.important_tasks||"",difficultyLevel:r.priority_level,timeToComplete:r.time_to_complete,progress:r.progress,dateCreated:new Date(r.date_created).toISOString().split("T")[0]};console.log("Development Item Updated:",s),j(e=>e.map(e=>e.id===f.id?s:e))}else{let e={goal:L.goal,tasks:L.tasks,important_tasks:L.importantTasks,priority_level:L.difficultyLevel,time_to_complete:L.timeToComplete,progress:L.progress},t=await fetch("/api/pdp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error(`Error creating PDP item: ${t.status}`);let r=await t.json(),s={id:r.id,goal:r.goal,tasks:r.tasks,importantTasks:r.important_tasks||"",difficultyLevel:r.priority_level,timeToComplete:r.time_to_complete,progress:r.progress,dateCreated:new Date(r.date_created).toISOString().split("T")[0]};console.log("New Development Item Added:",s),j(e=>[...e,s])}M({goal:"",tasks:"",importantTasks:"",difficultyLevel:"",timeToComplete:"",progress:"Not Started"}),v(null),y(!1),N(e=>e+1)}catch(e){console.error("Error saving PDP item:",e),z("Failed to save development item. Please try again.")}finally{A(!1)}},F=e=>{switch(e){case"High Priority":return"text-red-600 dark:text-red-400";case"Medium Priority":return"text-yellow-600 dark:text-yellow-400";case"Low Priority":return"text-green-600 dark:text-green-400";default:return"text-gray-600 dark:text-gray-400"}},O=e=>{switch(e){case"Complete":return"text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20";case"In Progress":return"text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20";default:return"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800"}};return(0,s.jsx)("div",{className:"w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-[1100px] mx-auto px-5 pt-8 relative",children:[(0,s.jsx)("div",{className:"text-center mb-10 mt-5",children:(0,s.jsx)("h1",{className:"text-[3rem] font-light text-[#3793F7] dark:text-blue-400 mb-8 md:text-4xl lg:text-[3rem]",children:"4.4 Personal Development Plan"})}),(0,s.jsx)(d.A,{color:"blue",children:(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold",children:"1."}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"Write 3 unique goals that are important for you to achieve in the next 3-6months, using the SWOD Development Areas."})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold",children:"2."}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"Think \xe2€“ development of skills/tools/methods; acing a particular subject/test/paper; Internship / job placement success."})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold",children:"3."}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"Avoid BIG Goals \xe2€“ such as buying a dream home; becoming the CEO of a company \xe2€“ those are years away if you are being practical. Right now identify goals that will significantly boost your confidence and purpose."})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold",children:"4."}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"Once you prepare your SMART Goals, revisit them on a daily basis and track your progress. Do not leave it as an academic or theoretical exercise."})]})]})})}),(0,s.jsx)(h.nD,{type:"single",collapsible:!0,className:"mb-8",children:(0,s.jsxs)(h.As,{value:"reference-template",children:[(0,s.jsx)(h.$m,{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:"Reference Template"}),(0,s.jsx)(h.ub,{children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-sm",children:[(0,s.jsx)("thead",{className:"bg-blue-600 text-white",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-3 py-2 text-left font-medium",children:"GOALS"}),(0,s.jsx)("th",{className:"px-3 py-2 text-left font-medium",children:"IMPORTANT TASKS"}),(0,s.jsx)("th",{className:"px-3 py-2 text-left font-medium",children:"DIFFICULTY TO SUCCESS"}),(0,s.jsx)("th",{className:"px-3 py-2 text-left font-medium",children:"TIME TO COMPLETE"}),(0,s.jsx)("th",{className:"px-3 py-2 text-left font-medium",children:"PROGRESS"})]})}),(0,s.jsxs)("tbody",{className:"bg-white dark:bg-gray-800",children:[(0,s.jsxs)("tr",{className:"border-b border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:"Must be Specific & achievable"}),(0,s.jsxs)("ul",{className:"space-y-1 text-xs text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("li",{children:"\xe2€\xa2 High on Time and importance"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Must be specific"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Effort 20% gets Effort 80% tasks"}),(0,s.jsx)("li",{children:"\xe2€\xa2 High Priority"})]})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:"High on Time and importance"}),(0,s.jsxs)("ul",{className:"space-y-1 text-xs text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("li",{children:"\xe2€\xa2 High on Time and importance"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Must be specific"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Effort 80% tasks"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Low Priority"})]})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-red-600",children:"Write for EACH GOAL Completion criteria"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Integrated conceptual learning"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Feedback"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Actual score on a test or paper"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Quality of completeness of tasks in all aspects"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Within budget"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Others"})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:"Write for EACH TASK"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"How much time\xe2€”specific time - hours/day/wk, deadlines, as applicable."}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Budget slack time"})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:"Write for EACH progress"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"% Complete"})]})})]}),(0,s.jsxs)("tr",{className:"border-b border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-blue-600",children:'Example - "I will improve my math skills by 30% by the end of the semester"'}),(0,s.jsxs)("ul",{className:"space-y-1 text-xs text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("li",{children:"\xe2€\xa2 Practice 1 test each week"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Study math for 1 hour daily"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Ask questions to teacher, tutor or classmates when confused"})]})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-blue-600",children:"Identifying learning videos"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Make a 3 month study calendar"})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"80% score in mid-term test"})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"31/08/2024"})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsx)("p",{className:"text-blue-600 text-xs",children:"In Progress"})})]})]})]})})})})]})}),(0,s.jsx)("div",{className:"flex justify-end mb-6",children:(0,s.jsxs)(g.cj,{open:a,onOpenChange:y,children:[(0,s.jsx)(g.CG,{asChild:!0,children:(0,s.jsxs)(m.$,{className:"bg-[#3793F7] hover:bg-blue-600 text-white",onClick:()=>{v(null),M({goal:"",tasks:"",importantTasks:"",difficultyLevel:"",timeToComplete:"",progress:"Not Started"}),N(e=>e+1),y(!0)},children:[(0,s.jsx)(w,{className:"w-4 h-4 mr-2"}),"Add Development Item"]})}),(0,s.jsxs)(g.h,{className:"overflow-y-auto p-8 bg-white dark:bg-gray-800",style:{width:"900px !important",maxWidth:"90vw !important",minWidth:"900px"},children:[(0,s.jsx)(i(),{id:"b5f2d5d429a9abbd",children:"[data-radix-dialog-content]{width:900px!important;max-width:90vw!important;min-width:900px!important}.fixed.inset-y-0.right-0.z-50{width:900px!important;max-width:90vw!important}@media(max-width:1024px){[data-radix-dialog-content]{width:90vw!important;min-width:auto!important}.fixed.inset-y-0.right-0.z-50{width:90vw!important}}"}),(0,s.jsxs)(g.Fm,{className:"mb-8",children:[(0,s.jsx)(g.qp,{className:"text-xl",children:f?"Edit Personal Development Item":"Add Personal Development Item"}),(0,s.jsx)(g.Qs,{className:"text-base mt-2",children:f?"Update your goal with specific tasks and timeline.":"Create a new goal with specific tasks and timeline for your personal development plan."})]}),(0,s.jsxs)("form",{onSubmit:E,className:"jsx-b5f2d5d429a9abbd space-y-8",children:[(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"goal",className:"mb-2 block text-base font-medium",children:"Goal"}),(0,s.jsx)("div",{className:"jsx-b5f2d5d429a9abbd mb-4",children:(0,s.jsx)(S,{id:"goal-editor",content:L.goal,onChange:e=>I("goal",e),placeholder:"Describe your specific, measurable goal..."},`goal-${k}`)})]}),(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"tasks",className:"mb-2 block text-base font-medium",children:"Tasks"}),(0,s.jsx)("div",{className:"jsx-b5f2d5d429a9abbd mb-4",children:(0,s.jsx)(S,{id:"tasks-editor",content:L.tasks,onChange:e=>I("tasks",e),placeholder:"List the specific tasks you will complete..."},`tasks-${k}`)})]}),(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"importantTasks",className:"mb-2 block text-base font-medium",children:"Most Important Tasks"}),(0,s.jsx)("div",{className:"jsx-b5f2d5d429a9abbd mb-4",children:(0,s.jsx)(S,{id:"important-tasks-editor",content:L.importantTasks,onChange:e=>I("importantTasks",e),placeholder:"Identify the most critical tasks for success..."},`important-tasks-${k}`)})]}),(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"difficultyLevel",className:"mb-2 block text-base font-medium",children:"Priority Level"}),(0,s.jsxs)(x.l6,{onValueChange:e=>I("difficultyLevel",e),value:L.difficultyLevel,required:!0,children:[(0,s.jsx)(x.bq,{className:"h-11",children:(0,s.jsx)(x.yv,{placeholder:"Select priority level"})}),(0,s.jsxs)(x.gC,{className:"bg-white dark:bg-gray-800",children:[(0,s.jsx)(x.eb,{value:"High Priority",children:"High Priority"}),(0,s.jsx)(x.eb,{value:"Medium Priority",children:"Medium Priority"}),(0,s.jsx)(x.eb,{value:"Low Priority",children:"Low Priority"})]})]})]}),(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"timeToComplete",className:"mb-2 block text-base font-medium",children:"Target Completion Date"}),(0,s.jsx)(p.p,{id:"timeToComplete",type:"date",value:L.timeToComplete,onChange:e=>I("timeToComplete",e.target.value),required:!0,className:"h-11"})]})]}),(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"progress",className:"mb-2 block text-base font-medium",children:"Progress Status"}),(0,s.jsxs)(x.l6,{onValueChange:e=>I("progress",e),value:L.progress,children:[(0,s.jsx)(x.bq,{className:"h-11",children:(0,s.jsx)(x.yv,{})}),(0,s.jsxs)(x.gC,{className:"bg-white dark:bg-gray-800",children:[(0,s.jsx)(x.eb,{value:"Not Started",children:"Not Started"}),(0,s.jsx)(x.eb,{value:"In Progress",children:"In Progress"}),(0,s.jsx)(x.eb,{value:"Complete",children:"Complete"})]})]})]}),(0,s.jsxs)(g.XW,{className:"flex gap-4 pt-8 mt-8 border-t",children:[(0,s.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>y(!1),className:"px-6 py-2",children:"Cancel"}),(0,s.jsx)(m.$,{type:"submit",className:"bg-[#3793F7] hover:bg-blue-600 px-6 py-2",children:f?"Update Item":"Add Item"})]})]})]})]})}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-8",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:["My Personal Development Goals (",b.length,")"]})}),T?(0,s.jsx)("div",{className:"flex justify-center items-center py-10",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):b.length>0?(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Goal"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Tasks"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Important Tasks"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Priority"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Target Date"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Progress"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:b.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,s.jsx)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-gray-100",children:(0,s.jsx)("div",{className:"max-w-xs",children:(0,s.jsx)("div",{className:"font-medium text-sm prose prose-sm dark:prose-invert max-w-none [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4",dangerouslySetInnerHTML:{__html:e.goal}})})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm text-gray-600 dark:text-gray-300",children:(0,s.jsx)("div",{className:"max-w-xs",children:(0,s.jsx)("div",{className:"text-sm prose prose-sm dark:prose-invert max-w-none line-clamp-3 [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4",dangerouslySetInnerHTML:{__html:e.tasks}})})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm text-gray-600 dark:text-gray-300",children:(0,s.jsx)("div",{className:"max-w-xs",children:(0,s.jsx)("div",{className:"text-sm prose prose-sm dark:prose-invert max-w-none line-clamp-3 [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4",dangerouslySetInnerHTML:{__html:e.importantTasks}})})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,s.jsx)("span",{className:`font-medium ${F(e.difficultyLevel)}`,children:e.difficultyLevel})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm text-gray-600 dark:text-gray-300",children:new Date(e.timeToComplete).toLocaleDateString()}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${O(e.progress)}`,children:e.progress})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>R(e),className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",title:"Edit",children:(0,s.jsx)(_,{className:"w-4 h-4"})}),(0,s.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>D(e.id),className:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",title:"Delete",children:(0,s.jsx)(C,{className:"w-4 h-4"})})]})})]},e.id))})]})}):(0,s.jsx)("div",{className:"px-6 py-8 text-center",children:(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:'No personal development goals added yet. Click "Add Development Item" to create your first goal.'})})]}),(0,s.jsx)("p",{className:"my-8 leading-relaxed max-w-[980px] mx-auto text-gray-900 dark:text-gray-200",children:"Your personal development plan is a living document that should be reviewed and updated regularly. Track your progress, celebrate achievements, and adjust goals as needed to ensure continuous growth and development in both personal and professional areas."}),(0,s.jsx)(o.A,{text:"CONTINUE",href:"/4_5_conclusion"}),(0,s.jsx)(c.A,{})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},59872:(e,t,r)=>{Promise.resolve().then(r.bind(r,42664))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,t,r)=>{"use strict";r.d(t,{bq:()=>m,eb:()=>u,gC:()=>p,l6:()=>d,yv:()=>c});var s=r(60687);r(43210);var a=r(46837),i=r(78272),n=r(13964),l=r(3589),o=r(96241);function d({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function m({className:e,size:t="default",children:r,...n}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[r,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...i}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...i,children:[(0,s.jsx)(x,{}),(0,s.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(h,{})]})})}function u({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}function h({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},68988:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(96241);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},73024:(e,t,r)=>{Promise.resolve().then(r.bind(r,81503))},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,t,r)=>{"use strict";r(56397);var s=r(43210),a=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),i="undefined"!=typeof process&&process.env&&!0,n=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,a=t.optimizeForSpeed,l=void 0===a?i:a;o(n(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",o("boolean"==typeof l,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=l,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){o("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),o(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;o(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){return o(n(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},r.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},r.deleteRule=function(e){this._serverSheet.deleteRule(e)},r.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},r.cssRules=function(){return this._serverSheet.cssRules},r.makeStyleTag=function(e,t,r){t&&o(n(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var a=document.head||document.getElementsByTagName("head")[0];return r?a.insertBefore(s,r):a.appendChild(s),s},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,t),e}();function o(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},c={};function m(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return c[s]||(c[s]="jsx-"+d(e+"-"+r)),c[s]}function p(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return c[r]||(c[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[r]}var u=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,a=t.optimizeForSpeed,i=void 0!==a&&a;this._sheet=s||new l({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),s&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var r=this.getIdAndRules(e),s=r.styleId,a=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var i=a.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=i,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return a.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var a=m(s,r);return{styleId:a,rules:Array.isArray(t)?t.map(function(e){return p(a,e)}):[p(a,t)]}}return{styleId:m(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),x=s.createContext(null);x.displayName="StyleSheetContext";a.default.useInsertionEffect||a.default.useLayoutEffect;var h=void 0;function g(e){var t=h||s.useContext(x);return t&&t.add(e),null}g.dynamic=function(e){return e.map(function(e){return m(e[0],e[1])}).join(" ")},t.style=g},76180:(e,t,r)=>{"use strict";e.exports=r(75913).style},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81503:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\TM\\\\ai_report_01062025\\\\frontend_v2\\\\app\\\\(with-sidebar)\\\\4_4_personal_development_plan\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\4_4_personal_development_plan\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},87217:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["(with-sidebar)",{children:["4_4_personal_development_plan",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81503)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\4_4_personal_development_plan\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,35363)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,87707)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\(with-sidebar)\\4_4_personal_development_plan\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(with-sidebar)/4_4_personal_development_plan/page",pathname:"/4_4_personal_development_plan",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,854,256,658,611,705,793,74],()=>r(87217));module.exports=s})();