(()=>{var e={};e.id=633,e.ids=[633],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13581:(e,r)=>{"use strict";r.A=function(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95971:(e,r,t)=>{"use strict";t.d(r,{N:()=>s});let s={debug:!0,providers:[(0,t(13581).A)({name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("\uD83D\uDD11 Starting authentication process..."),console.log("\uD83D\uDCE7 Email being used:",e?.email),!e?.email||!e?.password)throw console.error("❌ Missing credentials"),Error("Missing credentials");try{let r,t="http://localhost:8000/auth/login";console.log("\uD83C\uDF10 Attempting login at:",t);let s=new URLSearchParams({username:e.email,password:e.password});console.log("\uD83D\uDCE6 Request payload:",{url:t,method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Accept:"application/json"},body:s.toString()});let o=await fetch(t,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Accept:"application/json"},body:s});console.log("\uD83D\uDCE5 Login response status:",o.status);let a=await o.text();if(console.log("\uD83D\uDCC4 Raw response:",a),!o.ok)return console.error("❌ Authentication failed:",a),null;try{r=JSON.parse(a),console.log("\uD83D\uDD13 Auth response parsed:",r)}catch(e){throw console.error("❌ Failed to parse auth response:",e),Error("Invalid response format from server")}if(!r.access_token)throw console.error("❌ No access token in response"),Error("No access token received");let n="candidate";try{n=JSON.parse(Buffer.from(r.access_token.split(".")[1],"base64").toString()).role||"candidate"}catch(e){console.warn("Could not decode JWT for role, defaulting to candidate",e)}return{id:e.email,email:e.email,name:e.email.split("@")[0],access_token:r.access_token,role:n}}catch(e){return console.error("❌ Authorization error:",e),null}}})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.access_token=r.access_token,e.id=r.id,e.email=r.email,e.name=r.name,e.role=r.role),e),session:async({session:e,token:r})=>(r&&e.user&&(e.user.id=r.id,e.user.access_token=r.access_token,e.user.email=r.email,e.user.name=r.name,e.user.role=r.role),e)},pages:{signIn:"/login"},session:{strategy:"jwt",maxAge:86400},secret:process.env.NEXTAUTH_SECRET}},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)},97032:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>l});var o=t(96559),a=t(48088),n=t(37719),i=t(19854),c=t.n(i),u=t(95971);let l=c()(u.N),p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\api\\auth\\[...nextauth]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:h}=p;function x(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,854],()=>t(97032));module.exports=s})();