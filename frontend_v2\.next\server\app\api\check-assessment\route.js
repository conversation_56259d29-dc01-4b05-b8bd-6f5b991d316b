(()=>{var e={};e.id=761,e.ids=[761],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13581:(e,r)=>{"use strict";r.A=function(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65693:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{GET:()=>u});var o=s(96559),a=s(48088),n=s(37719),i=s(32190),c=s(19854),l=s(95971);async function u(e){try{let r=new URL(e.url).searchParams.get("email");if(!r)return i.NextResponse.json({error:"Email parameter is required"},{status:400});let s=await (0,c.getServerSession)(l.N),t=s?.user?.access_token;if(!t)return i.NextResponse.json({error:"Unauthorized: No access token"},{status:401});try{let e=await fetch("http://localhost:8000/api/v1/assessment?force_new=false",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({email:r})});if(e.ok)return i.NextResponse.json({exists:!0,email:r});{if(404===e.status)return i.NextResponse.json({exists:!1});let r=await e.text();return console.error("Backend API error:",r),i.NextResponse.json({exists:!1})}}catch(e){return console.error("API request error:",e),i.NextResponse.json({exists:!1})}}catch(e){return console.error("General error:",e),i.NextResponse.json({exists:!1})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/check-assessment/route",pathname:"/api/check-assessment",filename:"route",bundlePath:"app/api/check-assessment/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\api\\check-assessment\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:h}=p;function x(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95971:(e,r,s)=>{"use strict";s.d(r,{N:()=>t});let t={debug:!0,providers:[(0,s(13581).A)({name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("\uD83D\uDD11 Starting authentication process..."),console.log("\uD83D\uDCE7 Email being used:",e?.email),!e?.email||!e?.password)throw console.error("❌ Missing credentials"),Error("Missing credentials");try{let r,s="http://localhost:8000/auth/login";console.log("\uD83C\uDF10 Attempting login at:",s);let t=new URLSearchParams({username:e.email,password:e.password});console.log("\uD83D\uDCE6 Request payload:",{url:s,method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Accept:"application/json"},body:t.toString()});let o=await fetch(s,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Accept:"application/json"},body:t});console.log("\uD83D\uDCE5 Login response status:",o.status);let a=await o.text();if(console.log("\uD83D\uDCC4 Raw response:",a),!o.ok)return console.error("❌ Authentication failed:",a),null;try{r=JSON.parse(a),console.log("\uD83D\uDD13 Auth response parsed:",r)}catch(e){throw console.error("❌ Failed to parse auth response:",e),Error("Invalid response format from server")}if(!r.access_token)throw console.error("❌ No access token in response"),Error("No access token received");let n="candidate";try{n=JSON.parse(Buffer.from(r.access_token.split(".")[1],"base64").toString()).role||"candidate"}catch(e){console.warn("Could not decode JWT for role, defaulting to candidate",e)}return{id:e.email,email:e.email,name:e.email.split("@")[0],access_token:r.access_token,role:n}}catch(e){return console.error("❌ Authorization error:",e),null}}})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.access_token=r.access_token,e.id=r.id,e.email=r.email,e.name=r.name,e.role=r.role),e),session:async({session:e,token:r})=>(r&&e.user&&(e.user.id=r.id,e.user.access_token=r.access_token,e.user.email=r.email,e.user.name=r.name,e.user.role=r.role),e)},pages:{signIn:"/login"},session:{strategy:"jwt",maxAge:86400},secret:process.env.NEXTAUTH_SECRET}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,854,580],()=>s(65693));module.exports=t})();