(()=>{var e={};e.id=474,e.ids=[474],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14423:(e,t,r)=>{"use strict";r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{POST:()=>i});var n=r(32190),s=r(83636),a=e([s]);async function i(e){console.log("\uD83D\uDE80 PDF generation API called");try{let{url:t,filename:r="resume.pdf",cookies:o=[]}=await e.json();if(!t)return console.error("❌ No URL provided for PDF generation"),n.NextResponse.json({error:"No URL provided for PDF generation"},{status:400});let a=e.cookies.getAll();console.log("\uD83C\uDF6A Received cookies:",a.map(e=>e.name));let i=o.length>0?o:a.map(e=>({name:e.name,value:e.value,domain:new URL(t).hostname,path:"/"}));console.log("\uD83D\uDCC4 Generating PDF for URL:",t);let l=await s.default.launch({headless:!0,args:["--no-sandbox","--disable-setuid-sandbox","--disable-web-security","--disable-features=IsolateOrigins,site-per-process","--ignore-certificate-errors"]}),u=await l.newPage();u.on("console",e=>console.log(`🖥️ Page console ${e.type()}: ${e.text()}`)),u.on("pageerror",e=>console.error(`❌ Page error: ${e.message}`)),u.on("requestfailed",e=>console.error(`❌ Request failed: ${e.url()}`)),await u.setViewport({width:1200,height:800}),i.length>0&&(console.log("\uD83C\uDF6A Setting authentication cookies..."),await u.setCookie(...i)),console.log("\uD83D\uDD0E Navigating to URL:",t),await u.goto(t,{waitUntil:"networkidle0",timeout:3e4});let p=await u.title(),d=u.url();if(console.log("\uD83D\uDCDD Page title:",p),console.log("\uD83D\uDD17 Current URL:",d),d.includes("login")||p.toLowerCase().includes("login"))return console.error("❌ Redirected to login page - authentication failed"),n.NextResponse.json({error:"Authentication failed - redirected to login page"},{status:401});if(await new Promise(e=>setTimeout(e,2e3)),!await u.evaluate(()=>!!document.getElementById("resume-preview")))return console.error("❌ Resume preview element not found"),n.NextResponse.json({error:"Resume preview element not found on the page"},{status:404});console.log("✅ Resume preview element found, capturing it..."),await u.evaluate(()=>{let e=document.getElementById("resume-preview");e&&(e.style.position,e.style.top,e.style.left,e.style.margin,e.style.padding,e.style.background,document.body.innerHTML="",document.body.style.margin="0",document.body.style.padding="0",document.body.style.background="#ffffff",document.body.appendChild(e),e.style.position="absolute",e.style.top="0",e.style.left="0",e.style.margin="0",e.style.padding="20px",e.style.background="#ffffff")}),await new Promise(e=>setTimeout(e,1e3));let c=await u.pdf({format:"A4",printBackground:!0,margin:{top:"20px",right:"20px",bottom:"20px",left:"20px"},preferCSSPageSize:!0});return await l.close(),console.log("✅ PDF generated successfully"),new n.NextResponse(c,{status:200,headers:{"Content-Type":"application/pdf","Content-Disposition":`attachment; filename="${r}"`}})}catch(r){console.error("❌ PDF generation error:",r);let e="Unknown error",t="";return r instanceof Error&&(e=r.message,t=r.stack||"",console.error("Error stack:",r.stack)),n.NextResponse.json({error:"Failed to generate PDF",message:e,details:t},{status:500})}}s=(a.then?(await a)():a)[0],o()}catch(e){o(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48407:(e,t,r)=>{"use strict";r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var n=r(96559),s=r(48088),a=r(37719),i=r(14423),l=e([i]);i=(l.then?(await l)():l)[0];let p=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/generate-pdf/route",pathname:"/api/generate-pdf",filename:"route",bundlePath:"app/api/generate-pdf/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\api\\generate-pdf\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:g}=p;function u(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}o()}catch(e){o(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},83636:e=>{"use strict";e.exports=import("puppeteer")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[243,580],()=>r(48407));module.exports=o})();