(()=>{var e={};e.id=714,e.ids=[714],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9174:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>k,routeModule:()=>w,serverHooks:()=>y,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{DELETE:()=>h,GET:()=>d,POST:()=>g,PUT:()=>m});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),l=t(19854),c=t(95971);let u=process.env.BACKEND_API_URL||"http://localhost:8000";async function p(e,r,t,s){try{console.log("Session:",JSON.stringify(s,null,2));let o=s?.user?.access_token;if(console.log("Token:",o?"Token exists":"No token"),!o){console.error("No access token found in session");let e=i.NextResponse.json({error:"Unauthorized: No access token",redirectToLogin:!0},{status:401});return e.headers.set("X-Auth-Required","true"),e}let n=null;"GET"!==t&&"DELETE"!==t&&(n=await e.json());let a={"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${o}`},l=`${u}/pdp/${r}`;console.log(`Making ${t} request to: ${l}`),console.log("Headers:",JSON.stringify(a,null,2)),n&&console.log("Request body:",JSON.stringify(n,null,2));let c=await fetch(l,{method:t,headers:a,body:n?JSON.stringify(n):void 0});if(console.log(`Response status: ${c.status}`),!c.ok){console.error(`Error response: ${c.status}`);try{let e=await c.json();return console.error("Error data:",JSON.stringify(e,null,2)),i.NextResponse.json(e,{status:c.status})}catch(r){console.error("Failed to parse error response:",r);let e=await c.text().catch(()=>"Could not read response text");return console.error("Error text:",e),i.NextResponse.json({error:"Unknown error",details:e},{status:c.status})}}if("DELETE"===t&&204===c.status)return new i.NextResponse(null,{status:204});let p=await c.json();return i.NextResponse.json(p)}catch(e){return console.error("Error forwarding request to backend:",e),i.NextResponse.json({error:"Failed to process request"},{status:500})}}async function d(e){let r=await (0,l.getServerSession)(c.N),t=new URL(e.url),s=t.searchParams.get("id");return p(e,s?`items/${s}`:"items"+t.search,"GET",r)}async function g(e){return p(e,"items","POST",await (0,l.getServerSession)(c.N))}async function m(e){let r=await (0,l.getServerSession)(c.N),{id:t,...s}=await e.json();return t?p(new i.NextRequest(e.url,{method:e.method,headers:e.headers,body:JSON.stringify(s)}),`items/${t}`,"PUT",r):i.NextResponse.json({error:"Item ID is required"},{status:400})}async function h(e){let r=await (0,l.getServerSession)(c.N),t=new URL(e.url).searchParams.get("id");return t?p(e,`items/${t}`,"DELETE",r):i.NextResponse.json({error:"Item ID is required"},{status:400})}let w=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/pdp/route",pathname:"/api/pdp",filename:"route",bundlePath:"app/api/pdp/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\api\\pdp\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:y}=w;function k(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13581:(e,r)=>{"use strict";r.A=function(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95971:(e,r,t)=>{"use strict";t.d(r,{N:()=>s});let s={debug:!0,providers:[(0,t(13581).A)({name:"Credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e,r){if(console.log("\uD83D\uDD11 Starting authentication process..."),console.log("\uD83D\uDCE7 Email being used:",e?.email),!e?.email||!e?.password)throw console.error("❌ Missing credentials"),Error("Missing credentials");try{let r,t="http://localhost:8000/auth/login";console.log("\uD83C\uDF10 Attempting login at:",t);let s=new URLSearchParams({username:e.email,password:e.password});console.log("\uD83D\uDCE6 Request payload:",{url:t,method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Accept:"application/json"},body:s.toString()});let o=await fetch(t,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",Accept:"application/json"},body:s});console.log("\uD83D\uDCE5 Login response status:",o.status);let n=await o.text();if(console.log("\uD83D\uDCC4 Raw response:",n),!o.ok)return console.error("❌ Authentication failed:",n),null;try{r=JSON.parse(n),console.log("\uD83D\uDD13 Auth response parsed:",r)}catch(e){throw console.error("❌ Failed to parse auth response:",e),Error("Invalid response format from server")}if(!r.access_token)throw console.error("❌ No access token in response"),Error("No access token received");let a="candidate";try{a=JSON.parse(Buffer.from(r.access_token.split(".")[1],"base64").toString()).role||"candidate"}catch(e){console.warn("Could not decode JWT for role, defaulting to candidate",e)}return{id:e.email,email:e.email,name:e.email.split("@")[0],access_token:r.access_token,role:a}}catch(e){return console.error("❌ Authorization error:",e),null}}})],callbacks:{jwt:async({token:e,user:r})=>(r&&(e.access_token=r.access_token,e.id=r.id,e.email=r.email,e.name=r.name,e.role=r.role),e),session:async({session:e,token:r})=>(r&&e.user&&(e.user.id=r.id,e.user.access_token=r.access_token,e.user.email=r.email,e.user.name=r.name,e.user.role=r.role),e)},pages:{signIn:"/login"},session:{strategy:"jwt",maxAge:86400},secret:process.env.NEXTAUTH_SECRET}},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,854,580],()=>t(9174));module.exports=s})();