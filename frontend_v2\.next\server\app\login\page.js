(()=>{var e={};e.id=520,e.ids=[520],e.modules={3018:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>i,TN:()=>c,XL:()=>l});var s=r(60687);r(43210);var a=r(24224),n=r(96241);let o=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(o({variant:t}),e),...r})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39390:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(60687);r(43210);var a=r(78148),n=r(96241);function o({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},46387:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\TM\\\\ai_report_01062025\\\\frontend_v2\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\login\\page.tsx","default")},55192:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>d});var s=r(60687);r(43210);var a=r(96241);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64345:(e,t,r)=>{Promise.resolve().then(r.bind(r,78584))},68988:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(96241);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},74075:e=>{"use strict";e.exports=require("zlib")},77369:(e,t,r)=>{Promise.resolve().then(r.bind(r,46387))},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var s=r(43210),a=r(3416),n=r(60687),o=s.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},78584:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),a=r(43210),n=r(16189),o=r(82136),i=r(24934),l=r(68988),c=r(39390),d=r(55192),u=r(93613),p=r(62688);let m=(0,p.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),g=(0,p.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),x=(0,p.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var f=r(3018);function h(){console.log("\uD83C\uDFD7️ LoginPage component rendering/re-rendering");let e=(0,n.useRouter)(),t=(0,n.useSearchParams)().get("callbackUrl")||"/landingpage_v2",[r,p]=(0,a.useState)(""),[h,v]=(0,a.useState)(""),[b,y]=(0,a.useState)(null),[j,w]=(0,a.useState)(!1);console.log("\uD83D\uDD27 Component state:",{email:r,passwordLength:h.length,error:b,loading:j,callbackUrl:t});let N=async s=>{s.preventDefault(),console.log("\uD83D\uDE80 LOGIN FORM SUBMITTED"),console.log("\uD83D\uDCE7 Email:",r),console.log("\uD83D\uDD12 Password length:",h.length),console.log("\uD83D\uDD04 Callback URL:",t),y(null),w(!0),console.log("⏳ Loading state set to true");try{console.log("\uD83D\uDD11 Calling signIn with credentials..."),console.log("\uD83D\uDCE6 SignIn payload:",{provider:"credentials",email:r,passwordLength:h.length,redirect:!1});let s=await (0,o.signIn)("credentials",{email:r,password:h,redirect:!1});console.log("\uD83D\uDCE5 SignIn result received:",s),console.log("❓ Result error:",s?.error),console.log("✅ Result ok:",s?.ok),console.log("\uD83D\uDD17 Result url:",s?.url),console.log("\uD83D\uDCCA Result status:",s?.status),s?.error?(console.error("❌ Authentication failed with error:",s.error),y(s.error),w(!1),console.log("⏳ Loading state set to false (error case)")):s?.ok?(console.log("✅ Authentication successful!"),console.log("\uD83D\uDD04 Redirecting to:",t),e.push(t),e.refresh(),console.log("\uD83D\uDD04 Router refresh called")):(console.warn("⚠️ Unexpected result state:",s),y("Authentication failed. Please try again."),w(!1),console.log("⏳ Loading state set to false (unexpected case)"))}catch(e){console.error("\uD83D\uDCA5 CATCH BLOCK - Unexpected error during sign in:",e),console.error("\uD83D\uDCA5 Error type:",typeof e),console.error("\uD83D\uDCA5 Error message:",e instanceof Error?e.message:String(e)),console.error("\uD83D\uDCA5 Error stack:",e instanceof Error?e.stack:"No stack trace"),y("An unexpected error occurred."),w(!1),console.log("⏳ Loading state set to false (catch case)")}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-secondary/5 relative",children:(0,s.jsxs)("div",{className:"w-full max-w-md p-4 z-10",children:[(0,s.jsxs)("div",{className:"mb-8 text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Welcome back"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"Sign in to access your account"})]}),(0,s.jsxs)(d.Zp,{className:"shadow-lg border-muted/30",children:[(0,s.jsxs)(d.aR,{className:"space-y-1 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-xl font-semibold",children:"Sign In"}),(0,s.jsx)(d.BT,{children:"Enter your credentials to continue"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[b&&(0,s.jsxs)(f.Fc,{variant:"destructive",className:"animate-in slide-in-from-top-1 duration-300",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),(0,s.jsx)(f.TN,{children:b})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"email",className:"text-sm font-medium",children:"Email"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground",children:(0,s.jsx)(m,{className:"h-4 w-4"})}),(0,s.jsx)(l.p,{id:"email",type:"email",value:r,onChange:e=>{console.log("\uD83D\uDCE7 Email input changed:",e.target.value),p(e.target.value)},onFocus:()=>console.log("\uD83D\uDCE7 Email input focused"),onBlur:()=>console.log("\uD83D\uDCE7 Email input blurred"),placeholder:"<EMAIL>",required:!0,disabled:j,className:"pl-10 transition-all focus:ring-2 focus:ring-primary/20"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(c.J,{htmlFor:"password",className:"text-sm font-medium",children:"Password"}),(0,s.jsx)(i.$,{variant:"link",className:"px-0 h-auto text-xs",disabled:j,children:"Forgot password?"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground",children:(0,s.jsx)(g,{className:"h-4 w-4"})}),(0,s.jsx)(l.p,{id:"password",type:"password",value:h,onChange:e=>{console.log("\uD83D\uDD12 Password input changed, length:",e.target.value.length),v(e.target.value)},onFocus:()=>console.log("\uD83D\uDD12 Password input focused"),onBlur:()=>console.log("\uD83D\uDD12 Password input blurred"),placeholder:"••••••••",required:!0,disabled:j,className:"pl-10 transition-all focus:ring-2 focus:ring-primary/20"})]})]}),(0,s.jsx)(i.$,{type:"submit",className:"w-full transition-all hover:shadow-md group",disabled:j,onClick:()=>{console.log("\uD83D\uDDB1️ Sign In button clicked"),console.log("⏳ Current loading state:",j),console.log("\uD83D\uDCE7 Current email:",r),console.log("\uD83D\uDD12 Current password length:",h.length)},children:j?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Signing in..."]}):(0,s.jsxs)("div",{className:"flex items-center justify-center",children:["Sign In",(0,s.jsx)(x,{className:"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1"})]})})]})}),(0,s.jsx)(d.wL,{className:"flex justify-center border-t pt-4 text-xs",children:(0,s.jsxs)("p",{className:"text-muted-foreground",children:["Don't have an account? ",(0,s.jsx)("span",{className:"text-primary font-medium",children:"Contact your administrator"})]})})]}),(0,s.jsx)("div",{className:"mt-8 text-center text-xs text-muted-foreground",children:(0,s.jsx)("p",{children:"By signing in, you agree to our Terms of Service and Privacy Policy"})})]})})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87017:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,46387)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,87707)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,854,256,658,793],()=>r(87017));module.exports=s})();