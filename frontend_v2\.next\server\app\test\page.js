(()=>{var e={};e.id=11,e.ids=[11],e.modules={3018:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>o,XL:()=>l});var s=r(60687);r(43210);var a=r(24224),n=r(96241);let i=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...r}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:t}),e),...r})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19044:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\TM\\\\ai_report_01062025\\\\frontend_v2\\\\app\\\\test\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\test\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20877:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eu});var s=r(60687),a=r(24934),n=r(55192),i=r(68988),d=r(39390),l=r(63974),o=r(43210),c=r(70569),u=r(11273),x=r(72942),p=r(46059),h=r(3416),m=r(43),f=r(65551),v=r(96963),g="Tabs",[b,j]=(0,u.A)(g,[x.RG]),y=(0,x.RG)(),[w,N]=b(g),k=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:i,orientation:d="horizontal",dir:l,activationMode:o="automatic",...c}=e,u=(0,m.jH)(l),[x,p]=(0,f.i)({prop:a,onChange:n,defaultProp:i??"",caller:g});return(0,s.jsx)(w,{scope:r,baseId:(0,v.B)(),value:x,onValueChange:p,orientation:d,dir:u,activationMode:o,children:(0,s.jsx)(h.sG.div,{dir:u,"data-orientation":d,...c,ref:t})})});k.displayName=g;var _="TabsList",C=o.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,i=N(_,r),d=y(r);return(0,s.jsx)(x.bL,{asChild:!0,...d,orientation:i.orientation,dir:i.dir,loop:a,children:(0,s.jsx)(h.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});C.displayName=_;var z="TabsTrigger",A=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:n=!1,...i}=e,d=N(z,r),l=y(r),o=T(d.baseId,a),u=E(d.baseId,a),p=a===d.value;return(0,s.jsx)(x.q7,{asChild:!0,...l,focusable:!n,active:p,children:(0,s.jsx)(h.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":u,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:o,...i,ref:t,onMouseDown:(0,c.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,c.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,c.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||n||!e||d.onValueChange(a)})})})});A.displayName=z;var P="TabsContent",q=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:n,children:i,...d}=e,l=N(P,r),c=T(l.baseId,a),u=E(l.baseId,a),x=a===l.value,m=o.useRef(x);return o.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.jsx)(p.C,{present:n||x,children:({present:r})=>(0,s.jsx)(h.sG.div,{"data-state":x?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:u,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&i})})});function T(e,t){return`${e}-trigger-${t}`}function E(e,t){return`${e}-content-${t}`}q.displayName=P;var D=r(96241);function R({className:e,...t}){return(0,s.jsx)(k,{"data-slot":"tabs",className:(0,D.cn)("flex flex-col gap-2",e),...t})}function $({className:e,...t}){return(0,s.jsx)(C,{"data-slot":"tabs-list",className:(0,D.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function L({className:e,...t}){return(0,s.jsx)(A,{"data-slot":"tabs-trigger",className:(0,D.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function F({className:e,...t}){return(0,s.jsx)(q,{"data-slot":"tabs-content",className:(0,D.cn)("flex-1 outline-none",e),...t})}var I=r(98599),G=r(83721),B=r(18853),M="Checkbox",[U,Z]=(0,u.A)(M),[S,V]=U(M);function X(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:n,disabled:i,form:d,name:l,onCheckedChange:c,required:u,value:x="on",internal_do_not_use_render:p}=e,[h,m]=(0,f.i)({prop:r,defaultProp:n??!1,onChange:c,caller:M}),[v,g]=o.useState(null),[b,j]=o.useState(null),y=o.useRef(!1),w=!v||!!d||!!v.closest("form"),N={checked:h,disabled:i,setChecked:m,control:v,setControl:g,name:l,form:d,value:x,hasConsumerStoppedPropagationRef:y,required:u,defaultChecked:!ee(n)&&n,isFormControl:w,bubbleInput:b,setBubbleInput:j};return(0,s.jsx)(S,{scope:t,...N,children:"function"==typeof p?p(N):a})}var J="CheckboxTrigger",K=o.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...a},n)=>{let{control:i,value:d,disabled:l,checked:u,required:x,setControl:p,setChecked:m,hasConsumerStoppedPropagationRef:f,isFormControl:v,bubbleInput:g}=V(J,e),b=(0,I.s)(n,p),j=o.useRef(u);return o.useEffect(()=>{let e=i?.form;if(e){let t=()=>m(j.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,m]),(0,s.jsx)(h.sG.button,{type:"button",role:"checkbox","aria-checked":ee(u)?"mixed":u,"aria-required":x,"data-state":et(u),"data-disabled":l?"":void 0,disabled:l,value:d,...a,ref:b,onKeyDown:(0,c.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,c.m)(r,e=>{m(e=>!!ee(e)||!e),g&&v&&(f.current=e.isPropagationStopped(),f.current||e.stopPropagation())})})});K.displayName=J;var W=o.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:n,defaultChecked:i,required:d,disabled:l,value:o,onCheckedChange:c,form:u,...x}=e;return(0,s.jsx)(X,{__scopeCheckbox:r,checked:n,defaultChecked:i,disabled:l,required:d,onCheckedChange:c,name:a,form:u,value:o,internal_do_not_use_render:({isFormControl:e})=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(K,{...x,ref:t,__scopeCheckbox:r}),e&&(0,s.jsx)(Q,{__scopeCheckbox:r})]})})});W.displayName=M;var O="CheckboxIndicator",H=o.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:a,...n}=e,i=V(O,r);return(0,s.jsx)(p.C,{present:a||ee(i.checked)||!0===i.checked,children:(0,s.jsx)(h.sG.span,{"data-state":et(i.checked),"data-disabled":i.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});H.displayName=O;var Y="CheckboxBubbleInput",Q=o.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:a,hasConsumerStoppedPropagationRef:n,checked:i,defaultChecked:d,required:l,disabled:c,name:u,value:x,form:p,bubbleInput:m,setBubbleInput:f}=V(Y,e),v=(0,I.s)(r,f),g=(0,G.Z)(i),b=(0,B.X)(a);o.useEffect(()=>{if(!m)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!n.current;if(g!==i&&e){let r=new Event("click",{bubbles:t});m.indeterminate=ee(i),e.call(m,!ee(i)&&i),m.dispatchEvent(r)}},[m,g,i,n]);let j=o.useRef(!ee(i)&&i);return(0,s.jsx)(h.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??j.current,required:l,disabled:c,name:u,value:x,form:p,...t,tabIndex:-1,ref:v,style:{...t.style,...b,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function ee(e){return"indeterminate"===e}function et(e){return ee(e)?"indeterminate":e?"checked":"unchecked"}Q.displayName=Y;var er=r(13964);function es({className:e,...t}){return(0,s.jsx)(W,{"data-slot":"checkbox",className:(0,D.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(H,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(er.A,{className:"size-3.5"})})})}var ea=r(70373),en=r(8730);let ei=(0,r(24224).F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function ed({className:e,variant:t,asChild:r=!1,...a}){let n=r?en.DX:"span";return(0,s.jsx)(n,{"data-slot":"badge",className:(0,D.cn)(ei({variant:t}),e),...a})}var el=r(93613),eo=r(5336),ec=r(3018);function eu(){return(0,s.jsxs)("div",{className:"container mx-auto py-10",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"ShadCN UI Components Test"}),(0,s.jsxs)(R,{defaultValue:"cards",className:"w-full mb-10",children:[(0,s.jsxs)($,{className:"grid w-full grid-cols-4",children:[(0,s.jsx)(L,{value:"cards",children:"Cards"}),(0,s.jsx)(L,{value:"inputs",children:"Inputs"}),(0,s.jsx)(L,{value:"buttons",children:"Buttons"}),(0,s.jsx)(L,{value:"alerts",children:"Alerts"})]}),(0,s.jsxs)(F,{value:"cards",className:"space-y-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold my-4",children:"Card Examples"}),(0,s.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"Basic Card"}),(0,s.jsx)(n.BT,{children:"A simple card with title and description"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("p",{children:"This is a basic card component from shadcn/ui."})}),(0,s.jsx)(n.wL,{children:(0,s.jsx)(a.$,{children:"Action"})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"User Profile"}),(0,s.jsx)(n.BT,{children:"User information display"})]}),(0,s.jsxs)(n.Wu,{className:"flex items-center space-x-4",children:[(0,s.jsxs)(ea.eu,{children:[(0,s.jsx)(ea.BK,{src:"https://github.com/shadcn.png",alt:"User"}),(0,s.jsx)(ea.q5,{children:"CN"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"John Doe"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Product Designer"})]})]}),(0,s.jsxs)(n.wL,{className:"flex justify-between",children:[(0,s.jsx)(a.$,{variant:"outline",children:"Message"}),(0,s.jsx)(a.$,{children:"Follow"})]})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"Project Status"}),(0,s.jsx)(n.BT,{children:"Current project metrics"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Progress"}),(0,s.jsx)("span",{children:"75%"})]}),(0,s.jsx)("div",{className:"w-full bg-secondary h-2 rounded-full",children:(0,s.jsx)("div",{className:"bg-primary h-2 rounded-full w-3/4"})}),(0,s.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,s.jsx)(ed,{children:"In Progress"}),(0,s.jsx)(ed,{variant:"outline",children:"Frontend"})]})]})}),(0,s.jsx)(n.wL,{children:(0,s.jsx)(a.$,{variant:"outline",className:"w-full",children:"View Details"})})]})]})]}),(0,s.jsxs)(F,{value:"inputs",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold my-4",children:"Input Components"}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"Input Examples"}),(0,s.jsx)(n.BT,{children:"Various input components from shadcn/ui"})]}),(0,s.jsxs)(n.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"username",children:"Username"}),(0,s.jsx)(i.p,{id:"username",placeholder:"Enter your username"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(i.p,{id:"email",type:"email",placeholder:"Enter your email"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"role",children:"Role"}),(0,s.jsxs)(l.l6,{children:[(0,s.jsx)(l.bq,{id:"role",children:(0,s.jsx)(l.yv,{placeholder:"Select a role"})}),(0,s.jsxs)(l.gC,{children:[(0,s.jsx)(l.eb,{value:"admin",children:"Admin"}),(0,s.jsx)(l.eb,{value:"user",children:"User"}),(0,s.jsx)(l.eb,{value:"guest",children:"Guest"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(es,{id:"notifications"}),(0,s.jsx)(d.J,{htmlFor:"notifications",children:"Email Notifications"})]})]}),(0,s.jsx)(n.wL,{children:(0,s.jsx)(a.$,{children:"Submit"})})]})]}),(0,s.jsxs)(F,{value:"buttons",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold my-4",children:"Button Variants"}),(0,s.jsxs)("div",{className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)(a.$,{children:"Default"}),(0,s.jsx)(a.$,{variant:"secondary",children:"Secondary"}),(0,s.jsx)(a.$,{variant:"destructive",children:"Destructive"}),(0,s.jsx)(a.$,{variant:"outline",children:"Outline"}),(0,s.jsx)(a.$,{variant:"ghost",children:"Ghost"}),(0,s.jsx)(a.$,{variant:"link",children:"Link"})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)(a.$,{size:"sm",children:"Small"}),(0,s.jsx)(a.$,{children:"Default"}),(0,s.jsx)(a.$,{size:"lg",children:"Large"}),(0,s.jsx)(a.$,{size:"icon",children:(0,s.jsx)(el.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)(a.$,{disabled:!0,children:"Disabled"}),(0,s.jsx)(a.$,{variant:"outline",disabled:!0,children:"Disabled Outline"})]})]})]}),(0,s.jsxs)(F,{value:"alerts",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold my-4",children:"Alerts"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(ec.Fc,{children:[(0,s.jsx)(el.A,{className:"h-4 w-4"}),(0,s.jsx)(ec.XL,{children:"Information"}),(0,s.jsx)(ec.TN,{children:"This is an informational alert that provides neutral guidance."})]}),(0,s.jsxs)(ec.Fc,{variant:"destructive",children:[(0,s.jsx)(el.A,{className:"h-4 w-4"}),(0,s.jsx)(ec.XL,{children:"Error"}),(0,s.jsx)(ec.TN,{children:"There was an error processing your request. Please try again."})]}),(0,s.jsxs)(ec.Fc,{className:"border-green-500 text-green-600",children:[(0,s.jsx)(eo.A,{className:"h-4 w-4"}),(0,s.jsx)(ec.XL,{children:"Success"}),(0,s.jsx)(ec.TN,{children:"Your changes have been saved successfully."})]})]})]})]})]})}},22511:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),d=r(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(t,l);let o={children:["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,19044)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\test\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,87707)),"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\app\\test\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39390:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var s=r(60687);r(43210);var a=r(78148),n=r(96241);function i({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},41052:(e,t,r)=>{Promise.resolve().then(r.bind(r,19044))},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},50780:(e,t,r)=>{Promise.resolve().then(r.bind(r,20877))},55192:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>c});var s=r(60687);r(43210);var a=r(96241);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>p,gC:()=>x,l6:()=>o,yv:()=>c});var s=r(60687);r(43210);var a=r(46837),n=r(78272),i=r(13964),d=r(3589),l=r(96241);function o({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...i}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[r,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:t,position:r="popper",...n}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,s.jsx)(h,{}),(0,s.jsx)(a.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(m,{})]})})}function p({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function h({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(d.A,{className:"size-4"})})}function m({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}},68988:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(96241);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},74075:e=>{"use strict";e.exports=require("zlib")},78272:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,854,256,658,705,793],()=>r(22511));module.exports=s})();