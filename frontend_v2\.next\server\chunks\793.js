exports.id=793,exports.ids=[793],exports.modules={4432:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(60687),n=r(82136);function o({children:e,session:t}){return(0,s.jsx)(n.<PERSON><PERSON>,{session:t,children:e})}},5106:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\TM\\\\ai_report_01062025\\\\frontend_v2\\\\components\\\\providers\\\\auth-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\components\\providers\\auth-provider.tsx","default")},5973:(e,t,r)=>{Promise.resolve().then(r.bind(r,96046)),Promise.resolve().then(r.bind(r,5106)),Promise.resolve().then(r.bind(r,83066)),Promise.resolve().then(r.bind(r,9949)),Promise.resolve().then(r.bind(r,83957))},9949:(e,t,r)=>{"use strict";r.d(t,{AssessmentProvider:()=>n});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useAssessment() from the server but useAssessment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\context\\AssessmentContext.tsx","useAssessment");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call AssessmentProvider() from the server but AssessmentProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\context\\AssessmentContext.tsx","AssessmentProvider")},24934:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687);r(43210);var n=r(8730),o=r(24224),i=r(96241);let a=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:o=!1,...d}){let l=o?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(a({variant:t,size:r,className:e})),...d})}},30593:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},30967:(e,t,r)=>{"use strict";r.d(t,{SidebarProvider:()=>i,c:()=>a});var s=r(60687),n=r(43210);let o=(0,n.createContext)(void 0);function i({children:e}){let[t,r]=(0,n.useState)(!0);return(0,s.jsx)(o.Provider,{value:{isExpanded:t,toggleSidebar:()=>{r(!t)}},children:e})}function a(){let e=(0,n.useContext)(o);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},43745:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},62858:(e,t,r)=>{Promise.resolve().then(r.bind(r,70374)),Promise.resolve().then(r.bind(r,4432)),Promise.resolve().then(r.bind(r,92892)),Promise.resolve().then(r.bind(r,93947)),Promise.resolve().then(r.bind(r,30967))},70373:(e,t,r)=>{"use strict";r.d(t,{BK:()=>a,eu:()=>i,q5:()=>d});var s=r(60687);r(43210);var n=r(11096),o=r(96241);function i({className:e,...t}){return(0,s.jsx)(n.bL,{"data-slot":"avatar",className:(0,o.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function a({className:e,...t}){return(0,s.jsx)(n._V,{"data-slot":"avatar-image",className:(0,o.cn)("aspect-square size-full",e),...t})}function d({className:e,...t}){return(0,s.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,o.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},70374:(e,t,r)=>{"use strict";r.d(t,{default:()=>k});var s=r(60687);r(43210);var n=r(82136),o=r(21134),i=r(363),a=r(58869),d=r(84027),l=r(40083),c=r(10218),u=r(30474),m=r(12325),h=r(96241);function v({...e}){return(0,s.jsx)(m.bL,{"data-slot":"dropdown-menu",...e})}function f({...e}){return(0,s.jsx)(m.l9,{"data-slot":"dropdown-menu-trigger",...e})}function p({className:e,sideOffset:t=4,...r}){return(0,s.jsx)(m.ZL,{children:(0,s.jsx)(m.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,h.cn)("!bg-white dark:!bg-gray-800 text-gray-900 dark:text-gray-100 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md !border !border-gray-200 dark:!border-gray-700 p-1 shadow-md",e),style:{backgroundColor:"white !important",border:"1px solid #e5e7eb !important",opacity:"1 !important"},...r})})}function g({className:e,inset:t,variant:r="default",...n}){return(0,s.jsx)(m.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,h.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function x({className:e,inset:t,...r}){return(0,s.jsx)(m.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,h.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function b({className:e,...t}){return(0,s.jsx)(m.wv,{"data-slot":"dropdown-menu-separator",className:(0,h.cn)("bg-border -mx-1 my-1 h-px",e),...t})}var j=r(24934),y=r(70373),w=r(85814),C=r.n(w);function k(){let{data:e}=(0,n.useSession)(),{theme:t,setTheme:r}=(0,c.D)();return(0,s.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsx)("div",{className:"container flex h-16 items-center",children:(0,s.jsxs)("div",{className:"flex w-full items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center gap-2 ml-8",children:(0,s.jsx)(C(),{href:"/","aria-label":"Go to home",children:(0,s.jsx)(u.default,{src:"/TM_Logo_Black.svg",alt:"TalentMetrix Logo",width:150,height:40,className:"h-8 w-auto dark:invert cursor-pointer",priority:!0})})}),(0,s.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 flex items-center",children:(0,s.jsx)(C(),{href:"/","aria-label":"Go to home",children:(0,s.jsx)(u.default,{src:"/Kaleidoscope-Logo_GreyText.svg",alt:"Kaleidoscope Logo",width:150,height:35,className:"h-8 w-auto dark:invert cursor-pointer",priority:!0})})}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)(j.$,{variant:"ghost",size:"icon",onClick:()=>r("light"===t?"dark":"light"),className:"h-9 w-9",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(i.A,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}),e?.user&&(0,s.jsxs)(v,{children:[(0,s.jsx)(f,{asChild:!0,children:(0,s.jsx)(j.$,{variant:"ghost",className:"relative h-9 w-9 rounded-full",children:(0,s.jsx)(y.eu,{className:"h-9 w-9",children:(0,s.jsx)(y.q5,{children:e.user.name?.charAt(0)||"U"})})})}),(0,s.jsxs)(p,{className:"w-56 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg",align:"end",forceMount:!0,style:{backgroundColor:"white",border:"1px solid #e5e7eb",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},children:[(0,s.jsx)(x,{children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:e.user.name}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:e.user.email})]})}),(0,s.jsx)(b,{}),(0,s.jsxs)(g,{children:[(0,s.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]}),(0,s.jsx)(g,{asChild:!0,children:(0,s.jsxs)(C(),{href:"/settings",children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Settings"})]})}),(0,s.jsx)(b,{}),(0,s.jsxs)(g,{onClick:()=>(0,n.signOut)(),children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})]})]})]})]})})})}},82704:()=>{},83066:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\components\\theme-provider.tsx","ThemeProvider")},83957:(e,t,r)=>{"use strict";r.d(t,{SidebarProvider:()=>n});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\context\\SidebarContext.tsx","SidebarProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\context\\SidebarContext.tsx","useSidebar")},87707:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,metadata:()=>v});var s=r(37413),n=r(61421),o=r.n(n);r(82704);var i=r(83066),a=r(19854),d=r(83957),l=r(9949),c=r(96046),u=r(75986),m=r(8974),h=r(5106);let v={title:"TalentMetrix - Kaleidoscope",description:"TalentMetrix Kaleidoscope Application"};async function f({children:e}){let t=await (0,a.getServerSession)();return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:function(...e){return(0,m.QP)((0,u.$)(e))}(o().className,"min-h-screen bg-background antialiased"),children:(0,s.jsx)(i.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,s.jsx)(h.default,{session:t,children:(0,s.jsx)(l.AssessmentProvider,{children:(0,s.jsx)(d.SidebarProvider,{children:(0,s.jsxs)("div",{className:"relative min-h-screen flex flex-col",children:[!!t&&(0,s.jsx)("div",{className:"fixed top-0 left-0 right-0 z-50",children:(0,s.jsx)(c.default,{})}),(0,s.jsxs)("div",{className:"pt-16",children:[" ",(0,s.jsx)("div",{className:"flex min-h-screen",children:(0,s.jsx)("main",{className:"flex-1 pb-0 w-full",children:e})})]})]})})})})})})})}},92892:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var s=r(60687);r(43210);var n=r(10218);function o({children:e,...t}){return(0,s.jsx)(n.N,{...t,children:e})}},93947:(e,t,r)=>{"use strict";r.d(t,{AssessmentProvider:()=>l,U:()=>d});var s=r(60687),n=r(43210),o=r(82136),i=r(16189);let a=(0,n.createContext)({assessmentData:null,loading:!1,error:null,fetchAssessment:async()=>{},isInitialized:!1}),d=()=>(0,n.useContext)(a),l=({children:e})=>{let{data:t,status:r}=(0,o.useSession)(),d=(0,i.useRouter)(),[l,c]=(0,n.useState)(null),[u,m]=(0,n.useState)(!1),[h,v]=(0,n.useState)(null),[f,p]=(0,n.useState)(!1),[g,x]=(0,n.useState)(null),[b,j]=(0,n.useState)(!1),[y,w]=(0,n.useState)(0),[C,k]=(0,n.useState)(!1),P=async()=>{if(!b){console.log("Authentication error detected. Logging out and redirecting to login page..."),j(!0);try{await (0,o.signOut)({redirect:!1}),c(null),x(null),d.push("/login")}catch(e){console.error("Error during logout:",e),d.push("/login")}}},N=async e=>{if(C)return void console.log("Fetching disabled due to previous auth errors");let t=y+1;if(w(t),t>3){console.log("Too many fetch attempts, disabling further fetches"),k(!0),v("Authentication error. Please try logging in again.");return}if(!e){v("No email provided to fetch assessment."),p(!0),m(!1);return}if(b)return void console.log("Already redirecting to login, skipping fetch");try{m(!0),v(null),console.log(`Fetching assessment data for ${e}... (Attempt ${t}/3)`);let r=await fetch(`/api/assessment?email=${encodeURIComponent(e)}`);if(401===r.status){console.log("Authentication error (401). Disabling further fetches."),k(!0),P();return}if(!r.ok){let e=await r.json().catch(()=>null),t=e?.error||`API error: ${r.status}`;throw Error(t)}w(0);let s=await r.json();console.log("Assessment data fetched successfully"),c(s),x(e)}catch(t){let e=t instanceof Error?t.message:"An unknown error occurred";v(e),console.error("Error fetching assessment data:",e),t instanceof Error&&t.message.includes("NetworkError")&&(console.log("Network error. Disabling further fetches."),k(!0))}finally{m(!1),p(!0)}};return(0,n.useEffect)(()=>(console.log("AssessmentContext useEffect",{status:r,session:t?{...t,user:{...t?.user,access_token:"[REDACTED]"}}:null,fetchedForEmail:g,isRedirecting:b,fetchingDisabled:C,fetchAttempts:y}),C)?void console.log("Skipping effect because fetching is disabled"):b?void console.log("Skipping effect because we are redirecting"):void("authenticated"===r&&t?.user?.email?t.user.email!==g&&!u&&y<3&&(console.log("Session authenticated, fetching assessment for:",t.user.email),N(t.user.email)):"unauthenticated"===r?(c(null),x(null),p(!0),console.log("User not authenticated, clearing assessment data.")):"loading"===r&&!f&&m(!0),"loading"!==r&&u&&!f&&!t?.user?.email&&m(!1)),[t,r,g,b,u,C,y]),(0,s.jsx)(a.Provider,{value:{assessmentData:l,loading:u,error:h,fetchAssessment:N,isInitialized:f},children:e})}},96046:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\TM\\\\ai_report_01062025\\\\frontend_v2\\\\components\\\\header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\TM\\ai_report_01062025\\frontend_v2\\components\\header.tsx","default")},96241:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(49384),n=r(82348);function o(...e){return(0,n.QP)((0,s.$)(e))}}};