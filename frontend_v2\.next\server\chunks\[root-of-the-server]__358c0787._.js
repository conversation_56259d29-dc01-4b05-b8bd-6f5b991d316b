module.exports = {

"[project]/.next-internal/server/app/api/pdp/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/app/api/auth/auth-options.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
;
const authOptions = {
    debug: true,
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: "Credentials",
            credentials: {
                email: {
                    label: "Email",
                    type: "email"
                },
                password: {
                    label: "Password",
                    type: "password"
                }
            },
            async authorize (credentials, req) {
                console.log("🔑 Starting authentication process...");
                console.log("📧 Email being used:", credentials?.email);
                if (!credentials?.email || !credentials?.password) {
                    console.error("❌ Missing credentials");
                    throw new Error("Missing credentials");
                }
                try {
                    const loginUrl = `${("TURBOPACK compile-time value", "http://localhost:8000") || 'http://localhost:8000'}/auth/login`;
                    console.log("🌐 Attempting login at:", loginUrl);
                    const formData = new URLSearchParams({
                        username: credentials.email,
                        password: credentials.password
                    });
                    console.log("📦 Request payload:", {
                        url: loginUrl,
                        method: "POST",
                        headers: {
                            "Content-Type": "application/x-www-form-urlencoded",
                            "Accept": "application/json"
                        },
                        body: formData.toString()
                    });
                    const response = await fetch(loginUrl, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/x-www-form-urlencoded",
                            "Accept": "application/json"
                        },
                        body: formData
                    });
                    console.log("📥 Login response status:", response.status);
                    const responseText = await response.text();
                    console.log("📄 Raw response:", responseText);
                    if (!response.ok) {
                        console.error("❌ Authentication failed:", responseText);
                        // It's often better to return null here and let NextAuth handle the error display
                        // throw new Error(responseText); 
                        return null;
                    }
                    let authResponse;
                    try {
                        authResponse = JSON.parse(responseText);
                        console.log("🔓 Auth response parsed:", authResponse);
                    } catch (e) {
                        console.error("❌ Failed to parse auth response:", e);
                        // return null;
                        throw new Error("Invalid response format from server"); // Or return null
                    }
                    if (!authResponse.access_token) {
                        console.error("❌ No access token in response");
                        // return null;
                        throw new Error("No access token received"); // Or return null
                    }
                    // Decode JWT to extract role
                    let role = 'candidate';
                    try {
                        const payload = JSON.parse(Buffer.from(authResponse.access_token.split('.')[1], 'base64').toString());
                        role = payload.role || 'candidate';
                    } catch (e) {
                        console.warn('Could not decode JWT for role, defaulting to candidate', e);
                    }
                    return {
                        id: credentials.email,
                        email: credentials.email,
                        name: credentials.email.split('@')[0],
                        access_token: authResponse.access_token,
                        role
                    };
                } catch (error) {
                    console.error("❌ Authorization error:", error);
                    // In authorize, returning null signals an authentication failure to NextAuth
                    // Throwing an error here can sometimes lead to unhandled promise rejections
                    // or redirect to an error page with the error message in the URL.
                    // Consider returning null for most auth failures.
                    return null;
                }
            }
        })
    ],
    callbacks: {
        async jwt ({ token, user }) {
            // console.log("🔄 JWT Callback - Input:", { 
            //   token: { ...token, access_token: token.access_token ? '[REDACTED]' : undefined },
            //   user: user ? { ...user, access_token: user.access_token ? '[REDACTED]' : undefined } : undefined 
            // });
            // Simplified logging for user object to avoid potential issues with spreading undefined access_token
            // console.log("User object in JWT callback:", user);
            if (user) {
                token.access_token = user.access_token; // Cast user if access_token is not on default User type
                token.id = user.id;
                token.email = user.email;
                token.name = user.name;
                token.role = user.role; // Cast user if role is not on default User type
            // if ((user as any).access_token) {
            //   console.log("Raw token first 20 chars:", (user as any).access_token.substring(0, 20));
            // }
            }
            // console.log("🔄 JWT Callback - Output:", { 
            //   ...token, 
            //   access_token: token.access_token ? '[REDACTED]' : undefined 
            // });
            return token;
        },
        async session ({ session, token }) {
            // console.log("🔄 Session Callback - Input:", {
            //   session: { ...session, user: { ...session.user, access_token: undefined } },
            //   token: { ...token, access_token: token.access_token ? '[REDACTED]' : undefined }
            // });
            if (token && session.user) {
                session.user.id = token.id; // Cast session.user to add custom properties
                session.user.access_token = token.access_token;
                session.user.email = token.email; // email and name are usually on the default Session['user']
                session.user.name = token.name;
                session.user.role = token.role;
            }
            // console.log("🔄 Session Callback - Output:", {
            //   ...session,
            //   user: session.user ? { ...session.user, access_token: (session.user as any).access_token ? '[REDACTED]' : undefined } : undefined
            // });
            return session;
        }
    },
    pages: {
        signIn: "/login"
    },
    session: {
        strategy: "jwt",
        maxAge: 24 * 60 * 60
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[project]/app/api/pdp/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DELETE": (()=>DELETE),
    "GET": (()=>GET),
    "POST": (()=>POST),
    "PUT": (()=>PUT)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$api$2f$auth$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/api/auth/auth-options.ts [app-route] (ecmascript)");
;
;
;
// Get the backend URL from environment variables
const backendUrl = process.env.BACKEND_API_URL || 'http://localhost:8000';
// Helper function to forward requests to the backend
async function forwardToBackend(req, endpoint, method, session) {
    try {
        // Get the token from the session
        console.log('Session:', JSON.stringify(session, null, 2));
        const token = session?.user?.access_token;
        console.log('Token:', token ? 'Token exists' : 'No token');
        if (!token) {
            console.error('No access token found in session');
            // Return 401 with a special header to indicate auth error
            const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized: No access token',
                redirectToLogin: true
            }, {
                status: 401
            });
            // Add a special header to indicate this is an auth error
            response.headers.set('X-Auth-Required', 'true');
            return response;
        }
        // Get request body if it exists
        let body = null;
        if (method !== 'GET' && method !== 'DELETE') {
            body = await req.json();
        }
        // Prepare headers
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
        };
        // Build the URL
        const url = `${backendUrl}/pdp/${endpoint}`;
        console.log(`Making ${method} request to: ${url}`);
        console.log('Headers:', JSON.stringify(headers, null, 2));
        if (body) {
            console.log('Request body:', JSON.stringify(body, null, 2));
        }
        // Make the request to the backend
        const response = await fetch(url, {
            method,
            headers,
            body: body ? JSON.stringify(body) : undefined
        });
        console.log(`Response status: ${response.status}`);
        // Handle response
        if (!response.ok) {
            console.error(`Error response: ${response.status}`);
            try {
                const errorData = await response.json();
                console.error('Error data:', JSON.stringify(errorData, null, 2));
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(errorData, {
                    status: response.status
                });
            } catch (e) {
                console.error('Failed to parse error response:', e);
                const errorText = await response.text().catch(()=>'Could not read response text');
                console.error('Error text:', errorText);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Unknown error',
                    details: errorText
                }, {
                    status: response.status
                });
            }
        }
        // For DELETE requests with 204 status, return empty response
        if (method === 'DELETE' && response.status === 204) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
                status: 204
            });
        }
        // Return the response data
        const data = await response.json();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data);
    } catch (error) {
        console.error('Error forwarding request to backend:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to process request'
        }, {
            status: 500
        });
    }
}
async function GET(req) {
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$api$2f$auth$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
    // Get query parameters
    const url = new URL(req.url);
    const id = url.searchParams.get('id');
    // If ID is provided, get a specific item, otherwise list all items
    const endpoint = id ? `items/${id}` : 'items' + url.search;
    return forwardToBackend(req, endpoint, 'GET', session);
}
async function POST(req) {
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$api$2f$auth$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
    return forwardToBackend(req, 'items', 'POST', session);
}
async function PUT(req) {
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$api$2f$auth$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
    // Get the item ID from the request body
    const body = await req.json();
    const { id, ...data } = body;
    if (!id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Item ID is required'
        }, {
            status: 400
        });
    }
    // Create a new request with the updated body (without ID)
    const newReq = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextRequest"](req.url, {
        method: req.method,
        headers: req.headers,
        body: JSON.stringify(data)
    });
    return forwardToBackend(newReq, `items/${id}`, 'PUT', session);
}
async function DELETE(req) {
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$api$2f$auth$2f$auth$2d$options$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
    // Get the item ID from the URL
    const url = new URL(req.url);
    const id = url.searchParams.get('id');
    if (!id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Item ID is required'
        }, {
            status: 400
        });
    }
    return forwardToBackend(req, `items/${id}`, 'DELETE', session);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__358c0787._.js.map