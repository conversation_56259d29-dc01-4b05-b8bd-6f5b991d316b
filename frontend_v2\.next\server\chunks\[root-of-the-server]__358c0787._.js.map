{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/app/api/auth/auth-options.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\";\nimport CredentialsProvider from \"next-auth/providers/credentials\";\n\n// Define authOptions for NextAuth\nexport const authOptions: NextAuthOptions = {\n  debug: true, // Enable debug logs\n  providers: [\n    CredentialsProvider({\n      name: \"Credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials, req) {\n        console.log(\"🔑 Starting authentication process...\");\n        console.log(\"📧 Email being used:\", credentials?.email);\n\n        if (!credentials?.email || !credentials?.password) {\n          console.error(\"❌ Missing credentials\");\n          throw new Error(\"Missing credentials\");\n        }\n\n        try {\n          const loginUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/auth/login`;\n          console.log(\"🌐 Attempting login at:\", loginUrl);\n\n          const formData = new URLSearchParams({\n            username: credentials.email,\n            password: credentials.password,\n          });\n\n          console.log(\"📦 Request payload:\", {\n            url: loginUrl,\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\n              \"Accept\": \"application/json\",\n            },\n            body: formData.toString()\n          });\n\n          const response = await fetch(loginUrl, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\n              \"Accept\": \"application/json\",\n            },\n            body: formData,\n          });\n\n          console.log(\"📥 Login response status:\", response.status);\n          const responseText = await response.text();\n          console.log(\"📄 Raw response:\", responseText);\n\n          if (!response.ok) {\n            console.error(\"❌ Authentication failed:\", responseText);\n            // It's often better to return null here and let NextAuth handle the error display\n            // throw new Error(responseText); \n            return null; \n          }\n\n          let authResponse;\n          try {\n            authResponse = JSON.parse(responseText);\n            console.log(\"🔓 Auth response parsed:\", authResponse);\n          } catch (e) {\n            console.error(\"❌ Failed to parse auth response:\", e);\n            // return null;\n            throw new Error(\"Invalid response format from server\"); // Or return null\n          }\n\n          if (!authResponse.access_token) {\n            console.error(\"❌ No access token in response\");\n            // return null;\n            throw new Error(\"No access token received\"); // Or return null\n          }\n\n          // Decode JWT to extract role\n          let role = 'candidate';\n          try {\n            const payload = JSON.parse(Buffer.from(authResponse.access_token.split('.')[1], 'base64').toString());\n            role = payload.role || 'candidate';\n          } catch (e) {\n            console.warn('Could not decode JWT for role, defaulting to candidate', e);\n          }\n\n          return {\n            id: credentials.email, // Should ideally be a unique ID from your backend\n            email: credentials.email,\n            name: credentials.email.split('@')[0], // Or a name from your backend\n            access_token: authResponse.access_token,\n            role,\n          };\n\n        } catch (error) {\n          console.error(\"❌ Authorization error:\", error);\n          // In authorize, returning null signals an authentication failure to NextAuth\n          // Throwing an error here can sometimes lead to unhandled promise rejections\n          // or redirect to an error page with the error message in the URL.\n          // Consider returning null for most auth failures.\n          return null; \n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      // console.log(\"🔄 JWT Callback - Input:\", { \n      //   token: { ...token, access_token: token.access_token ? '[REDACTED]' : undefined },\n      //   user: user ? { ...user, access_token: user.access_token ? '[REDACTED]' : undefined } : undefined \n      // });\n      // Simplified logging for user object to avoid potential issues with spreading undefined access_token\n      // console.log(\"User object in JWT callback:\", user);\n\n\n      if (user) {\n        token.access_token = (user as any).access_token; // Cast user if access_token is not on default User type\n        token.id = user.id;\n        token.email = user.email;\n        token.name = user.name;\n        token.role = (user as any).role; // Cast user if role is not on default User type\n        \n        // if ((user as any).access_token) {\n        //   console.log(\"Raw token first 20 chars:\", (user as any).access_token.substring(0, 20));\n        // }\n      }\n\n      // console.log(\"🔄 JWT Callback - Output:\", { \n      //   ...token, \n      //   access_token: token.access_token ? '[REDACTED]' : undefined \n      // });\n      return token;\n    },\n    async session({ session, token }) {\n      // console.log(\"🔄 Session Callback - Input:\", {\n      //   session: { ...session, user: { ...session.user, access_token: undefined } },\n      //   token: { ...token, access_token: token.access_token ? '[REDACTED]' : undefined }\n      // });\n\n      if (token && session.user) { // Ensure session.user exists\n        (session.user as any).id = token.id; // Cast session.user to add custom properties\n        (session.user as any).access_token = token.access_token;\n        session.user.email = token.email; // email and name are usually on the default Session['user']\n        session.user.name = token.name;\n        (session.user as any).role = token.role;\n      }\n\n      // console.log(\"🔄 Session Callback - Output:\", {\n      //   ...session,\n      //   user: session.user ? { ...session.user, access_token: (session.user as any).access_token ? '[REDACTED]' : undefined } : undefined\n      // });\n      return session;\n    },\n  },\n  pages: {\n    signIn: \"/login\",\n  },\n  session: {\n    strategy: \"jwt\",\n    maxAge: 24 * 60 * 60, // 24 hours\n  },\n  secret: process.env.NEXTAUTH_SECRET, // IMPORTANT: Ensure this is set\n};\n"], "names": [], "mappings": ";;;AACA;;AAGO,MAAM,cAA+B;IAC1C,OAAO;IACP,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW,EAAE,GAAG;gBAC9B,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,wBAAwB,aAAa;gBAEjD,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,QAAQ,KAAK,CAAC;oBACd,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI;oBACF,MAAM,WAAW,GAAG,6DAAmC,wBAAwB,WAAW,CAAC;oBAC3F,QAAQ,GAAG,CAAC,2BAA2B;oBAEvC,MAAM,WAAW,IAAI,gBAAgB;wBACnC,UAAU,YAAY,KAAK;wBAC3B,UAAU,YAAY,QAAQ;oBAChC;oBAEA,QAAQ,GAAG,CAAC,uBAAuB;wBACjC,KAAK;wBACL,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,UAAU;wBACZ;wBACA,MAAM,SAAS,QAAQ;oBACzB;oBAEA,MAAM,WAAW,MAAM,MAAM,UAAU;wBACrC,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,UAAU;wBACZ;wBACA,MAAM;oBACR;oBAEA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,MAAM;oBACxD,MAAM,eAAe,MAAM,SAAS,IAAI;oBACxC,QAAQ,GAAG,CAAC,oBAAoB;oBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,kFAAkF;wBAClF,kCAAkC;wBAClC,OAAO;oBACT;oBAEA,IAAI;oBACJ,IAAI;wBACF,eAAe,KAAK,KAAK,CAAC;wBAC1B,QAAQ,GAAG,CAAC,4BAA4B;oBAC1C,EAAE,OAAO,GAAG;wBACV,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,eAAe;wBACf,MAAM,IAAI,MAAM,wCAAwC,iBAAiB;oBAC3E;oBAEA,IAAI,CAAC,aAAa,YAAY,EAAE;wBAC9B,QAAQ,KAAK,CAAC;wBACd,eAAe;wBACf,MAAM,IAAI,MAAM,6BAA6B,iBAAiB;oBAChE;oBAEA,6BAA6B;oBAC7B,IAAI,OAAO;oBACX,IAAI;wBACF,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,aAAa,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,QAAQ;wBAClG,OAAO,QAAQ,IAAI,IAAI;oBACzB,EAAE,OAAO,GAAG;wBACV,QAAQ,IAAI,CAAC,0DAA0D;oBACzE;oBAEA,OAAO;wBACL,IAAI,YAAY,KAAK;wBACrB,OAAO,YAAY,KAAK;wBACxB,MAAM,YAAY,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wBACrC,cAAc,aAAa,YAAY;wBACvC;oBACF;gBAEF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,6EAA6E;oBAC7E,4EAA4E;oBAC5E,kEAAkE;oBAClE,kDAAkD;oBAClD,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,6CAA6C;YAC7C,sFAAsF;YACtF,sGAAsG;YACtG,MAAM;YACN,qGAAqG;YACrG,qDAAqD;YAGrD,IAAI,MAAM;gBACR,MAAM,YAAY,GAAG,AAAC,KAAa,YAAY,EAAE,wDAAwD;gBACzG,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,IAAI,GAAG,AAAC,KAAa,IAAI,EAAE,gDAAgD;YAEjF,oCAAoC;YACpC,2FAA2F;YAC3F,IAAI;YACN;YAEA,8CAA8C;YAC9C,eAAe;YACf,iEAAiE;YACjE,MAAM;YACN,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,gDAAgD;YAChD,iFAAiF;YACjF,qFAAqF;YACrF,MAAM;YAEN,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAS,EAAE,GAAG,MAAM,EAAE,EAAE,6CAA6C;gBACjF,QAAQ,IAAI,CAAS,YAAY,GAAG,MAAM,YAAY;gBACvD,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK,EAAE,4DAA4D;gBAC9F,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC7B,QAAQ,IAAI,CAAS,IAAI,GAAG,MAAM,IAAI;YACzC;YAEA,iDAAiD;YACjD,gBAAgB;YAChB,sIAAsI;YACtI,MAAM;YACN,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/app/api/pdp/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { getServerSession } from 'next-auth';\r\nimport { authOptions } from '@/app/api/auth/auth-options';\r\n\r\n// Get the backend URL from environment variables\r\nconst backendUrl = process.env.BACKEND_API_URL || 'http://localhost:8000';\r\n\r\n// Helper function to forward requests to the backend\r\nasync function forwardToBackend(req: NextRequest, endpoint: string, method: string, session: any) {\r\n  try {\r\n    // Get the token from the session\r\n    console.log('Session:', JSON.stringify(session, null, 2));\r\n    const token = session?.user?.access_token;\r\n    console.log('Token:', token ? 'Token exists' : 'No token');\r\n    \r\n    if (!token) {\r\n      console.error('No access token found in session');\r\n      // Return 401 with a special header to indicate auth error\r\n      const response = NextResponse.json(\r\n        { error: 'Unauthorized: No access token', redirectToLogin: true },\r\n        { status: 401 }\r\n      );\r\n      // Add a special header to indicate this is an auth error\r\n      response.headers.set('X-Auth-Required', 'true');\r\n      return response;\r\n    }\r\n\r\n    // Get request body if it exists\r\n    let body = null;\r\n    if (method !== 'GET' && method !== 'DELETE') {\r\n      body = await req.json();\r\n    }\r\n\r\n    // Prepare headers\r\n    const headers: HeadersInit = {\r\n      'Content-Type': 'application/json',\r\n      'Accept': 'application/json',\r\n      'Authorization': `Bearer ${token}`\r\n    };\r\n\r\n    // Build the URL\r\n    const url = `${backendUrl}/pdp/${endpoint}`;\r\n    console.log(`Making ${method} request to: ${url}`);\r\n    console.log('Headers:', JSON.stringify(headers, null, 2));\r\n    if (body) {\r\n      console.log('Request body:', JSON.stringify(body, null, 2));\r\n    }\r\n\r\n    // Make the request to the backend\r\n    const response = await fetch(url, {\r\n      method,\r\n      headers,\r\n      body: body ? JSON.stringify(body) : undefined,\r\n    });\r\n    \r\n    console.log(`Response status: ${response.status}`);\r\n\r\n    // Handle response\r\n    if (!response.ok) {\r\n      console.error(`Error response: ${response.status}`);\r\n      try {\r\n        const errorData = await response.json();\r\n        console.error('Error data:', JSON.stringify(errorData, null, 2));\r\n        return NextResponse.json(errorData, { status: response.status });\r\n      } catch (e) {\r\n        console.error('Failed to parse error response:', e);\r\n        const errorText = await response.text().catch(() => 'Could not read response text');\r\n        console.error('Error text:', errorText);\r\n        return NextResponse.json({ error: 'Unknown error', details: errorText }, { status: response.status });\r\n      }\r\n    }\r\n\r\n    // For DELETE requests with 204 status, return empty response\r\n    if (method === 'DELETE' && response.status === 204) {\r\n      return new NextResponse(null, { status: 204 });\r\n    }\r\n\r\n    // Return the response data\r\n    const data = await response.json();\r\n    return NextResponse.json(data);\r\n  } catch (error) {\r\n    console.error('Error forwarding request to backend:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to process request' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// GET handler for listing PDP items\r\nexport async function GET(req: NextRequest) {\r\n  const session = await getServerSession(authOptions);\r\n  \r\n  // Get query parameters\r\n  const url = new URL(req.url);\r\n  const id = url.searchParams.get('id');\r\n  \r\n  // If ID is provided, get a specific item, otherwise list all items\r\n  const endpoint = id ? `items/${id}` : 'items' + url.search;\r\n  \r\n  return forwardToBackend(req, endpoint, 'GET', session);\r\n}\r\n\r\n// POST handler for creating new PDP items\r\nexport async function POST(req: NextRequest) {\r\n  const session = await getServerSession(authOptions);\r\n  return forwardToBackend(req, 'items', 'POST', session);\r\n}\r\n\r\n// PUT handler for updating PDP items\r\nexport async function PUT(req: NextRequest) {\r\n  const session = await getServerSession(authOptions);\r\n  \r\n  // Get the item ID from the request body\r\n  const body = await req.json();\r\n  const { id, ...data } = body;\r\n  \r\n  if (!id) {\r\n    return NextResponse.json(\r\n      { error: 'Item ID is required' },\r\n      { status: 400 }\r\n    );\r\n  }\r\n  \r\n  // Create a new request with the updated body (without ID)\r\n  const newReq = new NextRequest(req.url, {\r\n    method: req.method,\r\n    headers: req.headers,\r\n    body: JSON.stringify(data),\r\n  });\r\n  \r\n  return forwardToBackend(newReq, `items/${id}`, 'PUT', session);\r\n}\r\n\r\n// DELETE handler for deleting PDP items\r\nexport async function DELETE(req: NextRequest) {\r\n  const session = await getServerSession(authOptions);\r\n  \r\n  // Get the item ID from the URL\r\n  const url = new URL(req.url);\r\n  const id = url.searchParams.get('id');\r\n  \r\n  if (!id) {\r\n    return NextResponse.json(\r\n      { error: 'Item ID is required' },\r\n      { status: 400 }\r\n    );\r\n  }\r\n  \r\n  return forwardToBackend(req, `items/${id}`, 'DELETE', session);\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEA,iDAAiD;AACjD,MAAM,aAAa,QAAQ,GAAG,CAAC,eAAe,IAAI;AAElD,qDAAqD;AACrD,eAAe,iBAAiB,GAAgB,EAAE,QAAgB,EAAE,MAAc,EAAE,OAAY;IAC9F,IAAI;QACF,iCAAiC;QACjC,QAAQ,GAAG,CAAC,YAAY,KAAK,SAAS,CAAC,SAAS,MAAM;QACtD,MAAM,QAAQ,SAAS,MAAM;QAC7B,QAAQ,GAAG,CAAC,UAAU,QAAQ,iBAAiB;QAE/C,IAAI,CAAC,OAAO;YACV,QAAQ,KAAK,CAAC;YACd,0DAA0D;YAC1D,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAChC;gBAAE,OAAO;gBAAiC,iBAAiB;YAAK,GAChE;gBAAE,QAAQ;YAAI;YAEhB,yDAAyD;YACzD,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;YACxC,OAAO;QACT;QAEA,gCAAgC;QAChC,IAAI,OAAO;QACX,IAAI,WAAW,SAAS,WAAW,UAAU;YAC3C,OAAO,MAAM,IAAI,IAAI;QACvB;QAEA,kBAAkB;QAClB,MAAM,UAAuB;YAC3B,gBAAgB;YAChB,UAAU;YACV,iBAAiB,CAAC,OAAO,EAAE,OAAO;QACpC;QAEA,gBAAgB;QAChB,MAAM,MAAM,GAAG,WAAW,KAAK,EAAE,UAAU;QAC3C,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,aAAa,EAAE,KAAK;QACjD,QAAQ,GAAG,CAAC,YAAY,KAAK,SAAS,CAAC,SAAS,MAAM;QACtD,IAAI,MAAM;YACR,QAAQ,GAAG,CAAC,iBAAiB,KAAK,SAAS,CAAC,MAAM,MAAM;QAC1D;QAEA,kCAAkC;QAClC,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC;YACA;YACA,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;QAEA,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,MAAM,EAAE;QAEjD,kBAAkB;QAClB,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,CAAC,gBAAgB,EAAE,SAAS,MAAM,EAAE;YAClD,IAAI;gBACF,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,eAAe,KAAK,SAAS,CAAC,WAAW,MAAM;gBAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;oBAAE,QAAQ,SAAS,MAAM;gBAAC;YAChE,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM;gBACpD,QAAQ,KAAK,CAAC,eAAe;gBAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;oBAAiB,SAAS;gBAAU,GAAG;oBAAE,QAAQ,SAAS,MAAM;gBAAC;YACrG;QACF;QAEA,6DAA6D;QAC7D,IAAI,WAAW,YAAY,SAAS,MAAM,KAAK,KAAK;YAClD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;gBAAE,QAAQ;YAAI;QAC9C;QAEA,2BAA2B;QAC3B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,GAAgB;IACxC,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,uIAAA,CAAA,cAAW;IAElD,uBAAuB;IACvB,MAAM,MAAM,IAAI,IAAI,IAAI,GAAG;IAC3B,MAAM,KAAK,IAAI,YAAY,CAAC,GAAG,CAAC;IAEhC,mEAAmE;IACnE,MAAM,WAAW,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,UAAU,IAAI,MAAM;IAE1D,OAAO,iBAAiB,KAAK,UAAU,OAAO;AAChD;AAGO,eAAe,KAAK,GAAgB;IACzC,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,uIAAA,CAAA,cAAW;IAClD,OAAO,iBAAiB,KAAK,SAAS,QAAQ;AAChD;AAGO,eAAe,IAAI,GAAgB;IACxC,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,uIAAA,CAAA,cAAW;IAElD,wCAAwC;IACxC,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,GAAG;IAExB,IAAI,CAAC,IAAI;QACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsB,GAC/B;YAAE,QAAQ;QAAI;IAElB;IAEA,0DAA0D;IAC1D,MAAM,SAAS,IAAI,gIAAA,CAAA,cAAW,CAAC,IAAI,GAAG,EAAE;QACtC,QAAQ,IAAI,MAAM;QAClB,SAAS,IAAI,OAAO;QACpB,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,OAAO,iBAAiB,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO;AACxD;AAGO,eAAe,OAAO,GAAgB;IAC3C,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,uIAAA,CAAA,cAAW;IAElD,+BAA+B;IAC/B,MAAM,MAAM,IAAI,IAAI,IAAI,GAAG;IAC3B,MAAM,KAAK,IAAI,YAAY,CAAC,GAAG,CAAC;IAEhC,IAAI,CAAC,IAAI;QACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsB,GAC/B;YAAE,QAAQ;QAAI;IAElB;IAEA,OAAO,iBAAiB,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU;AACxD", "debugId": null}}]}