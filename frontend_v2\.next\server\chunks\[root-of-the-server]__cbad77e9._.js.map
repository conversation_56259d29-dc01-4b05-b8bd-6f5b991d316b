{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/app/api/hello/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n/**\n * Health check endpoint\n * GET /api/hello\n * \n * This endpoint can be used for:\n * - Health checks\n * - Monitoring\n * - Load balancer checks\n * - Uptime monitoring\n * \n * No authentication required.\n */\nexport async function GET(request: NextRequest) {\n  try {\n    const timestamp = new Date().toISOString();\n    const uptime = process.uptime();\n    \n    return NextResponse.json({\n      status: 'ok',\n      message: 'Hello! API is running successfully.',\n      timestamp: timestamp,\n      uptime: `${Math.floor(uptime)} seconds`,\n      version: process.env.npm_package_version || '1.0.0',\n      environment: process.env.NODE_ENV || 'development',\n      service: 'TalentMetrix Frontend API'\n    }, {\n      status: 200,\n      headers: {\n        'Content-Type': 'application/json',\n        'Cache-Control': 'no-cache, no-store, must-revalidate',\n        'Pragma': 'no-cache',\n        'Expires': '0'\n      }\n    });\n  } catch (error) {\n    console.error('Health check error:', error);\n    \n    return NextResponse.json({\n      status: 'error',\n      message: 'Health check failed',\n      timestamp: new Date().toISOString(),\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, {\n      status: 500,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n}\n\n/**\n * Also support POST for more comprehensive health checks\n */\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json().catch(() => ({}));\n    const timestamp = new Date().toISOString();\n    \n    return NextResponse.json({\n      status: 'ok',\n      message: 'Hello! API is running and can process POST requests.',\n      timestamp: timestamp,\n      receivedData: body,\n      service: 'TalentMetrix Frontend API'\n    }, {\n      status: 200,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  } catch (error) {\n    console.error('POST health check error:', error);\n    \n    return NextResponse.json({\n      status: 'error',\n      message: 'POST health check failed',\n      timestamp: new Date().toISOString(),\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, {\n      status: 500,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAcO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,YAAY,IAAI,OAAO,WAAW;QACxC,MAAM,SAAS,QAAQ,MAAM;QAE7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,SAAS;YACT,WAAW;YACX,QAAQ,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,CAAC;YACvC,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;YAC5C,aAAa,mDAAwB;YACrC,SAAS;QACX,GAAG;YACD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;gBACjB,UAAU;gBACV,WAAW;YACb;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YACD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF;AACF;AAKO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACjD,MAAM,YAAY,IAAI,OAAO,WAAW;QAExC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,SAAS;YACT,WAAW;YACX,cAAc;YACd,SAAS;QACX,GAAG;YACD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YACD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF;AACF", "debugId": null}}]}