{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/app/api/auth/auth-options.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\";\nimport CredentialsProvider from \"next-auth/providers/credentials\";\n\n// Define authOptions for NextAuth\nexport const authOptions: NextAuthOptions = {\n  debug: true, // Enable debug logs\n  providers: [\n    CredentialsProvider({\n      name: \"Credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials, req) {\n        console.log(\"🔑 Starting authentication process...\");\n        console.log(\"📧 Email being used:\", credentials?.email);\n\n        if (!credentials?.email || !credentials?.password) {\n          console.error(\"❌ Missing credentials\");\n          throw new Error(\"Missing credentials\");\n        }\n\n        try {\n          const loginUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/auth/login`;\n          console.log(\"🌐 Attempting login at:\", loginUrl);\n\n          const formData = new URLSearchParams({\n            username: credentials.email,\n            password: credentials.password,\n          });\n\n          console.log(\"📦 Request payload:\", {\n            url: loginUrl,\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\n              \"Accept\": \"application/json\",\n            },\n            body: formData.toString()\n          });\n\n          const response = await fetch(loginUrl, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\n              \"Accept\": \"application/json\",\n            },\n            body: formData,\n          });\n\n          console.log(\"📥 Login response status:\", response.status);\n          const responseText = await response.text();\n          console.log(\"📄 Raw response:\", responseText);\n\n          if (!response.ok) {\n            console.error(\"❌ Authentication failed:\", responseText);\n            // It's often better to return null here and let NextAuth handle the error display\n            // throw new Error(responseText); \n            return null; \n          }\n\n          let authResponse;\n          try {\n            authResponse = JSON.parse(responseText);\n            console.log(\"🔓 Auth response parsed:\", authResponse);\n          } catch (e) {\n            console.error(\"❌ Failed to parse auth response:\", e);\n            // return null;\n            throw new Error(\"Invalid response format from server\"); // Or return null\n          }\n\n          if (!authResponse.access_token) {\n            console.error(\"❌ No access token in response\");\n            // return null;\n            throw new Error(\"No access token received\"); // Or return null\n          }\n\n          // Decode JWT to extract role\n          let role = 'candidate';\n          try {\n            const payload = JSON.parse(Buffer.from(authResponse.access_token.split('.')[1], 'base64').toString());\n            role = payload.role || 'candidate';\n          } catch (e) {\n            console.warn('Could not decode JWT for role, defaulting to candidate', e);\n          }\n\n          return {\n            id: credentials.email, // Should ideally be a unique ID from your backend\n            email: credentials.email,\n            name: credentials.email.split('@')[0], // Or a name from your backend\n            access_token: authResponse.access_token,\n            role,\n          };\n\n        } catch (error) {\n          console.error(\"❌ Authorization error:\", error);\n          // In authorize, returning null signals an authentication failure to NextAuth\n          // Throwing an error here can sometimes lead to unhandled promise rejections\n          // or redirect to an error page with the error message in the URL.\n          // Consider returning null for most auth failures.\n          return null; \n        }\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      // console.log(\"🔄 JWT Callback - Input:\", { \n      //   token: { ...token, access_token: token.access_token ? '[REDACTED]' : undefined },\n      //   user: user ? { ...user, access_token: user.access_token ? '[REDACTED]' : undefined } : undefined \n      // });\n      // Simplified logging for user object to avoid potential issues with spreading undefined access_token\n      // console.log(\"User object in JWT callback:\", user);\n\n\n      if (user) {\n        token.access_token = (user as any).access_token; // Cast user if access_token is not on default User type\n        token.id = user.id;\n        token.email = user.email;\n        token.name = user.name;\n        token.role = (user as any).role; // Cast user if role is not on default User type\n        \n        // if ((user as any).access_token) {\n        //   console.log(\"Raw token first 20 chars:\", (user as any).access_token.substring(0, 20));\n        // }\n      }\n\n      // console.log(\"🔄 JWT Callback - Output:\", { \n      //   ...token, \n      //   access_token: token.access_token ? '[REDACTED]' : undefined \n      // });\n      return token;\n    },\n    async session({ session, token }) {\n      // console.log(\"🔄 Session Callback - Input:\", {\n      //   session: { ...session, user: { ...session.user, access_token: undefined } },\n      //   token: { ...token, access_token: token.access_token ? '[REDACTED]' : undefined }\n      // });\n\n      if (token && session.user) { // Ensure session.user exists\n        (session.user as any).id = token.id; // Cast session.user to add custom properties\n        (session.user as any).access_token = token.access_token;\n        session.user.email = token.email; // email and name are usually on the default Session['user']\n        session.user.name = token.name;\n        (session.user as any).role = token.role;\n      }\n\n      // console.log(\"🔄 Session Callback - Output:\", {\n      //   ...session,\n      //   user: session.user ? { ...session.user, access_token: (session.user as any).access_token ? '[REDACTED]' : undefined } : undefined\n      // });\n      return session;\n    },\n  },\n  pages: {\n    signIn: \"/login\",\n  },\n  session: {\n    strategy: \"jwt\",\n    maxAge: 24 * 60 * 60, // 24 hours\n  },\n  secret: process.env.NEXTAUTH_SECRET, // IMPORTANT: Ensure this is set\n};\n"], "names": [], "mappings": ";;;AACA;;AAGO,MAAM,cAA+B;IAC1C,OAAO;IACP,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW,EAAE,GAAG;gBAC9B,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,wBAAwB,aAAa;gBAEjD,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,QAAQ,KAAK,CAAC;oBACd,MAAM,IAAI,MAAM;gBAClB;gBAEA,IAAI;oBACF,MAAM,WAAW,GAAG,6DAAmC,wBAAwB,WAAW,CAAC;oBAC3F,QAAQ,GAAG,CAAC,2BAA2B;oBAEvC,MAAM,WAAW,IAAI,gBAAgB;wBACnC,UAAU,YAAY,KAAK;wBAC3B,UAAU,YAAY,QAAQ;oBAChC;oBAEA,QAAQ,GAAG,CAAC,uBAAuB;wBACjC,KAAK;wBACL,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,UAAU;wBACZ;wBACA,MAAM,SAAS,QAAQ;oBACzB;oBAEA,MAAM,WAAW,MAAM,MAAM,UAAU;wBACrC,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,UAAU;wBACZ;wBACA,MAAM;oBACR;oBAEA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,MAAM;oBACxD,MAAM,eAAe,MAAM,SAAS,IAAI;oBACxC,QAAQ,GAAG,CAAC,oBAAoB;oBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,kFAAkF;wBAClF,kCAAkC;wBAClC,OAAO;oBACT;oBAEA,IAAI;oBACJ,IAAI;wBACF,eAAe,KAAK,KAAK,CAAC;wBAC1B,QAAQ,GAAG,CAAC,4BAA4B;oBAC1C,EAAE,OAAO,GAAG;wBACV,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,eAAe;wBACf,MAAM,IAAI,MAAM,wCAAwC,iBAAiB;oBAC3E;oBAEA,IAAI,CAAC,aAAa,YAAY,EAAE;wBAC9B,QAAQ,KAAK,CAAC;wBACd,eAAe;wBACf,MAAM,IAAI,MAAM,6BAA6B,iBAAiB;oBAChE;oBAEA,6BAA6B;oBAC7B,IAAI,OAAO;oBACX,IAAI;wBACF,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,aAAa,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,QAAQ;wBAClG,OAAO,QAAQ,IAAI,IAAI;oBACzB,EAAE,OAAO,GAAG;wBACV,QAAQ,IAAI,CAAC,0DAA0D;oBACzE;oBAEA,OAAO;wBACL,IAAI,YAAY,KAAK;wBACrB,OAAO,YAAY,KAAK;wBACxB,MAAM,YAAY,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wBACrC,cAAc,aAAa,YAAY;wBACvC;oBACF;gBAEF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,6EAA6E;oBAC7E,4EAA4E;oBAC5E,kEAAkE;oBAClE,kDAAkD;oBAClD,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,6CAA6C;YAC7C,sFAAsF;YACtF,sGAAsG;YACtG,MAAM;YACN,qGAAqG;YACrG,qDAAqD;YAGrD,IAAI,MAAM;gBACR,MAAM,YAAY,GAAG,AAAC,KAAa,YAAY,EAAE,wDAAwD;gBACzG,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,IAAI,GAAG,AAAC,KAAa,IAAI,EAAE,gDAAgD;YAEjF,oCAAoC;YACpC,2FAA2F;YAC3F,IAAI;YACN;YAEA,8CAA8C;YAC9C,eAAe;YACf,iEAAiE;YACjE,MAAM;YACN,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,gDAAgD;YAChD,iFAAiF;YACjF,qFAAqF;YACrF,MAAM;YAEN,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAS,EAAE,GAAG,MAAM,EAAE,EAAE,6CAA6C;gBACjF,QAAQ,IAAI,CAAS,YAAY,GAAG,MAAM,YAAY;gBACvD,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK,EAAE,4DAA4D;gBAC9F,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC7B,QAAQ,IAAI,CAAS,IAAI,GAAG,MAAM,IAAI;YACzC;YAEA,iDAAiD;YACjD,gBAAgB;YAChB,sIAAsI;YACtI,MAAM;YACN,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport { authOptions } from \"../auth-options\";\n\n// Create NextAuth handler using the imported authOptions\nconst handler = NextAuth(authOptions);\n\n// Export the handler as GET and POST methods\nexport { handler as GET, handler as POST };"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,yDAAyD;AACzD,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,uIAAA,CAAA,cAAW", "debugId": null}}]}