{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/theme-provider.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\n\nexport function ThemeProvider({ children, ...props }: React.PropsWithChildren<{ [key: string]: any }>) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAAwD;IACnG,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/context/SidebarContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useState, useContext, ReactNode } from 'react';\r\n\r\ninterface SidebarContextType {\r\n  isExpanded: boolean;\r\n  toggleSidebar: () => void;\r\n}\r\n\r\nconst SidebarContext = createContext<SidebarContextType | undefined>(undefined);\r\n\r\nexport function SidebarProvider({ children }: { children: ReactNode }) {\r\n  const [isExpanded, setIsExpanded] = useState(true);\r\n\r\n  const toggleSidebar = () => {\r\n    setIsExpanded(!isExpanded);\r\n  };\r\n\r\n  return (\r\n    <SidebarContext.Provider value={{ isExpanded, toggleSidebar }}>\r\n      {children}\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useSidebar() {\r\n  const context = useContext(SidebarContext);\r\n  if (context === undefined) {\r\n    throw new Error('useSidebar must be used within a SidebarProvider');\r\n  }\r\n  return context;\r\n} "], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AASA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAE9D,SAAS,gBAAgB,EAAE,QAAQ,EAA2B;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,gBAAgB;QACpB,cAAc,CAAC;IACjB;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;YAAE;YAAY;QAAc;kBACzD;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/context/AssessmentContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { useSession, signOut } from 'next-auth/react';\r\nimport { useRouter } from 'next/navigation';\r\n\r\n// Define assessment data type\r\ntype AssessmentData = {\r\n  email: string;\r\n  individual_name: string;\r\n  assessment: {\r\n    section_i: any;\r\n    section_ii: any;\r\n  };\r\n  timestamp: string;\r\n  cached: boolean;\r\n} | null;\r\n\r\n// Create context with default values\r\ntype AssessmentContextType = {\r\n  assessmentData: AssessmentData;\r\n  loading: boolean;\r\n  error: string | null;\r\n  fetchAssessment: (email: string) => Promise<void>;\r\n  isInitialized: boolean;\r\n};\r\n\r\nconst AssessmentContext = createContext<AssessmentContextType>({\r\n  assessmentData: null,\r\n  loading: false,\r\n  error: null,\r\n  fetchAssessment: async () => {},\r\n  isInitialized: false\r\n});\r\n\r\n// Create hook for using the context\r\nexport const useAssessment = () => useContext(AssessmentContext);\r\n\r\n// Create provider component\r\nexport const AssessmentProvider = ({ children }: { children: ReactNode }) => {\r\n  const { data: session, status } = useSession();\r\n  const router = useRouter();\r\n  const [assessmentData, setAssessmentData] = useState<AssessmentData>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n  const [fetchedForEmail, setFetchedForEmail] = useState<string | null>(null);\r\n  // Add a flag to track if we're already redirecting to prevent infinite loops\r\n  const [isRedirecting, setIsRedirecting] = useState(false);\r\n  // Add a counter to track fetch attempts and prevent infinite loops\r\n  const [fetchAttempts, setFetchAttempts] = useState(0);\r\n  // Add a flag to disable fetching after authentication errors\r\n  const [fetchingDisabled, setFetchingDisabled] = useState(false);\r\n  \r\n  // Function to handle authentication errors\r\n  const handleAuthError = async () => {\r\n    // Only handle auth error once\r\n    if (isRedirecting) return;\r\n    \r\n    console.log('Authentication error detected. Logging out and redirecting to login page...');\r\n    setIsRedirecting(true);\r\n    \r\n    try {\r\n      // Sign out the user\r\n      await signOut({ redirect: false });\r\n      \r\n      // Clear any assessment data\r\n      setAssessmentData(null);\r\n      setFetchedForEmail(null);\r\n      \r\n      // Redirect to login page\r\n      router.push('/login');\r\n    } catch (err) {\r\n      console.error('Error during logout:', err);\r\n      // If signOut fails, redirect directly using Next.js router\r\n      router.push('/login');\r\n    }\r\n  };\r\n\r\n  const fetchAssessment = async (email: string) => {\r\n    // Skip fetching if disabled due to previous auth errors\r\n    if (fetchingDisabled) {\r\n      console.log('Fetching disabled due to previous auth errors');\r\n      return;\r\n    }\r\n\r\n    // Increment fetch attempts\r\n    const currentAttempts = fetchAttempts + 1;\r\n    setFetchAttempts(currentAttempts);\r\n    \r\n    // If we've tried too many times, disable fetching\r\n    if (currentAttempts > 3) {\r\n      console.log('Too many fetch attempts, disabling further fetches');\r\n      setFetchingDisabled(true);\r\n      setError('Authentication error. Please try logging in again.');\r\n      return;\r\n    }\r\n\r\n    if (!email) {\r\n      setError(\"No email provided to fetch assessment.\");\r\n      setIsInitialized(true);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n    \r\n    // Prevent multiple fetch attempts if we're already redirecting\r\n    if (isRedirecting) {\r\n      console.log('Already redirecting to login, skipping fetch');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      console.log(`Fetching assessment data for ${email}... (Attempt ${currentAttempts}/3)`);\r\n      \r\n      const response = await fetch(`/api/assessment?email=${encodeURIComponent(email)}`);\r\n      \r\n      // Handle 401 Unauthorized (expired token)\r\n      if (response.status === 401) {\r\n        console.log('Authentication error (401). Disabling further fetches.');\r\n        setFetchingDisabled(true);\r\n        // Call the auth error handler to logout and redirect\r\n        handleAuthError();\r\n        return;\r\n      }\r\n      \r\n      if (!response.ok) {\r\n        const errorData = await response.json().catch(() => null);\r\n        const errorMessage = errorData?.error || `API error: ${response.status}`;\r\n        throw new Error(errorMessage);\r\n      }\r\n      \r\n      // Reset fetch attempts on success\r\n      setFetchAttempts(0);\r\n      \r\n      const data = await response.json();\r\n      console.log('Assessment data fetched successfully');\r\n      setAssessmentData(data);\r\n      setFetchedForEmail(email);\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';\r\n      setError(errorMessage);\r\n      console.error('Error fetching assessment data:', errorMessage);\r\n      \r\n      // If we get a network error, disable further fetches\r\n      if (err instanceof Error && err.message.includes('NetworkError')) {\r\n        console.log('Network error. Disabling further fetches.');\r\n        setFetchingDisabled(true);\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n      setIsInitialized(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    console.log(\"AssessmentContext useEffect\", { \r\n      status, \r\n      session: session ? { ...session, user: { ...session?.user, access_token: '[REDACTED]' } } : null, \r\n      fetchedForEmail, \r\n      isRedirecting,\r\n      fetchingDisabled,\r\n      fetchAttempts\r\n    });\r\n    \r\n    // Skip if fetching is disabled due to errors\r\n    if (fetchingDisabled) {\r\n      console.log('Skipping effect because fetching is disabled');\r\n      return;\r\n    }\r\n    \r\n    // If we're already redirecting, don't do anything\r\n    if (isRedirecting) {\r\n      console.log('Skipping effect because we are redirecting');\r\n      return;\r\n    }\r\n    \r\n    // Handle authenticated state\r\n    if (status === \"authenticated\" && session?.user?.email) {\r\n      // Only fetch if we haven't fetched for this email yet and we're not already loading\r\n      // Also check if we haven't exceeded the maximum fetch attempts\r\n      if (session.user.email !== fetchedForEmail && !loading && fetchAttempts < 3) {\r\n        console.log('Session authenticated, fetching assessment for:', session.user.email);\r\n        fetchAssessment(session.user.email);\r\n      }\r\n    } \r\n    // Handle unauthenticated state\r\n    else if (status === \"unauthenticated\") {\r\n      setAssessmentData(null);\r\n      setFetchedForEmail(null);\r\n      setIsInitialized(true);\r\n      console.log(\"User not authenticated, clearing assessment data.\");\r\n    } \r\n    // Handle loading state\r\n    else if (status === \"loading\" && !isInitialized) {\r\n      setLoading(true);\r\n    }\r\n\r\n    // Clean up loading state if needed\r\n    if (status !== \"loading\" && loading && !isInitialized && !session?.user?.email) {\r\n      setLoading(false);\r\n    }\r\n\r\n  }, [session, status, fetchedForEmail, isRedirecting, loading, fetchingDisabled, fetchAttempts]);\r\n\r\n  return (\r\n    <AssessmentContext.Provider value={{ \r\n      assessmentData, \r\n      loading, \r\n      error, \r\n      fetchAssessment,\r\n      isInitialized \r\n    }}>\r\n      {children}\r\n    </AssessmentContext.Provider>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AA2BA,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAyB;IAC7D,gBAAgB;IAChB,SAAS;IACT,OAAO;IACP,iBAAiB,WAAa;IAC9B,eAAe;AACjB;AAGO,MAAM,gBAAgB,IAAM,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AAGvC,MAAM,qBAAqB,CAAC,EAAE,QAAQ,EAA2B;IACtE,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,6EAA6E;IAC7E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,mEAAmE;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,6DAA6D;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,2CAA2C;IAC3C,MAAM,kBAAkB;QACtB,8BAA8B;QAC9B,IAAI,eAAe;QAEnB,QAAQ,GAAG,CAAC;QACZ,iBAAiB;QAEjB,IAAI;YACF,oBAAoB;YACpB,MAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;gBAAE,UAAU;YAAM;YAEhC,4BAA4B;YAC5B,kBAAkB;YAClB,mBAAmB;YAEnB,yBAAyB;YACzB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,2DAA2D;YAC3D,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,wDAAwD;QACxD,IAAI,kBAAkB;YACpB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,2BAA2B;QAC3B,MAAM,kBAAkB,gBAAgB;QACxC,iBAAiB;QAEjB,kDAAkD;QAClD,IAAI,kBAAkB,GAAG;YACvB,QAAQ,GAAG,CAAC;YACZ,oBAAoB;YACpB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,OAAO;YACV,SAAS;YACT,iBAAiB;YACjB,WAAW;YACX;QACF;QAEA,+DAA+D;QAC/D,IAAI,eAAe;YACjB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,MAAM,aAAa,EAAE,gBAAgB,GAAG,CAAC;YAErF,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,mBAAmB,QAAQ;YAEjF,0CAA0C;YAC1C,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,QAAQ,GAAG,CAAC;gBACZ,oBAAoB;gBACpB,qDAAqD;gBACrD;gBACA;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM;gBACpD,MAAM,eAAe,WAAW,SAAS,CAAC,WAAW,EAAE,SAAS,MAAM,EAAE;gBACxE,MAAM,IAAI,MAAM;YAClB;YAEA,kCAAkC;YAClC,iBAAiB;YAEjB,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC;YACZ,kBAAkB;YAClB,mBAAmB;QACrB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,mCAAmC;YAEjD,qDAAqD;YACrD,IAAI,eAAe,SAAS,IAAI,OAAO,CAAC,QAAQ,CAAC,iBAAiB;gBAChE,QAAQ,GAAG,CAAC;gBACZ,oBAAoB;YACtB;QACF,SAAU;YACR,WAAW;YACX,iBAAiB;QACnB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,+BAA+B;YACzC;YACA,SAAS,UAAU;gBAAE,GAAG,OAAO;gBAAE,MAAM;oBAAE,GAAG,SAAS,IAAI;oBAAE,cAAc;gBAAa;YAAE,IAAI;YAC5F;YACA;YACA;YACA;QACF;QAEA,6CAA6C;QAC7C,IAAI,kBAAkB;YACpB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,kDAAkD;QAClD,IAAI,eAAe;YACjB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,6BAA6B;QAC7B,IAAI,WAAW,mBAAmB,SAAS,MAAM,OAAO;YACtD,oFAAoF;YACpF,+DAA+D;YAC/D,IAAI,QAAQ,IAAI,CAAC,KAAK,KAAK,mBAAmB,CAAC,WAAW,gBAAgB,GAAG;gBAC3E,QAAQ,GAAG,CAAC,mDAAmD,QAAQ,IAAI,CAAC,KAAK;gBACjF,gBAAgB,QAAQ,IAAI,CAAC,KAAK;YACpC;QACF,OAEK,IAAI,WAAW,mBAAmB;YACrC,kBAAkB;YAClB,mBAAmB;YACnB,iBAAiB;YACjB,QAAQ,GAAG,CAAC;QACd,OAEK,IAAI,WAAW,aAAa,CAAC,eAAe;YAC/C,WAAW;QACb;QAEA,mCAAmC;QACnC,IAAI,WAAW,aAAa,WAAW,CAAC,iBAAiB,CAAC,SAAS,MAAM,OAAO;YAC9E,WAAW;QACb;IAEF,GAAG;QAAC;QAAS;QAAQ;QAAiB;QAAe;QAAS;QAAkB;KAAc;IAE9F,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;YACjC;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"!bg-white dark:!bg-gray-800 text-gray-900 dark:text-gray-100 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md !border !border-gray-200 dark:!border-gray-700 p-1 shadow-md\",\n          className\n        )}\n        style={{\n          backgroundColor: 'white !important',\n          border: '1px solid #e5e7eb !important',\n          opacity: '1 !important'\n        }}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4nBACA;YAEF,OAAO;gBACL,iBAAiB;gBACjB,QAAQ;gBACR,SAAS;YACX;YACC,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useSession, signOut } from 'next-auth/react';\r\nimport { MoonIcon, SunIcon, User, LogOut, Settings } from 'lucide-react';\r\nimport { useTheme } from 'next-themes';\r\nimport Image from 'next/image';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport Link from 'next/link';\r\n\r\nexport default function Header() {\r\n  const { data: session } = useSession();\r\n  const { theme, setTheme } = useTheme();\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\r\n      <div className=\"container flex h-16 items-center\">\r\n        <div className=\"flex w-full items-center justify-between\">\r\n          {/* First logo on the left */}\r\n          <div className=\"flex items-center gap-2 ml-8\">\r\n            <Link href=\"/\" aria-label=\"Go to home\">\r\n              <Image\r\n                src=\"/TM_Logo_Black.svg\"\r\n                alt=\"TalentMetrix Logo\"\r\n                width={150}\r\n                height={40}\r\n                className=\"h-8 w-auto dark:invert cursor-pointer\"\r\n                priority\r\n              />\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Second logo in center */}\r\n          <div className=\"absolute left-1/2 transform -translate-x-1/2 flex items-center\">\r\n            <Link href=\"/\" aria-label=\"Go to home\">\r\n              <Image\r\n                src=\"/Kaleidoscope-Logo_GreyText.svg\"\r\n                alt=\"Kaleidoscope Logo\"\r\n                width={150}\r\n                height={35}\r\n                className=\"h-8 w-auto dark:invert cursor-pointer\"\r\n                priority\r\n              />\r\n            </Link>\r\n          </div>\r\n\r\n          {/* User controls on the right */}\r\n          <div className=\"flex items-center gap-4\">\r\n            {/* Theme Toggle */}\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\r\n              className=\"h-9 w-9\"\r\n            >\r\n              <SunIcon className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n              <MoonIcon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n              <span className=\"sr-only\">Toggle theme</span>\r\n            </Button>\r\n\r\n            {/* User Menu */}\r\n            {session?.user && (\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button variant=\"ghost\" className=\"relative h-9 w-9 rounded-full\">\r\n                    <Avatar className=\"h-9 w-9\">\r\n                      <AvatarFallback>{session.user.name?.charAt(0) || 'U'}</AvatarFallback>\r\n                    </Avatar>\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent\r\n                  className=\"w-56 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg\"\r\n                  align=\"end\"\r\n                  forceMount\r\n                  style={{\r\n                    backgroundColor: 'white',\r\n                    border: '1px solid #e5e7eb',\r\n                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'\r\n                  }}\r\n                >\r\n                  <DropdownMenuLabel>\r\n                    <div className=\"flex flex-col space-y-1\">\r\n                      <p className=\"text-sm font-medium leading-none\">{session.user.name}</p>\r\n                      <p className=\"text-xs leading-none text-muted-foreground\">{session.user.email}</p>\r\n                    </div>\r\n                  </DropdownMenuLabel>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem>\r\n                    <User className=\"mr-2 h-4 w-4\" />\r\n                    <span>Profile</span>\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem asChild>\r\n                    <Link href=\"/settings\">\r\n                      <Settings className=\"mr-2 h-4 w-4\" />\r\n                      <span>Settings</span>\r\n                    </Link>\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem onClick={() => signOut()}>\r\n                    <LogOut className=\"mr-2 h-4 w-4\" />\r\n                    <span>Log out</span>\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAQA;AACA;AACA;AAjBA;;;;;;;;;;AAmBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEnC,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,cAAW;sCACxB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;kCAMd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,cAAW;sCACxB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;kCAMd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;gCACrD,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC,sMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;4BAI3B,SAAS,sBACR,8OAAC,qIAAA,CAAA,eAAY;;kDACX,8OAAC,qIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,8OAAC,2HAAA,CAAA,SAAM;gDAAC,WAAU;0DAChB,cAAA,8OAAC,2HAAA,CAAA,iBAAc;8DAAE,QAAQ,IAAI,CAAC,IAAI,EAAE,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;kDAIvD,8OAAC,qIAAA,CAAA,sBAAmB;wCAClB,WAAU;wCACV,OAAM;wCACN,UAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,QAAQ;4CACR,WAAW;wCACb;;0DAEA,8OAAC,qIAAA,CAAA,oBAAiB;0DAChB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAoC,QAAQ,IAAI,CAAC,IAAI;;;;;;sEAClE,8OAAC;4DAAE,WAAU;sEAA8C,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;0DAGjF,8OAAC,qIAAA,CAAA,wBAAqB;;;;;0DACtB,8OAAC,qIAAA,CAAA,mBAAgB;;kEACf,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,qIAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,8OAAC,qIAAA,CAAA,wBAAqB;;;;;0DACtB,8OAAC,qIAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;;kEACrC,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B", "debugId": null}}, {"offset": {"line": 1017, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/providers/auth-provider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { SessionProvider } from \"next-auth/react\";\r\n\r\nexport default function AuthProvider({\r\n  children,\r\n  session\r\n}: {\r\n  children: React.ReactNode;\r\n  session: any;\r\n}) {\r\n  return <SessionProvider session={session}>{children}</SessionProvider>;\r\n} "], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,aAAa,EACnC,QAAQ,EACR,OAAO,EAIR;IACC,qBAAO,8OAAC,8IAAA,CAAA,kBAAe;QAAC,SAAS;kBAAU;;;;;;AAC7C", "debugId": null}}]}