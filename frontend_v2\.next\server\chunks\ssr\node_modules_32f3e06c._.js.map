{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/styled-jsx/dist/index/index.js"], "sourcesContent": ["require('client-only');\nvar React = require('react');\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && process.env.NODE_ENV === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\n\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\n\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\n\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        })// Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        })// filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\nfunction useStyleRegistry() {\n    return React.useContext(StyleSheetContext);\n}\n\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\n\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\n"], "names": [], "mappings": ";AACA,IAAI;AAEJ,SAAS,sBAAuB,CAAC;IAAI,OAAO,KAAK,OAAO,MAAM,YAAY,aAAa,IAAI,IAAI;QAAE,WAAW;IAAE;AAAG;AAEjH,IAAI,iBAAiB,WAAW,GAAE,sBAAsB;AAExD;;;AAGA,GAAG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IACvC,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI;QACjC,IAAI,aAAa,KAAK,CAAC,EAAE;QACzB,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QACjD,WAAW,YAAY,GAAG;QAC1B,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QACjD,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAClD;AACJ;AACA,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IACtD,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IACzD,IAAI,aAAa,kBAAkB,aAAa;IAChD,OAAO;AACX;AACA,IAAI,SAAS,OAAO,YAAY,eAAe,QAAQ,GAAG,IAAI,oDAAyB;AACvF,IAAI,WAAW,SAAS,CAAC;IACrB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;AACjD;AACA,IAAI,aAAa,WAAW,GAAG;IAC3B,SAAS,WAAW,KAAK;QACrB,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,QAAQ,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,IAAI,eAAe,OAAO,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,SAAS;QAChN,YAAY,SAAS,OAAO;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,uBAAuB,GAAG,MAAM,OAAO;QAC5C,YAAY,OAAO,qBAAqB,WAAW;QACnD,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,OAAO,WAAW,eAAe,SAAS,aAAa,CAAC;QACnE,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,YAAY,CAAC,aAAa;IACxD;IACA,IAAI,SAAS,WAAW,SAAS;IACjC,OAAO,mBAAmB,GAAG,SAAS,oBAAoB,IAAI;QAC1D,YAAY,OAAO,SAAS,WAAW;QACvC,YAAY,IAAI,CAAC,WAAW,KAAK,GAAG;QACpC,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,MAAM;IACf;IACA,OAAO,kBAAkB,GAAG,SAAS;QACjC,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,OAAO,MAAM,GAAG,SAAS;QACrB,IAAI,QAAQ,IAAI;QAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,OAAO,WAAW,eAAe,IAAI,CAAC,iBAAiB,EAAE;YACzD,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK;YAC5C,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,IAAI,CAAC,QAAQ;YACtD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACzB,wCAAa;oBACT,QAAQ,IAAI,CAAC;gBACjB;gBACA,IAAI,CAAC,KAAK;gBACV,IAAI,CAAC,SAAS,GAAG;YACrB;YACA;QACJ;QACA,IAAI,CAAC,YAAY,GAAG;YAChB,UAAU,EAAE;YACZ,YAAY,SAAS,IAAI,EAAE,KAAK;gBAC5B,IAAI,OAAO,UAAU,UAAU;oBAC3B,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;wBACjC,SAAS;oBACb;gBACJ,OAAO;oBACH,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC7B,SAAS;oBACb;gBACJ;gBACA,OAAO;YACX;YACA,YAAY,SAAS,KAAK;gBACtB,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;YACzC;QACJ;IACJ;IACA,OAAO,cAAc,GAAG,SAAS,eAAe,GAAG;QAC/C,IAAI,IAAI,KAAK,EAAE;YACX,OAAO,IAAI,KAAK;QACpB;QACA,2CAA2C;QAC3C,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,IAAI;YAChD,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC,SAAS,KAAK,KAAK;gBAC3C,OAAO,SAAS,WAAW,CAAC,EAAE;YAClC;QACJ;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAChE;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,IAAI,EAAE,KAAK;QAC/C,YAAY,SAAS,OAAO;QAC5B,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM;YAC7C;YACA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM;YACnC,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,QAAQ,IAAI,CAAC,QAAQ;YACzB,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,MAAM,QAAQ,CAAC,MAAM;YACjC;YACA,kDAAkD;YAClD,4FAA4F;YAC5F,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,OAAO,CAAC;YACZ;QACJ,OAAO;YACH,IAAI,iBAAiB,IAAI,CAAC,KAAK,CAAC,MAAM;YACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM;QACxD;QACA,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,KAAK,EAAE,IAAI;QACjD,IAAI,IAAI,CAAC,iBAAiB,IAAI,OAAO,WAAW,aAAa;YACzD,IAAI,QAAQ,OAAO,WAAW,cAAc,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;YAC/E,IAAI,CAAC,KAAK,IAAI,IAAI;gBACd,OAAO,IAAI,CAAC,uBAAuB;YACvC;YACA,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,EAAE;gBACxB,iCAAiC;gBACjC,OAAO;YACX;YACA,MAAM,UAAU,CAAC;YACjB,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,qEAAqE;gBACrE,MAAM,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACnD;QACJ,OAAO;YACH,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;YAC3B,YAAY,KAAK,wBAAwB,QAAQ;YACjD,IAAI,WAAW,GAAG;QACtB;QACA,OAAO;IACX;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,KAAK;QACzC,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;YAC7B;QACJ;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,WAAW,CAAC,OAAO;QAC5B,OAAO;YACH,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;YAC3B,YAAY,KAAK,oBAAoB,QAAQ;YAC7C,IAAI,UAAU,CAAC,WAAW,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACxB;IACJ;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,GAAG;gBAC3B,OAAO,OAAO,IAAI,UAAU,CAAC,WAAW,CAAC;YAC7C;YACA,IAAI,CAAC,KAAK,GAAG,EAAE;QACnB,OAAO;YACH,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE;QACnC;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,OAAO,WAAW,aAAa;YAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;QACrC;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,KAAK,EAAE,GAAG;YACxC,IAAI,KAAK;gBACL,QAAQ,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,cAAc,CAAC,KAAK,QAAQ,EAAE,SAAS,IAAI;oBAC3F,OAAO,KAAK,OAAO,KAAK,MAAM,uBAAuB,GAAG,OAAO;gBACnE;YACJ,OAAO;gBACH,MAAM,IAAI,CAAC;YACf;YACA,OAAO;QACX,GAAG,EAAE;IACT;IACA,OAAO,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,SAAS,EAAE,aAAa;QACtE,IAAI,WAAW;YACX,YAAY,SAAS,YAAY;QACrC;QACA,IAAI,MAAM,SAAS,aAAa,CAAC;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,YAAY,CAAC,SAAS,IAAI,CAAC,MAAM;QACtD,IAAI,IAAI,GAAG;QACX,IAAI,YAAY,CAAC,UAAU,MAAM;QACjC,IAAI,WAAW;YACX,IAAI,WAAW,CAAC,SAAS,cAAc,CAAC;QAC5C;QACA,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;QACpE,IAAI,eAAe;YACf,KAAK,YAAY,CAAC,KAAK;QAC3B,OAAO;YACH,KAAK,WAAW,CAAC;QACrB;QACA,OAAO;IACX;IACA,aAAa,YAAY;QACrB;YACI,KAAK;YACL,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,WAAW;YAC3B;QACJ;KACH;IACD,OAAO;AACX;AACA,SAAS,YAAY,SAAS,EAAE,OAAO;IACnC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,iBAAiB,UAAU;IAC/C;AACJ;AAEA,SAAS,KAAK,GAAG;IACb,IAAI,SAAS,MAAM,IAAI,IAAI,MAAM;IACjC,MAAM,EAAE;QACJ,SAAS,SAAS,KAAK,IAAI,UAAU,CAAC,EAAE;IAC5C;IACA;;8DAE0D,GAAG,OAAO,WAAW;AACnF;AACA,IAAI,aAAa;AAEjB,IAAI,WAAW,SAAS,IAAI;IACxB,OAAO,KAAK,OAAO,CAAC,aAAa;AACrC;AACA,IAAI,QAAQ,CAAC;AACb;;;;CAIC,GAAG,SAAS,UAAU,MAAM,EAAE,KAAK;IAChC,IAAI,CAAC,OAAO;QACR,OAAO,SAAS;IACpB;IACA,IAAI,gBAAgB,OAAO;IAC3B,IAAI,MAAM,SAAS;IACnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACb,KAAK,CAAC,IAAI,GAAG,SAAS,WAAW,SAAS,MAAM;IACpD;IACA,OAAO,KAAK,CAAC,IAAI;AACrB;AACA;;;;CAIC,GAAG,SAAS,gBAAgB,EAAE,EAAE,GAAG;IAChC,IAAI,2BAA2B;IAC/B,uBAAuB;IACvB,6DAA6D;IAC7D,2EAA2E;IAC3E,IAAI,OAAO,WAAW,aAAa;QAC/B,MAAM,SAAS;IACnB;IACA,IAAI,QAAQ,KAAK;IACjB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACf,KAAK,CAAC,MAAM,GAAG,IAAI,OAAO,CAAC,0BAA0B;IACzD;IACA,OAAO,KAAK,CAAC,MAAM;AACvB;AAEA,SAAS,gBAAgB,QAAQ,EAAE,OAAO;IACtC,IAAI,YAAY,KAAK,GAAG,UAAU,CAAC;IACnC,OAAO,SAAS,GAAG,CAAC,SAAS,IAAI;QAC7B,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS;YAClE,IAAI,OAAO;YACX,wCAAwC;YACxC,KAAK,OAAO;YACZ,OAAO,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG;YACvC,yBAAyB;gBACrB,QAAQ;YACZ;QACJ;IACJ;AACJ;AACA,IAAI,qBAAqB,WAAW,GAAG;IACnC,SAAS,mBAAmB,KAAK;QAC7B,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,cAAc,IAAI,UAAU,EAAE,aAAa,gBAAgB,KAAK,IAAI,OAAO,aAAa,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,QAAQ;QACrO,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,WAAW;YACvC,MAAM;YACN,kBAAkB;QACtB;QACA,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,cAAc,OAAO,qBAAqB,WAAW;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;YAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,IAAI,SAAS,mBAAmB,SAAS;IACzC,OAAO,GAAG,GAAG,SAAS,IAAI,KAAK;QAC3B,IAAI,QAAQ,IAAI;QAChB,IAAI,cAAc,IAAI,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,iBAAiB,GAAG,MAAM,OAAO,CAAC,MAAM,QAAQ;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB;YACtD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,OAAO,WAAW,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE;YACpD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB;YACxC,IAAI,CAAC,gBAAgB,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;gBAC9E,GAAG,CAAC,QAAQ,GAAG;gBACf,OAAO;YACX,GAAG,CAAC;QACR;QACA,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,OAAO,EAAE,QAAQ,IAAI,KAAK;QAC7E,+CAA+C;QAC/C,IAAI,WAAW,IAAI,CAAC,gBAAgB,EAAE;YAClC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;YAClC;QACJ;QACA,IAAI,UAAU,MAAM,GAAG,CAAC,SAAS,IAAI;YACjC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;QACnC,GAAE,2BAA2B;SAC5B,MAAM,CAAC,SAAS,KAAK;YAClB,OAAO,UAAU,CAAC;QACtB;QACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG;QACzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG;IACrC;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK;QACjC,IAAI,QAAQ,IAAI;QAChB,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,OAAO,OAAO;QAC/C,UAAU,WAAW,IAAI,CAAC,gBAAgB,EAAE,eAAe,UAAU;QACrE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;QAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,GAAG;YACpC,IAAI,gBAAgB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;YACjE,IAAI,eAAe;gBACf,cAAc,UAAU,CAAC,WAAW,CAAC;gBACrC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;YACpC,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;oBACzC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;gBACnC;gBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACjC;YACA,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QACzC;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK,EAAE,SAAS;QAC5C,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK;QACjB,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,aAAa,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,SAAS,OAAO;YAClF,OAAO;gBACH;gBACA,MAAM,WAAW,CAAC,QAAQ;aAC7B;QACL,KAAK,EAAE;QACP,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ;QACnC,OAAO,WAAW,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,OAAO;YACpE,OAAO;gBACH;gBACA,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,KAAK;oBACtC,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;gBAClC,GAAG,IAAI,CAAC,MAAM,iBAAiB,GAAG,KAAK;aAC1C;QACL,GAAE,yBAAyB;SAC1B,MAAM,CAAC,SAAS,IAAI;YACjB,OAAO,QAAQ,IAAI,CAAC,EAAE;QAC1B;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,OAAO;QACnC,OAAO,gBAAgB,IAAI,CAAC,QAAQ,IAAI;IAC5C;IACA,OAAO,aAAa,GAAG,SAAS,cAAc,KAAK;QAC/C,IAAI,MAAM,MAAM,QAAQ,EAAE,UAAU,MAAM,OAAO,EAAE,KAAK,MAAM,EAAE;QAChE,IAAI,SAAS;YACT,IAAI,UAAU,UAAU,IAAI;YAC5B,OAAO;gBACH,SAAS;gBACT,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,IAAI;oBAC7C,OAAO,gBAAgB,SAAS;gBACpC,KAAK;oBACD,gBAAgB,SAAS;iBAC5B;YACL;QACJ;QACA,OAAO;YACH,SAAS,UAAU;YACnB,OAAO,MAAM,OAAO,CAAC,OAAO,MAAM;gBAC9B;aACH;QACL;IACJ;IACA;;;;GAID,GAAG,OAAO,gBAAgB,GAAG,SAAS;QACjC,IAAI,WAAW,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACpE,OAAO,SAAS,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;YACxC,IAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,CAAC;YAC1B,GAAG,CAAC,GAAG,GAAG;YACV,OAAO;QACX,GAAG,CAAC;IACR;IACA,OAAO;AACX;AACA,SAAS,UAAU,SAAS,EAAE,OAAO;IACjC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,yBAAyB,UAAU;IACvD;AACJ;AACA,IAAI,oBAAoB,WAAW,GAAG,MAAM,aAAa,CAAC;AAC1D,kBAAkB,WAAW,GAAG;AAChC,SAAS;IACL,OAAO,IAAI;AACf;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,qBAAqB,MAAM,QAAQ,EAAE,WAAW,MAAM,QAAQ;IAClE,IAAI,eAAe,MAAM,UAAU,CAAC;IACpC,IAAI,MAAM,MAAM,QAAQ,CAAC;QACrB,OAAO,gBAAgB,sBAAsB;IACjD,IAAI,WAAW,GAAG,CAAC,EAAE;IACrB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,QAAQ,EAAE;QACrF,OAAO;IACX,GAAG;AACP;AACA,SAAS;IACL,OAAO,MAAM,UAAU,CAAC;AAC5B;AAEA,wFAAwF;AACxF,sDAAsD;AACtD,IAAI,qBAAqB,cAAc,CAAC,UAAU,CAAC,kBAAkB,IAAI,cAAc,CAAC,UAAU,CAAC,eAAe;AAClH,IAAI,kBAAkB,OAAO,WAAW,cAAc,wBAAwB;AAC9E,SAAS,SAAS,KAAK;IACnB,IAAI,WAAW,kBAAkB,kBAAkB;IACnD,oDAAoD;IACpD,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,IAAI,OAAO,WAAW,aAAa;QAC/B,SAAS,GAAG,CAAC;QACb,OAAO;IACX;IACA,mBAAmB;QACf,SAAS,GAAG,CAAC;QACb,OAAO;YACH,SAAS,MAAM,CAAC;QACpB;IACJ,wEAAwE;IACxE,GAAG;QACC,MAAM,EAAE;QACR,OAAO,MAAM,OAAO;KACvB;IACD,OAAO;AACX;AACA,SAAS,OAAO,GAAG,SAAS,IAAI;IAC5B,OAAO,KAAK,GAAG,CAAC,SAAS,OAAO;QAC5B,IAAI,SAAS,OAAO,CAAC,EAAE;QACvB,IAAI,QAAQ,OAAO,CAAC,EAAE;QACtB,OAAO,UAAU,QAAQ;IAC7B,GAAG,IAAI,CAAC;AACZ;AAEA,QAAQ,aAAa,GAAG;AACxB,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,KAAK,GAAG;AAChB,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/styled-jsx/style.js"], "sourcesContent": ["module.exports = require('./dist/index').style\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,wGAAwB,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ElementRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,kNAAc,aAAA,EAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;YAEtB,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;YAEvD,MAAM,WAAA,GAAc,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/%40radix-ui/number/src/number.ts"], "sourcesContent": ["function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,MAAM,KAAA,EAAe,CAAC,KAAK,GAAG,CAAA,EAA6B;IAClE,OAAO,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,KAAK,CAAC;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/%40radix-ui/react-select/node_modules/%40radix-ui/react-slot/src/slot.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlot(ownerName: string) {\n  const SlotClone = createSlotClone(ownerName);\n  const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n\n    if (slottable) {\n      // the new element to render is the one passed as a child of `Slottable`\n      const newElement = slottable.props.children;\n\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          // because the new element will be the one rendered, we are only interested\n          // in grabbing its children (`newElement.props.children`)\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement)\n            ? (newElement.props as { children: React.ReactNode }).children\n            : null;\n        } else {\n          return child;\n        }\n      });\n\n      return (\n        <SlotClone {...slotProps} ref={forwardedRef}>\n          {React.isValidElement(newElement)\n            ? React.cloneElement(newElement, undefined, newChildren)\n            : null}\n        </SlotClone>\n      );\n    }\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {children}\n      </SlotClone>\n    );\n  });\n\n  Slot.displayName = `${ownerName}.Slot`;\n  return Slot;\n}\n\nconst Slot = createSlot('Slot');\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ function createSlotClone(ownerName: string) {\n  const SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenRef = React.isValidElement(children) ? getElementRef(children) : undefined;\n    const ref = useComposedRefs(childrenRef, forwardedRef);\n\n    if (React.isValidElement(children)) {\n      const props = mergeProps(slotProps, children.props as AnyProps);\n      // do not pass ref to React.Fragment for React 19 compatibility\n      if (children.type !== React.Fragment) {\n        props.ref = ref;\n      }\n      return React.cloneElement(children, props);\n    }\n\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLOTTABLE_IDENTIFIER = Symbol('radix.slottable');\n\ninterface SlottableProps {\n  children: React.ReactNode;\n}\n\ninterface SlottableComponent extends React.FC<SlottableProps> {\n  __radixId: symbol;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlottable(ownerName: string) {\n  const Slottable: SlottableComponent = ({ children }) => {\n    return <>{children}</>;\n  };\n  Slottable.displayName = `${ownerName}.Slottable`;\n  Slottable.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable;\n}\n\nconst Slottable = createSlottable('Slottable');\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<SlottableProps, typeof Slottable> {\n  return (\n    React.isValidElement(child) &&\n    typeof child.type === 'function' &&\n    '__radixId' in child.type &&\n    child.type.__radixId === SLOTTABLE_IDENTIFIER\n  );\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Slot as Root,\n};\nexport type { SlotProps };\n"], "names": ["Fragment", "Slot", "props", "Slottable"], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,uBAAuB;AAmCxB,SAmEG,YAAAA,WAnEH;;;;;AAAA,uBAAA;AAzB0B,SAAS,WAAW,SAAA,EAAmB;IACvE,MAAM,YAAY,aAAA,GAAA,gBAAgB,SAAS;IAC3C,MAAMC,kNAAa,aAAA,EAAmC,CAAC,OAAO,iBAAiB;QAC7E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QACnC,MAAM,sNAAsB,WAAA,CAAS,OAAA,CAAQ,QAAQ;QACrD,MAAM,YAAY,cAAc,IAAA,CAAK,WAAW;QAEhD,IAAI,WAAW;YAEb,MAAM,aAAa,UAAU,KAAA,CAAM,QAAA;YAEnC,MAAM,cAAc,cAAc,GAAA,CAAI,CAAC,UAAU;gBAC/C,IAAI,UAAU,WAAW;oBAGvB,0MAAU,WAAA,CAAS,KAAA,CAAM,UAAU,IAAI,EAAG,CAAA,6MAAa,WAAA,CAAS,IAAA,CAAK,IAAI;oBACzE,iNAAa,iBAAA,EAAe,UAAU,IACjC,WAAW,KAAA,CAAwC,QAAA,GACpD;gBACN,OAAO;oBACL,OAAO;gBACT;YACF,CAAC;YAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAA;gBAAW,GAAG,SAAA;gBAAW,KAAK;gBAC5B,oNAAM,iBAAA,EAAe,UAAU,8MACtB,eAAA,EAAa,YAAY,KAAA,GAAW,WAAW,IACrD;YAAA,CACN;QAEJ;QAEA,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAA;YAAW,GAAG,SAAA;YAAW,KAAK;YAC5B;QAAA,CACH;IAEJ,CAAC;IAEDA,MAAK,WAAA,GAAc,GAAG,SAAS,CAAA,KAAA,CAAA;IAC/B,OAAOA;AACT;AAEA,IAAM,OAAO,aAAA,GAAA,WAAW,MAAM;AAAA,uBAAA;AAUH,SAAS,gBAAgB,SAAA,EAAmB;IACrE,MAAM,sNAAkB,aAAA,EAAgC,CAAC,OAAO,iBAAiB;QAC/E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QACnC,MAAM,wNAAoB,iBAAA,EAAe,QAAQ,IAAI,cAAc,QAAQ,IAAI,KAAA;QAC/E,MAAM,sLAAM,kBAAA,EAAgB,aAAa,YAAY;QAErD,8MAAU,iBAAA,EAAe,QAAQ,GAAG;YAClC,MAAMC,SAAQ,WAAW,WAAW,SAAS,KAAiB;YAE9D,IAAI,SAAS,IAAA,0MAAe,YAAA,EAAU;gBACpCA,OAAM,GAAA,GAAM;YACd;YACA,iNAAa,eAAA,EAAa,UAAUA,MAAK;QAC3C;QAEA,6MAAa,WAAA,CAAS,KAAA,CAAM,QAAQ,IAAI,0MAAU,WAAA,CAAS,IAAA,CAAK,IAAI,IAAI;IAC1E,CAAC;IAED,UAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpC,OAAO;AACT;AAMA,IAAM,uBAAuB,OAAO,iBAAiB;AAAA,uBAAA;AAUnB,SAAS,gBAAgB,SAAA,EAAmB;IAC5E,MAAMC,aAAgC,CAAC,EAAE,QAAA,CAAS,CAAA,KAAM;QACtD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,0NAAAH,WAAAA,EAAA;YAAG;QAAA,CAAS;IACrB;IACAG,WAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpCA,WAAU,SAAA,GAAY;IACtB,OAAOA;AACT;AAEA,IAAM,YAAY,aAAA,GAAA,gBAAgB,WAAW;AAM7C,SAAS,YACP,KAAA,EAC+D;IAC/D,QACQ,0NAAA,EAAe,KAAK,KAC1B,OAAO,MAAM,IAAA,KAAS,cACtB,eAAe,MAAM,IAAA,IACrB,MAAM,IAAA,CAAK,SAAA,KAAc;AAE7B;AAEA,SAAS,WAAW,SAAA,EAAqB,UAAA,EAAsB;IAE7D,MAAM,gBAAgB;QAAE,GAAG,UAAA;IAAW;IAEtC,IAAA,MAAW,YAAY,WAAY;QACjC,MAAM,gBAAgB,SAAA,CAAU,QAAQ,CAAA;QACxC,MAAM,iBAAiB,UAAA,CAAW,QAAQ,CAAA;QAE1C,MAAM,YAAY,WAAW,IAAA,CAAK,QAAQ;QAC1C,IAAI,WAAW;YAEb,IAAI,iBAAiB,gBAAgB;gBACnC,aAAA,CAAc,QAAQ,CAAA,GAAI,CAAA,GAAI,SAAoB;oBAChD,MAAM,SAAS,eAAe,GAAG,IAAI;oBACrC,cAAc,GAAG,IAAI;oBACrB,OAAO;gBACT;YACF,OAAA,IAES,eAAe;gBACtB,aAAA,CAAc,QAAQ,CAAA,GAAI;YAC5B;QACF,OAAA,IAES,aAAa,SAAS;YAC7B,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAE,GAAG,aAAA;gBAAe,GAAG,cAAA;YAAe;QAClE,OAAA,IAAW,aAAa,aAAa;YACnC,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAC;gBAAe,cAAc;aAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QACpF;IACF;IAEA,OAAO;QAAE,GAAG,SAAA;QAAW,GAAG,aAAA;IAAc;AAC1C;AAOA,SAAS,cAAc,OAAA,EAA6B;IAElD,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAQ,QAAQ,KAAA,CAAuC,GAAA;IACzD;IAGA,OAAQ,QAAQ,KAAA,CAAuC,GAAA,IAAQ,QAAgB,GAAA;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/%40radix-ui/react-use-previous/src/use-previous.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "names": [], "mappings": ";;;;AAAA,YAAY,WAAW;;AAEvB,SAAS,YAAe,KAAA,EAAU;IAChC,MAAM,gNAAY,SAAA,EAAO;QAAE;QAAO,UAAU;IAAM,CAAC;IAKnD,iNAAa,UAAA,EAAQ,MAAM;QACzB,IAAI,IAAI,OAAA,CAAQ,KAAA,KAAU,OAAO;YAC/B,IAAI,OAAA,CAAQ,QAAA,GAAW,IAAI,OAAA,CAAQ,KAAA;YACnC,IAAI,OAAA,CAAQ,KAAA,GAAQ;QACtB;QACA,OAAO,IAAI,OAAA,CAAQ,QAAA;IACrB,GAAG;QAAC,KAAK;KAAC;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/%40radix-ui/react-visually-hidden/src/visually-hidden.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: 'absolute',\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: 'hidden',\n  clip: 'rect(0, 0, 0, 0)',\n  whiteSpace: 'nowrap',\n  wordWrap: 'normal',\n}) satisfies React.CSSProperties;\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n  //\n  VISUALLY_HIDDEN_STYLES,\n};\nexport type { VisuallyHiddenProps };\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AA6BpB;;;;AAvBN,IAAM,yBAAyB,OAAO,MAAA,CAAO;IAAA,qFAAA;IAE3C,UAAU;IACV,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ,CAAA;IACR,UAAU;IACV,MAAM;IACN,YAAY;IACZ,UAAU;AACZ,CAAC;AAED,IAAM,OAAO;AAMb,IAAM,2NAAuB,aAAA,EAC3B,CAAC,OAAO,iBAAiB;IACvB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,OAAO;YAAE,GAAG,sBAAA;YAAwB,GAAG,MAAM,KAAA;QAAM;IAAA;AAGzD;AAGF,eAAe,WAAA,GAAc;AAI7B,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/%40radix-ui/react-select/src/select.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VISUALLY_HIDDEN_STYLES } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value: string | undefined;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface ControlledClearableSelectProps {\n  value: string | undefined;\n  defaultValue?: never;\n  onValueChange: (value: string | undefined) => void;\n}\n\ninterface ControlledUnclearableSelectProps {\n  value: string;\n  defaultValue?: never;\n  onValueChange: (value: string) => void;\n}\n\ninterface UncontrolledSelectProps {\n  value?: never;\n  defaultValue?: string;\n  onValueChange?: {\n    (value: string): void;\n    (value: string | undefined): void;\n  };\n}\n\ntype SelectControlProps =\n  | ControlledClearableSelectProps\n  | ControlledUnclearableSelectProps\n  | UncontrolledSelectProps;\n\ninterface SelectSharedProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n  form?: string;\n}\n\n// TODO: Should improve typing somewhat, but this would be a breaking change.\n// Consider using in the next major version (along with some testing to be sure\n// it works as expected and doesn't cause problems)\ntype _FutureSelectProps = SelectSharedProps & SelectControlProps;\n\ntype SelectProps = SelectSharedProps & {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n};\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange as any,\n    caller: SELECT_NAME,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? form || !!trigger.closest('form') : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <SelectBubbleInput\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n            form={form}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </SelectBubbleInput>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = (pointerEvent?: React.MouseEvent | React.PointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY),\n        };\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={shouldShowPlaceholder(context.value) ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n\n            // Open on click when using a touch or pen device\n            if (pointerTypeRef.current !== 'mouse') {\n              handleOpen(event);\n            }\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            pointerTypeRef.current = event.pointerType;\n\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click); also not for touch\n            // devices because that would open the menu on scroll. (pen devices behave as touch on iOS).\n            if (event.button === 0 && event.ctrlKey === false && event.pointerType === 'mouse') {\n              handleOpen(event);\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder = '', ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {shouldShowPlaceholder(context.value) ? <>{placeholder}</> : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst Slot = createSlot('SelectContent.RemoveScroll');\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem =\n          items.length > 0 && selectedItem === items[items.length - 1]!.ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0]!.ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              // Viewport should only be scrollable in the vertical direction.\n              // This won't work in vertical writing modes, so we'll need to\n              // revisit this if/when that is supported\n              // https://developer.chrome.com/blog/vertical-form-controls\n              overflow: 'hidden auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    if (value === '') {\n      throw new Error(\n        'A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.'\n      );\n    }\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onClick={composeEventHandlers(itemProps.onClick, () => {\n              // Open on click when using a touch or pen device\n              if (pointerTypeRef.current !== 'mouse') handleSelect();\n            })}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, () => {\n              // Using a mouse you should be able to do pointer down, move through\n              // the list, and release the pointer over the item to select it.\n              if (pointerTypeRef.current === 'mouse') handleSelect();\n            })}\n            onPointerDown={composeEventHandlers(itemProps.onPointerDown, (event) => {\n              pointerTypeRef.current = event.pointerType;\n            })}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              // Remember pointer type when sliding over to this item from another one\n              pointerTypeRef.current = event.pointerType;\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else if (pointerTypeRef.current === 'mouse') {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SelectBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.select>;\ninterface SwitchBubbleInputProps extends InputProps {}\n\nconst SelectBubbleInput = React.forwardRef<HTMLSelectElement, SwitchBubbleInputProps>(\n  ({ __scopeSelect, value, ...props }: ScopedProps<SwitchBubbleInputProps>, forwardedRef) => {\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much as\n     * possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange`\n     * event. Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use visually hidden styles rather than `display: \"none\"` because\n     * Safari autofill won't work otherwise.\n     */\n    return (\n      <Primitive.select\n        {...props}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n        ref={composedRefs}\n        defaultValue={value}\n      />\n    );\n  }\n);\n\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction shouldShowPlaceholder(value?: string) {\n  return value === '' || value === undefined;\n}\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n"], "names": ["handleScroll", "canScrollUp", "canScrollDown", "Root", "Content", "Arrow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,aAAa;AACtB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,oBAAoB;AAC7B,SAAS,wBAAwB;AACjC,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAC3B,SAAS,aAAa;AACtB,YAAY,qBAAqB;AAEjC,SAAS,UAAU,uBAAuB;AAC1C,SAAS,iBAAiB;AAC1B,SAAS,kBAAkB;AAC3B,SAAS,sBAAsB;AAC/B,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,mBAAmB;AAC5B,SAAS,8BAA8B;AACvC,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAiLnB,SA0LsC,UA1LtC,KAkBA,YAlBA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3KV,IAAM,YAAY;IAAC;IAAK;IAAS;IAAW,WAAW;CAAA;AACvD,IAAM,iBAAiB;IAAC;IAAK,OAAO;CAAA;AAMpC,IAAM,cAAc;AAGpB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,8KAAI,mBAAA,EAGzD,WAAW;AAGb,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,2KAAI,qBAAA,EAAmB,aAAa;IAC/E;uKACA,oBAAA;CACD;AACD,IAAM,kBAAiB,0LAAA,CAAkB;AAoBzC,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAQ9F,IAAM,CAAC,6BAA6B,6BAA6B,CAAA,GAC/D,oBAAqD,WAAW;AAoDlE,IAAM,SAAgC,CAAC,UAAoC;IACzE,MAAM,EACJ,aAAA,EACA,QAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,OAAO,SAAA,EACP,YAAA,EACA,aAAA,EACA,GAAA,EACA,IAAA,EACA,YAAA,EACA,QAAA,EACA,QAAA,EACA,IAAA,EACF,GAAI;IACJ,MAAM,cAAc,eAAe,aAAa;IAChD,MAAM,CAAC,SAAS,UAAU,CAAA,6MAAU,WAAA,EAAsC,IAAI;IAC9E,MAAM,CAAC,WAAW,YAAY,CAAA,OAAU,iNAAA,EAAoC,IAAI;IAChF,MAAM,CAAC,sBAAsB,uBAAuB,CAAA,6MAAU,WAAA,EAAS,KAAK;IAC5E,MAAM,sLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,MAAM,OAAO,CAAA,OAAI,gNAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,CAAC,OAAO,QAAQ,CAAA,gMAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,aAAa;QACb,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,qOAAiC,SAAA,EAAwC,IAAI;IAGnF,MAAM,gBAAgB,UAAU,QAAQ,CAAC,CAAC,QAAQ,OAAA,CAAQ,MAAM,IAAI;IACpE,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,6MAAU,WAAA,EAAS,aAAA,GAAA,IAAI,IAAkB,CAAC;IAOtF,MAAM,kBAAkB,MAAM,IAAA,CAAK,gBAAgB,EAChD,GAAA,CAAI,CAAC,SAAW,OAAO,KAAA,CAAM,KAAK,EAClC,IAAA,CAAK,GAAG;IAEX,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,qKAAiB,OAAA,EAAhB;QAAsB,GAAG,WAAA;QACxB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAC,gBAAA;YACC;YACA,OAAO;YACP;YACA,iBAAiB;YACjB;YACA,mBAAmB;YACnB;YACA,8BAA8B;YAC9B,WAAW,2KAAA,CAAM;YACjB;YACA,eAAe;YACf;YACA,cAAc;YACd,KAAK;YACL;YACA;YAEA,UAAA;gBAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;oBAAoB,OAAO;oBAC1B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,6BAAA;wBACC,OAAO,MAAM,aAAA;wBACb,6NAAyB,cAAA,EAAY,CAAC,WAAW;4BAC/C,oBAAoB,CAAC,OAAS,IAAI,IAAI,IAAI,EAAE,GAAA,CAAI,MAAM,CAAC;wBACzD,GAAG,CAAC,CAAC;wBACL,gOAA4B,cAAA,EAAY,CAAC,WAAW;4BAClD,oBAAoB,CAAC,SAAS;gCAC5B,MAAM,aAAa,IAAI,IAAI,IAAI;gCAC/B,WAAW,MAAA,CAAO,MAAM;gCACxB,OAAO;4BACT,CAAC;wBACH,GAAG,CAAC,CAAC;wBAEJ;oBAAA;gBACH,CACF;gBAEC,gBACC,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAC,mBAAA;oBAEC,eAAW;oBACX;oBACA,UAAU,CAAA;oBACV;oBACA;oBACA;oBAEA,UAAU,CAAC,QAAU,SAAS,MAAM,MAAA,CAAO,KAAK;oBAChD;oBACA;oBAEC,UAAA;wBAAA,UAAU,KAAA,IAAY,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,UAAA;4BAAO,OAAM;wBAAA,CAAG,IAAK;wBAC5C,MAAM,IAAA,CAAK,gBAAgB;qBAAA;gBAAA,GAbvB,mBAeL;aAAA;QAAA;IACN,CACF;AAEJ;AAEA,OAAO,WAAA,GAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,0NAAsB,aAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,aAAA,EAAe,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IAC7D,MAAM,cAAc,eAAe,aAAa;IAChD,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,aAAa,QAAQ,QAAA,IAAY;IACvC,MAAM,+LAAe,kBAAA,EAAgB,cAAc,QAAQ,eAAe;IAC1E,MAAM,WAAW,cAAc,aAAa;IAC5C,MAAM,iBAAuB,mNAAA,EAA0C,OAAO;IAE9E,MAAM,CAAC,WAAW,uBAAuB,cAAc,CAAA,GAAI,mBAAmB,CAAC,WAAW;QACxF,MAAM,eAAe,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;QAC/D,MAAM,cAAc,aAAa,IAAA,CAAK,CAAC,OAAS,KAAK,KAAA,KAAU,QAAQ,KAAK;QAC5E,MAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;QAC/D,IAAI,aAAa,KAAA,GAAW;YAC1B,QAAQ,aAAA,CAAc,SAAS,KAAK;QACtC;IACF,CAAC;IAED,MAAM,aAAa,CAAC,iBAAyD;QAC3E,IAAI,CAAC,YAAY;YACf,QAAQ,YAAA,CAAa,IAAI;YAEzB,eAAe;QACjB;QAEA,IAAI,cAAc;YAChB,QAAQ,wBAAA,CAAyB,OAAA,GAAU;gBACzC,GAAG,KAAK,KAAA,CAAM,aAAa,KAAK;gBAChC,GAAG,KAAK,KAAA,CAAM,aAAa,KAAK;YAClC;QACF;IACF;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,qKAAiB,SAAA,EAAhB;QAAuB,SAAO;QAAE,GAAG,WAAA;QAClC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,MAAK;YACL,iBAAe,QAAQ,SAAA;YACvB,iBAAe,QAAQ,IAAA;YACvB,iBAAe,QAAQ,QAAA;YACvB,qBAAkB;YAClB,KAAK,QAAQ,GAAA;YACb,cAAY,QAAQ,IAAA,GAAO,SAAS;YACpC,UAAU;YACV,iBAAe,aAAa,KAAK,KAAA;YACjC,oBAAkB,sBAAsB,QAAQ,KAAK,IAAI,KAAK,KAAA;YAC7D,GAAG,YAAA;YACJ,KAAK;YAEL,0KAAS,uBAAA,EAAqB,aAAa,OAAA,EAAS,CAAC,UAAU;gBAM7D,MAAM,aAAA,CAAc,KAAA,CAAM;gBAG1B,IAAI,eAAe,OAAA,KAAY,SAAS;oBACtC,WAAW,KAAK;gBAClB;YACF,CAAC;YACD,gLAAe,uBAAA,EAAqB,aAAa,aAAA,EAAe,CAAC,UAAU;gBACzE,eAAe,OAAA,GAAU,MAAM,WAAA;gBAI/B,MAAM,SAAS,MAAM,MAAA;gBACrB,IAAI,OAAO,iBAAA,CAAkB,MAAM,SAAS,GAAG;oBAC7C,OAAO,qBAAA,CAAsB,MAAM,SAAS;gBAC9C;gBAKA,IAAI,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,SAAS,MAAM,WAAA,KAAgB,SAAS;oBAClF,WAAW,KAAK;oBAEhB,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;YACD,YAAW,uLAAA,EAAqB,aAAa,SAAA,EAAW,CAAC,UAAU;gBACjE,MAAM,gBAAgB,UAAU,OAAA,KAAY;gBAC5C,MAAM,gBAAgB,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,OAAA;gBAC7D,IAAI,CAAC,iBAAiB,MAAM,GAAA,CAAI,MAAA,KAAW,EAAG,CAAA,sBAAsB,MAAM,GAAG;gBAC7E,IAAI,iBAAiB,MAAM,GAAA,KAAQ,IAAK,CAAA;gBACxC,IAAI,UAAU,QAAA,CAAS,MAAM,GAAG,GAAG;oBACjC,WAAW;oBACX,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;QAAA;IACH,CACF;AAEJ;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,aAAa;AAQnB,IAAM,wNAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IAEtD,MAAM,EAAE,aAAA,EAAe,SAAA,EAAW,KAAA,EAAO,QAAA,EAAU,cAAc,EAAA,EAAI,GAAG,WAAW,CAAA,GAAI;IACvF,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,EAAE,4BAAA,CAA6B,CAAA,GAAI;IACzC,MAAM,cAAc,aAAa,KAAA;IACjC,MAAM,+LAAe,kBAAA,EAAgB,cAAc,QAAQ,iBAAiB;IAE5E,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,6BAA6B,WAAW;IAC1C,GAAG;QAAC;QAA8B,WAAW;KAAC;IAE9C,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;QACE,GAAG,UAAA;QACJ,KAAK;QAGL,OAAO;YAAE,eAAe;QAAO;QAE9B,UAAA,sBAAsB,QAAQ,KAAK,IAAI,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;YAAG,UAAA;QAAA,CAAY,IAAM;IAAA;AAGnE;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,YAAY;AAKlB,IAAM,uNAAmB,aAAA,EACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EAAE,aAAA,EAAe,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;IAClD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kLAAA,CAAU,IAAA,EAAV;QAAe,eAAW;QAAE,GAAG,SAAA;QAAW,KAAK;QAC7C,UAAA,YAAY;IAAA,CACf;AAEJ;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,cAAc;AAWpB,IAAM,eAA4C,CAAC,UAA0C;IAC3F,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,qKAAC,SAAA,EAAA;QAAgB,SAAO;QAAE,GAAG,KAAA;IAAA,CAAO;AAC7C;AAEA,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAKrB,IAAM,0NAAsB,aAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,CAAC,UAAU,WAAW,CAAA,6MAAU,WAAA,CAA2B;IAGjE,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,YAAY,IAAI,iBAAiB,CAAC;IACpC,GAAG,CAAC,CAAC;IAEL,IAAI,CAAC,QAAQ,IAAA,EAAM;QACjB,MAAM,OAAO;QACb,OAAO,wNACM,eAAA,EACP,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;YAAsB,OAAO,MAAM,aAAA;YAClC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO,MAAM,aAAA;gBAC5B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,OAAA;oBAAK,UAAA,MAAM,QAAA;gBAAA,CAAS;YAAA,CACvB;QAAA,CACF,GACA,QAEF;IACN;IAEA,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,mBAAA;QAAmB,GAAG,KAAA;QAAO,KAAK;IAAA,CAAc;AAC1D;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,iBAAiB;AAqBvB,IAAM,CAAC,uBAAuB,uBAAuB,CAAA,GACnD,oBAA+C,YAAY;AAE7D,IAAM,oBAAoB;AA8B1B,IAAM,gOAAO,aAAA,EAAW,4BAA4B;AAEpD,IAAM,8NAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EACJ,aAAA,EACA,WAAW,cAAA,EACX,gBAAA,EACA,eAAA,EACA,oBAAA,EAAA,EAAA;IAAA,sBAAA;IAGA,IAAA,EACA,UAAA,EACA,KAAA,EACA,WAAA,EACA,YAAA,EACA,iBAAA,EACA,gBAAA,EACA,MAAA,EACA,gBAAA,EACA,eAAA,EAAA,EAAA;IAEA,GAAG,cACL,GAAI;IACJ,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,CAAC,SAAS,UAAU,CAAA,6MAAU,WAAA,EAA0C,IAAI;IAClF,MAAM,CAAC,UAAU,WAAW,CAAA,OAAU,iNAAA,EAAuC,IAAI;IACjF,MAAM,+LAAe,kBAAA,EAAgB,cAAc,CAAC,OAAS,WAAW,IAAI,CAAC;IAC7E,MAAM,CAAC,cAAc,eAAe,CAAA,6MAAU,WAAA,EAAmC,IAAI;IACrF,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,6MAAU,WAAA,EACpD;IAEF,MAAM,WAAW,cAAc,aAAa;IAC5C,MAAM,CAAC,cAAc,eAAe,CAAA,6MAAU,WAAA,EAAS,KAAK;IAC5D,MAAM,kOAA+B,UAAA,EAAO,KAAK;8MAG3C,YAAA,EAAU,MAAM;QACpB,IAAI,QAAS,CAAA,oKAAO,cAAA,EAAW,OAAO;IACxC,GAAG;QAAC,OAAO;KAAC;IAIZ,CAAA,GAAA,2KAAA,CAAA,iBAAA,CAAe;IAEf,MAAM,cAAmB,uNAAA,EACvB,CAAC,eAA0C;QACzC,MAAM,CAAC,WAAW,GAAG,SAAS,CAAA,GAAI,SAAS,EAAE,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAO;QAC3E,MAAM,CAAC,QAAQ,CAAA,GAAI,UAAU,KAAA,CAAM,CAAA,CAAE;QAErC,MAAM,6BAA6B,SAAS,aAAA;QAC5C,KAAA,MAAW,aAAa,WAAY;YAElC,IAAI,cAAc,2BAA4B,CAAA;YAC9C,WAAW,eAAe;gBAAE,OAAO;YAAU,CAAC;YAE9C,IAAI,cAAc,aAAa,SAAU,CAAA,SAAS,SAAA,GAAY;YAC9D,IAAI,cAAc,YAAY,SAAU,CAAA,SAAS,SAAA,GAAY,SAAS,YAAA;YACtE,WAAW,MAAM;YACjB,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;QAC7D;IACF,GACA;QAAC;QAAU,QAAQ;KAAA;IAGrB,MAAM,8NAA0B,cAAA,EAC9B,IAAM,WAAW;YAAC;YAAc,OAAO;SAAC,GACxC;QAAC;QAAY;QAAc,OAAO;KAAA;KAK9B,qNAAA,EAAU,MAAM;QACpB,IAAI,cAAc;YAChB,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAc,iBAAiB;KAAC;IAIpC,MAAM,EAAE,YAAA,EAAc,wBAAA,CAAyB,CAAA,GAAI;KAC7C,qNAAA,EAAU,MAAM;QACpB,IAAI,SAAS;YACX,IAAI,mBAAmB;gBAAE,GAAG;gBAAG,GAAG;YAAE;YAEpC,MAAM,oBAAoB,CAAC,UAAwB;gBACjD,mBAAmB;oBACjB,GAAG,KAAK,GAAA,CAAI,KAAK,KAAA,CAAM,MAAM,KAAK,IAAA,CAAK,yBAAyB,OAAA,EAAS,KAAK,CAAA,CAAE;oBAChF,GAAG,KAAK,GAAA,CAAI,KAAK,KAAA,CAAM,MAAM,KAAK,IAAA,CAAK,yBAAyB,OAAA,EAAS,KAAK,CAAA,CAAE;gBAClF;YACF;YACA,MAAM,kBAAkB,CAAC,UAAwB;gBAE/C,IAAI,iBAAiB,CAAA,IAAK,MAAM,iBAAiB,CAAA,IAAK,IAAI;oBACxD,MAAM,cAAA,CAAe;gBACvB,OAAO;oBAEL,IAAI,CAAC,QAAQ,QAAA,CAAS,MAAM,MAAqB,GAAG;wBAClD,aAAa,KAAK;oBACpB;gBACF;gBACA,SAAS,mBAAA,CAAoB,eAAe,iBAAiB;gBAC7D,yBAAyB,OAAA,GAAU;YACrC;YAEA,IAAI,yBAAyB,OAAA,KAAY,MAAM;gBAC7C,SAAS,gBAAA,CAAiB,eAAe,iBAAiB;gBAC1D,SAAS,gBAAA,CAAiB,aAAa,iBAAiB;oBAAE,SAAS;oBAAM,MAAM;gBAAK,CAAC;YACvF;YAEA,OAAO,MAAM;gBACX,SAAS,mBAAA,CAAoB,eAAe,iBAAiB;gBAC7D,SAAS,mBAAA,CAAoB,aAAa,iBAAiB;oBAAE,SAAS;gBAAK,CAAC;YAC9E;QACF;IACF,GAAG;QAAC;QAAS;QAAc,wBAAwB;KAAC;8MAE9C,YAAA,EAAU,MAAM;QACpB,MAAM,QAAQ,IAAM,aAAa,KAAK;QACtC,OAAO,gBAAA,CAAiB,QAAQ,KAAK;QACrC,OAAO,gBAAA,CAAiB,UAAU,KAAK;QACvC,OAAO,MAAM;YACX,OAAO,mBAAA,CAAoB,QAAQ,KAAK;YACxC,OAAO,mBAAA,CAAoB,UAAU,KAAK;QAC5C;IACF,GAAG;QAAC,YAAY;KAAC;IAEjB,MAAM,CAAC,WAAW,qBAAqB,CAAA,GAAI,mBAAmB,CAAC,WAAW;QACxE,MAAM,eAAe,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;QAC/D,MAAM,cAAc,aAAa,IAAA,CAAK,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,SAAS,aAAa;QAC3F,MAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;QAC/D,IAAI,UAAU;YAKZ,WAAW,IAAO,SAAS,GAAA,CAAI,OAAA,CAAwB,KAAA,CAAM,CAAC;QAChE;IACF,CAAC;IAED,MAAM,mBAAwB,uNAAA,EAC5B,CAAC,MAAgC,OAAe,aAAsB;QACpE,MAAM,mBAAmB,CAAC,uBAAuB,OAAA,IAAW,CAAC;QAC7D,MAAM,iBAAiB,QAAQ,KAAA,KAAU,KAAA,KAAa,QAAQ,KAAA,KAAU;QACxE,IAAI,kBAAkB,kBAAkB;YACtC,gBAAgB,IAAI;YACpB,IAAI,iBAAkB,CAAA,uBAAuB,OAAA,GAAU;QACzD;IACF,GACA;QAAC,QAAQ,KAAK;KAAA;IAEhB,MAAM,4NAAwB,cAAA,EAAY,IAAM,SAAS,MAAM,GAAG;QAAC,OAAO;KAAC;IAC3E,MAAM,gOAA4B,cAAA,EAChC,CAAC,MAAoC,OAAe,aAAsB;QACxE,MAAM,mBAAmB,CAAC,uBAAuB,OAAA,IAAW,CAAC;QAC7D,MAAM,iBAAiB,QAAQ,KAAA,KAAU,KAAA,KAAa,QAAQ,KAAA,KAAU;QACxE,IAAI,kBAAkB,kBAAkB;YACtC,oBAAoB,IAAI;QAC1B;IACF,GACA;QAAC,QAAQ,KAAK;KAAA;IAGhB,MAAM,iBAAiB,aAAa,WAAW,uBAAuB;IAGtE,MAAM,qBACJ,mBAAmB,uBACf;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF,IACA,CAAC;IAEP,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;QACC,OAAO;QACP;QACA;QACA,kBAAkB;QAClB;QACA;QACA,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wNAAC,eAAA,EAAA;YAAa,IAAI;YAAM,gBAAc;YACpC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,6KAAC,aAAA,EAAA;gBACC,SAAO;gBAGP,SAAS,QAAQ,IAAA;gBACjB,kBAAkB,CAAC,UAAU;oBAE3B,MAAM,cAAA,CAAe;gBACvB;gBACA,oLAAoB,wBAAA,EAAqB,kBAAkB,CAAC,UAAU;oBACpE,QAAQ,OAAA,EAAS,MAAM;wBAAE,eAAe;oBAAK,CAAC;oBAC9C,MAAM,cAAA,CAAe;gBACvB,CAAC;gBAED,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,oMAAA,EAAA;oBACC,SAAO;oBACP,6BAA2B;oBAC3B;oBACA;oBAGA,gBAAgB,CAAC,QAAU,MAAM,cAAA,CAAe;oBAChD,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;oBAE3C,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gBAAA;wBACC,MAAK;wBACL,IAAI,QAAQ,SAAA;wBACZ,cAAY,QAAQ,IAAA,GAAO,SAAS;wBACpC,KAAK,QAAQ,GAAA;wBACb,eAAe,CAAC,QAAU,MAAM,cAAA,CAAe;wBAC9C,GAAG,YAAA;wBACH,GAAG,kBAAA;wBACJ,UAAU,IAAM,gBAAgB,IAAI;wBACpC,KAAK;wBACL,OAAO;4BAAA,0DAAA;4BAEL,SAAS;4BACT,eAAe;4BAAA,8DAAA;4BAEf,SAAS;4BACT,GAAG,aAAa,KAAA;wBAClB;wBACA,4KAAW,uBAAA,EAAqB,aAAa,SAAA,EAAW,CAAC,UAAU;4BACjE,MAAM,gBAAgB,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,OAAA;4BAG7D,IAAI,MAAM,GAAA,KAAQ,MAAO,CAAA,MAAM,cAAA,CAAe;4BAE9C,IAAI,CAAC,iBAAiB,MAAM,GAAA,CAAI,MAAA,KAAW,EAAG,CAAA,sBAAsB,MAAM,GAAG;4BAE7E,IAAI;gCAAC;gCAAW;gCAAa;gCAAQ,KAAK;6BAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;gCAC/D,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;gCACxD,IAAI,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;gCAE1D,IAAI;oCAAC;oCAAW,KAAK;iCAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;oCAC1C,iBAAiB,eAAe,KAAA,CAAM,EAAE,OAAA,CAAQ;gCAClD;gCACA,IAAI;oCAAC;oCAAW,WAAW;iCAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;oCAChD,MAAM,iBAAiB,MAAM,MAAA;oCAC7B,MAAM,eAAe,eAAe,OAAA,CAAQ,cAAc;oCAC1D,iBAAiB,eAAe,KAAA,CAAM,eAAe,CAAC;gCACxD;gCAMA,WAAW,IAAM,WAAW,cAAc,CAAC;gCAE3C,MAAM,cAAA,CAAe;4BACvB;wBACF,CAAC;oBAAA;gBACH;YACF;QACF,CACF;IAAA;AAGN;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,6BAA6B;AAKnC,IAAM,sOAAkC,aAAA,EAGtC,CAAC,OAAoD,iBAAiB;IACtE,MAAM,EAAE,aAAA,EAAe,QAAA,EAAU,GAAG,YAAY,CAAA,GAAI;IACpD,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,iBAAiB,wBAAwB,cAAc,aAAa;IAC1E,MAAM,CAAC,gBAAgB,iBAAiB,CAAA,GAAU,qNAAA,EAAgC,IAAI;IACtF,MAAM,CAAC,SAAS,UAAU,CAAA,6MAAU,WAAA,EAAkD,IAAI;IAC1F,MAAM,gBAAe,iMAAA,EAAgB,cAAc,CAAC,OAAS,WAAW,IAAI,CAAC;IAC7E,MAAM,WAAW,cAAc,aAAa;IAC5C,MAAM,mOAAgC,UAAA,EAAO,KAAK;IAClD,MAAM,gOAA4B,SAAA,EAAO,IAAI;IAE7C,MAAM,EAAE,QAAA,EAAU,YAAA,EAAc,gBAAA,EAAkB,iBAAA,CAAkB,CAAA,GAAI;IACxE,MAAM,qNAAiB,cAAA,EAAY,MAAM;QACvC,IACE,QAAQ,OAAA,IACR,QAAQ,SAAA,IACR,kBACA,WACA,YACA,gBACA,kBACA;YACA,MAAM,cAAc,QAAQ,OAAA,CAAQ,qBAAA,CAAsB;YAK1D,MAAM,cAAc,QAAQ,qBAAA,CAAsB;YAClD,MAAM,gBAAgB,QAAQ,SAAA,CAAU,qBAAA,CAAsB;YAC9D,MAAM,eAAe,iBAAiB,qBAAA,CAAsB;YAE5D,IAAI,QAAQ,GAAA,KAAQ,OAAO;gBACzB,MAAM,iBAAiB,aAAa,IAAA,GAAO,YAAY,IAAA;gBACvD,MAAM,OAAO,cAAc,IAAA,GAAO;gBAClC,MAAM,YAAY,YAAY,IAAA,GAAO;gBACrC,MAAM,kBAAkB,YAAY,KAAA,GAAQ;gBAC5C,MAAM,eAAe,KAAK,GAAA,CAAI,iBAAiB,YAAY,KAAK;gBAChE,MAAM,YAAY,OAAO,UAAA,GAAa;gBACtC,MAAM,cAAc,sKAAA,EAAM,MAAM;oBAC9B;oBAAA,+DAAA;oBAAA,iEAAA;oBAAA,qEAAA;oBAAA,gBAAA;oBAAA,qDAAA;oBAMA,KAAK,GAAA,CAAI,gBAAgB,YAAY,YAAY;iBAClD;gBAED,eAAe,KAAA,CAAM,QAAA,GAAW,kBAAkB;gBAClD,eAAe,KAAA,CAAM,IAAA,GAAO,cAAc;YAC5C,OAAO;gBACL,MAAM,iBAAiB,YAAY,KAAA,GAAQ,aAAa,KAAA;gBACxD,MAAM,QAAQ,OAAO,UAAA,GAAa,cAAc,KAAA,GAAQ;gBACxD,MAAM,aAAa,OAAO,UAAA,GAAa,YAAY,KAAA,GAAQ;gBAC3D,MAAM,kBAAkB,YAAY,KAAA,GAAQ;gBAC5C,MAAM,eAAe,KAAK,GAAA,CAAI,iBAAiB,YAAY,KAAK;gBAChE,MAAM,WAAW,OAAO,UAAA,GAAa;gBACrC,MAAM,eAAe,sKAAA,EAAM,OAAO;oBAChC;oBACA,KAAK,GAAA,CAAI,gBAAgB,WAAW,YAAY;iBACjD;gBAED,eAAe,KAAA,CAAM,QAAA,GAAW,kBAAkB;gBAClD,eAAe,KAAA,CAAM,KAAA,GAAQ,eAAe;YAC9C;YAKA,MAAM,QAAQ,SAAS;YACvB,MAAM,kBAAkB,OAAO,WAAA,GAAc,iBAAiB;YAC9D,MAAM,cAAc,SAAS,YAAA;YAE7B,MAAM,gBAAgB,OAAO,gBAAA,CAAiB,OAAO;YACrD,MAAM,wBAAwB,SAAS,cAAc,cAAA,EAAgB,EAAE;YACvE,MAAM,oBAAoB,SAAS,cAAc,UAAA,EAAY,EAAE;YAC/D,MAAM,2BAA2B,SAAS,cAAc,iBAAA,EAAmB,EAAE;YAC7E,MAAM,uBAAuB,SAAS,cAAc,aAAA,EAAe,EAAE;YACrE,MAAM,oBAAoB,wBAAwB,oBAAoB,cAAc,uBAAuB;YAC3G,MAAM,mBAAmB,KAAK,GAAA,CAAI,aAAa,YAAA,GAAe,GAAG,iBAAiB;YAElF,MAAM,iBAAiB,OAAO,gBAAA,CAAiB,QAAQ;YACvD,MAAM,qBAAqB,SAAS,eAAe,UAAA,EAAY,EAAE;YACjE,MAAM,wBAAwB,SAAS,eAAe,aAAA,EAAe,EAAE;YAEvE,MAAM,yBAAyB,YAAY,GAAA,GAAM,YAAY,MAAA,GAAS,IAAI;YAC1E,MAAM,4BAA4B,kBAAkB;YAEpD,MAAM,yBAAyB,aAAa,YAAA,GAAe;YAC3D,MAAM,mBAAmB,aAAa,SAAA,GAAY;YAClD,MAAM,yBAAyB,wBAAwB,oBAAoB;YAC3E,MAAM,4BAA4B,oBAAoB;YAEtD,MAAM,8BAA8B,0BAA0B;YAE9D,IAAI,6BAA6B;gBAC/B,MAAM,aACJ,MAAM,MAAA,GAAS,KAAK,iBAAiB,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,CAAG,GAAA,CAAI,OAAA;gBACpE,eAAe,KAAA,CAAM,MAAA,GAAS;gBAC9B,MAAM,uBACJ,QAAQ,YAAA,GAAe,SAAS,SAAA,GAAY,SAAS,YAAA;gBACvD,MAAM,mCAAmC,KAAK,GAAA,CAC5C,2BACA,yBAAA,gFAAA;gBAAA,CAEG,aAAa,wBAAwB,CAAA,IACtC,uBACA;gBAEJ,MAAM,SAAS,yBAAyB;gBACxC,eAAe,KAAA,CAAM,MAAA,GAAS,SAAS;YACzC,OAAO;gBACL,MAAM,cAAc,MAAM,MAAA,GAAS,KAAK,iBAAiB,KAAA,CAAM,CAAC,CAAA,CAAG,GAAA,CAAI,OAAA;gBACvE,eAAe,KAAA,CAAM,GAAA,GAAM;gBAC3B,MAAM,gCAAgC,KAAK,GAAA,CACzC,wBACA,wBACE,SAAS,SAAA,GAAA,6EAAA;gBAAA,CAER,cAAc,qBAAqB,CAAA,IACpC;gBAEJ,MAAM,SAAS,gCAAgC;gBAC/C,eAAe,KAAA,CAAM,MAAA,GAAS,SAAS;gBACvC,SAAS,SAAA,GAAY,yBAAyB,yBAAyB,SAAS,SAAA;YAClF;YAEA,eAAe,KAAA,CAAM,MAAA,GAAS,GAAG,cAAc,CAAA,IAAA,CAAA;YAC/C,eAAe,KAAA,CAAM,SAAA,GAAY,mBAAmB;YACpD,eAAe,KAAA,CAAM,SAAA,GAAY,kBAAkB;YAGnD,WAAW;YAIX,sBAAsB,IAAO,wBAAwB,OAAA,GAAU,IAAK;QACtE;IACF,GAAG;QACD;QACA,QAAQ,OAAA;QACR,QAAQ,SAAA;QACR;QACA;QACA;QACA;QACA;QACA,QAAQ,GAAA;QACR;KACD;IAED,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,IAAM,SAAS,GAAG;QAAC,QAAQ;KAAC;IAG5C,MAAM,CAAC,eAAe,gBAAgB,CAAA,6MAAU,WAAA,CAAiB;IACjE,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,QAAS,CAAA,iBAAiB,OAAO,gBAAA,CAAiB,OAAO,EAAE,MAAM;IACvE,GAAG;QAAC,OAAO;KAAC;IAMZ,MAAM,qOAAiC,cAAA,EACrC,CAAC,SAA+C;QAC9C,IAAI,QAAQ,oBAAoB,OAAA,KAAY,MAAM;YAChD,SAAS;YACT,oBAAoB;YACpB,oBAAoB,OAAA,GAAU;QAChC;IACF,GACA;QAAC;QAAU,iBAAiB;KAAA;IAG9B,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;QACC,OAAO;QACP;QACA;QACA,sBAAsB;QAEtB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,OAAA;YACC,KAAK;YACL,OAAO;gBACL,SAAS;gBACT,eAAe;gBACf,UAAU;gBACV,QAAQ;YACV;YAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;gBACE,GAAG,WAAA;gBACJ,KAAK;gBACL,OAAO;oBAAA,gFAAA;oBAAA,2EAAA;oBAGL,WAAW;oBAAA,oEAAA;oBAEX,WAAW;oBACX,GAAG,YAAY,KAAA;gBACjB;YAAA;QACF;IACF;AAGN,CAAC;AAED,0BAA0B,WAAA,GAAc;AAMxC,IAAM,uBAAuB;AAM7B,IAAM,iOAA6B,aAAA,EAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EACJ,aAAA,EACA,QAAQ,OAAA,EACR,mBAAmB,cAAA,EACnB,GAAG,aACL,GAAI;IACJ,MAAM,cAAc,eAAe,aAAa;IAEhD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,6KAAA,EAAhB;QACE,GAAG,WAAA;QACH,GAAG,WAAA;QACJ,KAAK;QACL;QACA;QACA,OAAO;YAAA,iDAAA;YAEL,WAAW;YACX,GAAG,YAAY,KAAA;YAAA,iDAAA;YAEf,GAAG;gBACD,2CAA2C;gBAC3C,0CAA0C;gBAC1C,2CAA2C;gBAC3C,gCAAgC;gBAChC,iCAAiC;YACnC,CAAA;QACF;IAAA;AAGN,CAAC;AAED,qBAAqB,WAAA,GAAc;AAYnC,IAAM,CAAC,wBAAwB,wBAAwB,CAAA,GACrD,oBAAgD,cAAc,CAAC,CAAC;AAElE,IAAM,gBAAgB;AAQtB,IAAM,2NAAuB,aAAA,EAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,aAAA,EAAe,KAAA,EAAO,GAAG,cAAc,CAAA,GAAI;IACnD,MAAM,iBAAiB,wBAAwB,eAAe,aAAa;IAC3E,MAAM,kBAAkB,yBAAyB,eAAe,aAAa;IAC7E,MAAM,+LAAe,kBAAA,EAAgB,cAAc,eAAe,gBAAgB;IAClF,MAAM,6NAAyB,SAAA,EAAO,CAAC;IACvC,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;QAEE,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,SAAA;gBACC,yBAAyB;oBACvB,QAAQ,CAAA,yKAAA,CAAA;gBACV;gBACA;YAAA;YAEF,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO;gBACtB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;oBACC,8BAA2B;oBAC3B,MAAK;oBACJ,GAAG,aAAA;oBACJ,KAAK;oBACL,OAAO;wBAAA,0EAAA;wBAAA,mFAAA;wBAAA,uCAAA;wBAIL,UAAU;wBACV,MAAM;wBAAA,gEAAA;wBAAA,8DAAA;wBAAA,yCAAA;wBAAA,2DAAA;wBAKN,UAAU;wBACV,GAAG,cAAc,KAAA;oBACnB;oBACA,2KAAU,uBAAA,EAAqB,cAAc,QAAA,EAAU,CAAC,UAAU;wBAChE,MAAM,WAAW,MAAM,aAAA;wBACvB,MAAM,EAAE,cAAA,EAAgB,uBAAA,CAAwB,CAAA,GAAI;wBACpD,IAAI,yBAAyB,WAAW,gBAAgB;4BACtD,MAAM,aAAa,KAAK,GAAA,CAAI,iBAAiB,OAAA,GAAU,SAAS,SAAS;4BACzE,IAAI,aAAa,GAAG;gCAClB,MAAM,kBAAkB,OAAO,WAAA,GAAc,iBAAiB;gCAC9D,MAAM,eAAe,WAAW,eAAe,KAAA,CAAM,SAAS;gCAC9D,MAAM,YAAY,WAAW,eAAe,KAAA,CAAM,MAAM;gCACxD,MAAM,aAAa,KAAK,GAAA,CAAI,cAAc,SAAS;gCAEnD,IAAI,aAAa,iBAAiB;oCAChC,MAAM,aAAa,aAAa;oCAChC,MAAM,oBAAoB,KAAK,GAAA,CAAI,iBAAiB,UAAU;oCAC9D,MAAM,aAAa,aAAa;oCAEhC,eAAe,KAAA,CAAM,MAAA,GAAS,oBAAoB;oCAClD,IAAI,eAAe,KAAA,CAAM,MAAA,KAAW,OAAO;wCACzC,SAAS,SAAA,GAAY,aAAa,IAAI,aAAa;wCAEnD,eAAe,KAAA,CAAM,cAAA,GAAiB;oCACxC;gCACF;4BACF;wBACF;wBACA,iBAAiB,OAAA,GAAU,SAAS,SAAA;oBACtC,CAAC;gBAAA;YACH,CACF;SAAA;IAAA,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,aAAa;AAInB,IAAM,CAAC,4BAA4B,qBAAqB,CAAA,GACtD,oBAA6C,UAAU;AAKzD,IAAM,wNAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,6KAAU,QAAA,CAAM;IACtB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,4BAAA;QAA2B,OAAO;QAAe,IAAI;QACpD,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YAAc,MAAK;YAAQ,mBAAiB;YAAU,GAAG,UAAA;YAAY,KAAK;QAAA,CAAc;IAAA,CAC3F;AAEJ;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,cAAoB,uNAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,eAAe,sBAAsB,YAAY,aAAa;IACpE,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QAAc,IAAI,aAAa,EAAA;QAAK,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,YAAY;AAUlB,IAAM,CAAC,2BAA2B,oBAAoB,CAAA,GACpD,oBAA4C,SAAS;AASvD,IAAM,uNAAmB,aAAA,EACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EACJ,aAAA,EACA,KAAA,EACA,WAAW,KAAA,EACX,WAAW,aAAA,EACX,GAAG,WACL,GAAI;IACJ,MAAM,UAAU,iBAAiB,WAAW,aAAa;IACzD,MAAM,iBAAiB,wBAAwB,WAAW,aAAa;IACvE,MAAM,aAAa,QAAQ,KAAA,KAAU;IACrC,MAAM,CAAC,WAAW,YAAY,CAAA,4MAAU,YAAA,EAAS,iBAAiB,EAAE;IACpE,MAAM,CAAC,WAAW,YAAY,CAAA,6MAAU,WAAA,EAAS,KAAK;IACtD,MAAM,+LAAe,kBAAA,EAAgB,cAAc,CAAC,OAClD,eAAe,eAAA,GAAkB,MAAM,OAAO,QAAQ;IAExD,MAAM,4KAAS,QAAA,CAAM;IACrB,MAAM,iBAAuB,mNAAA,EAA0C,OAAO;IAE9E,MAAM,eAAe,MAAM;QACzB,IAAI,CAAC,UAAU;YACb,QAAQ,aAAA,CAAc,KAAK;YAC3B,QAAQ,YAAA,CAAa,KAAK;QAC5B;IACF;IAEA,IAAI,UAAU,IAAI;QAChB,MAAM,IAAI,MACR;IAEJ;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,2BAAA;QACC,OAAO;QACP;QACA;QACA;QACA;QACA,4NAAwB,cAAA,EAAY,CAAC,SAAS;YAC5C,aAAa,CAAC,gBAAkB,iBAAA,CAAkB,MAAM,eAAe,EAAA,EAAI,IAAA,CAAK,CAAC;QACnF,GAAG,CAAC,CAAC;QAEL,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;YACC,OAAO;YACP;YACA;YACA;YAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;gBACC,MAAK;gBACL,mBAAiB;gBACjB,oBAAkB,YAAY,KAAK,KAAA;gBAEnC,iBAAe,cAAc;gBAC7B,cAAY,aAAa,YAAY;gBACrC,iBAAe,YAAY,KAAA;gBAC3B,iBAAe,WAAW,KAAK,KAAA;gBAC/B,UAAU,WAAW,KAAA,IAAY,CAAA;gBAChC,GAAG,SAAA;gBACJ,KAAK;gBACL,0KAAS,uBAAA,EAAqB,UAAU,OAAA,EAAS,IAAM,aAAa,IAAI,CAAC;gBACzE,yKAAQ,uBAAA,EAAqB,UAAU,MAAA,EAAQ,IAAM,aAAa,KAAK,CAAC;gBACxE,0KAAS,uBAAA,EAAqB,UAAU,OAAA,EAAS,MAAM;oBAErD,IAAI,eAAe,OAAA,KAAY,QAAS,CAAA,aAAa;gBACvD,CAAC;gBACD,8KAAa,uBAAA,EAAqB,UAAU,WAAA,EAAa,MAAM;oBAG7D,IAAI,eAAe,OAAA,KAAY,QAAS,CAAA,aAAa;gBACvD,CAAC;gBACD,gLAAe,uBAAA,EAAqB,UAAU,aAAA,EAAe,CAAC,UAAU;oBACtE,eAAe,OAAA,GAAU,MAAM,WAAA;gBACjC,CAAC;gBACD,gLAAe,uBAAA,EAAqB,UAAU,aAAA,EAAe,CAAC,UAAU;oBAEtE,eAAe,OAAA,GAAU,MAAM,WAAA;oBAC/B,IAAI,UAAU;wBACZ,eAAe,WAAA,GAAc;oBAC/B,OAAA,IAAW,eAAe,OAAA,KAAY,SAAS;wBAG7C,MAAM,aAAA,CAAc,KAAA,CAAM;4BAAE,eAAe;wBAAK,CAAC;oBACnD;gBACF,CAAC;gBACD,iLAAgB,uBAAA,EAAqB,UAAU,cAAA,EAAgB,CAAC,UAAU;oBACxE,IAAI,MAAM,aAAA,KAAkB,SAAS,aAAA,EAAe;wBAClD,eAAe,WAAA,GAAc;oBAC/B;gBACF,CAAC;gBACD,4KAAW,uBAAA,EAAqB,UAAU,SAAA,EAAW,CAAC,UAAU;oBAC9D,MAAM,gBAAgB,eAAe,SAAA,EAAW,YAAY;oBAC5D,IAAI,iBAAiB,MAAM,GAAA,KAAQ,IAAK,CAAA;oBACxC,IAAI,eAAe,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,aAAa;oBAErD,IAAI,MAAM,GAAA,KAAQ,IAAK,CAAA,MAAM,cAAA,CAAe;gBAC9C,CAAC;YAAA;QACH;IACF;AAGN;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,iBAAiB;AAKvB,IAAM,2NAAuB,aAAA,EAC3B,CAAC,OAAyC,iBAAiB;IAEzD,MAAM,EAAE,aAAA,EAAe,SAAA,EAAW,KAAA,EAAO,GAAG,cAAc,CAAA,GAAI;IAC9D,MAAM,UAAU,iBAAiB,gBAAgB,aAAa;IAC9D,MAAM,iBAAiB,wBAAwB,gBAAgB,aAAa;IAC5E,MAAM,cAAc,qBAAqB,gBAAgB,aAAa;IACtE,MAAM,uBAAuB,8BAA8B,gBAAgB,aAAa;IACxF,MAAM,CAAC,cAAc,eAAe,CAAA,6MAAU,WAAA,EAAuC,IAAI;IACzF,MAAM,eAAe,kMAAA,EACnB,cACA,CAAC,OAAS,gBAAgB,IAAI,GAC9B,YAAY,gBAAA,EACZ,CAAC,OAAS,eAAe,mBAAA,GAAsB,MAAM,YAAY,KAAA,EAAO,YAAY,QAAQ;IAG9F,MAAM,cAAc,cAAc;IAClC,MAAM,mBAAqB,gNAAA,EACzB,IACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,UAAA;YAA+B,OAAO,YAAY,KAAA;YAAO,UAAU,YAAY,QAAA;YAC7E,UAAA;QAAA,GADU,YAAY,KAEzB,GAEF;QAAC,YAAY,QAAA;QAAU,YAAY,KAAA;QAAO,WAAW;KAAA;IAGvD,MAAM,EAAE,iBAAA,EAAmB,oBAAA,CAAqB,CAAA,GAAI;IACpD,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,kBAAkB,YAAY;QAC9B,OAAO,IAAM,qBAAqB,YAAY;IAChD,GAAG;QAAC;QAAmB;QAAsB,YAAY;KAAC;IAE1D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;QACE,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;gBAAe,IAAI,YAAY,MAAA;gBAAS,GAAG,aAAA;gBAAe,KAAK;YAAA,CAAc;YAG7E,YAAY,UAAA,IAAc,QAAQ,SAAA,IAAa,CAAC,QAAQ,oBAAA,oNAC5C,eAAA,EAAa,cAAc,QAAA,EAAU,QAAQ,SAAS,IAC/D;SAAA;IAAA,CACN;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,sBAAsB;AAK5B,IAAM,sBAA4B,uNAAA,EAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,aAAA,EAAe,GAAG,mBAAmB,CAAA,GAAI;IACjD,MAAM,cAAc,qBAAqB,qBAAqB,aAAa;IAC3E,OAAO,YAAY,UAAA,GACjB,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;QAAe,eAAW;QAAE,GAAG,kBAAA;QAAoB,KAAK;IAAA,CAAc,IACrE;AACN;AAGF,oBAAoB,WAAA,GAAc;AAMlC,IAAM,wBAAwB;AAK9B,IAAM,wBAA6B,sNAAA,EAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,iBAAiB,wBAAwB,uBAAuB,MAAM,aAAa;IACzF,MAAM,kBAAkB,yBAAyB,uBAAuB,MAAM,aAAa;IAC3F,MAAM,CAAC,aAAa,cAAc,CAAA,GAAU,qNAAA,EAAS,KAAK;IAC1D,MAAM,+LAAe,kBAAA,EAAgB,cAAc,gBAAgB,oBAAoB;IAEvF,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,eAAe,QAAA,IAAY,eAAe,YAAA,EAAc;YAE1D,IAASA,gBAAT,WAAwB;gBACtB,MAAMC,eAAc,SAAS,SAAA,GAAY;gBACzC,eAAeA,YAAW;YAC5B;YAHS,IAAA,eAAAD;YADT,MAAM,WAAW,eAAe,QAAA;YAKhCA,cAAa;YACb,SAAS,gBAAA,CAAiB,UAAUA,aAAY;YAChD,OAAO,IAAM,SAAS,mBAAA,CAAoB,UAAUA,aAAY;QAClE;IACF,GAAG;QAAC,eAAe,QAAA;QAAU,eAAe,YAAY;KAAC;IAEzD,OAAO,cACL,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,cAAc,MAAM;YAClB,MAAM,EAAE,QAAA,EAAU,YAAA,CAAa,CAAA,GAAI;YACnC,IAAI,YAAY,cAAc;gBAC5B,SAAS,SAAA,GAAY,SAAS,SAAA,GAAY,aAAa,YAAA;YACzD;QACF;IAAA,KAEA;AACN,CAAC;AAED,qBAAqB,WAAA,GAAc;AAMnC,IAAM,0BAA0B;AAKhC,IAAM,mOAA+B,aAAA,EAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,iBAAiB,wBAAwB,yBAAyB,MAAM,aAAa;IAC3F,MAAM,kBAAkB,yBAAyB,yBAAyB,MAAM,aAAa;IAC7F,MAAM,CAAC,eAAe,gBAAgB,CAAA,6MAAU,WAAA,EAAS,KAAK;IAC9D,MAAM,+LAAe,kBAAA,EAAgB,cAAc,gBAAgB,oBAAoB;IAEvF,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,eAAe,QAAA,IAAY,eAAe,YAAA,EAAc;YAE1D,IAASA,gBAAT,WAAwB;gBACtB,MAAM,YAAY,SAAS,YAAA,GAAe,SAAS,YAAA;gBAGnD,MAAME,iBAAgB,KAAK,IAAA,CAAK,SAAS,SAAS,IAAI;gBACtD,iBAAiBA,cAAa;YAChC;YANS,IAAA,eAAAF;YADT,MAAM,WAAW,eAAe,QAAA;YAQhCA,cAAa;YACb,SAAS,gBAAA,CAAiB,UAAUA,aAAY;YAChD,OAAO,IAAM,SAAS,mBAAA,CAAoB,UAAUA,aAAY;QAClE;IACF,GAAG;QAAC,eAAe,QAAA;QAAU,eAAe,YAAY;KAAC;IAEzD,OAAO,gBACL,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,cAAc,MAAM;YAClB,MAAM,EAAE,QAAA,EAAU,YAAA,CAAa,CAAA,GAAI;YACnC,IAAI,YAAY,cAAc;gBAC5B,SAAS,SAAA,GAAY,SAAS,SAAA,GAAY,aAAa,YAAA;YACzD;QACF;IAAA,KAEA;AACN,CAAC;AAED,uBAAuB,WAAA,GAAc;AAOrC,IAAM,mOAA+B,aAAA,EAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,aAAA,EAAe,YAAA,EAAc,GAAG,qBAAqB,CAAA,GAAI;IACjE,MAAM,iBAAiB,wBAAwB,sBAAsB,aAAa;IAClF,MAAM,+NAA2B,SAAA,EAAsB,IAAI;IAC3D,MAAM,WAAW,cAAc,aAAa;IAE5C,MAAM,iOAA6B,cAAA,EAAY,MAAM;QACnD,IAAI,mBAAmB,OAAA,KAAY,MAAM;YACvC,OAAO,aAAA,CAAc,mBAAmB,OAAO;YAC/C,mBAAmB,OAAA,GAAU;QAC/B;IACF,GAAG,CAAC,CAAC;8MAEC,YAAA,EAAU,MAAM;QACpB,OAAO,IAAM,qBAAqB;IACpC,GAAG;QAAC,oBAAoB;KAAC;IAMzB,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,MAAM,aAAa,SAAS,EAAE,IAAA,CAAK,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,SAAS,aAAa;QACxF,YAAY,IAAI,SAAS,eAAe;YAAE,OAAO;QAAU,CAAC;IAC9D,GAAG;QAAC,QAAQ;KAAC;IAEb,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QACC,eAAW;QACV,GAAG,oBAAA;QACJ,KAAK;QACL,OAAO;YAAE,YAAY;YAAG,GAAG,qBAAqB,KAAA;QAAM;QACtD,gLAAe,uBAAA,EAAqB,qBAAqB,aAAA,EAAe,MAAM;YAC5E,IAAI,mBAAmB,OAAA,KAAY,MAAM;gBACvC,mBAAmB,OAAA,GAAU,OAAO,WAAA,CAAY,cAAc,EAAE;YAClE;QACF,CAAC;QACD,+KAAe,wBAAA,EAAqB,qBAAqB,aAAA,EAAe,MAAM;YAC5E,eAAe,WAAA,GAAc;YAC7B,IAAI,mBAAmB,OAAA,KAAY,MAAM;gBACvC,mBAAmB,OAAA,GAAU,OAAO,WAAA,CAAY,cAAc,EAAE;YAClE;QACF,CAAC;QACD,iLAAgB,uBAAA,EAAqB,qBAAqB,cAAA,EAAgB,MAAM;YAC9E,qBAAqB;QACvB,CAAC;IAAA;AAGP,CAAC;AAMD,IAAM,iBAAiB;AAKvB,IAAM,4NAAwB,aAAA,EAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EAAE,aAAA,EAAe,GAAG,eAAe,CAAA,GAAI;IAC7C,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kLAAA,CAAU,GAAA,EAAV;QAAc,eAAW;QAAE,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc;AAC3E;AAGF,gBAAgB,WAAA,GAAc;AAM9B,IAAM,aAAa;AAMnB,IAAM,wNAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,cAAc,eAAe,aAAa;IAChD,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,iBAAiB,wBAAwB,YAAY,aAAa;IACxE,OAAO,QAAQ,IAAA,IAAQ,eAAe,QAAA,KAAa,WACjD,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,qKAAiB,QAAA,EAAhB;QAAuB,GAAG,WAAA;QAAc,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc,IACzE;AACN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,oBAAoB;AAK1B,IAAM,8NAA0B,aAAA,EAC9B,CAAC,EAAE,aAAA,EAAe,KAAA,EAAO,GAAG,MAAM,CAAA,EAAwC,iBAAiB;IACzF,MAAM,MAAY,mNAAA,EAA0B,IAAI;IAChD,MAAM,+LAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,4LAAY,cAAA,EAAY,KAAK;8MAG7B,YAAA,EAAU,MAAM;QACpB,MAAM,SAAS,IAAI,OAAA;QACnB,IAAI,CAAC,OAAQ,CAAA;QAEb,MAAM,cAAc,OAAO,iBAAA,CAAkB,SAAA;QAC7C,MAAM,aAAa,OAAO,wBAAA,CACxB,aACA;QAEF,MAAM,WAAW,WAAW,GAAA;QAC5B,IAAI,cAAc,SAAS,UAAU;YACnC,MAAM,QAAQ,IAAI,MAAM,UAAU;gBAAE,SAAS;YAAK,CAAC;YACnD,SAAS,IAAA,CAAK,QAAQ,KAAK;YAC3B,OAAO,aAAA,CAAc,KAAK;QAC5B;IACF,GAAG;QAAC;QAAW,KAAK;KAAC;IAcrB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kLAAA,CAAU,MAAA,EAAV;QACE,GAAG,KAAA;QACJ,OAAO;YAAE,kLAAG,yBAAA;YAAwB,GAAG,MAAM,KAAA;QAAM;QACnD,KAAK;QACL,cAAc;IAAA;AAGpB;AAGF,kBAAkB,WAAA,GAAc;AAIhC,SAAS,sBAAsB,KAAA,EAAgB;IAC7C,OAAO,UAAU,MAAM,UAAU,KAAA;AACnC;AAEA,SAAS,mBAAmB,cAAA,EAA0C;IACpE,MAAM,4MAAqB,iBAAA,EAAe,cAAc;IACxD,MAAM,YAAkB,mNAAA,EAAO,EAAE;IACjC,MAAM,qNAAiB,SAAA,EAAO,CAAC;IAE/B,MAAM,kOAA8B,cAAA,EAClC,CAAC,QAAgB;QACf,MAAM,SAAS,UAAU,OAAA,GAAU;QACnC,mBAAmB,MAAM;QAEzB,CAAC,SAAS,aAAa,KAAA,EAAe;YACpC,UAAU,OAAA,GAAU;YACpB,OAAO,YAAA,CAAa,SAAS,OAAO;YAEpC,IAAI,UAAU,GAAI,CAAA,SAAS,OAAA,GAAU,OAAO,UAAA,CAAW,IAAM,aAAa,EAAE,GAAG,GAAI;QACrF,CAAA,EAAG,MAAM;IACX,GACA;QAAC,kBAAkB;KAAA;IAGrB,MAAM,2NAAuB,cAAA,EAAY,MAAM;QAC7C,UAAU,OAAA,GAAU;QACpB,OAAO,YAAA,CAAa,SAAS,OAAO;IACtC,GAAG,CAAC,CAAC;8MAEC,YAAA,EAAU,MAAM;QACpB,OAAO,IAAM,OAAO,YAAA,CAAa,SAAS,OAAO;IACnD,GAAG,CAAC,CAAC;IAEL,OAAO;QAAC;QAAW;QAAuB,cAAc;KAAA;AAC1D;AAmBA,SAAS,aACP,KAAA,EACA,MAAA,EACA,WAAA,EACA;IACA,MAAM,aAAa,OAAO,MAAA,GAAS,KAAK,MAAM,IAAA,CAAK,MAAM,EAAE,KAAA,CAAM,CAAC,OAAS,SAAS,MAAA,CAAO,CAAC,CAAC;IAC7F,MAAM,mBAAmB,aAAa,MAAA,CAAO,CAAC,CAAA,GAAK;IACnD,MAAM,mBAAmB,cAAc,MAAM,OAAA,CAAQ,WAAW,IAAI,CAAA;IACpE,IAAI,eAAe,UAAU,OAAO,KAAK,GAAA,CAAI,kBAAkB,CAAC,CAAC;IACjE,MAAM,qBAAqB,iBAAiB,MAAA,KAAW;IACvD,IAAI,mBAAoB,CAAA,eAAe,aAAa,MAAA,CAAO,CAAC,IAAM,MAAM,WAAW;IACnF,MAAM,WAAW,aAAa,IAAA,CAAK,CAAC,OAClC,KAAK,SAAA,CAAU,WAAA,CAAY,EAAE,UAAA,CAAW,iBAAiB,WAAA,CAAY,CAAC;IAExE,OAAO,aAAa,cAAc,WAAW,KAAA;AAC/C;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAMG,QAAO;AACb,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AACzB,IAAM,YAAY;AAClB,IAAMC,SAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2018, "column": 0}, "map": {"version": 3, "file": "chevron-up.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/chevron-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm18 15-6-6-6 6', key: '153udz' }]];\n\n/**\n * @component @name ChevronUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggMTUtNi02LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUp = createLucideIcon('chevron-up', __iconNode);\n\nexport default ChevronUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA;AAa/E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2057, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/%40radix-ui/react-accordion/src/accordion.tsx"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\nimport { createCollapsibleScope } from '@radix-ui/react-collapsible';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * Accordion\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACCORDION_NAME = 'Accordion';\nconst ACCORDION_KEYS = ['Home', 'End', 'ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight'];\n\nconst [Collection, useCollection, createCollectionScope] =\n  createCollection<AccordionTriggerElement>(ACCORDION_NAME);\n\ntype ScopedProps<P> = P & { __scopeAccordion?: Scope };\nconst [createAccordionContext, createAccordionScope] = createContextScope(ACCORDION_NAME, [\n  createCollectionScope,\n  createCollapsibleScope,\n]);\nconst useCollapsibleScope = createCollapsibleScope();\n\ntype AccordionElement = AccordionImplMultipleElement | AccordionImplSingleElement;\ninterface AccordionSingleProps extends AccordionImplSingleProps {\n  type: 'single';\n}\ninterface AccordionMultipleProps extends AccordionImplMultipleProps {\n  type: 'multiple';\n}\n\nconst Accordion = React.forwardRef<AccordionElement, AccordionSingleProps | AccordionMultipleProps>(\n  (props: ScopedProps<AccordionSingleProps | AccordionMultipleProps>, forwardedRef) => {\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps as AccordionImplSingleProps;\n    const multipleProps = accordionProps as AccordionImplMultipleProps;\n    return (\n      <Collection.Provider scope={props.__scopeAccordion}>\n        {type === 'multiple' ? (\n          <AccordionImplMultiple {...multipleProps} ref={forwardedRef} />\n        ) : (\n          <AccordionImplSingle {...singleProps} ref={forwardedRef} />\n        )}\n      </Collection.Provider>\n    );\n  }\n);\n\nAccordion.displayName = ACCORDION_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionValueContextValue = {\n  value: string[];\n  onItemOpen(value: string): void;\n  onItemClose(value: string): void;\n};\n\nconst [AccordionValueProvider, useAccordionValueContext] =\n  createAccordionContext<AccordionValueContextValue>(ACCORDION_NAME);\n\nconst [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(\n  ACCORDION_NAME,\n  { collapsible: false }\n);\n\ntype AccordionImplSingleElement = AccordionImplElement;\ninterface AccordionImplSingleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion item whose content is expanded.\n   */\n  value?: string;\n  /**\n   * The value of the item whose content is expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string;\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string): void;\n  /**\n   * Whether an accordion item can be collapsed after it has been opened.\n   * @default false\n   */\n  collapsible?: boolean;\n}\n\nconst AccordionImplSingle = React.forwardRef<AccordionImplSingleElement, AccordionImplSingleProps>(\n  (props: ScopedProps<AccordionImplSingleProps>, forwardedRef) => {\n    const {\n      value: valueProp,\n      defaultValue,\n      onValueChange = () => {},\n      collapsible = false,\n      ...accordionSingleProps\n    } = props;\n\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? '',\n      onChange: onValueChange,\n      caller: ACCORDION_NAME,\n    });\n\n    return (\n      <AccordionValueProvider\n        scope={props.__scopeAccordion}\n        value={React.useMemo(() => (value ? [value] : []), [value])}\n        onItemOpen={setValue}\n        onItemClose={React.useCallback(() => collapsible && setValue(''), [collapsible, setValue])}\n      >\n        <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={collapsible}>\n          <AccordionImpl {...accordionSingleProps} ref={forwardedRef} />\n        </AccordionCollapsibleProvider>\n      </AccordionValueProvider>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplMultipleElement = AccordionImplElement;\ninterface AccordionImplMultipleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion items whose contents are expanded.\n   */\n  value?: string[];\n  /**\n   * The value of the items whose contents are expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string[];\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string[]): void;\n}\n\nconst AccordionImplMultiple = React.forwardRef<\n  AccordionImplMultipleElement,\n  AccordionImplMultipleProps\n>((props: ScopedProps<AccordionImplMultipleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...accordionMultipleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? [],\n    onChange: onValueChange,\n    caller: ACCORDION_NAME,\n  });\n\n  const handleItemOpen = React.useCallback(\n    (itemValue: string) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n\n  const handleItemClose = React.useCallback(\n    (itemValue: string) =>\n      setValue((prevValue = []) => prevValue.filter((value) => value !== itemValue)),\n    [setValue]\n  );\n\n  return (\n    <AccordionValueProvider\n      scope={props.__scopeAccordion}\n      value={value}\n      onItemOpen={handleItemOpen}\n      onItemClose={handleItemClose}\n    >\n      <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={true}>\n        <AccordionImpl {...accordionMultipleProps} ref={forwardedRef} />\n      </AccordionCollapsibleProvider>\n    </AccordionValueProvider>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplContextValue = {\n  disabled?: boolean;\n  direction: AccordionImplProps['dir'];\n  orientation: AccordionImplProps['orientation'];\n};\n\nconst [AccordionImplProvider, useAccordionContext] =\n  createAccordionContext<AccordionImplContextValue>(ACCORDION_NAME);\n\ntype AccordionImplElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface AccordionImplProps extends PrimitiveDivProps {\n  /**\n   * Whether or not an accordion is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * The layout in which the Accordion operates.\n   * @default vertical\n   */\n  orientation?: React.AriaAttributes['aria-orientation'];\n  /**\n   * The language read direction.\n   */\n  dir?: Direction;\n}\n\nconst AccordionImpl = React.forwardRef<AccordionImplElement, AccordionImplProps>(\n  (props: ScopedProps<AccordionImplProps>, forwardedRef) => {\n    const { __scopeAccordion, disabled, dir, orientation = 'vertical', ...accordionProps } = props;\n    const accordionRef = React.useRef<AccordionImplElement>(null);\n    const composedRefs = useComposedRefs(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === 'ltr';\n\n    const handleKeyDown = composeEventHandlers(props.onKeyDown, (event) => {\n      if (!ACCORDION_KEYS.includes(event.key)) return;\n      const target = event.target as HTMLElement;\n      const triggerCollection = getItems().filter((item) => !item.ref.current?.disabled);\n      const triggerIndex = triggerCollection.findIndex((item) => item.ref.current === target);\n      const triggerCount = triggerCollection.length;\n\n      if (triggerIndex === -1) return;\n\n      // Prevents page scroll while user is navigating\n      event.preventDefault();\n\n      let nextIndex = triggerIndex;\n      const homeIndex = 0;\n      const endIndex = triggerCount - 1;\n\n      const moveNext = () => {\n        nextIndex = triggerIndex + 1;\n        if (nextIndex > endIndex) {\n          nextIndex = homeIndex;\n        }\n      };\n\n      const movePrev = () => {\n        nextIndex = triggerIndex - 1;\n        if (nextIndex < homeIndex) {\n          nextIndex = endIndex;\n        }\n      };\n\n      switch (event.key) {\n        case 'Home':\n          nextIndex = homeIndex;\n          break;\n        case 'End':\n          nextIndex = endIndex;\n          break;\n        case 'ArrowRight':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              moveNext();\n            } else {\n              movePrev();\n            }\n          }\n          break;\n        case 'ArrowDown':\n          if (orientation === 'vertical') {\n            moveNext();\n          }\n          break;\n        case 'ArrowLeft':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              movePrev();\n            } else {\n              moveNext();\n            }\n          }\n          break;\n        case 'ArrowUp':\n          if (orientation === 'vertical') {\n            movePrev();\n          }\n          break;\n      }\n\n      const clampedIndex = nextIndex % triggerCount;\n      triggerCollection[clampedIndex]!.ref.current?.focus();\n    });\n\n    return (\n      <AccordionImplProvider\n        scope={__scopeAccordion}\n        disabled={disabled}\n        direction={dir}\n        orientation={orientation}\n      >\n        <Collection.Slot scope={__scopeAccordion}>\n          <Primitive.div\n            {...accordionProps}\n            data-orientation={orientation}\n            ref={composedRefs}\n            onKeyDown={disabled ? undefined : handleKeyDown}\n          />\n        </Collection.Slot>\n      </AccordionImplProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'AccordionItem';\n\ntype AccordionItemContextValue = { open?: boolean; disabled?: boolean; triggerId: string };\nconst [AccordionItemProvider, useAccordionItemContext] =\n  createAccordionContext<AccordionItemContextValue>(ITEM_NAME);\n\ntype AccordionItemElement = React.ElementRef<typeof CollapsiblePrimitive.Root>;\ntype CollapsibleProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Root>;\ninterface AccordionItemProps\n  extends Omit<CollapsibleProps, 'open' | 'defaultOpen' | 'onOpenChange'> {\n  /**\n   * Whether or not an accordion item is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * A string value for the accordion item. All items within an accordion should use a unique value.\n   */\n  value: string;\n}\n\n/**\n * `AccordionItem` contains all of the parts of a collapsible section inside of an `Accordion`.\n */\nconst AccordionItem = React.forwardRef<AccordionItemElement, AccordionItemProps>(\n  (props: ScopedProps<AccordionItemProps>, forwardedRef) => {\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = useId();\n    const open = (value && valueContext.value.includes(value)) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n\n    return (\n      <AccordionItemProvider\n        scope={__scopeAccordion}\n        open={open}\n        disabled={disabled}\n        triggerId={triggerId}\n      >\n        <CollapsiblePrimitive.Root\n          data-orientation={accordionContext.orientation}\n          data-state={getState(open)}\n          {...collapsibleScope}\n          {...accordionItemProps}\n          ref={forwardedRef}\n          disabled={disabled}\n          open={open}\n          onOpenChange={(open) => {\n            if (open) {\n              valueContext.onItemOpen(value);\n            } else {\n              valueContext.onItemClose(value);\n            }\n          }}\n        />\n      </AccordionItemProvider>\n    );\n  }\n);\n\nAccordionItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionHeader\n * -----------------------------------------------------------------------------------------------*/\n\nconst HEADER_NAME = 'AccordionHeader';\n\ntype AccordionHeaderElement = React.ElementRef<typeof Primitive.h3>;\ntype PrimitiveHeading3Props = React.ComponentPropsWithoutRef<typeof Primitive.h3>;\ninterface AccordionHeaderProps extends PrimitiveHeading3Props {}\n\n/**\n * `AccordionHeader` contains the content for the parts of an `AccordionItem` that will be visible\n * whether or not its content is collapsed.\n */\nconst AccordionHeader = React.forwardRef<AccordionHeaderElement, AccordionHeaderProps>(\n  (props: ScopedProps<AccordionHeaderProps>, forwardedRef) => {\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return (\n      <Primitive.h3\n        data-orientation={accordionContext.orientation}\n        data-state={getState(itemContext.open)}\n        data-disabled={itemContext.disabled ? '' : undefined}\n        {...headerProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nAccordionHeader.displayName = HEADER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'AccordionTrigger';\n\ntype AccordionTriggerElement = React.ElementRef<typeof CollapsiblePrimitive.Trigger>;\ntype CollapsibleTriggerProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Trigger>;\ninterface AccordionTriggerProps extends CollapsibleTriggerProps {}\n\n/**\n * `AccordionTrigger` is the trigger that toggles the collapsed state of an `AccordionItem`. It\n * should always be nested inside of an `AccordionHeader`.\n */\nconst AccordionTrigger = React.forwardRef<AccordionTriggerElement, AccordionTriggerProps>(\n  (props: ScopedProps<AccordionTriggerProps>, forwardedRef) => {\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <Collection.ItemSlot scope={__scopeAccordion}>\n        <CollapsiblePrimitive.Trigger\n          aria-disabled={(itemContext.open && !collapsibleContext.collapsible) || undefined}\n          data-orientation={accordionContext.orientation}\n          id={itemContext.triggerId}\n          {...collapsibleScope}\n          {...triggerProps}\n          ref={forwardedRef}\n        />\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nAccordionTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AccordionContent';\n\ntype AccordionContentElement = React.ElementRef<typeof CollapsiblePrimitive.Content>;\ntype CollapsibleContentProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Content>;\ninterface AccordionContentProps extends CollapsibleContentProps {}\n\n/**\n * `AccordionContent` contains the collapsible content for an `AccordionItem`.\n */\nconst AccordionContent = React.forwardRef<AccordionContentElement, AccordionContentProps>(\n  (props: ScopedProps<AccordionContentProps>, forwardedRef) => {\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <CollapsiblePrimitive.Content\n        role=\"region\"\n        aria-labelledby={itemContext.triggerId}\n        data-orientation={accordionContext.orientation}\n        {...collapsibleScope}\n        {...contentProps}\n        ref={forwardedRef}\n        style={{\n          ['--radix-accordion-content-height' as any]: 'var(--radix-collapsible-content-height)',\n          ['--radix-accordion-content-width' as any]: 'var(--radix-collapsible-content-width)',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nAccordionContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Accordion;\nconst Item = AccordionItem;\nconst Header = AccordionHeader;\nconst Trigger = AccordionTrigger;\nconst Content = AccordionContent;\n\nexport {\n  createAccordionScope,\n  //\n  Accordion,\n  AccordionItem,\n  AccordionHeader,\n  AccordionTrigger,\n  AccordionContent,\n  //\n  Root,\n  Item,\n  Header,\n  Trigger,\n  Content,\n};\nexport type {\n  AccordionSingleProps,\n  AccordionMultipleProps,\n  AccordionItemProps,\n  AccordionHeaderProps,\n  AccordionTriggerProps,\n  AccordionContentProps,\n};\n"], "names": ["value", "open", "Root", "<PERSON><PERSON>", "Content"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,WAAW;AAClB,SAAS,0BAA0B;AACnC,SAAS,wBAAwB;AACjC,SAAS,uBAAuB;AAChC,SAAS,4BAA4B;AACrC,SAAS,4BAA4B;AACrC,SAAS,iBAAiB;AAC1B,YAAY,0BAA0B;AAEtC,SAAS,aAAa;AAGtB,SAAS,oBAAoB;AAqCnB;;;;;;;;;;;;;;AA7BV,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;IAAC;IAAQ;IAAO;IAAa;IAAW;IAAa,YAAY;CAAA;AAExF,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,8KACrD,mBAAA,EAA0C,cAAc;AAG1D,IAAM,CAAC,wBAAwB,oBAAoB,CAAA,GAAI,6LAAA,EAAmB,gBAAgB;IACxF;4KACA,yBAAA;CACD;AACD,IAAM,kMAAsB,yBAAA,CAAuB;AAUnD,IAAM,iNAAY,WAAA,CAAM,UAAA,CACtB,CAAC,OAAmE,iBAAiB;IACnF,MAAM,EAAE,IAAA,EAAM,GAAG,eAAe,CAAA,GAAI;IACpC,MAAM,cAAc;IACpB,MAAM,gBAAgB;IACtB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,gBAAA;QAC/B,UAAA,SAAS,aACR,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;YAAuB,GAAG,aAAA;YAAe,KAAK;QAAA,CAAc,IAE7D,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,qBAAA;YAAqB,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA,CAE7D;AAEJ;AAGF,UAAU,WAAA,GAAc;AAUxB,IAAM,CAAC,wBAAwB,wBAAwB,CAAA,GACrD,uBAAmD,cAAc;AAEnE,IAAM,CAAC,8BAA8B,8BAA8B,CAAA,GAAI,uBACrE,gBACA;IAAE,aAAa;AAAM;AAyBvB,IAAM,4NAAsB,UAAA,CAAM,UAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EACJ,OAAO,SAAA,EACP,YAAA,EACA,gBAAgB,KAAO,CAAD,AAAC,EACvB,cAAc,KAAA,EACd,GAAG,sBACL,GAAI;IAEJ,MAAM,CAAC,OAAO,QAAQ,CAAA,gMAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,aAAa,gBAAgB;QAC7B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;QACC,OAAO,MAAM,gBAAA;QACb,6MAAO,UAAA,CAAM,OAAA,CAAQ,IAAO,QAAQ;gBAAC,KAAK;aAAA,GAAI,CAAC,CAAA,EAAI;YAAC,KAAK;SAAC;QAC1D,YAAY;QACZ,mNAAa,UAAA,CAAM,WAAA,CAAY,IAAM,eAAe,SAAS,EAAE,GAAG;YAAC;YAAa,QAAQ;SAAC;QAEzF,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,8BAAA;YAA6B,OAAO,MAAM,gBAAA;YAAkB;YAC3D,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,eAAA;gBAAe,GAAG,oBAAA;gBAAsB,KAAK;YAAA,CAAc;QAAA,CAC9D;IAAA;AAGN;AAsBF,IAAM,8NAAwB,UAAA,CAAM,UAAA,CAGlC,CAAC,OAAgD,iBAAiB;IAClE,MAAM,EACJ,OAAO,SAAA,EACP,YAAA,EACA,gBAAgB,KAAO,CAAD,AAAC,EACvB,GAAG,wBACL,GAAI;IAEJ,MAAM,CAAC,OAAO,QAAQ,CAAA,gMAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,aAAa,gBAAgB,CAAC,CAAA;QAC9B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,MAAM,uNAAiB,UAAA,CAAM,WAAA,CAC3B,CAAC,YAAsB,SAAS,CAAC,YAAY,CAAC,CAAA,GAAM,CAAC;mBAAG;gBAAW,SAAS;aAAC,GAC7E;QAAC,QAAQ;KAAA;IAGX,MAAM,wNAAkB,UAAA,CAAM,WAAA,CAC5B,CAAC,YACC,SAAS,CAAC,YAAY,CAAC,CAAA,GAAM,UAAU,MAAA,CAAO,CAACA,SAAUA,WAAU,SAAS,CAAC,GAC/E;QAAC,QAAQ;KAAA;IAGX,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;QACC,OAAO,MAAM,gBAAA;QACb;QACA,YAAY;QACZ,aAAa;QAEb,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,8BAAA;YAA6B,OAAO,MAAM,gBAAA;YAAkB,aAAa;YACxE,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,eAAA;gBAAe,GAAG,sBAAA;gBAAwB,KAAK;YAAA,CAAc;QAAA,CAChE;IAAA;AAGN,CAAC;AAUD,IAAM,CAAC,uBAAuB,mBAAmB,CAAA,GAC/C,uBAAkD,cAAc;AAsBlE,IAAM,sNAAgB,UAAA,CAAM,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,gBAAA,EAAkB,QAAA,EAAU,GAAA,EAAK,cAAc,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IACzF,MAAM,qNAAe,UAAA,CAAM,MAAA,CAA6B,IAAI;IAC5D,MAAM,eAAe,kMAAA,EAAgB,cAAc,YAAY;IAC/D,MAAM,WAAW,cAAc,gBAAgB;IAC/C,MAAM,gBAAY,qLAAA,EAAa,GAAG;IAClC,MAAM,iBAAiB,cAAc;IAErC,MAAM,iLAAgB,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;QACrE,IAAI,CAAC,eAAe,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA;QACzC,MAAM,SAAS,MAAM,MAAA;QACrB,MAAM,oBAAoB,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,GAAA,CAAI,OAAA,EAAS,QAAQ;QACjF,MAAM,eAAe,kBAAkB,SAAA,CAAU,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,MAAM;QACtF,MAAM,eAAe,kBAAkB,MAAA;QAEvC,IAAI,iBAAiB,CAAA,EAAI,CAAA;QAGzB,MAAM,cAAA,CAAe;QAErB,IAAI,YAAY;QAChB,MAAM,YAAY;QAClB,MAAM,WAAW,eAAe;QAEhC,MAAM,WAAW,MAAM;YACrB,YAAY,eAAe;YAC3B,IAAI,YAAY,UAAU;gBACxB,YAAY;YACd;QACF;QAEA,MAAM,WAAW,MAAM;YACrB,YAAY,eAAe;YAC3B,IAAI,YAAY,WAAW;gBACzB,YAAY;YACd;QACF;QAEA,OAAQ,MAAM,GAAA,EAAK;YACjB,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,IAAI,gBAAgB,cAAc;oBAChC,IAAI,gBAAgB;wBAClB,SAAS;oBACX,OAAO;wBACL,SAAS;oBACX;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,gBAAgB,YAAY;oBAC9B,SAAS;gBACX;gBACA;YACF,KAAK;gBACH,IAAI,gBAAgB,cAAc;oBAChC,IAAI,gBAAgB;wBAClB,SAAS;oBACX,OAAO;wBACL,SAAS;oBACX;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,gBAAgB,YAAY;oBAC9B,SAAS;gBACX;gBACA;QACJ;QAEA,MAAM,eAAe,YAAY;QACjC,iBAAA,CAAkB,YAAY,CAAA,CAAG,GAAA,CAAI,OAAA,EAAS,MAAM;IACtD,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;QACC,OAAO;QACP;QACA,WAAW;QACX;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;YAAgB,OAAO;YACtB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;gBACE,GAAG,cAAA;gBACJ,oBAAkB;gBAClB,KAAK;gBACL,WAAW,WAAW,KAAA,IAAY;YAAA;QACpC,CACF;IAAA;AAGN;AAOF,IAAM,YAAY;AAGlB,IAAM,CAAC,uBAAuB,uBAAuB,CAAA,GACnD,uBAAkD,SAAS;AAqB7D,IAAM,sNAAgB,UAAA,CAAM,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,gBAAA,EAAkB,KAAA,EAAO,GAAG,mBAAmB,CAAA,GAAI;IAC3D,MAAM,mBAAmB,oBAAoB,WAAW,gBAAgB;IACxE,MAAM,eAAe,yBAAyB,WAAW,gBAAgB;IACzE,MAAM,mBAAmB,oBAAoB,gBAAgB;IAC7D,MAAM,+KAAY,QAAA,CAAM;IACxB,MAAM,OAAQ,SAAS,aAAa,KAAA,CAAM,QAAA,CAAS,KAAK,KAAM;IAC9D,MAAM,WAAW,iBAAiB,QAAA,IAAY,MAAM,QAAA;IAEpD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;QACC,OAAO;QACP;QACA;QACA;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,0KAAsB,OAAA,EAArB;YACC,oBAAkB,iBAAiB,WAAA;YACnC,cAAY,SAAS,IAAI;YACxB,GAAG,gBAAA;YACH,GAAG,kBAAA;YACJ,KAAK;YACL;YACA;YACA,cAAc,CAACC,UAAS;gBACtB,IAAIA,OAAM;oBACR,aAAa,UAAA,CAAW,KAAK;gBAC/B,OAAO;oBACL,aAAa,WAAA,CAAY,KAAK;gBAChC;YACF;QAAA;IACF;AAGN;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,cAAc;AAUpB,IAAM,kBAAkB,gNAAA,CAAM,UAAA,CAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EAAE,gBAAA,EAAkB,GAAG,YAAY,CAAA,GAAI;IAC7C,MAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;IAC7E,MAAM,cAAc,wBAAwB,aAAa,gBAAgB;IACzE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,EAAA,EAAV;QACC,oBAAkB,iBAAiB,WAAA;QACnC,cAAY,SAAS,YAAY,IAAI;QACrC,iBAAe,YAAY,QAAA,GAAW,KAAK,KAAA;QAC1C,GAAG,WAAA;QACJ,KAAK;IAAA;AAGX;AAGF,gBAAgB,WAAA,GAAc;AAM9B,IAAM,eAAe;AAUrB,IAAM,yNAAmB,UAAA,CAAM,UAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,gBAAA,EAAkB,GAAG,aAAa,CAAA,GAAI;IAC9C,MAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;IAC7E,MAAM,cAAc,wBAAwB,cAAc,gBAAgB;IAC1E,MAAM,qBAAqB,+BAA+B,cAAc,gBAAgB;IACxF,MAAM,mBAAmB,oBAAoB,gBAAgB;IAC7D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO;QAC1B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,0KAAsB,UAAA,EAArB;YACC,iBAAgB,YAAY,IAAA,IAAQ,CAAC,mBAAmB,WAAA,IAAgB,KAAA;YACxE,oBAAkB,iBAAiB,WAAA;YACnC,IAAI,YAAY,SAAA;YACf,GAAG,gBAAA;YACH,GAAG,YAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,eAAe;AASrB,IAAM,yNAAmB,UAAA,CAAM,UAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,gBAAA,EAAkB,GAAG,aAAa,CAAA,GAAI;IAC9C,MAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;IAC7E,MAAM,cAAc,wBAAwB,cAAc,gBAAgB;IAC1E,MAAM,mBAAmB,oBAAoB,gBAAgB;IAC7D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,0KAAsB,UAAA,EAArB;QACC,MAAK;QACL,mBAAiB,YAAY,SAAA;QAC7B,oBAAkB,iBAAiB,WAAA;QAClC,GAAG,gBAAA;QACH,GAAG,YAAA;QACJ,KAAK;QACL,OAAO;YACL,CAAC,kCAAyC,CAAA,EAAG;YAC7C,CAAC,iCAAwC,CAAA,EAAG;YAC5C,GAAG,MAAM,KAAA;QACX;IAAA;AAGN;AAGF,iBAAiB,WAAA,GAAc;AAI/B,SAAS,SAAS,IAAA,EAAgB;IAChC,OAAO,OAAO,SAAS;AACzB;AAEA,IAAMC,QAAO;AACb,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,WAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2383, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2429, "column": 0}, "map": {"version": 3, "file": "pen.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z',\n      key: '1a8usu',\n    },\n  ],\n];\n\n/**\n * @component @name Pen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuMTc0IDYuODEyYTEgMSAwIDAgMC0zLjk4Ni0zLjk4N0wzLjg0MiAxNi4xNzRhMiAyIDAgMCAwLS41LjgzbC0xLjMyMSA0LjM1MmEuNS41IDAgMCAwIC42MjMuNjIybDQuMzUzLTEuMzJhMiAyIDAgMCAwIC44My0uNDk3eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pen = createLucideIcon('pen', __iconNode);\n\nexport default Pen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2468, "column": 0}, "map": {"version": 3, "file": "bold.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/bold.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    { d: 'M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8', key: 'mg9rjx' },\n  ],\n];\n\n/**\n * @component @name Bold\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAxMmg5YTQgNCAwIDAgMSAwIDhIN2ExIDEgMCAwIDEtMS0xVjVhMSAxIDAgMCAxIDEtMWg3YTQgNCAwIDAgMSAwIDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bold\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bold = createLucideIcon('bold', __iconNode);\n\nexport default Bold;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YAAE,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,QAAS;QAAA;KAC9F;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2507, "column": 0}, "map": {"version": 3, "file": "italic.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/italic.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '19', x2: '10', y1: '4', y2: '4', key: '15jd3p' }],\n  ['line', { x1: '14', x2: '5', y1: '20', y2: '20', key: 'bu0au3' }],\n  ['line', { x1: '15', x2: '9', y1: '4', y2: '20', key: 'uljnxc' }],\n];\n\n/**\n * @component @name Italic\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTkiIHgyPSIxMCIgeTE9IjQiIHkyPSI0IiAvPgogIDxsaW5lIHgxPSIxNCIgeDI9IjUiIHkxPSIyMCIgeTI9IjIwIiAvPgogIDxsaW5lIHgxPSIxNSIgeDI9IjkiIHkxPSI0IiB5Mj0iMjAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/italic\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Italic = createLucideIcon('italic', __iconNode);\n\nexport default Italic;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAClE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2569, "column": 0}, "map": {"version": 3, "file": "list.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/list.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12h.01', key: 'nlz23k' }],\n  ['path', { d: 'M3 18h.01', key: '1tta3j' }],\n  ['path', { d: 'M3 6h.01', key: '1rqtza' }],\n  ['path', { d: 'M8 12h13', key: '1za7za' }],\n  ['path', { d: 'M8 18h13', key: '1lx6n3' }],\n  ['path', { d: 'M8 6h13', key: 'ik3vkj' }],\n];\n\n/**\n * @component @name List\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmguMDEiIC8+CiAgPHBhdGggZD0iTTMgMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik0zIDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEyaDEzIiAvPgogIDxwYXRoIGQ9Ik04IDE4aDEzIiAvPgogIDxwYXRoIGQ9Ik04IDZoMTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/list\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst List = createLucideIcon('list', __iconNode);\n\nexport default List;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC1C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2643, "column": 0}, "map": {"version": 3, "file": "list-ordered.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/list-ordered.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 12h11', key: '6m4ad9' }],\n  ['path', { d: 'M10 18h11', key: '11hvi2' }],\n  ['path', { d: 'M10 6h11', key: 'c7qv1k' }],\n  ['path', { d: 'M4 10h2', key: '16xx2s' }],\n  ['path', { d: 'M4 6h1v4', key: 'cnovpq' }],\n  ['path', { d: 'M6 18H4c0-1 2-2 2-3s-1-1.5-2-1', key: 'm9a95d' }],\n];\n\n/**\n * @component @name ListOrdered\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTJoMTEiIC8+CiAgPHBhdGggZD0iTTEwIDE4aDExIiAvPgogIDxwYXRoIGQ9Ik0xMCA2aDExIiAvPgogIDxwYXRoIGQ9Ik00IDEwaDIiIC8+CiAgPHBhdGggZD0iTTQgNmgxdjQiIC8+CiAgPHBhdGggZD0iTTYgMThINGMwLTEgMi0yIDItM3MtMS0xLjUtMi0xIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/list-ordered\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ListOrdered = createLucideIcon('list-ordered', __iconNode);\n\nexport default ListOrdered;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACjE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2717, "column": 0}, "map": {"version": 3, "file": "rotate-ccw.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/rotate-ccw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8', key: '1357e3' }],\n  ['path', { d: 'M3 3v5h5', key: '1xhq8a' }],\n];\n\n/**\n * @component @name RotateCcw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAxIDAgOS05IDkuNzUgOS43NSAwIDAgMC02Ljc0IDIuNzRMMyA4IiAvPgogIDxwYXRoIGQ9Ik0zIDN2NWg1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/rotate-ccw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RotateCcw = createLucideIcon('rotate-ccw', __iconNode);\n\nexport default RotateCcw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAqD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2763, "column": 0}, "map": {"version": 3, "file": "rotate-cw.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/rotate-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8', key: '1p45f6' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n];\n\n/**\n * @component @name RotateCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTktOWMyLjUyIDAgNC45MyAxIDYuNzQgMi43NEwyMSA4IiAvPgogIDxwYXRoIGQ9Ik0yMSAzdjVoLTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/rotate-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RotateCw = createLucideIcon('rotate-cw', __iconNode);\n\nexport default RotateCw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAqD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2809, "column": 0}, "map": {"version": 3, "file": "trash-2.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}