{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { getToken } from 'next-auth/jwt';\r\nimport { NextResponse } from 'next/server';\r\nimport type { NextRequest } from 'next/server';\r\n\r\nexport async function middleware(request: NextRequest) {\r\n  console.log('🔍 Middleware running for:', request.nextUrl.pathname);\r\n\r\n  const token = await getToken({ req: request });\r\n  const isAuthenticated = !!token;\r\n\r\n  // Public paths that don't require authentication\r\n  const publicPaths = ['/login', '/logout', '/api/auth', '/api/hello'];\r\n  const isPublicPath = publicPaths.some(path => request.nextUrl.pathname.startsWith(path));\r\n\r\n  console.log('🔐 Auth check:', {\r\n    pathname: request.nextUrl.pathname,\r\n    isAuthenticated,\r\n    isPublicPath,\r\n    publicPaths\r\n  });\r\n\r\n  // Root path should redirect to logout page when not authenticated\r\n  const isRootPath = request.nextUrl.pathname === '/';\r\n\r\n  // If the user is trying to access a protected route without authentication\r\n  if (!isAuthenticated && !isPublicPath) {\r\n    if (isRootPath) {\r\n      console.log('❌ Redirecting root to logout page:', request.nextUrl.pathname);\r\n      return NextResponse.redirect(new URL('/logout', request.url));\r\n    } else {\r\n      console.log('❌ Redirecting to login:', request.nextUrl.pathname);\r\n      return NextResponse.redirect(new URL('/login', request.url));\r\n    }\r\n  }\r\n\r\n  // If the user is authenticated and trying to access login page\r\n  if (isAuthenticated && request.nextUrl.pathname === '/login') {\r\n    console.log('✅ Redirecting authenticated user to landing page');\r\n    return NextResponse.redirect(new URL('/landingpage_v2', request.url));\r\n  }\r\n\r\n  console.log('✅ Allowing request to proceed:', request.nextUrl.pathname);\r\n  return NextResponse.next();\r\n}\r\n\r\nexport const config = {\r\n  matcher: ['/((?!api/auth|api/hello|_next/static|_next/image|favicon.ico).*)'],\r\n};"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAGO,eAAe,WAAW,OAAoB;IACnD,QAAQ,GAAG,CAAC,8BAA8B,QAAQ,OAAO,CAAC,QAAQ;IAElE,MAAM,QAAQ,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;IAAQ;IAC5C,MAAM,kBAAkB,CAAC,CAAC;IAE1B,iDAAiD;IACjD,MAAM,cAAc;QAAC;QAAU;QAAW;QAAa;KAAa;IACpE,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,OAAQ,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAElF,QAAQ,GAAG,CAAC,kBAAkB;QAC5B,UAAU,QAAQ,OAAO,CAAC,QAAQ;QAClC;QACA;QACA;IACF;IAEA,kEAAkE;IAClE,MAAM,aAAa,QAAQ,OAAO,CAAC,QAAQ,KAAK;IAEhD,2EAA2E;IAC3E,IAAI,CAAC,mBAAmB,CAAC,cAAc;QACrC,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,sCAAsC,QAAQ,OAAO,CAAC,QAAQ;YAC1E,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,WAAW,QAAQ,GAAG;QAC7D,OAAO;YACL,QAAQ,GAAG,CAAC,2BAA2B,QAAQ,OAAO,CAAC,QAAQ;YAC/D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D;IACF;IAEA,+DAA+D;IAC/D,IAAI,mBAAmB,QAAQ,OAAO,CAAC,QAAQ,KAAK,UAAU;QAC5D,QAAQ,GAAG,CAAC;QACZ,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,mBAAmB,QAAQ,GAAG;IACrE;IAEA,QAAQ,GAAG,CAAC,kCAAkC,QAAQ,OAAO,CAAC,QAAQ;IACtE,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QAAC;KAAmE;AAC/E"}}]}