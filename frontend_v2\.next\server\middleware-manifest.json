{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hULMpil43EtZvKmSJ9dAA", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "kyBzAMl7P2rx1euBZIzdSvNaZ0LqAbvt6yUzec37hOc=", "__NEXT_PREVIEW_MODE_ID": "1784ddb46faa5b9fefc379e58630e66d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d499519a2ab33b60ce19ee889937c07b4100cf25ba56a2bf9e8fe4e98b021dc2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0a959099b39e8fcb07aa0dd90fb792ef4ce190c38c63d95bdbb64cfb7f583f98"}}}, "functions": {}, "sortedMiddleware": ["/"]}