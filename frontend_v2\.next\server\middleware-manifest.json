{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "91o8kKduE7r8nGvgh3NJr", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "kyBzAMl7P2rx1euBZIzdSvNaZ0LqAbvt6yUzec37hOc=", "__NEXT_PREVIEW_MODE_ID": "81b18931eda5efaa1c3fce467ee6dacd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cc23c40e84944f9a08358dc5f7127ec7477377146514a4be24a249d89be9094f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "70f85b5cd418f3644d416ca09f675676a8dde3205a2ffdb18c445ced8fc47dc0"}}}, "functions": {}, "sortedMiddleware": ["/"]}