{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0deeb1a4._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_b1a685ee.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|api/hello|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|api/hello|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "kyBzAMl7P2rx1euBZIzdSvNaZ0LqAbvt6yUzec37hOc=", "__NEXT_PREVIEW_MODE_ID": "4304066eb6b84139f1820e97871a7ebc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5f60fb241c52eec917bd5d0eefe3160ce71b79862d0dbdc9820f25a2d1337cca", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "15f590218c6ae0ce5a1c1c0dcea8579bc4ad49e6cc88b5f7d4f41713f9703b39"}}}, "instrumentation": null, "functions": {}}