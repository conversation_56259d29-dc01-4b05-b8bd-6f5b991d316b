{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0deeb1a4._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_b1a685ee.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "kyBzAMl7P2rx1euBZIzdSvNaZ0LqAbvt6yUzec37hOc=", "__NEXT_PREVIEW_MODE_ID": "877afedcbe75f4c8cdb313b4686b1497", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "56252c70f5c1e4ef00d30c003a4409fc9b81325eca48f4b53259588a638453fe", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "16324a58d6161bd473ab4236753a222883a45e58e8663429746039f71a08d969"}}}, "instrumentation": null, "functions": {}}