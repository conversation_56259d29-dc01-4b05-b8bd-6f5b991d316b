"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1055],{381:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1007:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1362:(e,t,r)=>{r.d(t,{D:()=>s,N:()=>d});var n=r(2115),o=(e,t,r,n,o,a,l,i)=>{let u=document.documentElement,s=["light","dark"];function d(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?o.map(e=>a[e]||e):o;r?(u.classList.remove(...n),u.classList.add(a&&a[t]?a[t]:t)):u.setAttribute(e,t)}),r=t,i&&s.includes(r)&&(u.style.colorScheme=r)}if(n)d(n);else try{let e=localStorage.getItem(t)||r,n=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(n)}catch(e){}},a=["light","dark"],l="(prefers-color-scheme: dark)",i=n.createContext(void 0),u={setTheme:e=>{},themes:[]},s=()=>{var e;return null!=(e=n.useContext(i))?e:u},d=e=>n.useContext(i)?n.createElement(n.Fragment,null,e.children):n.createElement(f,{...e}),c=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:o=!0,enableColorScheme:u=!0,storageKey:s="theme",themes:d=c,defaultTheme:f=o?"system":"light",attribute:g="data-theme",value:w,children:y,nonce:x,scriptProps:b}=e,[C,k]=n.useState(()=>m(s,f)),[M,R]=n.useState(()=>"system"===C?h():C),j=w?Object.values(w):d,E=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=h());let n=w?w[t]:t,l=r?v(x):null,i=document.documentElement,s=e=>{"class"===e?(i.classList.remove(...j),n&&i.classList.add(n)):e.startsWith("data-")&&(n?i.setAttribute(e,n):i.removeAttribute(e))};if(Array.isArray(g)?g.forEach(s):s(g),u){let e=a.includes(f)?f:null,r=a.includes(t)?t:e;i.style.colorScheme=r}null==l||l()},[x]),S=n.useCallback(e=>{let t="function"==typeof e?e(C):e;k(t);try{localStorage.setItem(s,t)}catch(e){}},[C]),D=n.useCallback(e=>{R(h(e)),"system"===C&&o&&!t&&E("system")},[C,t]);n.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(D),D(e),()=>e.removeListener(D)},[D]),n.useEffect(()=>{let e=e=>{e.key===s&&(e.newValue?k(e.newValue):S(f))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[S]),n.useEffect(()=>{E(null!=t?t:C)},[t,C]);let _=n.useMemo(()=>({theme:C,setTheme:S,forcedTheme:t,resolvedTheme:"system"===C?M:C,themes:o?[...d,"system"]:d,systemTheme:o?M:void 0}),[C,S,t,M,o,d]);return n.createElement(i.Provider,{value:_},n.createElement(p,{forcedTheme:t,storageKey:s,attribute:g,enableSystem:o,enableColorScheme:u,defaultTheme:f,value:w,themes:d,nonce:x,scriptProps:b}),y)},p=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:l,enableColorScheme:i,defaultTheme:u,value:s,themes:d,nonce:c,scriptProps:f}=e,p=JSON.stringify([a,r,u,t,d,s,l,i]).slice(1,-1);return n.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(p,")")}})}),m=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},v=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},h=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},1414:(e,t,r)=>{e.exports=r(2436)},2098:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2436:(e,t,r)=>{var n=r(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,l=n.useEffect,i=n.useLayoutEffect,u=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),o=n[0].inst,d=n[1];return i(function(){o.value=r,o.getSnapshot=t,s(o)&&d({inst:o})},[e,r,t]),l(function(){return s(o)&&d({inst:o}),e(function(){s(o)&&d({inst:o})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:d},3509:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},4011:(e,t,r)=>{r.d(t,{H4:()=>M,_V:()=>k,bL:()=>C});var n=r(2115),o=r(6081),a=r(9033),l=r(2712),i=r(3540),u=r(1414);function s(){return()=>{}}var d=r(5155),c="Avatar",[f,p]=(0,o.A)(c),[m,v]=f(c),h=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,l]=n.useState("idle");return(0,d.jsx)(m,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,d.jsx)(i.sG.span,{...o,ref:t})})});h.displayName=c;var g="AvatarImage",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:c=()=>{},...f}=e,p=v(g,r),m=function(e,t){let{referrerPolicy:r,crossOrigin:o}=t,a=(0,u.useSyncExternalStore)(s,()=>!0,()=>!1),i=n.useRef(null),d=a?(i.current||(i.current=new window.Image),i.current):null,[c,f]=n.useState(()=>b(d,e));return(0,l.N)(()=>{f(b(d,e))},[d,e]),(0,l.N)(()=>{let e=e=>()=>{f(e)};if(!d)return;let t=e("loaded"),n=e("error");return d.addEventListener("load",t),d.addEventListener("error",n),r&&(d.referrerPolicy=r),"string"==typeof o&&(d.crossOrigin=o),()=>{d.removeEventListener("load",t),d.removeEventListener("error",n)}},[d,o,r]),c}(o,f),h=(0,a.c)(e=>{c(e),p.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==m&&h(m)},[m,h]),"loaded"===m?(0,d.jsx)(i.sG.img,{...f,ref:t,src:o}):null});w.displayName=g;var y="AvatarFallback",x=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,l=v(y,r),[u,s]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==l.imageLoadingStatus?(0,d.jsx)(i.sG.span,{...a,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=y;var C=h,k=w,M=x},4835:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},6215:(e,t,r)=>{r.d(t,{UC:()=>eZ,q7:()=>eY,JU:()=>eJ,ZL:()=>eX,bL:()=>ez,wv:()=>eQ,l9:()=>eq});var n=r(2115),o=r(5185),a=r(6101),l=r(6081),i=r(5845),u=r(3540),s=r(7683),d=r(4315),c=r(9178),f=r(2293),p=r(7900),m=r(1285),v=r(5152),h=r(4378),g=r(8905),w=r(9196),y=r(5155),x=Symbol("radix.slottable");function b(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===x}var C=r(9033),k=r(8168),M=r(3795),R=["Enter"," "],j=["ArrowUp","PageDown","End"],E=["ArrowDown","PageUp","Home",...j],S={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},D={ltr:["ArrowLeft"],rtl:["ArrowRight"]},_="Menu",[A,I,T]=(0,s.N)(_),[L,P]=(0,l.A)(_,[T,v.Bk,w.RG]),N=(0,v.Bk)(),O=(0,w.RG)(),[F,K]=L(_),[G,V]=L(_),B=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:l,modal:i=!0}=e,u=N(t),[s,c]=n.useState(null),f=n.useRef(!1),p=(0,C.c)(l),m=(0,d.jH)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,y.jsx)(v.bL,{...u,children:(0,y.jsx)(F,{scope:t,open:r,onOpenChange:p,content:s,onContentChange:c,children:(0,y.jsx)(G,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:i,children:o})})})};B.displayName=_;var U=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=N(r);return(0,y.jsx)(v.Mz,{...o,...n,ref:t})});U.displayName="MenuAnchor";var H="MenuPortal",[W,z]=L(H,{forceMount:void 0}),q=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=K(H,t);return(0,y.jsx)(W,{scope:t,forceMount:r,children:(0,y.jsx)(g.C,{present:r||a.open,children:(0,y.jsx)(h.Z,{asChild:!0,container:o,children:n})})})};q.displayName=H;var X="MenuContent",[Z,J]=L(X),Y=n.forwardRef((e,t)=>{let r=z(X,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=K(X,e.__scopeMenu),l=V(X,e.__scopeMenu);return(0,y.jsx)(A.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(g.C,{present:n||a.open,children:(0,y.jsx)(A.Slot,{scope:e.__scopeMenu,children:l.modal?(0,y.jsx)(Q,{...o,ref:t}):(0,y.jsx)($,{...o,ref:t})})})})}),Q=n.forwardRef((e,t)=>{let r=K(X,e.__scopeMenu),l=n.useRef(null),i=(0,a.s)(t,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,k.Eq)(e)},[]),(0,y.jsx)(et,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),$=n.forwardRef((e,t)=>{let r=K(X,e.__scopeMenu);return(0,y.jsx)(et,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),ee=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{var r,o,l;let i,u,{children:s,...d}=e,c=n.isValidElement(s)?(u=(i=null==(o=Object.getOwnPropertyDescriptor((r=s).props,"ref"))?void 0:o.get)&&"isReactWarning"in i&&i.isReactWarning)?r.ref:(u=(i=null==(l=Object.getOwnPropertyDescriptor(r,"ref"))?void 0:l.get)&&"isReactWarning"in i&&i.isReactWarning)?r.props.ref:r.props.ref||r.ref:void 0,f=(0,a.s)(c,t);if(n.isValidElement(s)){let e=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=a(...t);return o(...t),n}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(d,s.props);return s.type!==n.Fragment&&(e.ref=f),n.cloneElement(s,e)}return n.Children.count(s)>1?n.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),r=n.forwardRef((e,r)=>{let{children:o,...a}=e,l=n.Children.toArray(o),i=l.find(b);if(i){let e=i.props.children,o=l.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,y.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,y.jsx)(t,{...a,ref:r,children:o})});return r.displayName="".concat(e,".Slot"),r}("MenuContent.ScrollLock"),et=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:l=!1,trapFocus:i,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:x,onInteractOutside:b,onDismiss:C,disableOutsideScroll:k,...R}=e,S=K(X,r),D=V(X,r),_=N(r),A=O(r),T=I(r),[L,P]=n.useState(null),F=n.useRef(null),G=(0,a.s)(t,F,S.onContentChange),B=n.useRef(0),U=n.useRef(""),H=n.useRef(0),W=n.useRef(null),z=n.useRef("right"),q=n.useRef(0),J=k?M.A:n.Fragment,Y=e=>{var t,r;let n=U.current+e,o=T().filter(e=>!e.disabled),a=document.activeElement,l=null==(t=o.find(e=>e.ref.current===a))?void 0:t.textValue,i=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,l=(n=Math.max(a,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(l=l.filter(e=>e!==r));let i=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==r?i:void 0}(o.map(e=>e.textValue),n,l),u=null==(r=o.find(e=>e.textValue===i))?void 0:r.ref.current;!function e(t){U.current=t,window.clearTimeout(B.current),""!==t&&(B.current=window.setTimeout(()=>e(""),1e3))}(n),u&&setTimeout(()=>u.focus())};n.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,f.Oh)();let Q=n.useCallback(e=>{var t,r;return z.current===(null==(t=W.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e],i=t[a],u=l.x,s=l.y,d=i.x,c=i.y;s>n!=c>n&&r<(d-u)*(n-s)/(c-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(r=W.current)?void 0:r.area)},[]);return(0,y.jsx)(Z,{scope:r,searchRef:U,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{var t;Q(e)||(null==(t=F.current)||t.focus(),P(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:H,onPointerGraceIntentChange:n.useCallback(e=>{W.current=e},[]),children:(0,y.jsx)(J,{...k?{as:ee,allowPinchZoom:!0}:void 0,children:(0,y.jsx)(p.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.m)(u,e=>{var t;e.preventDefault(),null==(t=F.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,y.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:x,onInteractOutside:b,onDismiss:C,children:(0,y.jsx)(w.bL,{asChild:!0,...A,dir:D.dir,orientation:"vertical",loop:l,currentTabStopId:L,onCurrentTabStopIdChange:P,onEntryFocus:(0,o.m)(m,e=>{D.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,y.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":eE(S.open),"data-radix-menu-content":"",dir:D.dir,..._,...R,ref:G,style:{outline:"none",...R.style},onKeyDown:(0,o.m)(R.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&Y(e.key));let o=F.current;if(e.target!==o||!E.includes(e.key))return;e.preventDefault();let a=T().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),U.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,e_(e=>{let t=e.target,r=q.current!==e.clientX;e.currentTarget.contains(t)&&r&&(z.current=e.clientX>q.current?"right":"left",q.current=e.clientX)}))})})})})})})});Y.displayName=X;var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,y.jsx)(u.sG.div,{role:"group",...n,ref:t})});er.displayName="MenuGroup";var en=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,y.jsx)(u.sG.div,{...n,ref:t})});en.displayName="MenuLabel";var eo="MenuItem",ea="menu.itemSelect",el=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:l,...i}=e,s=n.useRef(null),d=V(eo,e.__scopeMenu),c=J(eo,e.__scopeMenu),f=(0,a.s)(t,s),p=n.useRef(!1);return(0,y.jsx)(ei,{...i,ref:f,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!r&&e){let t=new CustomEvent(ea,{bubbles:!0,cancelable:!0});e.addEventListener(ea,e=>null==l?void 0:l(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:d.onClose()}}),onPointerDown:t=>{var r;null==(r=e.onPointerDown)||r.call(e,t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;r||t&&" "===e.key||R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});el.displayName=eo;var ei=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:l=!1,textValue:i,...s}=e,d=J(eo,r),c=O(r),f=n.useRef(null),p=(0,a.s)(t,f),[m,v]=n.useState(!1),[h,g]=n.useState("");return n.useEffect(()=>{let e=f.current;if(e){var t;g((null!=(t=e.textContent)?t:"").trim())}},[s.children]),(0,y.jsx)(A.ItemSlot,{scope:r,disabled:l,textValue:null!=i?i:h,children:(0,y.jsx)(w.q7,{asChild:!0,...c,focusable:!l,children:(0,y.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e_(e=>{l?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,e_(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),eu=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,y.jsx)(eh,{scope:e.__scopeMenu,checked:r,children:(0,y.jsx)(el,{role:"menuitemcheckbox","aria-checked":eS(r)?"mixed":r,...a,ref:t,"data-state":eD(r),onSelect:(0,o.m)(a.onSelect,()=>null==n?void 0:n(!!eS(r)||!r),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var es="MenuRadioGroup",[ed,ec]=L(es,{value:void 0,onValueChange:()=>{}}),ef=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,C.c)(n);return(0,y.jsx)(ed,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,y.jsx)(er,{...o,ref:t})})});ef.displayName=es;var ep="MenuRadioItem",em=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=ec(ep,e.__scopeMenu),l=r===a.value;return(0,y.jsx)(eh,{scope:e.__scopeMenu,checked:l,children:(0,y.jsx)(el,{role:"menuitemradio","aria-checked":l,...n,ref:t,"data-state":eD(l),onSelect:(0,o.m)(n.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});em.displayName=ep;var ev="MenuItemIndicator",[eh,eg]=L(ev,{checked:!1}),ew=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=eg(ev,r);return(0,y.jsx)(g.C,{present:n||eS(a.checked)||!0===a.checked,children:(0,y.jsx)(u.sG.span,{...o,ref:t,"data-state":eD(a.checked)})})});ew.displayName=ev;var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,y.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ey.displayName="MenuSeparator";var ex=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=N(r);return(0,y.jsx)(v.i3,{...o,...n,ref:t})});ex.displayName="MenuArrow";var[eb,eC]=L("MenuSub"),ek="MenuSubTrigger",eM=n.forwardRef((e,t)=>{let r=K(ek,e.__scopeMenu),l=V(ek,e.__scopeMenu),i=eC(ek,e.__scopeMenu),u=J(ek,e.__scopeMenu),s=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=u,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,y.jsx)(U,{asChild:!0,...f,children:(0,y.jsx)(ei,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":eE(r.open),...e,ref:(0,a.t)(t,i.onTriggerChange),onClick:t=>{var n;null==(n=e.onClick)||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,e_(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,e_(e=>{var t,n;p();let o=null==(t=r.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(n=r.content)?void 0:n.dataset.side,a="right"===t,l=o[a?"left":"right"],i=o[a?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==u.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&S[l.dir].includes(t.key)){var o;r.onOpenChange(!0),null==(o=r.content)||o.focus(),t.preventDefault()}})})})});eM.displayName=ek;var eR="MenuSubContent",ej=n.forwardRef((e,t)=>{let r=z(X,e.__scopeMenu),{forceMount:l=r.forceMount,...i}=e,u=K(X,e.__scopeMenu),s=V(X,e.__scopeMenu),d=eC(eR,e.__scopeMenu),c=n.useRef(null),f=(0,a.s)(t,c);return(0,y.jsx)(A.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(g.C,{present:l||u.open,children:(0,y.jsx)(A.Slot,{scope:e.__scopeMenu,children:(0,y.jsx)(et,{id:d.contentId,"aria-labelledby":d.triggerId,...i,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null==(t=c.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=D[s.dir].includes(e.key);if(t&&r){var n;u.onOpenChange(!1),null==(n=d.trigger)||n.focus(),e.preventDefault()}})})})})})});function eE(e){return e?"open":"closed"}function eS(e){return"indeterminate"===e}function eD(e){return eS(e)?"indeterminate":e?"checked":"unchecked"}function e_(e){return t=>"mouse"===t.pointerType?e(t):void 0}ej.displayName=eR;var eA="DropdownMenu",[eI,eT]=(0,l.A)(eA,[P]),eL=P(),[eP,eN]=eI(eA),eO=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:l,onOpenChange:u,modal:s=!0}=e,d=eL(t),c=n.useRef(null),[f,p]=(0,i.i)({prop:a,defaultProp:null!=l&&l,onChange:u,caller:eA});return(0,y.jsx)(eP,{scope:t,triggerId:(0,m.B)(),triggerRef:c,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,y.jsx)(B,{...d,open:f,onOpenChange:p,dir:o,modal:s,children:r})})};eO.displayName=eA;var eF="DropdownMenuTrigger",eK=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...l}=e,i=eN(eF,r),s=eL(r);return(0,y.jsx)(U,{asChild:!0,...s,children:(0,y.jsx)(u.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,a.t)(t,i.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eK.displayName=eF;var eG=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eL(t);return(0,y.jsx)(q,{...n,...r})};eG.displayName="DropdownMenuPortal";var eV="DropdownMenuContent",eB=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,l=eN(eV,r),i=eL(r),u=n.useRef(!1);return(0,y.jsx)(Y,{id:l.contentId,"aria-labelledby":l.triggerId,...i,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;u.current||null==(t=l.triggerRef.current)||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!l.modal||n)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eB.displayName=eV,n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(er,{...o,...n,ref:t})}).displayName="DropdownMenuGroup";var eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(en,{...o,...n,ref:t})});eU.displayName="DropdownMenuLabel";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(el,{...o,...n,ref:t})});eH.displayName="DropdownMenuItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(eu,{...o,...n,ref:t})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(ef,{...o,...n,ref:t})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(em,{...o,...n,ref:t})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(ew,{...o,...n,ref:t})}).displayName="DropdownMenuItemIndicator";var eW=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(ey,{...o,...n,ref:t})});eW.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(ex,{...o,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(eM,{...o,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eL(r);return(0,y.jsx)(ej,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var ez=eO,eq=eK,eX=eG,eZ=eB,eJ=eU,eY=eH,eQ=eW},9196:(e,t,r)=>{r.d(t,{RG:()=>b,bL:()=>_,q7:()=>A});var n=r(2115),o=r(5185),a=r(7683),l=r(6101),i=r(6081),u=r(1285),s=r(3540),d=r(9033),c=r(5845),f=r(4315),p=r(5155),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,w,y]=(0,a.N)(h),[x,b]=(0,i.A)(h,[y]),[C,k]=x(h),M=n.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:t})})}));M.displayName=h;var R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:x,onEntryFocus:b,preventScrollOnEntryFocus:k=!1,...M}=e,R=n.useRef(null),j=(0,l.s)(t,R),E=(0,f.jH)(u),[S,_]=(0,c.i)({prop:g,defaultProp:null!=y?y:null,onChange:x,caller:h}),[A,I]=n.useState(!1),T=(0,d.c)(b),L=w(r),P=n.useRef(!1),[N,O]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(m,T),()=>e.removeEventListener(m,T)},[T]),(0,p.jsx)(C,{scope:r,orientation:a,dir:E,loop:i,currentTabStopId:S,onItemFocus:n.useCallback(e=>_(e),[_]),onItemShiftTab:n.useCallback(()=>I(!0),[]),onFocusableItemAdd:n.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>O(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:A||0===N?-1:0,"data-orientation":a,...M,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{P.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!P.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),k)}}P.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>I(!1))})})}),j="RovingFocusGroupItem",E=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:l=!1,tabStopId:i,children:d,...c}=e,f=(0,u.B)(),m=i||f,v=k(j,r),h=v.currentTabStopId===m,y=w(r),{onFocusableItemAdd:x,onFocusableItemRemove:b,currentTabStopId:C}=v;return n.useEffect(()=>{if(a)return x(),()=>b()},[a,x,b]),(0,p.jsx)(g.ItemSlot,{scope:r,id:m,focusable:a,active:l,children:(0,p.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return S[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>D(r))}}),children:"function"==typeof d?d({isCurrentTabStop:h,hasTabStop:null!=C}):d})})});E.displayName=j;var S={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var _=M,A=E}}]);