"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1428],{968:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(2115),l=r(3540),o=r(5155),a=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5501:(e,t,r)=>{r.d(t,{UC:()=>eM,In:()=>eI,q7:()=>eA,VF:()=>eH,p4:()=>e_,ZL:()=>eD,bL:()=>eE,wn:()=>eV,PP:()=>eB,l9:()=>eT,WT:()=>eP,LM:()=>eL});var n=r(2115),l=r(7650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(5185),i=r(7683),s=r(6101),u=r(6081),d=r(4315),c=r(9178),p=r(2293),f=r(7900),v=r(1285),h=r(5152),m=r(4378),g=r(3540),w=r(5155),y=Symbol("radix.slottable");function x(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===y}var b=r(9033),S=r(5845),C=r(2712),j=r(5503),k=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,w.jsx)(g.sG.span,{...e,ref:t,style:{...k,...e.style}})).displayName="VisuallyHidden";var R=r(8168),N=r(3795),E=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],P="Select",[I,D,M]=(0,i.N)(P),[L,A]=(0,u.A)(P,[M,h.Bk]),_=(0,h.Bk)(),[H,B]=L(P),[V,O]=L(P),G=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:f,disabled:m,required:g,form:y}=e,x=_(t),[b,C]=n.useState(null),[j,k]=n.useState(null),[R,N]=n.useState(!1),E=(0,d.jH)(c),[T,D]=(0,S.i)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:P}),[M,L]=(0,S.i)({prop:i,defaultProp:s,onChange:u,caller:P}),A=n.useRef(null),B=!b||y||!!b.closest("form"),[O,G]=n.useState(new Set),W=Array.from(O).map(e=>e.props.value).join(";");return(0,w.jsx)(h.bL,{...x,children:(0,w.jsxs)(H,{required:g,scope:t,trigger:b,onTriggerChange:C,valueNode:j,onValueNodeChange:k,valueNodeHasChildren:R,onValueNodeHasChildrenChange:N,contentId:(0,v.B)(),value:M,onValueChange:L,open:T,onOpenChange:D,dir:E,triggerPointerDownPosRef:A,disabled:m,children:[(0,w.jsx)(I.Provider,{scope:t,children:(0,w.jsx)(V,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{G(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{G(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),B?(0,w.jsxs)(ej,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:f,value:M,onChange:e=>L(e.target.value),disabled:m,form:y,children:[void 0===M?(0,w.jsx)("option",{value:""}):null,Array.from(O)]},W):null]})})};G.displayName=P;var W="SelectTrigger",F=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=_(r),u=B(W,r),d=u.disabled||l,c=(0,s.s)(t,u.onTriggerChange),p=D(r),f=n.useRef("touch"),[v,m,y]=eR(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eN(t,e,r);void 0!==n&&u.onValueChange(n.value)}),x=e=>{d||(u.onOpenChange(!0),y()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,w.jsx)(h.Mz,{asChild:!0,...i,children:(0,w.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":ek(u.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&E.includes(e.key)&&(x(),e.preventDefault())})})})});F.displayName=W;var K="SelectValue",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,u=B(K,r),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.s)(t,u.onValueNodeChange);return(0,C.N)(()=>{d(c)},[d,c]),(0,w.jsx)(g.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:ek(u.value)?(0,w.jsx)(w.Fragment,{children:a}):o})});U.displayName=K;var z=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,w.jsx)(g.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});z.displayName="SelectIcon";var q=e=>(0,w.jsx)(m.Z,{asChild:!0,...e});q.displayName="SelectPortal";var Z="SelectContent",X=n.forwardRef((e,t)=>{let r=B(Z,e.__scopeSelect),[o,a]=n.useState();return((0,C.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,w.jsx)($,{...e,ref:t}):o?l.createPortal((0,w.jsx)(Y,{scope:e.__scopeSelect,children:(0,w.jsx)(I.Slot,{scope:e.__scopeSelect,children:(0,w.jsx)("div",{children:e.children})})}),o):null});X.displayName=Z;var[Y,J]=L(Z),Q=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{var r,l,o;let a,i,{children:u,...d}=e,c=n.isValidElement(u)?(i=(a=null==(l=Object.getOwnPropertyDescriptor((r=u).props,"ref"))?void 0:l.get)&&"isReactWarning"in a&&a.isReactWarning)?r.ref:(i=(a=null==(o=Object.getOwnPropertyDescriptor(r,"ref"))?void 0:o.get)&&"isReactWarning"in a&&a.isReactWarning)?r.props.ref:r.props.ref||r.ref:void 0,p=(0,s.s)(c,t);if(n.isValidElement(u)){let e=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=o(...t);return l(...t),n}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(d,u.props);return u.type!==n.Fragment&&(e.ref=p),n.cloneElement(u,e)}return n.Children.count(u)>1?n.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,a=n.Children.toArray(l),i=a.find(x);if(i){let e=i.props.children,l=a.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,w.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,w.jsx)(t,{...o,ref:r,children:l})});return r.displayName="".concat(e,".Slot"),r}("SelectContent.RemoveScroll"),$=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:u,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:S,avoidCollisions:C,...j}=e,k=B(Z,r),[E,T]=n.useState(null),[P,I]=n.useState(null),M=(0,s.s)(t,e=>T(e)),[L,A]=n.useState(null),[_,H]=n.useState(null),V=D(r),[O,G]=n.useState(!1),W=n.useRef(!1);n.useEffect(()=>{if(E)return(0,R.Eq)(E)},[E]),(0,p.Oh)();let F=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&P&&(P.scrollTop=0),r===n&&P&&(P.scrollTop=P.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[V,P]),K=n.useCallback(()=>F([L,E]),[F,L,E]);n.useEffect(()=>{O&&K()},[O,K]);let{onOpenChange:U,triggerPointerDownPosRef:z}=k;n.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=z.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(n=z.current)?void 0:n.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():E.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[E,U,z]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[q,X]=eR(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eN(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),J=n.useCallback((e,t,r)=>{let n=!W.current&&!r;(void 0!==k.value&&k.value===t||n)&&(A(e),n&&(W.current=!0))},[k.value]),$=n.useCallback(()=>null==E?void 0:E.focus(),[E]),er=n.useCallback((e,t,r)=>{let n=!W.current&&!r;(void 0!==k.value&&k.value===t||n)&&H(e)},[k.value]),en="popper"===l?et:ee,el=en===et?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:S,avoidCollisions:C}:{};return(0,w.jsx)(Y,{scope:r,content:E,viewport:P,onViewportChange:I,itemRefCallback:J,selectedItem:L,onItemLeave:$,itemTextRefCallback:er,focusSelectedItem:K,selectedItemText:_,position:l,isPositioned:O,searchRef:q,children:(0,w.jsx)(N.A,{as:Q,allowPinchZoom:!0,children:(0,w.jsx)(f.n,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=k.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,w.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,w.jsx)(en,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>G(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,a.m)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=B(Z,r),u=J(Z,r),[d,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.s)(t,e=>f(e)),h=D(r),m=n.useRef(!1),y=n.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:S,focusSelectedItem:j}=u,k=n.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&p&&x&&b&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let a=h(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),w=f+v+u+parseInt(c.paddingBottom,10)+g,y=Math.min(5*b.offsetHeight,w),C=window.getComputedStyle(x),j=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,N=b.offsetHeight/2,E=f+v+(b.offsetTop+N);if(E<=R){let e=a.length>0&&b===a[a.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-R,N+(e?k:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+g);d.style.height=E+t+"px"}else{let e=a.length>0&&b===a[0].ref.current;d.style.top="0px";let t=Math.max(R,f+x.offsetTop+(e?j:0)+N);d.style.height=t+(w-E)+"px",x.scrollTop=E-R+x.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=y+"px",d.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,d,p,x,b,S,i.dir,l]);(0,C.N)(()=>k(),[k]);let[R,N]=n.useState();(0,C.N)(()=>{p&&N(window.getComputedStyle(p).zIndex)},[p]);let E=n.useCallback(e=>{e&&!0===y.current&&(k(),null==j||j(),y.current=!1)},[k,j]);return(0,w.jsx)(er,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:E,children:(0,w.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,w.jsx)(g.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});ee.displayName="SelectItemAlignedPosition";var et=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=_(r);return(0,w.jsx)(h.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});et.displayName="SelectPopperPosition";var[er,en]=L(Z,{}),el="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=J(el,r),u=en(el,r),d=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,w.jsx)(I.Slot,{scope:r,children:(0,w.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eo.displayName=el;var ea="SelectGroup",[ei,es]=L(ea);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.B)();return(0,w.jsx)(ei,{scope:r,id:l,children:(0,w.jsx)(g.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=ea;var eu="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=es(eu,r);return(0,w.jsx)(g.sG.div,{id:l.id,...n,ref:t})}).displayName=eu;var ed="SelectItem",[ec,ep]=L(ed),ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...u}=e,d=B(ed,r),c=J(ed,r),p=d.value===l,[f,h]=n.useState(null!=i?i:""),[m,y]=n.useState(!1),x=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,o)}),b=(0,v.B)(),S=n.useRef("touch"),C=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,w.jsx)(ec,{scope:r,value:l,disabled:o,textId:b,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,w.jsx)(I.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,w.jsx)(g.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:x,onFocus:(0,a.m)(u.onFocus,()=>y(!0)),onBlur:(0,a.m)(u.onBlur,()=>y(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==S.current&&C()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===S.current&&C()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{S.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(S.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===S.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(T.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ef.displayName=ed;var ev="SelectItemText",eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,u=B(ev,r),d=J(ev,r),c=ep(ev,r),p=O(ev,r),[f,v]=n.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=d.itemTextRefCallback)?void 0:t.call(d,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,y=n.useMemo(()=>(0,w.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=p;return(0,C.N)(()=>(x(y),()=>b(y)),[x,b,y]),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(g.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(i.children,u.valueNode):null]})});eh.displayName=ev;var em="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(em,r).isSelected?(0,w.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=em;var ew="SelectScrollUpButton",ey=n.forwardRef((e,t)=>{let r=J(ew,e.__scopeSelect),l=en(ew,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,C.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=ew;var ex="SelectScrollDownButton",eb=n.forwardRef((e,t)=>{let r=J(ex,e.__scopeSelect),l=en(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,C.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eb.displayName=ex;var eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=J("SelectScrollButton",r),s=n.useRef(null),u=D(r),d=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,C.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,w.jsx)(g.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{d()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,w.jsx)(g.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var eC="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=_(r),o=B(eC,r),a=J(eC,r);return o.open&&"popper"===a.position?(0,w.jsx)(h.i3,{...l,...n,ref:t}):null}).displayName=eC;var ej=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,s.s)(t,a),u=(0,j.Z)(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[u,l]),(0,w.jsx)(g.sG.select,{...o,style:{...k,...o.style},ref:i,defaultValue:l})});function ek(e){return""===e||void 0===e}function eR(e){let t=(0,b.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eN(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}ej.displayName="SelectBubbleInput";var eE=G,eT=F,eP=U,eI=z,eD=q,eM=X,eL=eo,eA=ef,e_=eh,eH=eg,eB=ey,eV=eb},5503:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(2115);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},6474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}}]);