(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1450],{133:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},2269:(e,t,r)=>{"use strict";var n=r(9509);r(8375);var i=r(2115),s=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),o=void 0!==n&&n.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,i=t.optimizeForSpeed,s=void 0===i?o:i;d(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",d("boolean"==typeof s,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=s,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){d("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),d(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(d(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(d(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];d(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},r.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];d(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},r.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&d(a(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return r?i.insertBefore(n,r):i.appendChild(n),n},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}();function d(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var u=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},c={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return c[n]||(c[n]="jsx-"+u(e+"-"+r)),c[n]}function p(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return c[r]||(c[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[r]}var f=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,i=t.optimizeForSpeed,s=void 0!==i&&i;this._sheet=n||new l({name:"styled-jsx",optimizeForSpeed:s}),this._sheet.inject(),n&&"boolean"==typeof s&&(this._sheet.setOptimizeForSpeed(s),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,i=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var s=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=s,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return s.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var i=h(n,r);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return p(i,e)}):[p(i,t)]}}return{styleId:h(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),y=i.createContext(null);y.displayName="StyleSheetContext";var _=s.default.useInsertionEffect||s.default.useLayoutEffect,v="undefined"!=typeof window?new f:void 0;function m(e){var t=v||i.useContext(y);return t&&("undefined"==typeof window?t.add(e):_(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}m.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=m},2292:(e,t,r)=>{"use strict";r.d(t,{UC:()=>Y,Y9:()=>G,bL:()=>B,l9:()=>K,q7:()=>V});var n=r(2115),i=r(6081),s=r(7683),o=r(6101),a=r(5185),l=r(5845),d=r(3540),u=r(8106),c=r(1285),h=r(4315),p=r(5155),f="Accordion",y=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[_,v,m]=(0,s.N)(f),[S,g]=(0,i.A)(f,[m,u.z3]),w=(0,u.z3)(),k=n.forwardRef((e,t)=>{let{type:r,...n}=e;return(0,p.jsx)(_.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,p.jsx)(C,{...n,ref:t}):(0,p.jsx)(R,{...n,ref:t})})});k.displayName=f;var[x,b]=S(f),[A,j]=S(f,{collapsible:!1}),R=n.forwardRef((e,t)=>{let{value:r,defaultValue:i,onValueChange:s=()=>{},collapsible:o=!1,...a}=e,[d,u]=(0,l.i)({prop:r,defaultProp:null!=i?i:"",onChange:s,caller:f});return(0,p.jsx)(x,{scope:e.__scopeAccordion,value:n.useMemo(()=>d?[d]:[],[d]),onItemOpen:u,onItemClose:n.useCallback(()=>o&&u(""),[o,u]),children:(0,p.jsx)(A,{scope:e.__scopeAccordion,collapsible:o,children:(0,p.jsx)(M,{...a,ref:t})})})}),C=n.forwardRef((e,t)=>{let{value:r,defaultValue:i,onValueChange:s=()=>{},...o}=e,[a,d]=(0,l.i)({prop:r,defaultProp:null!=i?i:[],onChange:s,caller:f}),u=n.useCallback(e=>d(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[d]),c=n.useCallback(e=>d(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[d]);return(0,p.jsx)(x,{scope:e.__scopeAccordion,value:a,onItemOpen:u,onItemClose:c,children:(0,p.jsx)(A,{scope:e.__scopeAccordion,collapsible:!0,children:(0,p.jsx)(M,{...o,ref:t})})})}),[z,F]=S(f),M=n.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:i,dir:s,orientation:l="vertical",...u}=e,c=n.useRef(null),f=(0,o.s)(c,t),m=v(r),S="ltr"===(0,h.jH)(s),g=(0,a.m)(e.onKeyDown,e=>{var t;if(!y.includes(e.key))return;let r=e.target,n=m().filter(e=>{var t;return!(null==(t=e.ref.current)?void 0:t.disabled)}),i=n.findIndex(e=>e.ref.current===r),s=n.length;if(-1===i)return;e.preventDefault();let o=i,a=s-1,d=()=>{(o=i+1)>a&&(o=0)},u=()=>{(o=i-1)<0&&(o=a)};switch(e.key){case"Home":o=0;break;case"End":o=a;break;case"ArrowRight":"horizontal"===l&&(S?d():u());break;case"ArrowDown":"vertical"===l&&d();break;case"ArrowLeft":"horizontal"===l&&(S?u():d());break;case"ArrowUp":"vertical"===l&&u()}null==(t=n[o%s].ref.current)||t.focus()});return(0,p.jsx)(z,{scope:r,disabled:i,direction:s,orientation:l,children:(0,p.jsx)(_.Slot,{scope:r,children:(0,p.jsx)(d.sG.div,{...u,"data-orientation":l,ref:f,onKeyDown:i?void 0:g})})})}),I="AccordionItem",[O,N]=S(I),T=n.forwardRef((e,t)=>{let{__scopeAccordion:r,value:n,...i}=e,s=F(I,r),o=b(I,r),a=w(r),l=(0,c.B)(),d=n&&o.value.includes(n)||!1,h=s.disabled||e.disabled;return(0,p.jsx)(O,{scope:r,open:d,disabled:h,triggerId:l,children:(0,p.jsx)(u.bL,{"data-orientation":s.orientation,"data-state":U(d),...a,...i,ref:t,disabled:h,open:d,onOpenChange:e=>{e?o.onItemOpen(n):o.onItemClose(n)}})})});T.displayName=I;var E="AccordionHeader",q=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=F(f,r),s=N(E,r);return(0,p.jsx)(d.sG.h3,{"data-orientation":i.orientation,"data-state":U(s.open),"data-disabled":s.disabled?"":void 0,...n,ref:t})});q.displayName=E;var L="AccordionTrigger",H=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=F(f,r),s=N(L,r),o=j(L,r),a=w(r);return(0,p.jsx)(_.ItemSlot,{scope:r,children:(0,p.jsx)(u.l9,{"aria-disabled":s.open&&!o.collapsible||void 0,"data-orientation":i.orientation,id:s.triggerId,...a,...n,ref:t})})});H.displayName=L;var P="AccordionContent",D=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=F(f,r),s=N(P,r),o=w(r);return(0,p.jsx)(u.UC,{role:"region","aria-labelledby":s.triggerId,"data-orientation":i.orientation,...o,...n,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function U(e){return e?"open":"closed"}D.displayName=P;var B=k,V=T,G=q,K=H,Y=D},2525:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2568:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("rotate-cw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]])},4616:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5968:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6287:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},8375:()=>{},9137:(e,t,r)=>{"use strict";e.exports=r(2269).style},9140:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("list-ordered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},9144:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},9727:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])}}]);