"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1563],{1285:(e,t,n)=>{n.d(t,{B:()=>a});var r,l=n(2115),i=n(2712),o=(r||(r=n.t(l,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function a(e){let[t,n]=l.useState(o());return(0,i.N)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},2085:(e,t,n)=>{n.d(t,{F:()=>o});var r=n(2596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,o=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:o,defaultVariants:u}=t,a=Object.keys(o).map(e=>{let t=null==n?void 0:n[e],r=null==u?void 0:u[e];if(null===t)return null;let i=l(t)||l(r);return o[e][i]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,a,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...u,...s}[t]):({...u,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2712:(e,t,n)=>{n.d(t,{N:()=>l});var r=n(2115),l=globalThis?.document?r.useLayoutEffect:()=>{}},3540:(e,t,n)=>{n.d(t,{sG:()=>s,hO:()=>c});var r=n(2115),l=n(7650),i=n(6101),o=n(5155),u=Symbol("radix.slottable");function a(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{var n,l,o;let u,a,{children:s,...c}=e,d=r.isValidElement(s)?(a=(u=null==(l=Object.getOwnPropertyDescriptor((n=s).props,"ref"))?void 0:l.get)&&"isReactWarning"in u&&u.isReactWarning)?n.ref:(a=(u=null==(o=Object.getOwnPropertyDescriptor(n,"ref"))?void 0:o.get)&&"isReactWarning"in u&&u.isReactWarning)?n.props.ref:n.props.ref||n.ref:void 0,f=(0,i.s)(d,t);if(r.isValidElement(s)){let e=function(e,t){let n={...t};for(let r in t){let l=e[r],i=t[r];/^on[A-Z]/.test(r)?l&&i?n[r]=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=i(...t);return l(...t),r}:l&&(n[r]=l):"style"===r?n[r]={...l,...i}:"className"===r&&(n[r]=[l,i].filter(Boolean).join(" "))}return{...e,...n}}(c,s.props);return s.type!==r.Fragment&&(e.ref=f),r.cloneElement(s,e)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=r.forwardRef((e,n)=>{let{children:l,...i}=e,u=r.Children.toArray(l),s=u.find(a);if(s){let e=s.props.children,l=u.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,o.jsx)(t,{...i,ref:n,children:l})});return n.displayName="".concat(e,".Slot"),n}(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:l,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l?n:t,{...i,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function c(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5845:(e,t,n)=>{n.d(t,{i:()=>u});var r,l=n(2115),i=n(2712),o=(r||(r=n.t(l,2)))[" useInsertionEffect ".trim().toString()]||i.N;function u({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,u,a]=function({defaultProp:e,onChange:t}){let[n,r]=l.useState(e),i=l.useRef(n),u=l.useRef(t);return o(()=>{u.current=t},[t]),l.useEffect(()=>{i.current!==n&&(u.current?.(n),i.current=n)},[n,i]),[n,r,u]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:i;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,l.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else u(t)},[s,e,u,a])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>o,q:()=>i});var r=n(2115),l=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,o=r.useMemo(()=>i,Object.values(i));return(0,l.jsx)(n.Provider,{value:o,children:t})};return i.displayName=e+"Provider",[i,function(l){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function o(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let l=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:l}}),[n,l])}};return i.scopeName=e,[function(t,i){let o=r.createContext(i),u=n.length;n=[...n,i];let a=t=>{let{scope:n,children:i,...a}=t,s=n?.[e]?.[u]||o,c=r.useMemo(()=>a,Object.values(a));return(0,l.jsx)(s.Provider,{value:c,children:i})};return a.displayName=t+"Provider",[a,function(n,l){let a=l?.[e]?.[u]||o,s=r.useContext(a);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=n.reduce((t,{useScope:n,scopeName:r})=>{let l=n(e)[`__scope${r}`];return{...t,...l}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}(i,...t)]}},6101:(e,t,n)=>{n.d(t,{s:()=>o,t:()=>i});var r=n(2115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function o(...e){return r.useCallback(i(...e),e)}},8905:(e,t,n)=>{n.d(t,{C:()=>o});var r=n(2115),l=n(6101),i=n(2712),o=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[l,o]=r.useState(),a=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(a.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=a.current,n=s.current;if(n!==e){let r=c.current,l=u(t);e?f("MOUNT"):"none"===l||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==l?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.N)(()=>{if(l){var e;let t,n=null!=(e=l.ownerDocument.defaultView)?e:window,r=e=>{let r=u(a.current).includes(e.animationName);if(e.target===l&&r&&(f("ANIMATION_END"),!s.current)){let e=l.style.animationFillMode;l.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===l.style.animationFillMode&&(l.style.animationFillMode=e)})}},i=e=>{e.target===l&&(c.current=u(a.current))};return l.addEventListener("animationstart",i),l.addEventListener("animationcancel",r),l.addEventListener("animationend",r),()=>{n.clearTimeout(t),l.removeEventListener("animationstart",i),l.removeEventListener("animationcancel",r),l.removeEventListener("animationend",r)}}f("ANIMATION_END")},[l,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,o(e)},[])}}(t),a="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),s=(0,l.s)(o.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,l=r&&"isReactWarning"in r&&r.isReactWarning;return l?e.ref:(l=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||o.isPresent?r.cloneElement(a,{ref:s}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},9708:(e,t,n)=>{n.d(t,{DX:()=>o});var r=n(2115),l=n(6101),i=n(5155),o=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var o;let e,u,a=(o=n,(u=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(u=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let n={...t};for(let r in t){let l=e[r],i=t[r];/^on[A-Z]/.test(r)?l&&i?n[r]=(...e)=>{let t=i(...e);return l(...e),t}:l&&(n[r]=l):"style"===r?n[r]={...l,...i}:"className"===r&&(n[r]=[l,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,l.t)(t,a):a),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:l,...o}=e,u=r.Children.toArray(l),s=u.find(a);if(s){let e=s.props.children,l=u.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...o,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,i.jsx)(t,{...o,ref:n,children:l})});return n.displayName=`${e}.Slot`,n}("Slot"),u=Symbol("radix.slottable");function a(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},9946:(e,t,n)=>{n.d(t,{A:()=>d});var r=n(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),o=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},u=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},a=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:f,...m}=e;return(0,r.createElement)("svg",{ref:t,...s,width:l,height:l,stroke:n,strokeWidth:o?24*Number(i)/Number(l):i,className:u("lucide",c),...!d&&!a(m)&&{"aria-hidden":"true"},...m},[...f.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:a,...s}=n;return(0,r.createElement)(c,{ref:i,iconNode:t,className:u("lucide-".concat(l(o(e))),"lucide-".concat(e),a),...s})});return n.displayName=o(e),n}}}]);