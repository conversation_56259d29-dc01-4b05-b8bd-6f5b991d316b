"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3085],{968:(e,r,t)=>{t.d(r,{b:()=>o});var n=t(2115),l=t(3540),i=t(5155),a=n.forwardRef((e,r)=>(0,i.jsx)(l.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));a.displayName="Label";var o=a},1264:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},2085:(e,r,t)=>{t.d(r,{F:()=>a});var n=t(2596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,a=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:o}=r,u=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],n=null==o?void 0:o[e];if(null===r)return null;let i=l(r)||l(n);return a[e][i]}),s=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return i(e,u,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...l}=r;return Object.entries(l).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...s}[r]):({...o,...s})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},2138:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2919:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3540:(e,r,t)=>{t.d(r,{sG:()=>s,hO:()=>d});var n=t(2115),l=t(7650),i=t(6101),a=t(5155),o=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{var t,l,a;let o,u,{children:s,...d}=e,c=n.isValidElement(s)?(u=(o=null==(l=Object.getOwnPropertyDescriptor((t=s).props,"ref"))?void 0:l.get)&&"isReactWarning"in o&&o.isReactWarning)?t.ref:(u=(o=null==(a=Object.getOwnPropertyDescriptor(t,"ref"))?void 0:a.get)&&"isReactWarning"in o&&o.isReactWarning)?t.props.ref:t.props.ref||t.ref:void 0,f=(0,i.s)(c,r);if(n.isValidElement(s)){let e=function(e,r){let t={...r};for(let n in r){let l=e[n],i=r[n];/^on[A-Z]/.test(n)?l&&i?t[n]=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];let n=i(...r);return l(...r),n}:l&&(t[n]=l):"style"===n?t[n]={...l,...i}:"className"===n&&(t[n]=[l,i].filter(Boolean).join(" "))}return{...e,...t}}(d,s.props);return s.type!==n.Fragment&&(e.ref=f),n.cloneElement(s,e)}return n.Children.count(s)>1?n.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),t=n.forwardRef((e,t)=>{let{children:l,...i}=e,o=n.Children.toArray(l),s=o.find(u);if(s){let e=s.props.children,l=o.map(r=>r!==s?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,a.jsx)(r,{...i,ref:t,children:l})});return t.displayName="".concat(e,".Slot"),t}(`Primitive.${r}`),l=n.forwardRef((e,n)=>{let{asChild:l,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l?t:r,{...i,ref:n})});return l.displayName=`Primitive.${r}`,{...e,[r]:l}},{});function d(e,r){e&&l.flushSync(()=>e.dispatchEvent(r))}},5339:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6101:(e,r,t)=>{t.d(r,{s:()=>a,t:()=>i});var n=t(2115);function l(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function i(...e){return r=>{let t=!1,n=e.map(e=>{let n=l(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():l(e[r],null)}}}}function a(...e){return n.useCallback(i(...e),e)}},9708:(e,r,t)=>{t.d(r,{DX:()=>a});var n=t(2115),l=t(6101),i=t(5155),a=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...i}=e;if(n.isValidElement(t)){var a;let e,o,u=(a=t,(o=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(o=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),s=function(e,r){let t={...r};for(let n in r){let l=e[n],i=r[n];/^on[A-Z]/.test(n)?l&&i?t[n]=(...e)=>{let r=i(...e);return l(...e),r}:l&&(t[n]=l):"style"===n?t[n]={...l,...i}:"className"===n&&(t[n]=[l,i].filter(Boolean).join(" "))}return{...e,...t}}(i,t.props);return t.type!==n.Fragment&&(s.ref=r?(0,l.t)(r,u):u),n.cloneElement(t,s)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:l,...a}=e,o=n.Children.toArray(l),s=o.find(u);if(s){let e=s.props.children,l=o.map(r=>r!==s?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(r,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,i.jsx)(r,{...a,ref:t,children:l})});return t.displayName=`${e}.Slot`,t}("Slot"),o=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},9946:(e,r,t)=>{t.d(r,{A:()=>c});var n=t(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),a=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},u=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:d="",children:c,iconNode:f,...p}=e;return(0,n.createElement)("svg",{ref:r,...s,width:l,height:l,stroke:t,strokeWidth:a?24*Number(i)/Number(l):i,className:o("lucide",d),...!c&&!u(p)&&{"aria-hidden":"true"},...p},[...f.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(c)?c:[c]])}),c=(e,r)=>{let t=(0,n.forwardRef)((t,i)=>{let{className:u,...s}=t;return(0,n.createElement)(d,{ref:i,iconNode:r,className:o("lucide-".concat(l(a(e))),"lucide-".concat(e),u),...s})});return t.displayName=a(e),t}}}]);