"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[599],{4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5821:(e,t,n)=>{n.d(t,{bm:()=>ea,UC:()=>er,VY:()=>el,hJ:()=>en,ZL:()=>et,bL:()=>$,hE:()=>eo,l9:()=>ee});var r=n(2115),o=n(5185),l=n(6101),a=n(6081),i=n(1285),s=n(5845),d=n(9178),u=n(7900),c=n(4378),p=n(8905),f=n(3540),g=n(2293),m=n(3795),v=n(8168),y=n(5155),h=Symbol("radix.slottable");function b(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===h}var x="Dialog",[C,R]=(0,a.A)(x),[D,j]=C(x),w=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:l,onOpenChange:a,modal:d=!0}=e,u=r.useRef(null),c=r.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=l&&l,onChange:a,caller:x});return(0,y.jsx)(D,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:d,children:n})};w.displayName=x;var N="DialogTrigger",E=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=j(N,n),i=(0,l.s)(t,a.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":z(a.open),...r,ref:i,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});E.displayName=N;var I="DialogPortal",[O,_]=C(I,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:l}=e,a=j(I,t);return(0,y.jsx)(O,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(p.C,{present:n||a.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:l,children:e})}))})};A.displayName=I;var F="DialogOverlay",k=r.forwardRef((e,t)=>{let n=_(F,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=j(F,e.__scopeDialog);return l.modal?(0,y.jsx)(p.C,{present:r||l.open,children:(0,y.jsx)(B,{...o,ref:t})}):null});k.displayName=F;var P=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{var n,o,a;let i,s,{children:d,...u}=e,c=r.isValidElement(d)?(s=(i=null==(o=Object.getOwnPropertyDescriptor((n=d).props,"ref"))?void 0:o.get)&&"isReactWarning"in i&&i.isReactWarning)?n.ref:(s=(i=null==(a=Object.getOwnPropertyDescriptor(n,"ref"))?void 0:a.get)&&"isReactWarning"in i&&i.isReactWarning)?n.props.ref:n.props.ref||n.ref:void 0,p=(0,l.s)(c,t);if(r.isValidElement(d)){let e=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=l(...t);return o(...t),r}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(u,d.props);return d.type!==r.Fragment&&(e.ref=p),r.cloneElement(d,e)}return r.Children.count(d)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=r.forwardRef((e,n)=>{let{children:o,...l}=e,a=r.Children.toArray(o),i=a.find(b);if(i){let e=i.props.children,o=a.map(t=>t!==i?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,y.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,y.jsx)(t,{...l,ref:n,children:o})});return n.displayName="".concat(e,".Slot"),n}("DialogOverlay.RemoveScroll"),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=j(F,n);return(0,y.jsx)(m.A,{as:P,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":z(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),G="DialogContent",T=r.forwardRef((e,t)=>{let n=_(G,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=j(G,e.__scopeDialog);return(0,y.jsx)(p.C,{present:r||l.open,children:l.modal?(0,y.jsx)(W,{...o,ref:t}):(0,y.jsx)(S,{...o,ref:t})})});T.displayName=G;var W=r.forwardRef((e,t)=>{let n=j(G,e.__scopeDialog),a=r.useRef(null),i=(0,l.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(V,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),S=r.forwardRef((e,t)=>{let n=j(G,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,y.jsx)(V,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let i=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),V=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...s}=e,c=j(G,n),p=r.useRef(null),f=(0,l.s)(t,p);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,y.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":z(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Q,{titleId:c.titleId}),(0,y.jsx)(X,{contentRef:p,descriptionId:c.descriptionId})]})]})}),M="DialogTitle",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=j(M,n);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...r,ref:t})});q.displayName=M;var Z="DialogDescription",L=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=j(Z,n);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...r,ref:t})});L.displayName=Z;var U="DialogClose",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=j(U,n);return(0,y.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function z(e){return e?"open":"closed"}K.displayName=U;var H="DialogTitleWarning",[J,Y]=(0,a.q)(H,{contentName:G,titleName:M,docsSlug:"dialog"}),Q=e=>{let{titleId:t}=e,n=Y(H),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},X=e=>{let{contentRef:t,descriptionId:n}=e,o=Y("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(l))},[l,t,n]),null},$=w,ee=E,et=A,en=k,er=T,eo=q,el=L,ea=K},8106:(e,t,n)=>{n.d(t,{Ke:()=>R,R6:()=>x,UC:()=>E,bL:()=>w,l9:()=>N,z3:()=>m});var r=n(2115),o=n(5185),l=n(6081),a=n(5845),i=n(2712),s=n(6101),d=n(3540),u=n(8905),c=n(1285),p=n(5155),f="Collapsible",[g,m]=(0,l.A)(f),[v,y]=g(f),h=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:l,disabled:i,onOpenChange:s,...u}=e,[g,m]=(0,a.i)({prop:o,defaultProp:null!=l&&l,onChange:s,caller:f});return(0,p.jsx)(v,{scope:n,disabled:i,contentId:(0,c.B)(),open:g,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),children:(0,p.jsx)(d.sG.div,{"data-state":j(g),"data-disabled":i?"":void 0,...u,ref:t})})});h.displayName=f;var b="CollapsibleTrigger",x=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,l=y(b,n);return(0,p.jsx)(d.sG.button,{type:"button","aria-controls":l.contentId,"aria-expanded":l.open||!1,"data-state":j(l.open),"data-disabled":l.disabled?"":void 0,disabled:l.disabled,...r,ref:t,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});x.displayName=b;var C="CollapsibleContent",R=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=y(C,e.__scopeCollapsible);return(0,p.jsx)(u.C,{present:n||o.open,children:e=>{let{present:n}=e;return(0,p.jsx)(D,{...r,ref:t,present:n})}})});R.displayName=C;var D=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:o,children:l,...a}=e,u=y(C,n),[c,f]=r.useState(o),g=r.useRef(null),m=(0,s.s)(t,g),v=r.useRef(0),h=v.current,b=r.useRef(0),x=b.current,R=u.open||c,D=r.useRef(R),w=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>D.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,i.N)(()=>{let e=g.current;if(e){w.current=w.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();v.current=t.height,b.current=t.width,D.current||(e.style.transitionDuration=w.current.transitionDuration,e.style.animationName=w.current.animationName),f(o)}},[u.open,o]),(0,p.jsx)(d.sG.div,{"data-state":j(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!R,...a,ref:m,style:{"--radix-collapsible-content-height":h?"".concat(h,"px"):void 0,"--radix-collapsible-content-width":x?"".concat(x,"px"):void 0,...e.style},children:R&&l})});function j(e){return e?"open":"closed"}var w=h,N=x,E=R}}]);