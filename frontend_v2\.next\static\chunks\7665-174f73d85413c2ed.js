"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7665],{4315:(e,t,r)=>{r.d(t,{jH:()=>o});var n=r(2115);r(5155);var l=n.createContext(void 0);function o(e){let t=n.useContext(l);return e||t||"ltr"}},7683:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function l(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function o(e,t,r){var l=n(e,t,"set");if(l.set)l.set.call(e,r);else{if(!l.writable)throw TypeError("attempted to set read only private field");l.value=r}return r}r.d(t,{N:()=>m});var i,a=r(2115),u=r(6081),c=r(6101),f=r(5155);function s(e){let t=function(e){let t=a.forwardRef((e,t)=>{var r,n,l;let o,i,{children:u,...f}=e,s=a.isValidElement(u)?(i=(o=null==(n=Object.getOwnPropertyDescriptor((r=u).props,"ref"))?void 0:n.get)&&"isReactWarning"in o&&o.isReactWarning)?r.ref:(i=(o=null==(l=Object.getOwnPropertyDescriptor(r,"ref"))?void 0:l.get)&&"isReactWarning"in o&&o.isReactWarning)?r.props.ref:r.props.ref||r.ref:void 0,d=(0,c.s)(s,t);if(a.isValidElement(u)){let e=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=o(...t);return l(...t),n}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(f,u.props);return u.type!==a.Fragment&&(e.ref=d),a.cloneElement(u,e)}return a.Children.count(u)>1?a.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),r=a.forwardRef((e,r)=>{let{children:n,...l}=e,o=a.Children.toArray(n),i=o.find(p);if(i){let e=i.props.children,n=o.map(t=>t!==i?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,f.jsx)(t,{...l,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,f.jsx)(t,{...l,ref:r,children:n})});return r.displayName="".concat(e,".Slot"),r}var d=Symbol("radix.slottable");function p(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}function m(e){let t=e+"CollectionProvider",[r,n]=(0,u.A)(t),[l,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:r}=e,n=a.useRef(null),o=a.useRef(new Map).current;return(0,f.jsx)(l,{scope:t,itemMap:o,collectionRef:n,children:r})};i.displayName=t;let d=e+"CollectionSlot",p=s(d),m=a.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=o(d,r),i=(0,c.s)(t,l.collectionRef);return(0,f.jsx)(p,{ref:i,children:n})});m.displayName=d;let y=e+"CollectionItemSlot",v="data-radix-collection-item",h=s(y),C=a.forwardRef((e,t)=>{let{scope:r,children:n,...l}=e,i=a.useRef(null),u=(0,c.s)(t,i),s=o(y,r);return a.useEffect(()=>(s.itemMap.set(i,{ref:i,...l}),()=>void s.itemMap.delete(i))),(0,f.jsx)(h,{...{[v]:""},ref:u,children:n})});return C.displayName=y,[{Provider:i,Slot:m,ItemSlot:C},function(t){let r=o(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var y=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=h(t),l=n>=0?n:r+n;return l<0||l>=r?-1:l}(e,t);return -1===r?void 0:e[r]}function h(e){return e!=e||0===e?0:Math.trunc(e)}i=new WeakMap}}]);