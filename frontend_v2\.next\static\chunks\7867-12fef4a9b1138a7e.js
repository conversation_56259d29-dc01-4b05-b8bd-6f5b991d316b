"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7867],{646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},704:(e,t,r)=>{r.d(t,{B8:()=>I,UC:()=>D,bL:()=>A,l9:()=>L});var n=r(2115),a=r(5185),o=r(6081),i=r(9196),l=r(8905),s=r(3540),u=r(4315),c=r(5845),d=r(1285),f=r(5155),p="Tabs",[v,m]=(0,o.A)(p,[i.RG]),h=(0,i.RG)(),[b,y]=v(p),w=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:o,orientation:i="horizontal",dir:l,activationMode:v="automatic",...m}=e,h=(0,u.jH)(l),[y,w]=(0,c.i)({prop:n,onChange:a,defaultProp:null!=o?o:"",caller:p});return(0,f.jsx)(b,{scope:r,baseId:(0,d.B)(),value:y,onValueChange:w,orientation:i,dir:h,activationMode:v,children:(0,f.jsx)(s.sG.div,{dir:h,"data-orientation":i,...m,ref:t})})});w.displayName=p;var g="TabsList",x=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,o=y(g,r),l=h(r);return(0,f.jsx)(i.bL,{asChild:!0,...l,orientation:o.orientation,dir:o.dir,loop:n,children:(0,f.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...a,ref:t})})});x.displayName=g;var k="TabsTrigger",E=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...l}=e,u=y(k,r),c=h(r),d=C(u.baseId,n),p=S(u.baseId,n),v=n===u.value;return(0,f.jsx)(i.q7,{asChild:!0,...c,focusable:!o,active:v,children:(0,f.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":p,"data-state":v?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:d,...l,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(n)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(n)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;v||o||!e||u.onValueChange(n)})})})});E.displayName=k;var j="TabsContent",R=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:o,children:i,...u}=e,c=y(j,r),d=C(c.baseId,a),p=S(c.baseId,a),v=a===c.value,m=n.useRef(v);return n.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.C,{present:o||v,children:r=>{let{present:n}=r;return(0,f.jsx)(s.sG.div,{"data-state":v?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!n,id:p,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:n&&i})}})});function C(e,t){return"".concat(e,"-trigger-").concat(t)}function S(e,t){return"".concat(e,"-content-").concat(t)}R.displayName=j;var A=w,I=x,L=E,D=R},1414:(e,t,r)=>{e.exports=r(2436)},2436:(e,t,r)=>{var n=r(2115),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=n.useState,i=n.useEffect,l=n.useLayoutEffect,s=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!a(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=o({inst:{value:r,getSnapshot:t}}),a=n[0].inst,c=n[1];return l(function(){a.value=r,a.getSnapshot=t,u(a)&&c({inst:a})},[e,r,t]),i(function(){return u(a)&&c({inst:a}),e(function(){u(a)&&c({inst:a})})},[e]),s(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},4011:(e,t,r)=>{r.d(t,{H4:()=>j,_V:()=>E,bL:()=>k});var n=r(2115),a=r(6081),o=r(9033),i=r(2712),l=r(3540),s=r(1414);function u(){return()=>{}}var c=r(5155),d="Avatar",[f,p]=(0,a.A)(d),[v,m]=f(d),h=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...a}=e,[o,i]=n.useState("idle");return(0,c.jsx)(v,{scope:r,imageLoadingStatus:o,onImageLoadingStatusChange:i,children:(0,c.jsx)(l.sG.span,{...a,ref:t})})});h.displayName=d;var b="AvatarImage",y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:a,onLoadingStatusChange:d=()=>{},...f}=e,p=m(b,r),v=function(e,t){let{referrerPolicy:r,crossOrigin:a}=t,o=(0,s.useSyncExternalStore)(u,()=>!0,()=>!1),l=n.useRef(null),c=o?(l.current||(l.current=new window.Image),l.current):null,[d,f]=n.useState(()=>x(c,e));return(0,i.N)(()=>{f(x(c,e))},[c,e]),(0,i.N)(()=>{let e=e=>()=>{f(e)};if(!c)return;let t=e("loaded"),n=e("error");return c.addEventListener("load",t),c.addEventListener("error",n),r&&(c.referrerPolicy=r),"string"==typeof a&&(c.crossOrigin=a),()=>{c.removeEventListener("load",t),c.removeEventListener("error",n)}},[c,a,r]),d}(a,f),h=(0,o.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,i.N)(()=>{"idle"!==v&&h(v)},[v,h]),"loaded"===v?(0,c.jsx)(l.sG.img,{...f,ref:t,src:a}):null});y.displayName=b;var w="AvatarFallback",g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:a,...o}=e,i=m(w,r),[s,u]=n.useState(void 0===a);return n.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>u(!0),a);return()=>window.clearTimeout(e)}},[a]),s&&"loaded"!==i.imageLoadingStatus?(0,c.jsx)(l.sG.span,{...o,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}g.displayName=w;var k=h,E=y,j=g},5339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6981:(e,t,r)=>{r.d(t,{C1:()=>E,bL:()=>x});var n=r(2115),a=r(6101),o=r(6081),i=r(5185),l=r(5845),s=r(5503),u=r(1275),c=r(8905),d=r(3540),f=r(5155),p="Checkbox",[v,m]=(0,o.A)(p),[h,b]=v(p);function y(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:o,disabled:i,form:s,name:u,onCheckedChange:c,required:d,value:v="on",internal_do_not_use_render:m}=e,[b,y]=(0,l.i)({prop:r,defaultProp:null!=o&&o,onChange:c,caller:p}),[w,g]=n.useState(null),[x,k]=n.useState(null),E=n.useRef(!1),j=!w||!!s||!!w.closest("form"),R={checked:b,disabled:i,setChecked:y,control:w,setControl:g,name:u,form:s,value:v,hasConsumerStoppedPropagationRef:E,required:d,defaultChecked:!C(o)&&o,isFormControl:j,bubbleInput:x,setBubbleInput:k};return(0,f.jsx)(h,{scope:t,...R,children:"function"==typeof m?m(R):a})}var w="CheckboxTrigger",g=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:o,onClick:l,...s}=e,{control:u,value:c,disabled:p,checked:v,required:m,setControl:h,setChecked:y,hasConsumerStoppedPropagationRef:g,isFormControl:x,bubbleInput:k}=b(w,r),E=(0,a.s)(t,h),j=n.useRef(v);return n.useEffect(()=>{let e=null==u?void 0:u.form;if(e){let t=()=>y(j.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,y]),(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":C(v)?"mixed":v,"aria-required":m,"data-state":S(v),"data-disabled":p?"":void 0,disabled:p,value:c,...s,ref:E,onKeyDown:(0,i.m)(o,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(l,e=>{y(e=>!!C(e)||!e),k&&x&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})})});g.displayName=w;var x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:a,defaultChecked:o,required:i,disabled:l,value:s,onCheckedChange:u,form:c,...d}=e;return(0,f.jsx)(y,{__scopeCheckbox:r,checked:a,defaultChecked:o,disabled:l,required:i,onCheckedChange:u,name:n,form:c,value:s,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(g,{...d,ref:t,__scopeCheckbox:r}),n&&(0,f.jsx)(R,{__scopeCheckbox:r})]})}})});x.displayName=p;var k="CheckboxIndicator",E=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...a}=e,o=b(k,r);return(0,f.jsx)(c.C,{present:n||C(o.checked)||!0===o.checked,children:(0,f.jsx)(d.sG.span,{"data-state":S(o.checked),"data-disabled":o.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});E.displayName=k;var j="CheckboxBubbleInput",R=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...o}=e,{control:i,hasConsumerStoppedPropagationRef:l,checked:c,defaultChecked:p,required:v,disabled:m,name:h,value:y,form:w,bubbleInput:g,setBubbleInput:x}=b(j,r),k=(0,a.s)(t,x),E=(0,s.Z)(c),R=(0,u.X)(i);n.useEffect(()=>{if(!g)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(E!==c&&e){let r=new Event("click",{bubbles:t});g.indeterminate=C(c),e.call(g,!C(c)&&c),g.dispatchEvent(r)}},[g,E,c,l]);let S=n.useRef(!C(c)&&c);return(0,f.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:S.current,required:v,disabled:m,name:h,value:y,form:w,...o,tabIndex:-1,ref:k,style:{...o.style,...R,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function S(e){return C(e)?"indeterminate":e?"checked":"unchecked"}R.displayName=j},9196:(e,t,r)=>{r.d(t,{RG:()=>x,bL:()=>L,q7:()=>D});var n=r(2115),a=r(5185),o=r(7683),i=r(6101),l=r(6081),s=r(1285),u=r(3540),c=r(9033),d=r(5845),f=r(4315),p=r(5155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[b,y,w]=(0,o.N)(h),[g,x]=(0,l.A)(h,[w]),[k,E]=g(h),j=n.forwardRef((e,t)=>(0,p.jsx)(b.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(b.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:t})})}));j.displayName=h;var R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:l=!1,dir:s,currentTabStopId:b,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:g,onEntryFocus:x,preventScrollOnEntryFocus:E=!1,...j}=e,R=n.useRef(null),C=(0,i.s)(t,R),S=(0,f.jH)(s),[A,L]=(0,d.i)({prop:b,defaultProp:null!=w?w:null,onChange:g,caller:h}),[D,F]=n.useState(!1),G=(0,c.c)(x),T=y(r),N=n.useRef(!1),[_,K]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,G),()=>e.removeEventListener(v,G)},[G]),(0,p.jsx)(k,{scope:r,orientation:o,dir:S,loop:l,currentTabStopId:A,onItemFocus:n.useCallback(e=>L(e),[L]),onItemShiftTab:n.useCallback(()=>F(!0),[]),onFocusableItemAdd:n.useCallback(()=>K(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>K(e=>e-1),[]),children:(0,p.jsx)(u.sG.div,{tabIndex:D||0===_?-1:0,"data-orientation":o,...j,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,a.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,a.m)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=T().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),E)}}N.current=!1}),onBlur:(0,a.m)(e.onBlur,()=>F(!1))})})}),C="RovingFocusGroupItem",S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:l,children:c,...d}=e,f=(0,s.B)(),v=l||f,m=E(C,r),h=m.currentTabStopId===v,w=y(r),{onFocusableItemAdd:g,onFocusableItemRemove:x,currentTabStopId:k}=m;return n.useEffect(()=>{if(o)return g(),()=>x()},[o,g,x]),(0,p.jsx)(b.ItemSlot,{scope:r,id:v,focusable:o,active:i,children:(0,p.jsx)(u.sG.span,{tabIndex:h?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,a.m)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,a.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let a=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return A[a]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>I(r))}}),children:"function"==typeof c?c({isCurrentTabStop:h,hasTabStop:null!=k}):c})})});S.displayName=C;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var L=j,D=S}}]);