"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7949],{3999:(e,t,a)=>{a.d(t,{cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},7168:(e,t,a)=>{a.d(t,{$:()=>o});var s=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(3999);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...l}=e,c=o?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:a,size:n,className:t})),...l})}},7949:(e,t,a)=>{a.d(t,{default:()=>y});var s=a(5155);a(2115);var r=a(2108),n=a(2098),i=a(3509),d=a(1007),o=a(381),l=a(4835),c=a(1362),u=a(6766),h=a(6215),x=a(3999);function m(e){let{...t}=e;return(0,s.jsx)(h.bL,{"data-slot":"dropdown-menu",...t})}function v(e){let{...t}=e;return(0,s.jsx)(h.l9,{"data-slot":"dropdown-menu-trigger",...t})}function f(e){let{className:t,sideOffset:a=4,...r}=e;return(0,s.jsx)(h.ZL,{children:(0,s.jsx)(h.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,x.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function g(e){let{className:t,inset:a,variant:r="default",...n}=e;return(0,s.jsx)(h.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,x.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n})}function p(e){let{className:t,inset:a,...r}=e;return(0,s.jsx)(h.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,x.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r})}function b(e){let{className:t,...a}=e;return(0,s.jsx)(h.wv,{"data-slot":"dropdown-menu-separator",className:(0,x.cn)("bg-border -mx-1 my-1 h-px",t),...a})}var j=a(7168),w=a(9663),k=a(6874),N=a.n(k);function y(){var e;let{data:t}=(0,r.useSession)(),{theme:a,setTheme:h}=(0,c.D)();return(0,s.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsx)("div",{className:"container flex h-16 items-center",children:(0,s.jsxs)("div",{className:"flex w-full items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center gap-2 ml-8",children:(0,s.jsx)(N(),{href:"/","aria-label":"Go to home",children:(0,s.jsx)(u.default,{src:"/TM_Logo_Black.svg",alt:"TalentMetrix Logo",width:150,height:40,className:"h-8 w-auto dark:invert cursor-pointer",priority:!0})})}),(0,s.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 flex items-center",children:(0,s.jsx)(N(),{href:"/","aria-label":"Go to home",children:(0,s.jsx)(u.default,{src:"/Kaleidoscope-Logo_GreyText.svg",alt:"Kaleidoscope Logo",width:150,height:35,className:"h-8 w-auto dark:invert cursor-pointer",priority:!0})})}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)(j.$,{variant:"ghost",size:"icon",onClick:()=>h("light"===a?"dark":"light"),className:"h-9 w-9",children:[(0,s.jsx)(n.A,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(i.A,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}),(null==t?void 0:t.user)&&(0,s.jsxs)(m,{children:[(0,s.jsx)(v,{asChild:!0,children:(0,s.jsx)(j.$,{variant:"ghost",className:"relative h-9 w-9 rounded-full",children:(0,s.jsx)(w.eu,{className:"h-9 w-9",children:(0,s.jsx)(w.q5,{children:(null==(e=t.user.name)?void 0:e.charAt(0))||"U"})})})}),(0,s.jsxs)(f,{className:"w-56",align:"end",forceMount:!0,children:[(0,s.jsx)(p,{children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:t.user.name}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:t.user.email})]})}),(0,s.jsx)(b,{}),(0,s.jsxs)(g,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]}),(0,s.jsx)(g,{asChild:!0,children:(0,s.jsxs)(N(),{href:"/settings",children:[(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Settings"})]})}),(0,s.jsx)(b,{}),(0,s.jsxs)(g,{onClick:()=>(0,r.signOut)(),children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})]})]})]})]})})})}},9663:(e,t,a)=>{a.d(t,{BK:()=>d,eu:()=>i,q5:()=>o});var s=a(5155);a(2115);var r=a(4011),n=a(3999);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(r._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}}}]);