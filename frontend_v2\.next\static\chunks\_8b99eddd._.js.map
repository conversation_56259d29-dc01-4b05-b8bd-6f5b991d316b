{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/app/login/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, FormEvent, useEffect } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport Image from 'next/image';\r\nimport { signIn } from 'next-auth/react';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { AlertCircle, Mail, Lock, ArrowRight } from \"lucide-react\";\r\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\r\n\r\nexport default function LoginPage() {\r\n  console.log(\"🏗️ LoginPage component rendering/re-rendering\");\r\n\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const callbackUrl = searchParams.get('callbackUrl') || '/landingpage_v2';\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  // Debug loading state changes\r\n  useEffect(() => {\r\n    console.log(\"⏳ Loading state changed to:\", loading);\r\n  }, [loading]);\r\n\r\n  console.log(\"🔧 Component state:\", {\r\n    email: email,\r\n    passwordLength: password.length,\r\n    error: error,\r\n    loading: loading,\r\n    callbackUrl: callbackUrl\r\n  });\r\n\r\n  useEffect(() => {\r\n    console.log(\"🔄 useEffect - Component mounted\");\r\n    console.log(\"🌐 Current URL:\", window.location.href);\r\n    console.log(\"📍 Callback URL from params:\", callbackUrl);\r\n\r\n    return () => {\r\n      console.log(\"🧹 useEffect cleanup - Component unmounting\");\r\n    };\r\n  }, [callbackUrl]);\r\n\r\n  const handleSubmit = async (event: FormEvent) => {\r\n    event.preventDefault();\r\n    console.log(\"🚀 LOGIN FORM SUBMITTED\");\r\n    console.log(\"📧 Email:\", email);\r\n    console.log(\"🔒 Password length:\", password.length);\r\n    console.log(\"🔄 Callback URL:\", callbackUrl);\r\n\r\n    setError(null);\r\n    setLoading(true);\r\n    console.log(\"⏳ Loading state set to true\");\r\n\r\n    try {\r\n      console.log(\"🔑 Calling signIn with credentials...\");\r\n      console.log(\"📦 SignIn payload:\", {\r\n        provider: 'credentials',\r\n        email: email,\r\n        passwordLength: password.length,\r\n        redirect: false\r\n      });\r\n\r\n      const result = await signIn('credentials', {\r\n        email,\r\n        password,\r\n        redirect: false,\r\n      });\r\n\r\n      console.log(\"📥 SignIn result received:\", result);\r\n      console.log(\"❓ Result error:\", result?.error);\r\n      console.log(\"✅ Result ok:\", result?.ok);\r\n      console.log(\"🔗 Result url:\", result?.url);\r\n      console.log(\"📊 Result status:\", result?.status);\r\n\r\n      if (result?.error) {\r\n        console.error(\"❌ Authentication failed with error:\", result.error);\r\n        setError(result.error);\r\n        setLoading(false);\r\n        console.log(\"⏳ Loading state set to false (error case)\");\r\n      } else if (result?.ok) {\r\n        console.log(\"✅ Authentication successful!\");\r\n        console.log(\"🔄 Redirecting to:\", callbackUrl);\r\n        router.push(callbackUrl);\r\n        router.refresh();\r\n        console.log(\"🔄 Router refresh called\");\r\n      } else {\r\n        console.warn(\"⚠️ Unexpected result state:\", result);\r\n        setError('Authentication failed. Please try again.');\r\n        setLoading(false);\r\n        console.log(\"⏳ Loading state set to false (unexpected case)\");\r\n      }\r\n    } catch (err) {\r\n      console.error(\"💥 CATCH BLOCK - Unexpected error during sign in:\", err);\r\n      console.error(\"💥 Error type:\", typeof err);\r\n      console.error(\"💥 Error message:\", err instanceof Error ? err.message : String(err));\r\n      console.error(\"💥 Error stack:\", err instanceof Error ? err.stack : 'No stack trace');\r\n      setError('An unexpected error occurred.');\r\n      setLoading(false);\r\n      console.log(\"⏳ Loading state set to false (catch case)\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-secondary/5 relative\">\r\n      {/* Login Card */}\r\n      <div className=\"w-full max-w-md p-4 z-10\">\r\n        <div className=\"mb-8 text-center\">\r\n          <h2 className=\"text-2xl font-bold tracking-tight\">Welcome back</h2>\r\n          <p className=\"text-muted-foreground mt-2\">Sign in to access your account</p>\r\n        </div>\r\n        \r\n        <Card className=\"shadow-lg border-muted/30\">\r\n          <CardHeader className=\"space-y-1 pb-2\">\r\n            <CardTitle className=\"text-xl font-semibold\">Sign In</CardTitle>\r\n            <CardDescription>\r\n              Enter your credentials to continue\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n              {error && (\r\n                <Alert variant=\"destructive\" className=\"animate-in slide-in-from-top-1 duration-300\">\r\n                  <AlertCircle className=\"h-4 w-4\" />\r\n                  <AlertDescription>{error}</AlertDescription>\r\n                </Alert>\r\n              )}\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"email\" className=\"text-sm font-medium\">Email</Label>\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground\">\r\n                    <Mail className=\"h-4 w-4\" />\r\n                  </div>\r\n                  <Input\r\n                    id=\"email\"\r\n                    type=\"email\"\r\n                    value={email}\r\n                    onChange={(e) => {\r\n                      console.log(\"📧 Email input changed:\", e.target.value);\r\n                      setEmail(e.target.value);\r\n                    }}\r\n                    onFocus={() => console.log(\"📧 Email input focused\")}\r\n                    onBlur={() => console.log(\"📧 Email input blurred\")}\r\n                    placeholder=\"<EMAIL>\"\r\n                    required\r\n                    disabled={loading}\r\n                    className=\"pl-10 transition-all focus:ring-2 focus:ring-primary/20\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <Label htmlFor=\"password\" className=\"text-sm font-medium\">Password</Label>\r\n                  <Button variant=\"link\" className=\"px-0 h-auto text-xs\" disabled={loading}>\r\n                    Forgot password?\r\n                  </Button>\r\n                </div>\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground\">\r\n                    <Lock className=\"h-4 w-4\" />\r\n                  </div>\r\n                  <Input\r\n                    id=\"password\"\r\n                    type=\"password\"\r\n                    value={password}\r\n                    onChange={(e) => {\r\n                      console.log(\"🔒 Password input changed, length:\", e.target.value.length);\r\n                      setPassword(e.target.value);\r\n                    }}\r\n                    onFocus={() => console.log(\"🔒 Password input focused\")}\r\n                    onBlur={() => console.log(\"🔒 Password input blurred\")}\r\n                    placeholder=\"••••••••\"\r\n                    required\r\n                    disabled={loading}\r\n                    className=\"pl-10 transition-all focus:ring-2 focus:ring-primary/20\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              <Button\r\n                type=\"submit\"\r\n                className=\"w-full transition-all hover:shadow-md group\"\r\n                disabled={loading}\r\n                onClick={() => {\r\n                  console.log(\"🖱️ Sign In button clicked\");\r\n                  console.log(\"⏳ Current loading state:\", loading);\r\n                  console.log(\"📧 Current email:\", email);\r\n                  console.log(\"🔒 Current password length:\", password.length);\r\n                }}\r\n              >\r\n                {loading ? (\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\r\n                    Signing in...\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"flex items-center justify-center\">\r\n                    Sign In\r\n                    <ArrowRight className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />\r\n                  </div>\r\n                )}\r\n              </Button>\r\n            </form>\r\n          </CardContent>\r\n          <CardFooter className=\"flex justify-center border-t pt-4 text-xs\">\r\n            <p className=\"text-muted-foreground\">\r\n              Don't have an account? <span className=\"text-primary font-medium\">Contact your administrator</span>\r\n            </p>\r\n          </CardFooter>\r\n        </Card>\r\n        \r\n        <div className=\"mt-8 text-center text-xs text-muted-foreground\">\r\n          <p>By signing in, you agree to our Terms of Service and Privacy Policy</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAXA;;;;;;;;;;AAae,SAAS;;IACtB,QAAQ,GAAG,CAAC;IAEZ,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,cAAc,aAAa,GAAG,CAAC,kBAAkB;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,QAAQ,GAAG,CAAC,+BAA+B;QAC7C;8BAAG;QAAC;KAAQ;IAEZ,QAAQ,GAAG,CAAC,uBAAuB;QACjC,OAAO;QACP,gBAAgB,SAAS,MAAM;QAC/B,OAAO;QACP,SAAS;QACT,aAAa;IACf;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,mBAAmB,OAAO,QAAQ,CAAC,IAAI;YACnD,QAAQ,GAAG,CAAC,gCAAgC;YAE5C;uCAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;;QACF;8BAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,aAAa;QACzB,QAAQ,GAAG,CAAC,uBAAuB,SAAS,MAAM;QAClD,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,SAAS;QACT,WAAW;QACX,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,sBAAsB;gBAChC,UAAU;gBACV,OAAO;gBACP,gBAAgB,SAAS,MAAM;gBAC/B,UAAU;YACZ;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC;gBACA;gBACA,UAAU;YACZ;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,QAAQ,GAAG,CAAC,mBAAmB,QAAQ;YACvC,QAAQ,GAAG,CAAC,gBAAgB,QAAQ;YACpC,QAAQ,GAAG,CAAC,kBAAkB,QAAQ;YACtC,QAAQ,GAAG,CAAC,qBAAqB,QAAQ;YAEzC,IAAI,QAAQ,OAAO;gBACjB,QAAQ,KAAK,CAAC,uCAAuC,OAAO,KAAK;gBACjE,SAAS,OAAO,KAAK;gBACrB,WAAW;gBACX,QAAQ,GAAG,CAAC;YACd,OAAO,IAAI,QAAQ,IAAI;gBACrB,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,sBAAsB;gBAClC,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,IAAI,CAAC,+BAA+B;gBAC5C,SAAS;gBACT,WAAW;gBACX,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,qDAAqD;YACnE,QAAQ,KAAK,CAAC,kBAAkB,OAAO;YACvC,QAAQ,KAAK,CAAC,qBAAqB,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;YAC/E,QAAQ,KAAK,CAAC,mBAAmB,eAAe,QAAQ,IAAI,KAAK,GAAG;YACpE,SAAS;YACT,WAAW;YACX,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAG5C,6LAAC,4HAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,4HAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,6LAAC,4HAAA,CAAA,YAAS;oCAAC,WAAU;8CAAwB;;;;;;8CAC7C,6LAAC,4HAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6LAAC,4HAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAK,UAAU;gCAAc,WAAU;;oCACrC,uBACC,6LAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;;0DACrC,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC,6HAAA,CAAA,mBAAgB;0DAAE;;;;;;;;;;;;kDAGvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAQ,WAAU;0DAAsB;;;;;;0DACvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC;4DACT,QAAQ,GAAG,CAAC,2BAA2B,EAAE,MAAM,CAAC,KAAK;4DACrD,SAAS,EAAE,MAAM,CAAC,KAAK;wDACzB;wDACA,SAAS,IAAM,QAAQ,GAAG,CAAC;wDAC3B,QAAQ,IAAM,QAAQ,GAAG,CAAC;wDAC1B,aAAY;wDACZ,QAAQ;wDACR,UAAU;wDACV,WAAU;;;;;;;;;;;;;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAsB;;;;;;kEAC1D,6LAAC,8HAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAO,WAAU;wDAAsB,UAAU;kEAAS;;;;;;;;;;;;0DAI5E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC;4DACT,QAAQ,GAAG,CAAC,sCAAsC,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM;4DACvE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC5B;wDACA,SAAS,IAAM,QAAQ,GAAG,CAAC;wDAC3B,QAAQ,IAAM,QAAQ,GAAG,CAAC;wDAC1B,aAAY;wDACZ,QAAQ;wDACR,UAAU;wDACV,WAAU;;;;;;;;;;;;;;;;;;kDAIhB,6LAAC,8HAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,UAAU;wCACV,SAAS;4CACP,QAAQ,GAAG,CAAC;4CACZ,QAAQ,GAAG,CAAC,4BAA4B;4CACxC,QAAQ,GAAG,CAAC,qBAAqB;4CACjC,QAAQ,GAAG,CAAC,+BAA+B,SAAS,MAAM;wCAC5D;kDAEC,wBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;gDAA0F;;;;;;iEAI3G,6LAAC;4CAAI,WAAU;;gDAAmC;8DAEhD,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,4HAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC;gCAAE,WAAU;;oCAAwB;kDACZ,6LAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;;;;;;;;;;;;8BAKxE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb;GA/MwB;;QAGP,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAJd", "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ElementRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,0KAAc,aAAA,EAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;YAEtB,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;YAEvD,MAAM,WAAA,GAAc,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "file": "mail.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/mail.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7', key: '132q7q' }],\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n];\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgNy04Ljk5MSA1LjcyN2EyIDIgMCAwIDEtMi4wMDkgMEwyIDciIC8+CiAgPHJlY3QgeD0iMiIgeT0iNCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjE2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('mail', __iconNode);\n\nexport default Mail;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "file": "lock.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/lock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '11', x: '3', y: '11', rx: '2', ry: '2', key: '1w4ew1' }],\n  ['path', { d: 'M7 11V7a5 5 0 0 1 10 0v4', key: 'fwvmzm' }],\n];\n\n/**\n * @component @name Lock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTEiIHg9IjMiIHk9IjExIiByeD0iMiIgcnk9IjIiIC8+CiAgPHBhdGggZD0iTTcgMTFWN2E1IDUgMCAwIDEgMTAgMHY0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lock = createLucideIcon('lock', __iconNode);\n\nexport default Lock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,EAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAG;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "file": "arrow-right.js", "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}