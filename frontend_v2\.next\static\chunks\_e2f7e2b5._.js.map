{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/NavigationButton.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface NavigationButtonProps {\r\n  text: string;\r\n  href?: string;\r\n  onClick?: () => void;\r\n  className?: string;\r\n  style?: React.CSSProperties;\r\n}\r\n\r\nexport function NavigationButton({ text, href, onClick, className = \"\", style }: NavigationButtonProps) {\r\n  const content = (\r\n    <>\r\n      {text}\r\n      <svg\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        width=\"20\"\r\n        height=\"20\"\r\n        fill=\"none\"\r\n        viewBox=\"0 0 24 24\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"2.5\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n        className=\"ml-2 group-hover:translate-x-1 transition-transform duration-150\"\r\n      >\r\n        <path d=\"M5 12h14\" />\r\n        <path d=\"M12 5l7 7-7 7\" />\r\n      </svg>\r\n    </>\r\n  );\r\n\r\n  const buttonClass =\r\n    \"rounded-full border-2 border-gray-800 dark:border-gray-200 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer \" +\r\n    (className || \"\");\r\n\r\n  return (\r\n    <div className=\"flex justify-center w-full my-8\">\r\n      {href ? (\r\n        <Button asChild variant=\"outline\" className={buttonClass} style={style} onClick={onClick}>\r\n          <Link href={href}>{content}</Link>\r\n        </Button>\r\n      ) : (\r\n        <Button\r\n          variant=\"outline\"\r\n          className={buttonClass}\r\n          style={style}\r\n          onClick={onClick}\r\n        >\r\n          {content}\r\n        </Button>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default NavigationButton;"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAaO,SAAS,iBAAiB,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,KAAK,EAAyB;IACpG,MAAM,wBACJ;;YACG;0BACD,6LAAC;gBACC,OAAM;gBACN,OAAM;gBACN,QAAO;gBACP,MAAK;gBACL,SAAQ;gBACR,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;gBACf,WAAU;;kCAEV,6LAAC;wBAAK,GAAE;;;;;;kCACR,6LAAC;wBAAK,GAAE;;;;;;;;;;;;;;IAKd,MAAM,cACJ,wPACA,CAAC,aAAa,EAAE;IAElB,qBACE,6LAAC;QAAI,WAAU;kBACZ,qBACC,6LAAC,8HAAA,CAAA,SAAM;YAAC,OAAO;YAAC,SAAQ;YAAU,WAAW;YAAa,OAAO;YAAO,SAAS;sBAC/E,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM;0BAAO;;;;;;;;;;iCAGrB,6LAAC,8HAAA,CAAA,SAAM;YACL,SAAQ;YACR,WAAW;YACX,OAAO;YACP,SAAS;sBAER;;;;;;;;;;;AAKX;KA5CgB;uCA8CD", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/GradientCard.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface GradientCardProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  style?: React.CSSProperties;\r\n  infoIcon?: boolean;\r\n  infoIconContent?: React.ReactNode;\r\n  color?: \"blue\" | \"orange\";\r\n}\r\n\r\n/**\r\n * GradientCard - Standardized card with gradient background and info icon (optional).\r\n * Usage: Wrap dynamic content inside <GradientCard>...</GradientCard>\r\n */\r\nconst colorMap = {\r\n  blue: {\r\n    gradient: \"linear-gradient(135deg, rgba(55,147,247,0.5) 0%, rgba(55,147,247,0.05) 30%, transparent 100%)\",\r\n    iconBg: \"#3793F7\",\r\n  },\r\n  orange: {\r\n    gradient: \"linear-gradient(135deg, rgba(255,168,0,0.5) 0%, rgba(255,168,0,0.05) 30%, transparent 100%)\",\r\n    iconBg: \"#FFA800\",\r\n  },\r\n};\r\n\r\nconst GradientCard: React.FC<GradientCardProps> = ({\r\n  children,\r\n  className = \"\",\r\n  style = {},\r\n  infoIcon = false,\r\n  infoIconContent = \"i\",\r\n  color = \"blue\",\r\n}) => {\r\n  const theme = colorMap[color] || colorMap.blue;\r\n  \r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"relative rounded-2xl p-6 mb-6 shadow-md bg-white dark:bg-gray-800 overflow-hidden max-w-[980px] mx-auto text-gray-900 dark:text-gray-100\",\r\n        className\r\n      )}\r\n      style={style}\r\n    >\r\n      {/* Gradient Background */}\r\n      <div\r\n        style={{\r\n          position: 'absolute',\r\n          inset: 0,\r\n          zIndex: 0,\r\n          pointerEvents: 'none',\r\n          borderRadius: '1rem',\r\n          background: theme.gradient,\r\n        }}\r\n      />\r\n      {infoIcon && (\r\n        <div\r\n          className=\"absolute top-5 right-5 w-6 h-6 text-white rounded-full flex items-center justify-center italic font-bold z-10\"\r\n          style={{ background: theme.iconBg }}\r\n        >\r\n          {infoIconContent}\r\n        </div>\r\n      )}\r\n      <div className=\"relative z-[1]\">{children}</div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GradientCard;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA;;;CAGC,GACD,MAAM,WAAW;IACf,MAAM;QACJ,UAAU;QACV,QAAQ;IACV;IACA,QAAQ;QACN,UAAU;QACV,QAAQ;IACV;AACF;AAEA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,CAAC,CAAC,EACV,WAAW,KAAK,EAChB,kBAAkB,GAAG,EACrB,QAAQ,MAAM,EACf;IACC,MAAM,QAAQ,QAAQ,CAAC,MAAM,IAAI,SAAS,IAAI;IAE9C,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4IACA;QAEF,OAAO;;0BAGP,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,cAAc;oBACd,YAAY,MAAM,QAAQ;gBAC5B;;;;;;YAED,0BACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,YAAY,MAAM,MAAM;gBAAC;0BAEjC;;;;;;0BAGL,6LAAC;gBAAI,WAAU;0BAAkB;;;;;;;;;;;;AAGvC;KAxCM;uCA0CS", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/Footer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport default function Footer() {\r\n  return (\r\n    <div className={cn(\r\n      \"w-full relative overflow-hidden text-black\",\r\n      \"py-4 px-8\",\r\n      \"text-xs leading-relaxed\",\r\n      \"rounded-t-[40px]\",\r\n      \"mt-10\"\r\n    )}>\r\n      {/* Gradient background */}\r\n      <div\r\n        style={{\r\n          position: 'absolute',\r\n          inset: 0,\r\n          zIndex: 0,\r\n          pointerEvents: 'none',\r\n          background: 'linear-gradient(135deg, rgba(75,159,239,0.9) 0%, rgba(75,159,239,0.3) 100%)',\r\n        }}\r\n      />\r\n      <div className=\"max-w-[1200px] mx-auto flex flex-col md:flex-row items-start md:items-center gap-2 md:gap-0 relative z-10\">\r\n        <div className=\"flex-1\">\r\n          Disclaimer – The report's findings are based on the specific test administered and the responses given. The report is designed to help people identify their natural abilities, strengths, limitations and career interests, and should be used only as a tool for self-discovery\r\n        </div>\r\n        <div className=\"whitespace-nowrap md:ml-4\">\r\n          |   Copyright – TalentMetrix 2025\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,8CACA,aACA,2BACA,oBACA;;0BAGA,6LAAC;gBACC,OAAO;oBACL,UAAU;oBACV,OAAO;oBACP,QAAQ;oBACR,eAAe;oBACf,YAAY;gBACd;;;;;;0BAEF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAS;;;;;;kCAGxB,6LAAC;wBAAI,WAAU;kCAA4B;;;;;;;;;;;;;;;;;;AAMnD;KA7BwB", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      data-slot=\"accordion-item\"\n      className={cn(\"border-b last:border-b-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        data-slot=\"accordion-trigger\"\n        className={cn(\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  )\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      data-slot=\"accordion-content\"\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\n      {...props}\n    >\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  )\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,6LAAC,wKAAA,CAAA,OAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;KAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,6LAAC,wKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,6LAAC,wKAAA,CAAA,UAA0B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,6LAAC,wKAAA,CAAA,UAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAGnD;MAdS", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/TM/ai_report_01062025/frontend_v2/app/%28with-sidebar%29/4_4_personal_development_plan/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport { useAssessment } from '@/context/AssessmentContext';\r\nimport NavigationButton from '@/components/NavigationButton';\r\nimport GradientCard from '@/components/GradientCard';\r\nimport Footer from '@/components/Footer';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetFooter,\r\n  She<PERSON><PERSON>eader,\r\n  She<PERSON><PERSON><PERSON><PERSON>,\r\n  SheetTrigger,\r\n} from '@/components/ui/sheet';\r\nimport { Plus, Edit2, Bold, Italic, List, ListOrdered, RotateCcw, RotateCw, Trash2 } from 'lucide-react';\r\n\r\n// Custom Rich Text Editor Component with improved list functionality\r\ninterface RichTextEditorProps {\r\n  content: string;\r\n  onChange: (html: string) => void;\r\n  placeholder?: string;\r\n  id: string;\r\n  key?: string; // Add key prop for React reconciliation\r\n}\r\n\r\nconst RichTextEditor: React.FC<RichTextEditorProps> = ({ content, onChange, placeholder, id }) => {\r\n  const editorRef = useRef<HTMLDivElement>(null);\r\n  const [isFocused, setIsFocused] = useState(false);\r\n  const [editorContent, setEditorContent] = useState(content);\r\n\r\n  // Initialize when component mounts\r\n  useEffect(() => {\r\n    if (editorRef.current) {\r\n      // Set initial content\r\n      editorRef.current.innerHTML = content || '';\r\n      setEditorContent(content || '');\r\n      applyListStyling();\r\n    }\r\n  }, []); // Only run once on mount\r\n\r\n  // Update when content prop changes\r\n  useEffect(() => {\r\n    if (editorRef.current && content !== editorContent) {\r\n      // Update content when it changes\r\n      editorRef.current.innerHTML = content || '';\r\n      setEditorContent(content || '');\r\n      applyListStyling();\r\n    }\r\n  }, [content, editorContent]);\r\n\r\n  // Helper function to apply list styling\r\n  const applyListStyling = () => {\r\n    if (!editorRef.current) return;\r\n\r\n    const lists = editorRef.current.querySelectorAll('ul, ol');\r\n    lists.forEach(list => {\r\n      if (list.tagName === 'UL') {\r\n        (list as HTMLElement).style.listStyleType = 'disc';\r\n        (list as HTMLElement).style.marginLeft = '20px';\r\n        (list as HTMLElement).style.paddingLeft = '0px';\r\n      } else if (list.tagName === 'OL') {\r\n        (list as HTMLElement).style.listStyleType = 'decimal';\r\n        (list as HTMLElement).style.marginLeft = '20px';\r\n        (list as HTMLElement).style.paddingLeft = '0px';\r\n      }\r\n    });\r\n  };\r\n\r\n  const handleInput = () => {\r\n    if (editorRef.current) {\r\n      const html = editorRef.current.innerHTML;\r\n      setEditorContent(html);\r\n      onChange(html);\r\n    }\r\n  };\r\n\r\n  const executeCommand = (command: string, value?: string) => {\r\n    if (editorRef.current) {\r\n      editorRef.current.focus();\r\n\r\n      // Special handling for lists\r\n      if (command === 'insertUnorderedList' || command === 'insertOrderedList') {\r\n        const selection = window.getSelection();\r\n        if (selection && selection.rangeCount > 0) {\r\n          document.execCommand(command, false, value);\r\n\r\n          // Force proper list styling\r\n          setTimeout(() => {\r\n            if (editorRef.current) {\r\n              const lists = editorRef.current.querySelectorAll('ul, ol');\r\n              lists.forEach(list => {\r\n                if (list.tagName === 'UL') {\r\n                  (list as HTMLElement).style.listStyleType = 'disc';\r\n                  (list as HTMLElement).style.marginLeft = '20px';\r\n                  (list as HTMLElement).style.paddingLeft = '0px';\r\n                } else if (list.tagName === 'OL') {\r\n                  (list as HTMLElement).style.listStyleType = 'decimal';\r\n                  (list as HTMLElement).style.marginLeft = '20px';\r\n                  (list as HTMLElement).style.paddingLeft = '0px';\r\n                }\r\n              });\r\n              handleInput();\r\n            }\r\n          }, 10);\r\n        }\r\n      } else {\r\n        document.execCommand(command, false, value);\r\n      }\r\n\r\n      handleInput();\r\n    }\r\n  };\r\n\r\n  const isCommandActive = (command: string): boolean => {\r\n    try {\r\n      if (editorRef.current && editorRef.current.contains(document.activeElement)) {\r\n        return document.queryCommandState(command);\r\n      }\r\n      return false;\r\n    } catch {\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // Handle Enter key in lists to create new list items\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter') {\r\n      const selection = window.getSelection();\r\n      if (selection && selection.rangeCount > 0) {\r\n        const range = selection.getRangeAt(0);\r\n        const listItem = range.startContainer.parentElement?.closest('li');\r\n\r\n        if (listItem) {\r\n          e.preventDefault();\r\n\r\n          // Check if current list item is empty\r\n          if (listItem.textContent?.trim() === '') {\r\n            // Exit the list if empty item\r\n            document.execCommand('outdent');\r\n          } else {\r\n            // Create new list item\r\n            document.execCommand('insertHTML', false, '<br>');\r\n            document.execCommand('insertHTML', false, '</li><li>');\r\n          }\r\n          handleInput();\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800\">\r\n      {/* Toolbar */}\r\n      <div className=\"border-b border-gray-300 dark:border-gray-600 p-2 flex items-center gap-1 bg-gray-50 dark:bg-gray-700 rounded-t-md\">\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onMouseDown={(e) => e.preventDefault()}\r\n          onClick={() => executeCommand('bold')}\r\n          className={`h-8 w-8 p-0 ${isCommandActive('bold') ? 'bg-gray-200 dark:bg-gray-600' : ''}`}\r\n        >\r\n          <Bold className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onMouseDown={(e) => e.preventDefault()}\r\n          onClick={() => executeCommand('italic')}\r\n          className={`h-8 w-8 p-0 ${isCommandActive('italic') ? 'bg-gray-200 dark:bg-gray-600' : ''}`}\r\n        >\r\n          <Italic className=\"h-4 w-4\" />\r\n        </Button>\r\n        <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1\" />\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onMouseDown={(e) => e.preventDefault()}\r\n          onClick={() => executeCommand('insertUnorderedList')}\r\n          className={`h-8 w-8 p-0 ${isCommandActive('insertUnorderedList') ? 'bg-gray-200 dark:bg-gray-600' : ''}`}\r\n        >\r\n          <List className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onMouseDown={(e) => e.preventDefault()}\r\n          onClick={() => executeCommand('insertOrderedList')}\r\n          className={`h-8 w-8 p-0 ${isCommandActive('insertOrderedList') ? 'bg-gray-200 dark:bg-gray-600' : ''}`}\r\n        >\r\n          <ListOrdered className=\"h-4 w-4\" />\r\n        </Button>\r\n        <div className=\"w-px h-6 bg-gray-300 dark:border-gray-600 mx-1\" />\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onMouseDown={(e) => e.preventDefault()}\r\n          onClick={() => executeCommand('undo')}\r\n          className=\"h-8 w-8 p-0\"\r\n        >\r\n          <RotateCcw className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onMouseDown={(e) => e.preventDefault()}\r\n          onClick={() => executeCommand('redo')}\r\n          className=\"h-8 w-8 p-0\"\r\n        >\r\n          <RotateCw className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Editor Content */}\r\n      <div className=\"relative\">\r\n        <div\r\n          ref={editorRef}\r\n          contentEditable\r\n          onInput={handleInput}\r\n          onKeyDown={handleKeyDown}\r\n          onFocus={() => setIsFocused(true)}\r\n          onBlur={() => setIsFocused(false)}\r\n          className=\"min-h-[100px] p-3 text-gray-900 dark:text-gray-100 focus:outline-none\"\r\n          style={{\r\n            wordBreak: 'break-word',\r\n            overflowWrap: 'break-word',\r\n            direction: 'ltr',\r\n            textAlign: 'left',\r\n            unicodeBidi: 'embed'\r\n          }}\r\n          suppressContentEditableWarning={true}\r\n          data-editor-id={id}\r\n        />\r\n        {!isFocused && (!editorContent || editorContent.trim() === '' || editorContent === '<br>') && (\r\n          <div className=\"absolute top-3 left-3 text-gray-400 dark:text-gray-500 pointer-events-none\">\r\n            {placeholder}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Ensure list styles are applied - scoped to this editor */}\r\n      <style jsx>{`\r\n        div[data-editor-id=\"${id}\"] ul {\r\n          list-style-type: disc !important;\r\n          margin-left: 20px !important;\r\n          padding-left: 0px !important;\r\n        }\r\n\r\n        div[data-editor-id=\"${id}\"] ol {\r\n          list-style-type: decimal !important;\r\n          margin-left: 20px !important;\r\n          padding-left: 0px !important;\r\n        }\r\n\r\n        div[data-editor-id=\"${id}\"] li {\r\n          margin-bottom: 4px !important;\r\n          display: list-item !important;\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Define types for development item\r\ninterface DevelopmentItem {\r\n  id: string;\r\n  goal: string;\r\n  tasks: string;\r\n  importantTasks: string;\r\n  difficultyLevel: 'Low Priority' | 'High Priority' | 'Medium Priority';\r\n  timeToComplete: string;\r\n  progress: 'Not Started' | 'In Progress' | 'Complete';\r\n  dateCreated: string;\r\n}\r\n\r\nexport default function PersonalDevelopmentPlanPage() {\r\n  const { assessmentData, loading, error } = useAssessment();\r\n  const [isSheetOpen, setIsSheetOpen] = useState(false);\r\n  const [editingItem, setEditingItem] = useState<DevelopmentItem | null>(null);\r\n  const [developmentItems, setDevelopmentItems] = useState<DevelopmentItem[]>([]);\r\n  const [editorKey, setEditorKey] = useState(0); // Add this to force editor re-render\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [apiError, setApiError] = useState<string | null>(null);\r\n\r\n  // Form state for new/edit development item\r\n  const [formData, setFormData] = useState({\r\n    goal: '',\r\n    tasks: '',\r\n    importantTasks: '',\r\n    difficultyLevel: '',\r\n    timeToComplete: '',\r\n    progress: 'Not Started'\r\n  });\r\n\r\n  // Function to handle deleting a PDP item\r\n  const handleDelete = async (id: string) => {\r\n    if (confirm('Are you sure you want to delete this development item?')) {\r\n      try {\r\n        setIsLoading(true);\r\n        setApiError(null);\r\n\r\n        // Send delete request to API\r\n        const response = await fetch(`/api/pdp?id=${id}`, {\r\n          method: 'DELETE',\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Error deleting PDP item: ${response.status}`);\r\n        }\r\n\r\n        // Remove from local state\r\n        setDevelopmentItems(prev => prev.filter(item => item.id !== id));\r\n      } catch (error) {\r\n        console.error('Error deleting PDP item:', error);\r\n        setApiError('Failed to delete development item. Please try again.');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Generate unique ID for development items (used as fallback)\r\n  const generateId = () => {\r\n    const randomNumber = Math.floor(100000 + Math.random() * 900000);\r\n    return `PDI_${randomNumber}`;\r\n  };\r\n\r\n  const handleInputChange = (field: string, value: string) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n  };\r\n\r\n  const openAddModal = () => {\r\n    setEditingItem(null);\r\n    setFormData({\r\n      goal: '',\r\n      tasks: '',\r\n      importantTasks: '',\r\n      difficultyLevel: '',\r\n      timeToComplete: '',\r\n      progress: 'Not Started'\r\n    });\r\n    setEditorKey(prev => prev + 1); // Force editor re-render\r\n    setIsSheetOpen(true);\r\n  };\r\n\r\n  const openEditModal = (item: DevelopmentItem) => {\r\n    console.log('Opening edit modal for item:', item);\r\n\r\n    // Generate a new key to force re-render of editors\r\n    const newKey = Date.now(); // Use timestamp for unique key\r\n    setEditorKey(newKey);\r\n\r\n    // Set form data with all fields from the item\r\n    const newFormData = {\r\n      goal: item.goal || '',\r\n      tasks: item.tasks || '',\r\n      importantTasks: item.importantTasks || '',\r\n      difficultyLevel: item.difficultyLevel,\r\n      timeToComplete: item.timeToComplete,\r\n      progress: item.progress\r\n    };\r\n\r\n    // Set form data\r\n    setFormData(newFormData);\r\n\r\n    // Set editing item\r\n    setEditingItem(item);\r\n\r\n    // Log the data we're setting\r\n    console.log('Setting form data for edit:', newFormData);\r\n\r\n    // Open sheet after a short delay to ensure state is properly set\r\n    setTimeout(() => {\r\n      setIsSheetOpen(true);\r\n    }, 50);\r\n  };\r\n\r\n  // Load PDP items from the backend API\r\n  const loadPDPItems = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setApiError(null);\r\n\r\n      const response = await fetch('/api/pdp');\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Error fetching PDP items: ${response.status}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      // Map backend data to frontend format\r\n      const mappedItems: DevelopmentItem[] = data.items.map((item: any) => ({\r\n        id: item.id,\r\n        goal: item.goal,\r\n        tasks: item.tasks,\r\n        importantTasks: item.important_tasks || '',\r\n        difficultyLevel: item.priority_level as DevelopmentItem['difficultyLevel'],\r\n        timeToComplete: item.time_to_complete,\r\n        progress: item.progress as DevelopmentItem['progress'],\r\n        dateCreated: new Date(item.date_created).toISOString().split('T')[0]\r\n      }));\r\n\r\n      setDevelopmentItems(mappedItems);\r\n    } catch (error) {\r\n      console.error('Failed to load PDP items:', error);\r\n      setApiError('Failed to load development items. Please try again later.');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Load items when component mounts\r\n  useEffect(() => {\r\n    loadPDPItems();\r\n  }, []);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setIsLoading(true);\r\n    setApiError(null);\r\n\r\n    try {\r\n      if (editingItem) {\r\n        // Map form data to API format for update\r\n        const apiData = {\r\n          goal: formData.goal,\r\n          tasks: formData.tasks,\r\n          important_tasks: formData.importantTasks,\r\n          priority_level: formData.difficultyLevel,\r\n          time_to_complete: formData.timeToComplete,\r\n          progress: formData.progress\r\n        };\r\n\r\n        // Send update request to API\r\n        const response = await fetch(`/api/pdp?id=${editingItem.id}`, {\r\n          method: 'PUT',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify({ id: editingItem.id, ...apiData }),\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Error updating PDP item: ${response.status}`);\r\n        }\r\n\r\n        const updatedData = await response.json();\r\n\r\n        // Update in local state\r\n        const updatedItem: DevelopmentItem = {\r\n          id: updatedData.id,\r\n          goal: updatedData.goal,\r\n          tasks: updatedData.tasks,\r\n          importantTasks: updatedData.important_tasks || '',\r\n          difficultyLevel: updatedData.priority_level as DevelopmentItem['difficultyLevel'],\r\n          timeToComplete: updatedData.time_to_complete,\r\n          progress: updatedData.progress as DevelopmentItem['progress'],\r\n          dateCreated: new Date(updatedData.date_created).toISOString().split('T')[0]\r\n        };\r\n\r\n        console.log('Development Item Updated:', updatedItem);\r\n\r\n        setDevelopmentItems(prev =>\r\n          prev.map(item => item.id === editingItem.id ? updatedItem : item)\r\n        );\r\n      } else {\r\n        // Map form data to API format for create\r\n        const apiData = {\r\n          goal: formData.goal,\r\n          tasks: formData.tasks,\r\n          important_tasks: formData.importantTasks,\r\n          priority_level: formData.difficultyLevel,\r\n          time_to_complete: formData.timeToComplete,\r\n          progress: formData.progress\r\n        };\r\n\r\n        // Send create request to API\r\n        const response = await fetch('/api/pdp', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify(apiData),\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Error creating PDP item: ${response.status}`);\r\n        }\r\n\r\n        const newData = await response.json();\r\n\r\n        // Create new item with API response data\r\n        const newItem: DevelopmentItem = {\r\n          id: newData.id,\r\n          goal: newData.goal,\r\n          tasks: newData.tasks,\r\n          importantTasks: newData.important_tasks || '',\r\n          difficultyLevel: newData.priority_level as DevelopmentItem['difficultyLevel'],\r\n          timeToComplete: newData.time_to_complete,\r\n          progress: newData.progress as DevelopmentItem['progress'],\r\n          dateCreated: new Date(newData.date_created).toISOString().split('T')[0]\r\n        };\r\n\r\n        console.log('New Development Item Added:', newItem);\r\n\r\n        // Add to local state\r\n        setDevelopmentItems(prev => [...prev, newItem]);\r\n      }\r\n\r\n      // Reset form and close modal\r\n      setFormData({\r\n        goal: '',\r\n        tasks: '',\r\n        importantTasks: '',\r\n        difficultyLevel: '',\r\n        timeToComplete: '',\r\n        progress: 'Not Started'\r\n      });\r\n      setEditingItem(null);\r\n      setIsSheetOpen(false);\r\n      setEditorKey(prev => prev + 1); // Reset editor key\r\n    } catch (error) {\r\n      console.error('Error saving PDP item:', error);\r\n      setApiError('Failed to save development item. Please try again.');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (level: string) => {\r\n    switch (level) {\r\n      case 'High Priority':\r\n        return 'text-red-600 dark:text-red-400';\r\n      case 'Medium Priority':\r\n        return 'text-yellow-600 dark:text-yellow-400';\r\n      case 'Low Priority':\r\n        return 'text-green-600 dark:text-green-400';\r\n      default:\r\n        return 'text-gray-600 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const getProgressColor = (progress: string) => {\r\n    switch (progress) {\r\n      case 'Complete':\r\n        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';\r\n      case 'In Progress':\r\n        return 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20';\r\n      case 'Not Started':\r\n        return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800';\r\n      default:\r\n        return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6\">\r\n      {/* Main Content */}\r\n      <div className=\"w-full max-w-[1100px] mx-auto px-5 pt-8 relative\">\r\n        {/* Header Section */}\r\n        <div className=\"text-center mb-10 mt-5\">\r\n          <h1 className=\"text-[3rem] font-light text-[#3793F7] dark:text-blue-400 mb-8 md:text-4xl lg:text-[3rem]\">\r\n            4.4 Personal Development Plan\r\n          </h1>\r\n        </div>\r\n\r\n        {/* Static Instructions */}\r\n        <GradientCard color=\"blue\" >\r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-3\">\r\n              <div className=\"flex items-start\">\r\n                <span className=\"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold\">1.</span>\r\n                <span className=\"text-gray-700 dark:text-gray-300\">\r\n                  Write 3 unique goals that are important for you to achieve in the next 3-6months, using the SWOD Development Areas.\r\n                </span>\r\n              </div>\r\n              <div className=\"flex items-start\">\r\n                <span className=\"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold\">2.</span>\r\n                <span className=\"text-gray-700 dark:text-gray-300\">\r\n                  Think â€“ development of skills/tools/methods; acing a particular subject/test/paper; Internship / job placement success.\r\n                </span>\r\n              </div>\r\n              <div className=\"flex items-start\">\r\n                <span className=\"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold\">3.</span>\r\n                <span className=\"text-gray-700 dark:text-gray-300\">\r\n                  Avoid BIG Goals â€“ such as buying a dream home; becoming the CEO of a company â€“ those are years away if you are being practical. Right now identify goals that will significantly boost your confidence and purpose.\r\n                </span>\r\n              </div>\r\n              <div className=\"flex items-start\">\r\n                <span className=\"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold\">4.</span>\r\n                <span className=\"text-gray-700 dark:text-gray-300\">\r\n                  Once you prepare your SMART Goals, revisit them on a daily basis and track your progress. Do not leave it as an academic or theoretical exercise.\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </GradientCard>\r\n\r\n        {/* Reference Template Table */}\r\n        <Accordion type=\"single\" collapsible className=\"mb-8\">\r\n          <AccordionItem value=\"reference-template\">\r\n            <AccordionTrigger className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 px-6 py-4 border-b border-gray-200 dark:border-gray-700\">Reference Template</AccordionTrigger>\r\n            <AccordionContent>\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\">\r\n                <div className=\"overflow-x-auto\">\r\n                  <table className=\"w-full text-sm\">\r\n                    <thead className=\"bg-blue-600 text-white\">\r\n                      <tr>\r\n                        <th className=\"px-3 py-2 text-left font-medium\">GOALS</th>\r\n                        <th className=\"px-3 py-2 text-left font-medium\">IMPORTANT TASKS</th>\r\n                        <th className=\"px-3 py-2 text-left font-medium\">DIFFICULTY TO SUCCESS</th>\r\n                        <th className=\"px-3 py-2 text-left font-medium\">TIME TO COMPLETE</th>\r\n                        <th className=\"px-3 py-2 text-left font-medium\">PROGRESS</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody className=\"bg-white dark:bg-gray-800\">\r\n                      <tr className=\"border-b border-gray-200 dark:border-gray-700\">\r\n                        <td className=\"px-3 py-4 align-top\">\r\n                          <div className=\"space-y-2\">\r\n                            <p className=\"font-medium text-gray-900 dark:text-gray-100\">Must be Specific & achievable</p>\r\n                            <ul className=\"space-y-1 text-xs text-gray-600 dark:text-gray-400\">\r\n                              <li>â€¢ High on Time and importance</li>\r\n                              <li>â€¢ Must be specific</li>\r\n                              <li>â€¢ Effort 20% gets Effort 80% tasks</li>\r\n                              <li>â€¢ High Priority</li>\r\n                            </ul>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"px-3 py-4 align-top\">\r\n                          <div className=\"space-y-2\">\r\n                            <p className=\"font-medium text-gray-900 dark:text-gray-100\">High on Time and importance</p>\r\n                            <ul className=\"space-y-1 text-xs text-gray-600 dark:text-gray-400\">\r\n                              <li>â€¢ High on Time and importance</li>\r\n                              <li>â€¢ Must be specific</li>\r\n                              <li>â€¢ Effort 80% tasks</li>\r\n                              <li>â€¢ Low Priority</li>\r\n                            </ul>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"px-3 py-4 align-top\">\r\n                          <div className=\"space-y-2\">\r\n                            <p className=\"font-medium text-red-600\">Write for EACH GOAL Completion criteria</p>\r\n                            <p className=\"text-xs text-gray-600 dark:text-gray-400\">Integrated conceptual learning</p>\r\n                            <p className=\"text-xs text-gray-600 dark:text-gray-400\">Feedback</p>\r\n                            <p className=\"text-xs text-gray-600 dark:text-gray-400\">Actual score on a test or paper</p>\r\n                            <p className=\"text-xs text-gray-600 dark:text-gray-400\">Quality of completeness of tasks in all aspects</p>\r\n                            <p className=\"text-xs text-gray-600 dark:text-gray-400\">Within budget</p>\r\n                            <p className=\"text-xs text-gray-600 dark:text-gray-400\">Others</p>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"px-3 py-4 align-top\">\r\n                          <div className=\"space-y-2\">\r\n                            <p className=\"font-medium text-gray-900 dark:text-gray-100\">Write for EACH TASK</p>\r\n                            <p className=\"text-xs text-gray-600 dark:text-gray-400\">How much timeâ€”specific time - hours/day/wk, deadlines, as applicable.</p>\r\n                            <p className=\"text-xs text-gray-600 dark:text-gray-400\">Budget slack time</p>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"px-3 py-4 align-top\">\r\n                          <div className=\"space-y-2\">\r\n                            <p className=\"font-medium text-gray-900 dark:text-gray-100\">Write for EACH progress</p>\r\n                            <p className=\"text-xs text-gray-600 dark:text-gray-400\">% Complete</p>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                      <tr className=\"border-b border-gray-200 dark:border-gray-700\">\r\n                        <td className=\"px-3 py-4 align-top\">\r\n                          <div className=\"space-y-2\">\r\n                            <p className=\"font-medium text-blue-600\">Example - \"I will improve my math skills by 30% by the end of the semester\"</p>\r\n                            <ul className=\"space-y-1 text-xs text-gray-600 dark:text-gray-400\">\r\n                              <li>â€¢ Practice 1 test each week</li>\r\n                              <li>â€¢ Study math for 1 hour daily</li>\r\n                              <li>â€¢ Ask questions to teacher, tutor or classmates when confused</li>\r\n                            </ul>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"px-3 py-4 align-top\">\r\n                          <div className=\"space-y-2\">\r\n                            <p className=\"font-medium text-blue-600\">Identifying learning videos</p>\r\n                            <p className=\"text-xs text-gray-600 dark:text-gray-400\">Make a 3 month study calendar</p>\r\n                          </div>\r\n                        </td>\r\n                        <td className=\"px-3 py-4 align-top\">\r\n                          <p className=\"text-xs text-gray-600 dark:text-gray-400\">80% score in mid-term test</p>\r\n                        </td>\r\n                        <td className=\"px-3 py-4 align-top\">\r\n                          <p className=\"text-xs text-gray-600 dark:text-gray-400\">31/08/2024</p>\r\n                        </td>\r\n                        <td className=\"px-3 py-4 align-top\">\r\n                          <p className=\"text-blue-600 text-xs\">In Progress</p>\r\n                        </td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n            </AccordionContent>\r\n          </AccordionItem>\r\n        </Accordion>\r\n\r\n        {/* Add New Item Button */}\r\n        <div className=\"flex justify-end mb-6\">\r\n          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>\r\n            <SheetTrigger asChild>\r\n              <Button className=\"bg-[#3793F7] hover:bg-blue-600 text-white\" onClick={openAddModal}>\r\n                <Plus className=\"w-4 h-4 mr-2\" />\r\n                Add Development Item\r\n              </Button>\r\n            </SheetTrigger>\r\n            <SheetContent \r\n  className=\"overflow-y-auto p-8 bg-white dark:bg-gray-800\"\r\n  style={{ \r\n    width: '900px !important', \r\n    maxWidth: '90vw !important',\r\n    minWidth: '900px'\r\n  }}\r\n>\r\n           \r\n              {/* Force width with CSS */}\r\n              <style jsx>{`\r\n                :global([data-radix-dialog-content]) {\r\n                  width: 900px !important;\r\n                  max-width: 90vw !important;\r\n                  min-width: 900px !important;\r\n                }\r\n\r\n                :global(.fixed.inset-y-0.right-0.z-50) {\r\n                  width: 900px !important;\r\n                  max-width: 90vw !important;\r\n                }\r\n\r\n                @media (max-width: 1024px) {\r\n                  :global([data-radix-dialog-content]) {\r\n                    width: 90vw !important;\r\n                    min-width: auto !important;\r\n                  }\r\n\r\n                  :global(.fixed.inset-y-0.right-0.z-50) {\r\n                    width: 90vw !important;\r\n                  }\r\n                }\r\n              `}</style>\r\n\r\n              <SheetHeader className=\"mb-8\">\r\n                <SheetTitle className=\"text-xl\">\r\n                  {editingItem ? 'Edit Personal Development Item' : 'Add Personal Development Item'}\r\n                </SheetTitle>\r\n                <SheetDescription className=\"text-base mt-2\">\r\n                  {editingItem\r\n                    ? 'Update your goal with specific tasks and timeline.'\r\n                    : 'Create a new goal with specific tasks and timeline for your personal development plan.'\r\n                  }\r\n                </SheetDescription>\r\n              </SheetHeader>\r\n\r\n              <form onSubmit={handleSubmit} className=\"space-y-8\">\r\n                <div>\r\n                  <Label htmlFor=\"goal\" className=\"mb-2 block text-base font-medium\">Goal</Label>\r\n                  <div className=\"mb-4\">\r\n                    <RichTextEditor\r\n                      key={`goal-${editorKey}`} // Use editorKey for force re-render\r\n                      id=\"goal-editor\"\r\n                      content={formData.goal}\r\n                      onChange={(html) => handleInputChange('goal', html)}\r\n                      placeholder=\"Describe your specific, measurable goal...\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <Label htmlFor=\"tasks\" className=\"mb-2 block text-base font-medium\">Tasks</Label>\r\n                  <div className=\"mb-4\">\r\n                    <RichTextEditor\r\n                      key={`tasks-${editorKey}`} // Use editorKey for force re-render\r\n                      id=\"tasks-editor\"\r\n                      content={formData.tasks}\r\n                      onChange={(html) => handleInputChange('tasks', html)}\r\n                      placeholder=\"List the specific tasks you will complete...\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <Label htmlFor=\"importantTasks\" className=\"mb-2 block text-base font-medium\">Most Important Tasks</Label>\r\n                  <div className=\"mb-4\">\r\n                    <RichTextEditor\r\n                      key={`important-tasks-${editorKey}`} // Use editorKey for force re-render\r\n                      id=\"important-tasks-editor\"\r\n                      content={formData.importantTasks}\r\n                      onChange={(html) => handleInputChange('importantTasks', html)}\r\n                      placeholder=\"Identify the most critical tasks for success...\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div>\r\n                    <Label htmlFor=\"difficultyLevel\" className=\"mb-2 block text-base font-medium\">Priority Level</Label>\r\n                    <Select onValueChange={(value) => handleInputChange('difficultyLevel', value)} value={formData.difficultyLevel} required>\r\n                      <SelectTrigger className=\"h-11\">\r\n                        <SelectValue placeholder=\"Select priority level\" />\r\n                      </SelectTrigger>\r\n                      < SelectContent className=\"bg-white dark:bg-gray-800\">\r\n                        <SelectItem value=\"High Priority\">High Priority</SelectItem>\r\n                        <SelectItem value=\"Medium Priority\">Medium Priority</SelectItem>\r\n                        <SelectItem value=\"Low Priority\">Low Priority</SelectItem>\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <Label htmlFor=\"timeToComplete\" className=\"mb-2 block text-base font-medium\">Target Completion Date</Label>\r\n                    <Input\r\n                      id=\"timeToComplete\"\r\n                      type=\"date\"\r\n                      value={formData.timeToComplete}\r\n                      onChange={(e) => handleInputChange('timeToComplete', e.target.value)}\r\n                      required\r\n                      className=\"h-11\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <Label htmlFor=\"progress\" className=\"mb-2 block text-base font-medium\">Progress Status</Label>\r\n                  <Select onValueChange={(value) => handleInputChange('progress', value)} value={formData.progress}>\r\n                    <SelectTrigger className=\"h-11\">\r\n                      <SelectValue />\r\n                    </SelectTrigger>\r\n                    <SelectContent className=\"bg-white dark:bg-gray-800\">\r\n                      <SelectItem value=\"Not Started\">Not Started</SelectItem>\r\n                      <SelectItem value=\"In Progress\">In Progress</SelectItem>\r\n                      <SelectItem value=\"Complete\">Complete</SelectItem>\r\n                    </SelectContent>\r\n                  </Select>\r\n                </div>\r\n\r\n                <SheetFooter className=\"flex gap-4 pt-8 mt-8 border-t\">\r\n                  <Button type=\"button\" variant=\"outline\" onClick={() => setIsSheetOpen(false)} className=\"px-6 py-2\">\r\n                    Cancel\r\n                  </Button>\r\n                  <Button type=\"submit\" className=\"bg-[#3793F7] hover:bg-blue-600 px-6 py-2\">\r\n                    {editingItem ? 'Update Item' : 'Add Item'}\r\n                  </Button>\r\n                </SheetFooter>\r\n              </form>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        {/* User's Development Items Table */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-8\">\r\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n              My Personal Development Goals ({developmentItems.length})\r\n            </h2>\r\n          </div>\r\n\r\n          {isLoading ? (\r\n            <div className=\"flex justify-center items-center py-10\">\r\n              <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"></div>\r\n            </div>\r\n          ) : developmentItems.length > 0 ? (\r\n            <div className=\"overflow-x-auto\">\r\n              <table className=\"w-full\">\r\n                <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n                  <tr>\r\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\r\n                      Goal\r\n                    </th>\r\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\r\n                      Tasks\r\n                    </th>\r\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\r\n                      Important Tasks\r\n                    </th>\r\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\r\n                      Priority\r\n                    </th>\r\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\r\n                      Target Date\r\n                    </th>\r\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\r\n                      Progress\r\n                    </th>\r\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\r\n                      Actions\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                  {developmentItems.map((item) => (\r\n                    <tr key={item.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                      <td className=\"px-4 py-4 text-sm text-gray-900 dark:text-gray-100\">\r\n                        <div className=\"max-w-xs\">\r\n                          <div\r\n                            className=\"font-medium text-sm prose prose-sm dark:prose-invert max-w-none [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4\"\r\n                            dangerouslySetInnerHTML={{ __html: item.goal }}\r\n                          />\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-4 py-4 text-sm text-gray-600 dark:text-gray-300\">\r\n                        <div className=\"max-w-xs\">\r\n                          <div\r\n                            className=\"text-sm prose prose-sm dark:prose-invert max-w-none line-clamp-3 [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4\"\r\n                            dangerouslySetInnerHTML={{ __html: item.tasks }}\r\n                          />\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-4 py-4 text-sm text-gray-600 dark:text-gray-300\">\r\n                        <div className=\"max-w-xs\">\r\n                          <div\r\n                            className=\"text-sm prose prose-sm dark:prose-invert max-w-none line-clamp-3 [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4\"\r\n                            dangerouslySetInnerHTML={{ __html: item.importantTasks }}\r\n                          />\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-4 py-4 text-sm\">\r\n                        <span className={`font-medium ${getPriorityColor(item.difficultyLevel)}`}>\r\n                          {item.difficultyLevel}\r\n                        </span>\r\n                      </td>\r\n                      <td className=\"px-4 py-4 text-sm text-gray-600 dark:text-gray-300\">\r\n                        {new Date(item.timeToComplete).toLocaleDateString()}\r\n                      </td>\r\n                      <td className=\"px-4 py-4 text-sm\">\r\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getProgressColor(item.progress)}`}>\r\n                          {item.progress}\r\n                        </span>\r\n                      </td>\r\n                      <td className=\"px-4 py-4 text-sm\">\r\n                        <div className=\"flex space-x-2\">\r\n                          <Button\r\n                            variant=\"ghost\"\r\n                            size=\"sm\"\r\n                            onClick={() => openEditModal(item)}\r\n                            className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\r\n                            title=\"Edit\"\r\n                          >\r\n                            <Edit2 className=\"w-4 h-4\" />\r\n                          </Button>\r\n                          <Button\r\n                            variant=\"ghost\"\r\n                            size=\"sm\"\r\n                            onClick={() => handleDelete(item.id)}\r\n                            className=\"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\r\n                            title=\"Delete\"\r\n                          >\r\n                            <Trash2 className=\"w-4 h-4\" />\r\n                          </Button>\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          ) : (\r\n            <div className=\"px-6 py-8 text-center\">\r\n              <p className=\"text-gray-500 dark:text-gray-400\">\r\n                No personal development goals added yet. Click \"Add Development Item\" to create your first goal.\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer Text */}\r\n        <p className=\"my-8 leading-relaxed max-w-[980px] mx-auto text-gray-900 dark:text-gray-200\">\r\n          Your personal development plan is a living document that should be reviewed and updated regularly.\r\n          Track your progress, celebrate achievements, and adjust goals as needed to ensure continuous growth\r\n          and development in both personal and professional areas.\r\n        </p>\r\n\r\n        {/* Continue Button */}\r\n        <NavigationButton\r\n          text=\"CONTINUE\"\r\n          href=\"/4_5_conclusion\"\r\n        />\r\n\r\n        {/* Footer */}\r\n        <Footer />\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAUA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AA9BA;;;;;;;;;;;;;;AAyCA,MAAM,iBAAgD,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE,EAAE;;IAC3F,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,OAAO,EAAE;gBACrB,sBAAsB;gBACtB,UAAU,OAAO,CAAC,SAAS,GAAG,WAAW;gBACzC,iBAAiB,WAAW;gBAC5B;YACF;QACF;mCAAG,EAAE,GAAG,yBAAyB;IAEjC,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,OAAO,IAAI,YAAY,eAAe;gBAClD,iCAAiC;gBACjC,UAAU,OAAO,CAAC,SAAS,GAAG,WAAW;gBACzC,iBAAiB,WAAW;gBAC5B;YACF;QACF;mCAAG;QAAC;QAAS;KAAc;IAE3B,wCAAwC;IACxC,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,MAAM,QAAQ,UAAU,OAAO,CAAC,gBAAgB,CAAC;QACjD,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,KAAK,OAAO,KAAK,MAAM;gBACxB,KAAqB,KAAK,CAAC,aAAa,GAAG;gBAC3C,KAAqB,KAAK,CAAC,UAAU,GAAG;gBACxC,KAAqB,KAAK,CAAC,WAAW,GAAG;YAC5C,OAAO,IAAI,KAAK,OAAO,KAAK,MAAM;gBAC/B,KAAqB,KAAK,CAAC,aAAa,GAAG;gBAC3C,KAAqB,KAAK,CAAC,UAAU,GAAG;gBACxC,KAAqB,KAAK,CAAC,WAAW,GAAG;YAC5C;QACF;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,OAAO,UAAU,OAAO,CAAC,SAAS;YACxC,iBAAiB;YACjB,SAAS;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC,SAAiB;QACvC,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,KAAK;YAEvB,6BAA6B;YAC7B,IAAI,YAAY,yBAAyB,YAAY,qBAAqB;gBACxE,MAAM,YAAY,OAAO,YAAY;gBACrC,IAAI,aAAa,UAAU,UAAU,GAAG,GAAG;oBACzC,SAAS,WAAW,CAAC,SAAS,OAAO;oBAErC,4BAA4B;oBAC5B,WAAW;wBACT,IAAI,UAAU,OAAO,EAAE;4BACrB,MAAM,QAAQ,UAAU,OAAO,CAAC,gBAAgB,CAAC;4BACjD,MAAM,OAAO,CAAC,CAAA;gCACZ,IAAI,KAAK,OAAO,KAAK,MAAM;oCACxB,KAAqB,KAAK,CAAC,aAAa,GAAG;oCAC3C,KAAqB,KAAK,CAAC,UAAU,GAAG;oCACxC,KAAqB,KAAK,CAAC,WAAW,GAAG;gCAC5C,OAAO,IAAI,KAAK,OAAO,KAAK,MAAM;oCAC/B,KAAqB,KAAK,CAAC,aAAa,GAAG;oCAC3C,KAAqB,KAAK,CAAC,UAAU,GAAG;oCACxC,KAAqB,KAAK,CAAC,WAAW,GAAG;gCAC5C;4BACF;4BACA;wBACF;oBACF,GAAG;gBACL;YACF,OAAO;gBACL,SAAS,WAAW,CAAC,SAAS,OAAO;YACvC;YAEA;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI;YACF,IAAI,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,QAAQ,CAAC,SAAS,aAAa,GAAG;gBAC3E,OAAO,SAAS,iBAAiB,CAAC;YACpC;YACA,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,qDAAqD;IACrD,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,MAAM,YAAY,OAAO,YAAY;YACrC,IAAI,aAAa,UAAU,UAAU,GAAG,GAAG;gBACzC,MAAM,QAAQ,UAAU,UAAU,CAAC;gBACnC,MAAM,WAAW,MAAM,cAAc,CAAC,aAAa,EAAE,QAAQ;gBAE7D,IAAI,UAAU;oBACZ,EAAE,cAAc;oBAEhB,sCAAsC;oBACtC,IAAI,SAAS,WAAW,EAAE,WAAW,IAAI;wBACvC,8BAA8B;wBAC9B,SAAS,WAAW,CAAC;oBACvB,OAAO;wBACL,uBAAuB;wBACvB,SAAS,WAAW,CAAC,cAAc,OAAO;wBAC1C,SAAS,WAAW,CAAC,cAAc,OAAO;oBAC5C;oBACA;gBACF;YACF;QACF;IACF;IAEA,qBACE,6LAAC;;;;;oBAgGyB;oBAMA;oBAMA;;;mBA5GX;;0BAEb,6LAAC;;;;;4BA8FuB;4BAMA;4BAMA;;;2BA1GT;;kCACb,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,aAAa,CAAC,IAAM,EAAE,cAAc;wBACpC,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,YAAY,EAAE,gBAAgB,UAAU,iCAAiC,IAAI;kCAEzF,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,aAAa,CAAC,IAAM,EAAE,cAAc;wBACpC,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,YAAY,EAAE,gBAAgB,YAAY,iCAAiC,IAAI;kCAE3F,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC;;;;;oCAyEqB;oCAMA;oCAMA;;;mCArFP;;;;;;kCACf,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,aAAa,CAAC,IAAM,EAAE,cAAc;wBACpC,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,YAAY,EAAE,gBAAgB,yBAAyB,iCAAiC,IAAI;kCAExG,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,aAAa,CAAC,IAAM,EAAE,cAAc;wBACpC,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,YAAY,EAAE,gBAAgB,uBAAuB,iCAAiC,IAAI;kCAEtG,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEzB,6LAAC;;;;;oCAoDqB;oCAMA;oCAMA;;;mCAhEP;;;;;;kCACf,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,aAAa,CAAC,IAAM,EAAE,cAAc;wBACpC,SAAS,IAAM,eAAe;wBAC9B,WAAU;kCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,aAAa,CAAC,IAAM,EAAE,cAAc;wBACpC,SAAS,IAAM,eAAe;wBAC9B,WAAU;kCAEV,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;;;;;4BA4BuB;4BAMA;4BAMA;;;2BAxCT;;kCACb,6LAAC;wBACC,KAAK;wBACL,eAAe;wBACf,SAAS;wBACT,WAAW;wBACX,SAAS,IAAM,aAAa;wBAC5B,QAAQ,IAAM,aAAa;wBAE3B,OAAO;4BACL,WAAW;4BACX,cAAc;4BACd,WAAW;4BACX,WAAW;4BACX,aAAa;wBACf;wBACA,gCAAgC;wBAChC,kBAAgB;;;;;oCAWI;oCAMA;oCAMA;;;mCAhCV;;;;;;oBAWX,CAAC,aAAa,CAAC,CAAC,iBAAiB,cAAc,IAAI,OAAO,MAAM,kBAAkB,MAAM,mBACvF,6LAAC;;;;;oCAQmB;oCAMA;oCAMA;;;mCApBL;kCACZ;;;;;;;;;;;;;;;oBAOiB;oBAMA;oBAMA;;gDAZA,0KAMA,6KAMA;;;;;;;;AAO9B;GAjPM;KAAA;AA+PS,SAAS;;IACtB,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACvE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,qCAAqC;IACpF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExD,2CAA2C;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,UAAU;IACZ;IAEA,yCAAyC;IACzC,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,2DAA2D;YACrE,IAAI;gBACF,aAAa;gBACb,YAAY;gBAEZ,6BAA6B;gBAC7B,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,IAAI,EAAE;oBAChD,QAAQ;gBACV;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,MAAM,EAAE;gBAC/D;gBAEA,0BAA0B;gBAC1B,oBAAoB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC9D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,YAAY;YACd,SAAU;gBACR,aAAa;YACf;QACF;IACF;IAEA,8DAA8D;IAC9D,MAAM,aAAa;QACjB,MAAM,eAAe,KAAK,KAAK,CAAC,SAAS,KAAK,MAAM,KAAK;QACzD,OAAO,CAAC,IAAI,EAAE,cAAc;IAC9B;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,YAAY;YACV,MAAM;YACN,OAAO;YACP,gBAAgB;YAChB,iBAAiB;YACjB,gBAAgB;YAChB,UAAU;QACZ;QACA,aAAa,CAAA,OAAQ,OAAO,IAAI,yBAAyB;QACzD,eAAe;IACjB;IAEA,MAAM,gBAAgB,CAAC;QACrB,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,mDAAmD;QACnD,MAAM,SAAS,KAAK,GAAG,IAAI,+BAA+B;QAC1D,aAAa;QAEb,8CAA8C;QAC9C,MAAM,cAAc;YAClB,MAAM,KAAK,IAAI,IAAI;YACnB,OAAO,KAAK,KAAK,IAAI;YACrB,gBAAgB,KAAK,cAAc,IAAI;YACvC,iBAAiB,KAAK,eAAe;YACrC,gBAAgB,KAAK,cAAc;YACnC,UAAU,KAAK,QAAQ;QACzB;QAEA,gBAAgB;QAChB,YAAY;QAEZ,mBAAmB;QACnB,eAAe;QAEf,6BAA6B;QAC7B,QAAQ,GAAG,CAAC,+BAA+B;QAE3C,iEAAiE;QACjE,WAAW;YACT,eAAe;QACjB,GAAG;IACL;IAEA,sCAAsC;IACtC,MAAM,eAAe;QACnB,IAAI;YACF,aAAa;YACb,YAAY;YAEZ,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,MAAM,EAAE;YAChE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,sCAAsC;YACtC,MAAM,cAAiC,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;oBACpE,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,gBAAgB,KAAK,eAAe,IAAI;oBACxC,iBAAiB,KAAK,cAAc;oBACpC,gBAAgB,KAAK,gBAAgB;oBACrC,UAAU,KAAK,QAAQ;oBACvB,aAAa,IAAI,KAAK,KAAK,YAAY,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtE,CAAC;YAED,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,YAAY;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iDAAE;YACR;QACF;gDAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,YAAY;QAEZ,IAAI;YACF,IAAI,aAAa;gBACf,yCAAyC;gBACzC,MAAM,UAAU;oBACd,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,iBAAiB,SAAS,cAAc;oBACxC,gBAAgB,SAAS,eAAe;oBACxC,kBAAkB,SAAS,cAAc;oBACzC,UAAU,SAAS,QAAQ;gBAC7B;gBAEA,6BAA6B;gBAC7B,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,YAAY,EAAE,EAAE,EAAE;oBAC5D,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE,IAAI,YAAY,EAAE;wBAAE,GAAG,OAAO;oBAAC;gBACxD;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,MAAM,EAAE;gBAC/D;gBAEA,MAAM,cAAc,MAAM,SAAS,IAAI;gBAEvC,wBAAwB;gBACxB,MAAM,cAA+B;oBACnC,IAAI,YAAY,EAAE;oBAClB,MAAM,YAAY,IAAI;oBACtB,OAAO,YAAY,KAAK;oBACxB,gBAAgB,YAAY,eAAe,IAAI;oBAC/C,iBAAiB,YAAY,cAAc;oBAC3C,gBAAgB,YAAY,gBAAgB;oBAC5C,UAAU,YAAY,QAAQ;oBAC9B,aAAa,IAAI,KAAK,YAAY,YAAY,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC7E;gBAEA,QAAQ,GAAG,CAAC,6BAA6B;gBAEzC,oBAAoB,CAAA,OAClB,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,GAAG,cAAc;YAEhE,OAAO;gBACL,yCAAyC;gBACzC,MAAM,UAAU;oBACd,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,iBAAiB,SAAS,cAAc;oBACxC,gBAAgB,SAAS,eAAe;oBACxC,kBAAkB,SAAS,cAAc;oBACzC,UAAU,SAAS,QAAQ;gBAC7B;gBAEA,6BAA6B;gBAC7B,MAAM,WAAW,MAAM,MAAM,YAAY;oBACvC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,MAAM,EAAE;gBAC/D;gBAEA,MAAM,UAAU,MAAM,SAAS,IAAI;gBAEnC,yCAAyC;gBACzC,MAAM,UAA2B;oBAC/B,IAAI,QAAQ,EAAE;oBACd,MAAM,QAAQ,IAAI;oBAClB,OAAO,QAAQ,KAAK;oBACpB,gBAAgB,QAAQ,eAAe,IAAI;oBAC3C,iBAAiB,QAAQ,cAAc;oBACvC,gBAAgB,QAAQ,gBAAgB;oBACxC,UAAU,QAAQ,QAAQ;oBAC1B,aAAa,IAAI,KAAK,QAAQ,YAAY,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACzE;gBAEA,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,qBAAqB;gBACrB,oBAAoB,CAAA,OAAQ;2BAAI;wBAAM;qBAAQ;YAChD;YAEA,6BAA6B;YAC7B,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,gBAAgB;gBAChB,iBAAiB;gBACjB,gBAAgB;gBAChB,UAAU;YACZ;YACA,eAAe;YACf,eAAe;YACf,aAAa,CAAA,OAAQ,OAAO,IAAI,mBAAmB;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,YAAY;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAA2F;;;;;;;;;;;8BAM3G,6LAAC,8HAAA,CAAA,UAAY;oBAAC,OAAM;8BAClB,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA2D;;;;;;sDAC3E,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;8CAIrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA2D;;;;;;sDAC3E,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;8CAIrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA2D;;;;;;sDAC3E,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;8CAIrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA2D;;;;;;sDAC3E,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS3D,6LAAC,iIAAA,CAAA,YAAS;oBAAC,MAAK;oBAAS,WAAW;oBAAC,WAAU;8BAC7C,cAAA,6LAAC,iIAAA,CAAA,gBAAa;wBAAC,OAAM;;0CACnB,6LAAC,iIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CAAiH;;;;;;0CAC7I,6LAAC,iIAAA,CAAA,mBAAgB;0CACf,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDAAM,WAAU;8DACf,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;0EAChD,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;0EAChD,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;0EAChD,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;0EAChD,6LAAC;gEAAG,WAAU;0EAAkC;;;;;;;;;;;;;;;;;8DAGpD,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA+C;;;;;;0FAC5D,6LAAC;gFAAG,WAAU;;kGACZ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;;;;;;;;;;;;;;;;;;8EAIV,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA+C;;;;;;0FAC5D,6LAAC;gFAAG,WAAU;;kGACZ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;;;;;;;;;;;;;;;;;;8EAIV,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA2B;;;;;;0FACxC,6LAAC;gFAAE,WAAU;0FAA2C;;;;;;0FACxD,6LAAC;gFAAE,WAAU;0FAA2C;;;;;;0FACxD,6LAAC;gFAAE,WAAU;0FAA2C;;;;;;0FACxD,6LAAC;gFAAE,WAAU;0FAA2C;;;;;;0FACxD,6LAAC;gFAAE,WAAU;0FAA2C;;;;;;0FACxD,6LAAC;gFAAE,WAAU;0FAA2C;;;;;;;;;;;;;;;;;8EAG5D,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA+C;;;;;;0FAC5D,6LAAC;gFAAE,WAAU;0FAA2C;;;;;;0FACxD,6LAAC;gFAAE,WAAU;0FAA2C;;;;;;;;;;;;;;;;;8EAG5D,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA+C;;;;;;0FAC5D,6LAAC;gFAAE,WAAU;0FAA2C;;;;;;;;;;;;;;;;;;;;;;;sEAI9D,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA4B;;;;;;0FACzC,6LAAC;gFAAG,WAAU;;kGACZ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;;;;;;;;;;;;;;;;;;8EAIV,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA4B;;;;;;0FACzC,6LAAC;gFAAE,WAAU;0FAA2C;;;;;;;;;;;;;;;;;8EAG5D,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAE,WAAU;kFAA2C;;;;;;;;;;;8EAE1D,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAE,WAAU;kFAA2C;;;;;;;;;;;8EAE1D,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAYvD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;wBAAC,MAAM;wBAAa,cAAc;;0CACtC,6LAAC,6HAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCAAC,WAAU;oCAA4C,SAAS;;sDACrE,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,6LAAC,6HAAA,CAAA,eAAY;gCACvB,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,UAAU;oCACV,UAAU;gCACZ;;;;;;kDA4BY,6LAAC,6HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC,6HAAA,CAAA,aAAU;gDAAC,WAAU;0DACnB,cAAc,mCAAmC;;;;;;0DAEpD,6LAAC,6HAAA,CAAA,mBAAgB;gDAAC,WAAU;0DACzB,cACG,uDACA;;;;;;;;;;;;kDAKR,6LAAC;wCAAK,UAAU;kFAAwB;;0DACtC,6LAAC;;;kEACC,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAO,WAAU;kEAAmC;;;;;;kEACnE,6LAAC;kGAAc;kEACb,cAAA,6LAAC;4DAEC,IAAG;4DACH,SAAS,SAAS,IAAI;4DACtB,UAAU,CAAC,OAAS,kBAAkB,QAAQ;4DAC9C,aAAY;2DAJP,CAAC,KAAK,EAAE,WAAW;;;;;;;;;;;;;;;;0DAS9B,6LAAC;;;kEACC,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAQ,WAAU;kEAAmC;;;;;;kEACpE,6LAAC;kGAAc;kEACb,cAAA,6LAAC;4DAEC,IAAG;4DACH,SAAS,SAAS,KAAK;4DACvB,UAAU,CAAC,OAAS,kBAAkB,SAAS;4DAC/C,aAAY;2DAJP,CAAC,MAAM,EAAE,WAAW;;;;;;;;;;;;;;;;0DAS/B,6LAAC;;;kEACC,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAiB,WAAU;kEAAmC;;;;;;kEAC7E,6LAAC;kGAAc;kEACb,cAAA,6LAAC;4DAEC,IAAG;4DACH,SAAS,SAAS,cAAc;4DAChC,UAAU,CAAC,OAAS,kBAAkB,kBAAkB;4DACxD,aAAY;2DAJP,CAAC,gBAAgB,EAAE,WAAW;;;;;;;;;;;;;;;;0DASzC,6LAAC;0FAAc;;kEACb,6LAAC;;;0EACC,6LAAC,6HAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAkB,WAAU;0EAAmC;;;;;;0EAC9E,6LAAC,8HAAA,CAAA,SAAM;gEAAC,eAAe,CAAC,QAAU,kBAAkB,mBAAmB;gEAAQ,OAAO,SAAS,eAAe;gEAAE,QAAQ;;kFACtH,6LAAC,8HAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;4EAAC,aAAY;;;;;;;;;;;kFAE3B,6LAAE,8HAAA,CAAA,gBAAa;wEAAC,WAAU;;0FACxB,6LAAC,8HAAA,CAAA,aAAU;gFAAC,OAAM;0FAAgB;;;;;;0FAClC,6LAAC,8HAAA,CAAA,aAAU;gFAAC,OAAM;0FAAkB;;;;;;0FACpC,6LAAC,8HAAA,CAAA,aAAU;gFAAC,OAAM;0FAAe;;;;;;;;;;;;;;;;;;;;;;;;kEAKvC,6LAAC;;;0EACC,6LAAC,6HAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAiB,WAAU;0EAAmC;;;;;;0EAC7E,6LAAC,6HAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,cAAc;gEAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;gEACnE,QAAQ;gEACR,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;;;kEACC,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAmC;;;;;;kEACvE,6LAAC,8HAAA,CAAA,SAAM;wDAAC,eAAe,CAAC,QAAU,kBAAkB,YAAY;wDAAQ,OAAO,SAAS,QAAQ;;0EAC9F,6LAAC,8HAAA,CAAA,gBAAa;gEAAC,WAAU;0EACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;;;;;;;;;;0EAEd,6LAAC,8HAAA,CAAA,gBAAa;gEAAC,WAAU;;kFACvB,6LAAC,8HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAc;;;;;;kFAChC,6LAAC,8HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAc;;;;;;kFAChC,6LAAC,8HAAA,CAAA,aAAU;wEAAC,OAAM;kFAAW;;;;;;;;;;;;;;;;;;;;;;;;0DAKnC,6LAAC,6HAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC,8HAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,SAAQ;wDAAU,SAAS,IAAM,eAAe;wDAAQ,WAAU;kEAAY;;;;;;kEAGpG,6LAAC,8HAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,WAAU;kEAC7B,cAAc,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS3C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;;oCAAyD;oCACrC,iBAAiB,MAAM;oCAAC;;;;;;;;;;;;wBAI3D,0BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;mCAEf,iBAAiB,MAAM,GAAG,kBAC5B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;8DAGlH,6LAAC;oDAAG,WAAU;8DAAoG;;;;;;;;;;;;;;;;;kDAKtH,6LAAC;wCAAM,WAAU;kDACd,iBAAiB,GAAG,CAAC,CAAC,qBACrB,6LAAC;gDAAiB,WAAU;;kEAC1B,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,yBAAyB;oEAAE,QAAQ,KAAK,IAAI;gEAAC;;;;;;;;;;;;;;;;kEAInD,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,yBAAyB;oEAAE,QAAQ,KAAK,KAAK;gEAAC;;;;;;;;;;;;;;;;kEAIpD,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,yBAAyB;oEAAE,QAAQ,KAAK,cAAc;gEAAC;;;;;;;;;;;;;;;;kEAI7D,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAW,CAAC,YAAY,EAAE,iBAAiB,KAAK,eAAe,GAAG;sEACrE,KAAK,eAAe;;;;;;;;;;;kEAGzB,6LAAC;wDAAG,WAAU;kEACX,IAAI,KAAK,KAAK,cAAc,EAAE,kBAAkB;;;;;;kEAEnD,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,KAAK,QAAQ,GAAG;sEAC7F,KAAK,QAAQ;;;;;;;;;;;kEAGlB,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8HAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,cAAc;oEAC7B,WAAU;oEACV,OAAM;8EAEN,cAAA,6LAAC,qMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,6LAAC,8HAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,aAAa,KAAK,EAAE;oEACnC,WAAU;oEACV,OAAM;8EAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAxDjB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;iDAkExB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;8BAQtD,6LAAC;oBAAE,WAAU;8BAA8E;;;;;;8BAO3F,6LAAC,kIAAA,CAAA,UAAgB;oBACf,MAAK;oBACL,MAAK;;;;;;8BAIP,6LAAC,wHAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf;IAxsBwB;;QACqB,gIAAA,CAAA,gBAAa;;;MADlC", "debugId": null}}]}