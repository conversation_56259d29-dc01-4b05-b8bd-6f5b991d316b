(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[889,1365,3270,3746,6127,7169,8032,8508],{1493:(e,t,r)=>{Promise.resolve().then(r.bind(r,3028))},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var i=r(2596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=i.$,o=(e,t)=>r=>{var i;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:l}=t,a=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],i=null==l?void 0:l[e];if(null===t)return null;let s=n(t)||n(i);return o[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,i]=t;return void 0===i||(e[r]=i),e},{});return s(e,a,null==t||null==(i=t.compoundVariants)?void 0:i.reduce((e,t)=>{let{class:r,className:i,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,i]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2269:(e,t,r)=>{"use strict";var i=r(9509);r(8375);var n=r(2115),s=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(n),o=void 0!==i&&i.env&&!0,l=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,r=t.name,i=void 0===r?"stylesheet":r,n=t.optimizeForSpeed,s=void 0===n?o:n;d(l(i),"`name` must be a string"),this._name=i,this._deletedRulePlaceholder="#"+i+"-deleted-rule____{}",d("boolean"==typeof s,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=s,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var a="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=a?a.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){d("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),d(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(d(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(d(l(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var i=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,i))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(i){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var i=this._tags[e];d(i,"old rule at index `"+e+"` not found"),i.textContent=t}return e},r.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];d(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},r.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&d(l(t),"makeStyleTag accepts only strings as second parameter");var i=document.createElement("style");this._nonce&&i.setAttribute("nonce",this._nonce),i.type="text/css",i.setAttribute("data-"+e,""),t&&i.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return r?n.insertBefore(i,r):n.appendChild(i),i},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(e.prototype,t),e}();function d(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var u=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},c={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),i=e+r;return c[i]||(c[i]="jsx-"+u(e+"-"+r)),c[i]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return c[r]||(c[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,i=void 0===r?null:r,n=t.optimizeForSpeed,s=void 0!==n&&n;this._sheet=i||new a({name:"styled-jsx",optimizeForSpeed:s}),this._sheet.inject(),i&&"boolean"==typeof s&&(this._sheet.setOptimizeForSpeed(s),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),i=r.styleId,n=r.rules;if(i in this._instancesCounts){this._instancesCounts[i]+=1;return}var s=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[i]=s,this._instancesCounts[i]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var i=this._fromServer&&this._fromServer[r];i?(i.parentNode.removeChild(i),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],i=e[1];return s.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:i}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,i=e.id;if(r){var n=h(i,r);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return f(n,e)}):[f(n,t)]}}return{styleId:h(i),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=n.createContext(null);m.displayName="StyleSheetContext";var x=s.default.useInsertionEffect||s.default.useLayoutEffect,g="undefined"!=typeof window?new p:void 0;function v(e){var t=g||n.useContext(m);return t&&("undefined"==typeof window?t.add(e):x(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}v.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=v},3028:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var i=r(5155),n=r(9137),s=r.n(n);r(2115);var o=r(6874),l=r.n(o),a=r(7168),d=r(6766),u=r(9010);function c(){return(0,i.jsxs)("div",{className:"jsx-485dec7556801de8 w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1]",children:[(0,i.jsx)(s(),{id:"485dec7556801de8",children:'.card.jsx-485dec7556801de8{-webkit-border-radius:16px;-moz-border-radius:16px;border-radius:16px;padding:24px;margin-bottom:24px;position:relative;-webkit-box-shadow:0 2px 6px rgba(0,0,0,.1);-moz-box-shadow:0 2px 6px rgba(0,0,0,.1);box-shadow:0 2px 6px rgba(0,0,0,.1);background-color:white;position:relative;overflow:hidden;max-width:980px;margin-left:auto;margin-right:auto}.card.jsx-485dec7556801de8::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:-webkit-linear-gradient(315deg,rgba(55,147,247,.7)0%,rgba(55,147,247,.2)10%,rgba(55,147,247,.05)20%,rgba(55,147,247,0)30%);background:-moz-linear-gradient(315deg,rgba(55,147,247,.7)0%,rgba(55,147,247,.2)10%,rgba(55,147,247,.05)20%,rgba(55,147,247,0)30%);background:-o-linear-gradient(315deg,rgba(55,147,247,.7)0%,rgba(55,147,247,.2)10%,rgba(55,147,247,.05)20%,rgba(55,147,247,0)30%);background:linear-gradient(135deg,rgba(55,147,247,.7)0%,rgba(55,147,247,.2)10%,rgba(55,147,247,.05)20%,rgba(55,147,247,0)30%);-webkit-border-radius:16px;-moz-border-radius:16px;border-radius:16px;z-index:0;pointer-events:none}.card-content.jsx-485dec7556801de8{position:relative;z-index:1}.info-icon.jsx-485dec7556801de8{position:absolute;top:20px;right:20px;width:24px;height:24px;background-color:#3793F7;color:white;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;font-style:italic;font-weight:bold;z-index:2}.bullet-list.jsx-485dec7556801de8{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px}.bullet-item.jsx-485dec7556801de8{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;line-height:1.5}.bullet.jsx-485dec7556801de8{color:#3793F7;font-size:18px;margin-right:10px;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}'}),(0,i.jsxs)("div",{className:"jsx-485dec7556801de8 w-full max-w-[1100px] mx-auto px-5 pt-8 relative",children:[(0,i.jsxs)("div",{className:"jsx-485dec7556801de8 flex flex-col md:flex-row relative mb-10 mt-5",children:[(0,i.jsx)("div",{className:"jsx-485dec7556801de8 flex-none w-full md:w-[400px] lg:w-[400px] relative",children:(0,i.jsx)("div",{className:"jsx-485dec7556801de8 relative w-full h-[320px]",children:(0,i.jsx)(d.default,{src:"/2.1TM.svg",alt:"Making Impact Illustration",fill:!0,style:{objectFit:"contain"},priority:!0})})}),(0,i.jsxs)("div",{className:"jsx-485dec7556801de8 flex-1 md:pl-10 mt-4 md:mt-0",children:[(0,i.jsx)("h1",{className:"jsx-485dec7556801de8 text-[3rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[3rem]",children:"2.1 How You can make an Impact"}),(0,i.jsx)("p",{className:"jsx-485dec7556801de8 mb-6 leading-relaxed",children:"This segment examines how your strengths, limitations, and stressors influence your academic and professional performance. This segment also highlights key competencies for workplace success and identifies the work environment where you are most likely to thrive."})]})]}),(0,i.jsx)("div",{className:"jsx-485dec7556801de8 flex justify-center w-full my-8",children:(0,i.jsx)(l(),{href:"/2_2_academic_impact",children:(0,i.jsxs)(a.$,{variant:"outline",className:"rounded-full border-2 border-gray-800 text-gray-900 hover:bg-gray-100 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer",style:{boxShadow:"0 2px 8px rgba(55,147,247,0.10)"},children:["Continue",(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",className:"jsx-485dec7556801de8 ml-2 group-hover:translate-x-1 transition-transform duration-150",children:[(0,i.jsx)("path",{d:"M5 12h14",className:"jsx-485dec7556801de8"}),(0,i.jsx)("path",{d:"M12 5l7 7-7 7",className:"jsx-485dec7556801de8"})]})]})})}),(0,i.jsx)(u.A,{})]})]})}},3999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var i=r(2596),n=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,i.$)(t))}},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>o,t:()=>s});var i=r(2115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,i=e.map(e=>{let i=n(e,t);return r||"function"!=typeof i||(r=!0),i});if(r)return()=>{for(let t=0;t<i.length;t++){let r=i[t];"function"==typeof r?r():n(e[t],null)}}}}function o(...e){return i.useCallback(s(...e),e)}},7168:(e,t,r)=>{"use strict";r.d(t,{$:()=>a});var i=r(5155);r(2115);var n=r(9708),s=r(2085),o=r(3999);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function a(e){let{className:t,variant:r,size:s,asChild:a=!1,...d}=e,u=a?n.DX:"button";return(0,i.jsx)(u,{"data-slot":"button",className:(0,o.cn)(l({variant:r,size:s,className:t})),...d})}},8375:()=>{},9010:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(5155);r(2115);var n=r(3999);function s(){return(0,i.jsxs)("div",{className:(0,n.cn)("w-full relative overflow-hidden text-black","py-4 px-8","text-xs leading-relaxed","rounded-t-[40px]","mt-10"),children:[(0,i.jsx)("div",{style:{position:"absolute",inset:0,zIndex:0,pointerEvents:"none",background:"linear-gradient(135deg, rgba(75,159,239,0.9) 0%, rgba(75,159,239,0.3) 100%)"}}),(0,i.jsxs)("div",{className:"max-w-[1200px] mx-auto flex flex-col md:flex-row items-start md:items-center gap-2 md:gap-0 relative z-10",children:[(0,i.jsx)("div",{className:"flex-1",children:"Disclaimer – The report's findings are based on the specific test administered and the responses given. The report is designed to help people identify their natural abilities, strengths, limitations and career interests, and should be used only as a tool for self-discovery"}),(0,i.jsx)("div",{className:"whitespace-nowrap md:ml-4",children:"|   Copyright – TalentMetrix 2025"})]})]})}},9137:(e,t,r)=>{"use strict";e.exports=r(2269).style},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o});var i=r(2115),n=r(6101),s=r(5155),o=function(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...s}=e;if(i.isValidElement(r)){var o;let e,l,a=(o=r,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),d=function(e,t){let r={...t};for(let i in t){let n=e[i],s=t[i];/^on[A-Z]/.test(i)?n&&s?r[i]=(...e)=>{let t=s(...e);return n(...e),t}:n&&(r[i]=n):"style"===i?r[i]={...n,...s}:"className"===i&&(r[i]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==i.Fragment&&(d.ref=t?(0,n.t)(t,a):a),i.cloneElement(r,d)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:n,...o}=e,l=i.Children.toArray(n),d=l.find(a);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...o,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,n):null})}return(0,s.jsx)(t,{...o,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}("Slot"),l=Symbol("radix.slottable");function a(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,6766,8441,1684,7358],()=>t(1493)),_N_E=e.O()}]);