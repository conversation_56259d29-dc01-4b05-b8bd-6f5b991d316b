(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3638],{51:(e,t,n)=>{"use strict";n.d(t,{AssessmentProvider:()=>d,U:()=>l});var s=n(5155),r=n(2115),i=n(2108),o=n(5695);let a=(0,r.createContext)({assessmentData:null,loading:!1,error:null,fetchAssessment:async()=>{},isInitialized:!1}),l=()=>(0,r.useContext)(a),d=e=>{let{children:t}=e,{data:n,status:l}=(0,i.useSession)(),d=(0,o.useRouter)(),[c,u]=(0,r.useState)(null),[g,p]=(0,r.useState)(!1),[m,h]=(0,r.useState)(null),[f,x]=(0,r.useState)(!1),[v,y]=(0,r.useState)(null),[b,j]=(0,r.useState)(!1),[k,w]=(0,r.useState)(0),[S,_]=(0,r.useState)(!1),N=async()=>{if(!b){console.log("Authentication error detected. Logging out and redirecting to login page..."),j(!0);try{await (0,i.signOut)({redirect:!1}),u(null),y(null),d.push("/login")}catch(e){console.error("Error during logout:",e),d.push("/login")}}},A=async e=>{if(S)return void console.log("Fetching disabled due to previous auth errors");let t=k+1;if(w(t),t>3){console.log("Too many fetch attempts, disabling further fetches"),_(!0),h("Authentication error. Please try logging in again.");return}if(!e){h("No email provided to fetch assessment."),x(!0),p(!1);return}if(b)return void console.log("Already redirecting to login, skipping fetch");try{p(!0),h(null),console.log("Fetching assessment data for ".concat(e,"... (Attempt ").concat(t,"/3)"));let n=await fetch("/api/assessment?email=".concat(encodeURIComponent(e)));if(401===n.status){console.log("Authentication error (401). Disabling further fetches."),_(!0),N();return}if(!n.ok){let e=await n.json().catch(()=>null),t=(null==e?void 0:e.error)||"API error: ".concat(n.status);throw Error(t)}w(0);let s=await n.json();console.log("Assessment data fetched successfully"),u(s),y(e)}catch(t){let e=t instanceof Error?t.message:"An unknown error occurred";h(e),console.error("Error fetching assessment data:",e),t instanceof Error&&t.message.includes("NetworkError")&&(console.log("Network error. Disabling further fetches."),_(!0))}finally{p(!1),x(!0)}};return(0,r.useEffect)(()=>{var e,t;return(console.log("AssessmentContext useEffect",{status:l,session:n?{...n,user:{...null==n?void 0:n.user,access_token:"[REDACTED]"}}:null,fetchedForEmail:v,isRedirecting:b,fetchingDisabled:S,fetchAttempts:k}),S)?void console.log("Skipping effect because fetching is disabled"):b?void console.log("Skipping effect because we are redirecting"):void("authenticated"===l&&(null==n||null==(e=n.user)?void 0:e.email)?n.user.email!==v&&!g&&k<3&&(console.log("Session authenticated, fetching assessment for:",n.user.email),A(n.user.email)):"unauthenticated"===l?(u(null),y(null),x(!0),console.log("User not authenticated, clearing assessment data.")):"loading"===l&&!f&&p(!0),"loading"!==l&&g&&!f&&!(null==n||null==(t=n.user)?void 0:t.email)&&p(!1))},[n,l,v,b,g,S,k]),(0,s.jsx)(a.Provider,{value:{assessmentData:c,loading:g,error:m,fetchAssessment:A,isInitialized:f},children:t})}},3999:(e,t,n)=>{"use strict";n.d(t,{cn:()=>i});var s=n(2596),r=n(9688);function i(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.QP)((0,s.$)(t))}},6270:(e,t,n)=>{Promise.resolve().then(n.bind(n,8439))},7168:(e,t,n)=>{"use strict";n.d(t,{$:()=>l});var s=n(5155);n(2115);var r=n(9708),i=n(2085),o=n(3999);let a=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:n,size:i,asChild:l=!1,...d}=e,c=l?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,o.cn)(a({variant:n,size:i,className:t})),...d})}},8439:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>g});var s=n(5155),r=n(9137),i=n.n(r),o=n(2115),a=n(6766),l=n(51),d=n(9345),c=n(9010);let u=e=>{let{cardId:t,type:n,icon:r,title:i,description:l,expandedContent:d,color:c,bgGradient:u}=e,[g,p]=(0,o.useState)(!1),m={position:"relative",borderRadius:"16px",padding:"24px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",backgroundColor:"white",overflow:"hidden",display:"flex",flexDirection:"column",minHeight:g?"fit-content":"320px",height:g?"fit-content":"320px",transition:"all 0.3s ease",background:"linear-gradient(135deg, ".concat(u.from," 0%, ").concat(u.via," 20%, transparent 40%), white")},h=e=>({__html:e.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"")});return(0,s.jsx)("div",{style:m,"data-card-id":t,"data-expanded":g,children:(0,s.jsxs)("div",{style:{position:"relative",zIndex:10,display:"flex",flexDirection:"column",flex:1},children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px",gap:"16px"},children:[(0,s.jsx)("div",{style:{width:"50px",height:"50px",position:"relative",flexShrink:0},children:(0,s.jsx)(a.default,{src:r,alt:"".concat(i," Icon"),fill:!0,style:{objectFit:"contain"}})}),(0,s.jsx)("h2",{style:{fontSize:"24px",fontWeight:"600",color:"#374151",margin:0},children:i})]}),(0,s.jsx)("p",{style:{fontSize:"16px",lineHeight:"1.6",color:"#4B5563",marginBottom:"20px"},children:l}),(0,s.jsx)("button",{style:{marginTop:"auto",display:"inline-flex",alignItems:"center",justifyContent:"center",padding:"8px 16px",backgroundColor:"transparent",border:"2px solid ".concat(c),color:c,borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer",transition:"opacity 0.2s ease",height:"36px",minWidth:"120px"},onClick:e=>{e.preventDefault(),e.stopPropagation(),p(e=>!e)},type:"button","aria-expanded":g,"aria-label":"".concat(g?"Hide":"Show"," more ").concat(i.toLowerCase()," details"),onMouseEnter:e=>e.currentTarget.style.opacity="0.8",onMouseLeave:e=>e.currentTarget.style.opacity="1",children:g?"Show Less":"Show More"}),(0,s.jsx)("div",{style:{overflow:"hidden",transition:"all 0.3s ease-in-out",maxHeight:g?"2000px":"0px",opacity:+!!g,marginTop:g?"20px":"0px",paddingTop:g?"8px":"0px"},children:(0,s.jsx)("ul",{style:{display:"flex",flexDirection:"column",gap:"12px",listStyle:"none",padding:0,margin:0},children:d.map((e,r)=>(0,s.jsxs)("li",{style:{display:"flex",alignItems:"flex-start",lineHeight:"1.6"},children:[(0,s.jsx)("span",{style:{color:c,fontSize:"18px",marginRight:"12px",flexShrink:0},children:"•"}),(0,s.jsx)("span",{dangerouslySetInnerHTML:h(e)})]},"".concat(t,"-").concat(n,"-").concat(r)))})})]})})};function g(){var e,t,n,r,o,a,g,p,m,h,f,x,v,y,b,j,k,w,S,_,N,A,C,z,D,T,I,E;let{assessmentData:W,loading:O,error:M}=(0,l.U)();if(O)return(0,s.jsx)("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"50vh"},children:(0,s.jsxs)("div",{style:{textAlign:"center"},children:[(0,s.jsx)("p",{style:{fontSize:"1.25rem",color:"#3793F7",marginBottom:"8px"},children:"Loading your SWOD analysis..."}),(0,s.jsx)("div",{style:{width:"48px",height:"48px",border:"4px solid #f3f4f6",borderTop:"4px solid #3793F7",borderRadius:"50%",animation:"spin 1s linear infinite",margin:"0 auto"}})]})});let B=(null==W||null==(n=W.assessment)||null==(t=n.section_i)||null==(e=t.strengths)?void 0:e.strengths)||[],F=B.length>0?B[0].name:"No Data Available",L=B.length>0?B[0].description:"No strengths data could be fetched from the assessment",R=(null==W||null==(a=W.assessment)||null==(o=a.section_i)||null==(r=o.study_manifestations)?void 0:r.enablers)||[],H=R.length>0?R[0]:"No opportunities data could be fetched from the assessment",P=(null==W||null==(m=W.assessment)||null==(p=m.section_i)||null==(g=p.strengths)?void 0:g.critical_actions)||[],U=(null==W||null==(x=W.assessment)||null==(f=x.section_i)||null==(h=f.stressors)?void 0:h.length)>0&&(null==W||null==(b=W.assessment)||null==(y=b.section_i)||null==(v=y.stressors[0])?void 0:v.mitigation_steps)||[],G=(null==W||null==(w=W.assessment)||null==(k=w.section_ii)||null==(j=k.impact_strategies)?void 0:j.academic)||[],$=P.length>0?P[0]:U.length>0?U[0]:G.length>0?G[0]:"No workarounds data could be fetched from the assessment",K=(null==W||null==(N=W.assessment)||null==(_=N.section_i)||null==(S=_.study_manifestations)?void 0:S.derailers)||[],Q=(null==W||null==(z=W.assessment)||null==(C=z.section_i)||null==(A=C.limitations)?void 0:A.action_steps)||[],V=(null==W||null==(E=W.assessment)||null==(I=E.section_ii)||null==(T=I.competency_assessment)||null==(D=T.time_management)?void 0:D.improvements)||[],X=K.length>0?K[0]:Q.length>0?Q[0]:V.length>0?V[0]:"No development areas data could be fetched from the assessment",Y=[...B.slice(1).map(e=>"<strong>".concat(e.name,":</strong> ").concat(e.description))],q=[...P.slice(1),...U.slice(1),...G.slice(1)],J=[...R.slice(1)],Z=[...K.slice(1),...Q.slice(1),...V.slice(1)],ee={width:"100%",marginTop:"-32px",marginLeft:"auto",marginRight:"auto",paddingBottom:"0",position:"relative",backgroundColor:"#f1f1f1",padding:"16px"},et={width:"100%",maxWidth:"1100px",margin:"0 auto",padding:"32px 20px 0"},en={fontSize:"2.8rem",fontWeight:"300",color:"#3793F7",marginBottom:"40px",textAlign:"center"};return M?(0,s.jsx)("div",{style:ee,children:(0,s.jsxs)("div",{style:et,children:[(0,s.jsx)("h1",{style:en,children:"4.2 SWOD Analysis"}),(0,s.jsx)("div",{style:{textAlign:"center",padding:"2rem",backgroundColor:"#fef2f2",borderRadius:"8px",border:"1px solid #fecaca"},children:(0,s.jsx)("p",{style:{color:"#dc2626",fontSize:"1.125rem"},children:"Unable to load your SWOD analysis. Please try refreshing the page."})})]})}):(0,s.jsxs)("div",{style:ee,className:"jsx-ff161281ed666c63",children:[(0,s.jsx)(i(),{id:"ff161281ed666c63",children:"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"}),(0,s.jsxs)("div",{style:et,className:"jsx-ff161281ed666c63",children:[(0,s.jsx)("h1",{style:en,className:"jsx-ff161281ed666c63",children:"4.2 SWOD Analysis"}),(0,s.jsx)("p",{style:{marginBottom:"2.5rem",lineHeight:"1.625",maxWidth:"none"},className:"jsx-ff161281ed666c63",children:"Your SWOD is a summarization of your Strengths, Workarounds, Development Areas and Opportunities, that have been presented in the various segments and sections of your Kaleidoscope."}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(2, 1fr)",gap:"2rem",marginBottom:"2.5rem",alignItems:"flex-start"},className:"jsx-ff161281ed666c63",children:[(0,s.jsx)(u,{cardId:"strengths-001",type:"strengths",icon:"/StrengthTM.svg",title:"Strengths",description:"".concat(F,": ").concat(L),expandedContent:Y,color:"#6ec850",bgGradient:{from:"rgba(110,200,80,0.7)",via:"rgba(110,200,80,0.2)"}}),(0,s.jsx)(u,{cardId:"workarounds-002",type:"workarounds",icon:"/WorkaroundsTM.svg",title:"Workarounds",description:$,expandedContent:q,color:"#ff9933",bgGradient:{from:"rgba(255,153,51,0.7)",via:"rgba(255,153,51,0.2)"}}),(0,s.jsx)(u,{cardId:"opportunities-003",type:"opportunities",icon:"/OpportunitiesTM.svg",title:"Opportunities",description:H,expandedContent:J,color:"#3793F7",bgGradient:{from:"rgba(55,147,247,0.7)",via:"rgba(55,147,247,0.2)"}}),(0,s.jsx)(u,{cardId:"development-004",type:"development",icon:"/Dev AreasTM.svg",title:"Development Areas",description:X,expandedContent:Z,color:"#8a67e2",bgGradient:{from:"rgba(138,103,226,0.7)",via:"rgba(138,103,226,0.2)"}})]}),(0,s.jsx)(d.A,{text:"CONTINUE",href:"/4_4_conclusion"}),(0,s.jsx)(c.A,{})]})]})}},9010:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var s=n(5155);n(2115);var r=n(3999);function i(){return(0,s.jsxs)("div",{className:(0,r.cn)("w-full relative overflow-hidden text-black","py-4 px-8","text-xs leading-relaxed","rounded-t-[40px]","mt-10"),children:[(0,s.jsx)("div",{style:{position:"absolute",inset:0,zIndex:0,pointerEvents:"none",background:"linear-gradient(135deg, rgba(75,159,239,0.9) 0%, rgba(75,159,239,0.3) 100%)"}}),(0,s.jsxs)("div",{className:"max-w-[1200px] mx-auto flex flex-col md:flex-row items-start md:items-center gap-2 md:gap-0 relative z-10",children:[(0,s.jsx)("div",{className:"flex-1",children:"Disclaimer – The report's findings are based on the specific test administered and the responses given. The report is designed to help people identify their natural abilities, strengths, limitations and career interests, and should be used only as a tool for self-discovery"}),(0,s.jsx)("div",{className:"whitespace-nowrap md:ml-4",children:"|   Copyright – TalentMetrix 2025"})]})]})}},9345:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var s=n(5155);n(2115);var r=n(6874),i=n.n(r),o=n(7168);let a=function(e){let{text:t,href:n,onClick:r,className:a="",style:l}=e,d=(0,s.jsxs)(s.Fragment,{children:[t,(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 group-hover:translate-x-1 transition-transform duration-150",children:[(0,s.jsx)("path",{d:"M5 12h14"}),(0,s.jsx)("path",{d:"M12 5l7 7-7 7"})]})]}),c="rounded-full border-2 border-gray-800 dark:border-gray-200 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer "+(a||"");return(0,s.jsx)("div",{className:"flex justify-center w-full my-8",children:n?(0,s.jsx)(o.$,{asChild:!0,variant:"outline",className:c,style:l,onClick:r,children:(0,s.jsx)(i(),{href:n,children:d})}):(0,s.jsx)(o.$,{variant:"outline",className:c,style:l,onClick:r,children:d})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,6454,6766,3270,8441,1684,7358],()=>t(6270)),_N_E=e.O()}]);