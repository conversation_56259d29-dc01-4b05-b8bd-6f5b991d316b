(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8950],{51:(e,t,r)=>{"use strict";r.d(t,{AssessmentProvider:()=>d,U:()=>o});var s=r(5155),n=r(2115),a=r(2108),i=r(5695);let l=(0,n.createContext)({assessmentData:null,loading:!1,error:null,fetchAssessment:async()=>{},isInitialized:!1}),o=()=>(0,n.useContext)(l),d=e=>{let{children:t}=e,{data:r,status:o}=(0,a.useSession)(),d=(0,i.useRouter)(),[c,u]=(0,n.useState)(null),[g,x]=(0,n.useState)(!1),[m,h]=(0,n.useState)(null),[f,p]=(0,n.useState)(!1),[v,b]=(0,n.useState)(null),[y,j]=(0,n.useState)(!1),[k,w]=(0,n.useState)(0),[N,A]=(0,n.useState)(!1),_=async()=>{if(!y){console.log("Authentication error detected. Logging out and redirecting to login page..."),j(!0);try{await (0,a.signOut)({redirect:!1}),u(null),b(null),d.push("/login")}catch(e){console.error("Error during logout:",e),d.push("/login")}}},E=async e=>{if(N)return void console.log("Fetching disabled due to previous auth errors");let t=k+1;if(w(t),t>3){console.log("Too many fetch attempts, disabling further fetches"),A(!0),h("Authentication error. Please try logging in again.");return}if(!e){h("No email provided to fetch assessment."),p(!0),x(!1);return}if(y)return void console.log("Already redirecting to login, skipping fetch");try{x(!0),h(null),console.log("Fetching assessment data for ".concat(e,"... (Attempt ").concat(t,"/3)"));let r=await fetch("/api/assessment?email=".concat(encodeURIComponent(e)));if(401===r.status){console.log("Authentication error (401). Disabling further fetches."),A(!0),_();return}if(!r.ok){let e=await r.json().catch(()=>null),t=(null==e?void 0:e.error)||"API error: ".concat(r.status);throw Error(t)}w(0);let s=await r.json();console.log("Assessment data fetched successfully"),u(s),b(e)}catch(t){let e=t instanceof Error?t.message:"An unknown error occurred";h(e),console.error("Error fetching assessment data:",e),t instanceof Error&&t.message.includes("NetworkError")&&(console.log("Network error. Disabling further fetches."),A(!0))}finally{x(!1),p(!0)}};return(0,n.useEffect)(()=>{var e,t;return(console.log("AssessmentContext useEffect",{status:o,session:r?{...r,user:{...null==r?void 0:r.user,access_token:"[REDACTED]"}}:null,fetchedForEmail:v,isRedirecting:y,fetchingDisabled:N,fetchAttempts:k}),N)?void console.log("Skipping effect because fetching is disabled"):y?void console.log("Skipping effect because we are redirecting"):void("authenticated"===o&&(null==r||null==(e=r.user)?void 0:e.email)?r.user.email!==v&&!g&&k<3&&(console.log("Session authenticated, fetching assessment for:",r.user.email),E(r.user.email)):"unauthenticated"===o?(u(null),b(null),p(!0),console.log("User not authenticated, clearing assessment data.")):"loading"===o&&!f&&x(!0),"loading"!==o&&g&&!f&&!(null==r||null==(t=r.user)?void 0:t.email)&&x(!1))},[r,o,v,y,g,N,k]),(0,s.jsx)(l.Provider,{value:{assessmentData:c,loading:g,error:m,fetchAssessment:E,isInitialized:f},children:t})}},897:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(5155);r(2115);var n=r(51),a=r(9345),i=r(2225),l=r(9010);function o(){var e,t,r;let{assessmentData:o,loading:d,error:c}=(0,n.U)(),u=(null==o||null==(r=o.assessment)||null==(t=r.section_ii)||null==(e=t.implementation_activities)?void 0:e.activities)||[];return d?(0,s.jsx)("div",{className:"w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6",children:(0,s.jsx)("div",{className:"w-full max-w-[1100px] mx-auto px-5 pt-8 relative",children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[50vh]",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-xl text-blue-500 dark:text-blue-400 mb-2",children:"Loading your top activities..."}),(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 dark:border-blue-400 mx-auto"})]})})})}):c?(0,s.jsx)("div",{className:"w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-[1100px] mx-auto px-5 pt-8 relative",children:[(0,s.jsx)("h1",{className:"text-[3rem] font-light text-[#3793F7] dark:text-blue-400 mb-8 text-center",children:"4.3 Top 5 Activities"}),(0,s.jsx)("div",{className:"text-center p-8 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-600 dark:text-red-400 text-lg",children:"Unable to load your top activities. Please try refreshing the page."})})]})}):(0,s.jsx)("div",{className:"w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-[1100px] mx-auto px-5 pt-8 relative",children:[(0,s.jsx)("div",{className:"text-center mb-10 mt-5",children:(0,s.jsx)("h1",{className:"text-[3rem] font-light text-[#3793F7] dark:text-blue-400 mb-8 md:text-4xl lg:text-[3rem]",children:"4.3 Top 5 Activities"})}),(0,s.jsx)("p",{className:"mb-6 leading-relaxed text-gray-900 dark:text-gray-200 max-w-[980px] mx-auto",children:"Listed below are the top 5 activities that you can immediately implement for the next 30-60 days."}),(0,s.jsx)(i.A,{color:"blue",children:u&&u.length>0?(0,s.jsx)("div",{className:"space-y-8",children:u.map((e,t)=>(0,s.jsxs)("div",{className:"mb-6 last:mb-0",children:[(0,s.jsxs)("h3",{className:"text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-start",children:[(0,s.jsxs)("span",{className:"text-[#3793F7] dark:text-blue-400 mr-3 text-2xl font-bold flex-shrink-0",children:[t+1,"."]}),e.title]}),e.sub_activities&&e.sub_activities.length>0&&(0,s.jsx)("div",{className:"ml-8",children:e.sub_activities.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start mb-3 last:mb-0",children:[(0,s.jsx)("span",{className:"text-[#3793F7] dark:text-blue-400 mr-3 text-lg flex-shrink-0",children:"•"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:e})]},t))})]},t))}):(0,s.jsx)("div",{className:"text-center p-8",children:(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-lg",children:"No implementation activities data could be fetched from the assessment."})})}),(0,s.jsxs)("p",{className:"my-8 leading-relaxed max-w-[980px] mx-auto text-gray-900 dark:text-gray-200",children:["Consider your SWOD as well as the given activities, to work on specific goals that are achievable in the next 60 days.",(0,s.jsx)("br",{}),(0,s.jsx)("br",{}),"Using the given template in the next section, write your goals, outline the specific steps and strategies you will follow to achieve them. Identify the skills you need to develop, resources you will use, and actions you will take. Include timelines for key milestones and specify how you will track your progress. This section should provide a clear, practical roadmap to guide your personal and professional growth."]}),(0,s.jsx)(a.A,{text:"CONTINUE",href:"/4_4_personal_development_plan"}),(0,s.jsx)(l.A,{})]})})}},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(2596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=s.$,i=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let a=n(t)||n(s);return i[e][a]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return a(e,o,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2225:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(5155);r(2115);var n=r(3999);let a={blue:{gradient:"linear-gradient(135deg, rgba(55,147,247,0.5) 0%, rgba(55,147,247,0.05) 30%, transparent 100%)",iconBg:"#3793F7"},orange:{gradient:"linear-gradient(135deg, rgba(255,168,0,0.5) 0%, rgba(255,168,0,0.05) 30%, transparent 100%)",iconBg:"#FFA800"}},i=e=>{let{children:t,className:r="",style:i={},infoIcon:l=!1,infoIconContent:o="i",color:d="blue"}=e,c=a[d]||a.blue;return(0,s.jsxs)("div",{className:(0,n.cn)("relative rounded-2xl p-6 mb-6 shadow-md bg-white dark:bg-gray-800 overflow-hidden max-w-[980px] mx-auto text-gray-900 dark:text-gray-100",r),style:i,children:[(0,s.jsx)("div",{style:{position:"absolute",inset:0,zIndex:0,pointerEvents:"none",borderRadius:"1rem",background:c.gradient}}),l&&(0,s.jsx)("div",{className:"absolute top-5 right-5 w-6 h-6 text-white rounded-full flex items-center justify-center italic font-bold z-10",style:{background:c.iconBg},children:o}),(0,s.jsx)("div",{className:"relative z-[1]",children:t})]})}},3999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(2596),n=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,s.$)(t))}},5528:(e,t,r)=>{Promise.resolve().then(r.bind(r,897))},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>a});var s=r(2115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,s=e.map(e=>{let s=n(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():n(e[t],null)}}}}function i(...e){return s.useCallback(a(...e),e)}},7168:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var s=r(5155);r(2115);var n=r(9708),a=r(2085),i=r(3999);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:a,asChild:o=!1,...d}=e,c=o?n.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:a,className:t})),...d})}},9010:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(5155);r(2115);var n=r(3999);function a(){return(0,s.jsxs)("div",{className:(0,n.cn)("w-full relative overflow-hidden text-black","py-4 px-8","text-xs leading-relaxed","rounded-t-[40px]","mt-10"),children:[(0,s.jsx)("div",{style:{position:"absolute",inset:0,zIndex:0,pointerEvents:"none",background:"linear-gradient(135deg, rgba(75,159,239,0.9) 0%, rgba(75,159,239,0.3) 100%)"}}),(0,s.jsxs)("div",{className:"max-w-[1200px] mx-auto flex flex-col md:flex-row items-start md:items-center gap-2 md:gap-0 relative z-10",children:[(0,s.jsx)("div",{className:"flex-1",children:"Disclaimer – The report's findings are based on the specific test administered and the responses given. The report is designed to help people identify their natural abilities, strengths, limitations and career interests, and should be used only as a tool for self-discovery"}),(0,s.jsx)("div",{className:"whitespace-nowrap md:ml-4",children:"|   Copyright – TalentMetrix 2025"})]})]})}},9345:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(5155);r(2115);var n=r(6874),a=r.n(n),i=r(7168);let l=function(e){let{text:t,href:r,onClick:n,className:l="",style:o}=e,d=(0,s.jsxs)(s.Fragment,{children:[t,(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 group-hover:translate-x-1 transition-transform duration-150",children:[(0,s.jsx)("path",{d:"M5 12h14"}),(0,s.jsx)("path",{d:"M12 5l7 7-7 7"})]})]}),c="rounded-full border-2 border-gray-800 dark:border-gray-200 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer "+(l||"");return(0,s.jsx)("div",{className:"flex justify-center w-full my-8",children:r?(0,s.jsx)(i.$,{asChild:!0,variant:"outline",className:c,style:o,onClick:n,children:(0,s.jsx)(a(),{href:r,children:d})}):(0,s.jsx)(i.$,{variant:"outline",className:c,style:o,onClick:n,children:d})})}},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i});var s=r(2115),n=r(6101),a=r(5155),i=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...a}=e;if(s.isValidElement(r)){var i;let e,l,o=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let s in t){let n=e[s],a=t[s];/^on[A-Z]/.test(s)?n&&a?r[s]=(...e)=>{let t=a(...e);return n(...e),t}:n&&(r[s]=n):"style"===s?r[s]={...n,...a}:"className"===s&&(r[s]=[n,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==s.Fragment&&(d.ref=t?(0,n.t)(t,o):o),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:n,...i}=e,l=s.Children.toArray(n),d=l.find(o);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,a.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}("Slot"),l=Symbol("radix.slottable");function o(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,6454,8441,1684,7358],()=>t(5528)),_N_E=e.O()}]);