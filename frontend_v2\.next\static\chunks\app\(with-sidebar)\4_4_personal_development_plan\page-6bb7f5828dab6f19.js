(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[404],{51:(e,t,a)=>{"use strict";a.d(t,{AssessmentProvider:()=>d,U:()=>o});var s=a(5155),r=a(2115),i=a(2108),l=a(5695);let n=(0,r.createContext)({assessmentData:null,loading:!1,error:null,fetchAssessment:async()=>{},isInitialized:!1}),o=()=>(0,r.useContext)(n),d=e=>{let{children:t}=e,{data:a,status:o}=(0,i.useSession)(),d=(0,l.useRouter)(),[c,m]=(0,r.useState)(null),[x,u]=(0,r.useState)(!1),[p,g]=(0,r.useState)(null),[h,f]=(0,r.useState)(!1),[y,b]=(0,r.useState)(null),[v,j]=(0,r.useState)(!1),[k,N]=(0,r.useState)(0),[w,C]=(0,r.useState)(!1),T=async()=>{if(!v){console.log("Authentication error detected. Logging out and redirecting to login page..."),j(!0);try{await (0,i.signOut)({redirect:!1}),m(null),b(null),d.push("/login")}catch(e){console.error("Error during logout:",e),d.push("/login")}}},S=async e=>{if(w)return void console.log("Fetching disabled due to previous auth errors");let t=k+1;if(N(t),t>3){console.log("Too many fetch attempts, disabling further fetches"),C(!0),g("Authentication error. Please try logging in again.");return}if(!e){g("No email provided to fetch assessment."),f(!0),u(!1);return}if(v)return void console.log("Already redirecting to login, skipping fetch");try{u(!0),g(null),console.log("Fetching assessment data for ".concat(e,"... (Attempt ").concat(t,"/3)"));let a=await fetch("/api/assessment?email=".concat(encodeURIComponent(e)));if(401===a.status){console.log("Authentication error (401). Disabling further fetches."),C(!0),T();return}if(!a.ok){let e=await a.json().catch(()=>null),t=(null==e?void 0:e.error)||"API error: ".concat(a.status);throw Error(t)}N(0);let s=await a.json();console.log("Assessment data fetched successfully"),m(s),b(e)}catch(t){let e=t instanceof Error?t.message:"An unknown error occurred";g(e),console.error("Error fetching assessment data:",e),t instanceof Error&&t.message.includes("NetworkError")&&(console.log("Network error. Disabling further fetches."),C(!0))}finally{u(!1),f(!0)}};return(0,r.useEffect)(()=>{var e,t;return(console.log("AssessmentContext useEffect",{status:o,session:a?{...a,user:{...null==a?void 0:a.user,access_token:"[REDACTED]"}}:null,fetchedForEmail:y,isRedirecting:v,fetchingDisabled:w,fetchAttempts:k}),w)?void console.log("Skipping effect because fetching is disabled"):v?void console.log("Skipping effect because we are redirecting"):void("authenticated"===o&&(null==a||null==(e=a.user)?void 0:e.email)?a.user.email!==y&&!x&&k<3&&(console.log("Session authenticated, fetching assessment for:",a.user.email),S(a.user.email)):"unauthenticated"===o?(m(null),b(null),f(!0),console.log("User not authenticated, clearing assessment data.")):"loading"===o&&!h&&u(!0),"loading"!==o&&x&&!h&&!(null==a||null==(t=a.user)?void 0:t.email)&&u(!1))},[a,o,y,v,x,w,k]),(0,s.jsx)(n.Provider,{value:{assessmentData:c,loading:x,error:p,fetchAssessment:S,isInitialized:h},children:t})}},1085:(e,t,a)=>{"use strict";a.d(t,{CG:()=>o,Fm:()=>x,Qs:()=>g,XW:()=>u,cj:()=>n,h:()=>m,qp:()=>p});var s=a(5155);a(2115);var r=a(5821),i=a(4416),l=a(3999);function n(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"sheet",...t})}function o(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"sheet-trigger",...t})}function d(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"sheet-portal",...t})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"sheet-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",t),style:{backgroundColor:"rgba(0, 0, 0, 0.5)",backdropFilter:"blur(2px)"},...a})}function m(e){let{className:t,children:a,side:n="right",...o}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(c,{}),(0,s.jsxs)(r.UC,{"data-slot":"sheet-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===n&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===n&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===n&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===n&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...o,children:[a,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(i.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,l.cn)("flex flex-col gap-1.5 p-4",t),...a})}function u(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sheet-footer",className:(0,l.cn)("mt-auto flex flex-col gap-2 p-4",t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"sheet-title",className:(0,l.cn)("text-foreground font-semibold",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"sheet-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}},1350:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>S});var s=a(5155),r=a(9137),i=a.n(r),l=a(2115),n=a(51),o=a(9345),d=a(2225),c=a(9010),m=a(7168),x=a(9852),u=a(2714),p=a(5784),g=a(6490),h=a(1085),f=a(9727),y=a(9144),b=a(5968),v=a(9140),j=a(133),k=a(2568),N=a(4616),w=a(6287),C=a(2525);let T=e=>{let{content:t,onChange:a,placeholder:r,id:n}=e,o=(0,l.useRef)(null),[d,c]=(0,l.useState)(!1),[x,u]=(0,l.useState)(t);(0,l.useEffect)(()=>{o.current&&(o.current.innerHTML=t||"",u(t||""),p())},[]),(0,l.useEffect)(()=>{o.current&&t!==x&&(o.current.innerHTML=t||"",u(t||""),p())},[t,x]);let p=()=>{o.current&&o.current.querySelectorAll("ul, ol").forEach(e=>{"UL"===e.tagName?(e.style.listStyleType="disc",e.style.marginLeft="20px",e.style.paddingLeft="0px"):"OL"===e.tagName&&(e.style.listStyleType="decimal",e.style.marginLeft="20px",e.style.paddingLeft="0px")})},g=()=>{if(o.current){let e=o.current.innerHTML;u(e),a(e)}},h=(e,t)=>{if(o.current){if(o.current.focus(),"insertUnorderedList"===e||"insertOrderedList"===e){let a=window.getSelection();a&&a.rangeCount>0&&(document.execCommand(e,!1,t),setTimeout(()=>{o.current&&(o.current.querySelectorAll("ul, ol").forEach(e=>{"UL"===e.tagName?(e.style.listStyleType="disc",e.style.marginLeft="20px",e.style.paddingLeft="0px"):"OL"===e.tagName&&(e.style.listStyleType="decimal",e.style.marginLeft="20px",e.style.paddingLeft="0px")}),g())},10))}else document.execCommand(e,!1,t);g()}},N=e=>{try{if(o.current&&o.current.contains(document.activeElement))return document.queryCommandState(e);return!1}catch(e){return!1}};return(0,s.jsxs)("div",{className:i().dynamic([["9e5cf363cf309452",[n,n,n]]])+" border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800",children:[(0,s.jsxs)("div",{className:i().dynamic([["9e5cf363cf309452",[n,n,n]]])+" border-b border-gray-300 dark:border-gray-600 p-2 flex items-center gap-1 bg-gray-50 dark:bg-gray-700 rounded-t-md",children:[(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("bold"),className:"h-8 w-8 p-0 ".concat(N("bold")?"bg-gray-200 dark:bg-gray-600":""),children:(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("italic"),className:"h-8 w-8 p-0 ".concat(N("italic")?"bg-gray-200 dark:bg-gray-600":""),children:(0,s.jsx)(y.A,{className:"h-4 w-4"})}),(0,s.jsx)("div",{className:i().dynamic([["9e5cf363cf309452",[n,n,n]]])+" w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("insertUnorderedList"),className:"h-8 w-8 p-0 ".concat(N("insertUnorderedList")?"bg-gray-200 dark:bg-gray-600":""),children:(0,s.jsx)(b.A,{className:"h-4 w-4"})}),(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("insertOrderedList"),className:"h-8 w-8 p-0 ".concat(N("insertOrderedList")?"bg-gray-200 dark:bg-gray-600":""),children:(0,s.jsx)(v.A,{className:"h-4 w-4"})}),(0,s.jsx)("div",{className:i().dynamic([["9e5cf363cf309452",[n,n,n]]])+" w-px h-6 bg-gray-300 dark:border-gray-600 mx-1"}),(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("undo"),className:"h-8 w-8 p-0",children:(0,s.jsx)(j.A,{className:"h-4 w-4"})}),(0,s.jsx)(m.$,{type:"button",variant:"ghost",size:"sm",onMouseDown:e=>e.preventDefault(),onClick:()=>h("redo"),className:"h-8 w-8 p-0",children:(0,s.jsx)(k.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:i().dynamic([["9e5cf363cf309452",[n,n,n]]])+" relative",children:[(0,s.jsx)("div",{ref:o,contentEditable:!0,onInput:g,onKeyDown:e=>{if("Enter"===e.key){let s=window.getSelection();if(s&&s.rangeCount>0){var t,a;let r=null==(t=s.getRangeAt(0).startContainer.parentElement)?void 0:t.closest("li");r&&(e.preventDefault(),(null==(a=r.textContent)?void 0:a.trim())===""?document.execCommand("outdent"):(document.execCommand("insertHTML",!1,"<br>"),document.execCommand("insertHTML",!1,"</li><li>")),g())}}},onFocus:()=>c(!0),onBlur:()=>c(!1),style:{wordBreak:"break-word",overflowWrap:"break-word",direction:"ltr",textAlign:"left",unicodeBidi:"embed"},suppressContentEditableWarning:!0,"data-editor-id":n,className:i().dynamic([["9e5cf363cf309452",[n,n,n]]])+" min-h-[100px] p-3 text-gray-900 dark:text-gray-100 focus:outline-none"}),!d&&(!x||""===x.trim()||"<br>"===x)&&(0,s.jsx)("div",{className:i().dynamic([["9e5cf363cf309452",[n,n,n]]])+" absolute top-3 left-3 text-gray-400 dark:text-gray-500 pointer-events-none",children:r})]}),(0,s.jsx)(i(),{id:"9e5cf363cf309452",dynamic:[n,n,n],children:'div[data-editor-id="'.concat(n,'"].__jsx-style-dynamic-selector ul.__jsx-style-dynamic-selector{list-style-type:disc!important;margin-left:20px!important;padding-left:0px!important}div[data-editor-id="').concat(n,'"].__jsx-style-dynamic-selector ol.__jsx-style-dynamic-selector{list-style-type:decimal!important;margin-left:20px!important;padding-left:0px!important}div[data-editor-id="').concat(n,'"].__jsx-style-dynamic-selector li.__jsx-style-dynamic-selector{margin-bottom:4px!important;display:list-item!important}')})]})};function S(){let{assessmentData:e,loading:t,error:a}=(0,n.U)(),[r,f]=(0,l.useState)(!1),[y,b]=(0,l.useState)(null),[v,j]=(0,l.useState)([]),[k,S]=(0,l.useState)(0),[L,A]=(0,l.useState)(!0),[_,E]=(0,l.useState)(null),[P,D]=(0,l.useState)({goal:"",tasks:"",importantTasks:"",difficultyLevel:"",timeToComplete:"",progress:"Not Started"}),z=async e=>{if(confirm("Are you sure you want to delete this development item?"))try{A(!0),E(null);let t=await fetch("/api/pdp?id=".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Error deleting PDP item: ".concat(t.status));j(t=>t.filter(t=>t.id!==e))}catch(e){console.error("Error deleting PDP item:",e),E("Failed to delete development item. Please try again.")}finally{A(!1)}},I=(e,t)=>{D(a=>({...a,[e]:t}))},F=e=>{console.log("Opening edit modal for item:",e),S(Date.now());let t={goal:e.goal||"",tasks:e.tasks||"",importantTasks:e.importantTasks||"",difficultyLevel:e.difficultyLevel,timeToComplete:e.timeToComplete,progress:e.progress};D(t),b(e),console.log("Setting form data for edit:",t),setTimeout(()=>{f(!0)},50)},M=async()=>{try{A(!0),E(null);let e=await fetch("/api/pdp");if(!e.ok)throw Error("Error fetching PDP items: ".concat(e.status));let t=(await e.json()).items.map(e=>({id:e.id,goal:e.goal,tasks:e.tasks,importantTasks:e.important_tasks||"",difficultyLevel:e.priority_level,timeToComplete:e.time_to_complete,progress:e.progress,dateCreated:new Date(e.date_created).toISOString().split("T")[0]}));j(t)}catch(e){console.error("Failed to load PDP items:",e),E("Failed to load development items. Please try again later.")}finally{A(!1)}};(0,l.useEffect)(()=>{M()},[]);let O=async e=>{e.preventDefault(),A(!0),E(null);try{if(y){let e={goal:P.goal,tasks:P.tasks,important_tasks:P.importantTasks,priority_level:P.difficultyLevel,time_to_complete:P.timeToComplete,progress:P.progress},t=await fetch("/api/pdp?id=".concat(y.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:y.id,...e})});if(!t.ok)throw Error("Error updating PDP item: ".concat(t.status));let a=await t.json(),s={id:a.id,goal:a.goal,tasks:a.tasks,importantTasks:a.important_tasks||"",difficultyLevel:a.priority_level,timeToComplete:a.time_to_complete,progress:a.progress,dateCreated:new Date(a.date_created).toISOString().split("T")[0]};console.log("Development Item Updated:",s),j(e=>e.map(e=>e.id===y.id?s:e))}else{let e={goal:P.goal,tasks:P.tasks,important_tasks:P.importantTasks,priority_level:P.difficultyLevel,time_to_complete:P.timeToComplete,progress:P.progress},t=await fetch("/api/pdp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Error creating PDP item: ".concat(t.status));let a=await t.json(),s={id:a.id,goal:a.goal,tasks:a.tasks,importantTasks:a.important_tasks||"",difficultyLevel:a.priority_level,timeToComplete:a.time_to_complete,progress:a.progress,dateCreated:new Date(a.date_created).toISOString().split("T")[0]};console.log("New Development Item Added:",s),j(e=>[...e,s])}D({goal:"",tasks:"",importantTasks:"",difficultyLevel:"",timeToComplete:"",progress:"Not Started"}),b(null),f(!1),S(e=>e+1)}catch(e){console.error("Error saving PDP item:",e),E("Failed to save development item. Please try again.")}finally{A(!1)}},H=e=>{switch(e){case"High Priority":return"text-red-600 dark:text-red-400";case"Medium Priority":return"text-yellow-600 dark:text-yellow-400";case"Low Priority":return"text-green-600 dark:text-green-400";default:return"text-gray-600 dark:text-gray-400"}},U=e=>{switch(e){case"Complete":return"text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20";case"In Progress":return"text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20";default:return"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800"}};return(0,s.jsx)("div",{className:"w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-[1100px] mx-auto px-5 pt-8 relative",children:[(0,s.jsx)("div",{className:"text-center mb-10 mt-5",children:(0,s.jsx)("h1",{className:"text-[3rem] font-light text-[#3793F7] dark:text-blue-400 mb-8 md:text-4xl lg:text-[3rem]",children:"4.4 Personal Development Plan"})}),(0,s.jsx)(d.A,{color:"blue",children:(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold",children:"1."}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"Write 3 unique goals that are important for you to achieve in the next 3-6months, using the SWOD Development Areas."})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold",children:"2."}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"Think \xe2€“ development of skills/tools/methods; acing a particular subject/test/paper; Internship / job placement success."})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold",children:"3."}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"Avoid BIG Goals \xe2€“ such as buying a dream home; becoming the CEO of a company \xe2€“ those are years away if you are being practical. Right now identify goals that will significantly boost your confidence and purpose."})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold",children:"4."}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"Once you prepare your SMART Goals, revisit them on a daily basis and track your progress. Do not leave it as an academic or theoretical exercise."})]})]})})}),(0,s.jsx)(g.nD,{type:"single",collapsible:!0,className:"mb-8",children:(0,s.jsxs)(g.As,{value:"reference-template",children:[(0,s.jsx)(g.$m,{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:"Reference Template"}),(0,s.jsx)(g.ub,{children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-sm",children:[(0,s.jsx)("thead",{className:"bg-blue-600 text-white",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-3 py-2 text-left font-medium",children:"GOALS"}),(0,s.jsx)("th",{className:"px-3 py-2 text-left font-medium",children:"IMPORTANT TASKS"}),(0,s.jsx)("th",{className:"px-3 py-2 text-left font-medium",children:"DIFFICULTY TO SUCCESS"}),(0,s.jsx)("th",{className:"px-3 py-2 text-left font-medium",children:"TIME TO COMPLETE"}),(0,s.jsx)("th",{className:"px-3 py-2 text-left font-medium",children:"PROGRESS"})]})}),(0,s.jsxs)("tbody",{className:"bg-white dark:bg-gray-800",children:[(0,s.jsxs)("tr",{className:"border-b border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:"Must be Specific & achievable"}),(0,s.jsxs)("ul",{className:"space-y-1 text-xs text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("li",{children:"\xe2€\xa2 High on Time and importance"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Must be specific"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Effort 20% gets Effort 80% tasks"}),(0,s.jsx)("li",{children:"\xe2€\xa2 High Priority"})]})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:"High on Time and importance"}),(0,s.jsxs)("ul",{className:"space-y-1 text-xs text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("li",{children:"\xe2€\xa2 High on Time and importance"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Must be specific"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Effort 80% tasks"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Low Priority"})]})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-red-600",children:"Write for EACH GOAL Completion criteria"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Integrated conceptual learning"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Feedback"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Actual score on a test or paper"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Quality of completeness of tasks in all aspects"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Within budget"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Others"})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:"Write for EACH TASK"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"How much time\xe2€”specific time - hours/day/wk, deadlines, as applicable."}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Budget slack time"})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:"Write for EACH progress"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"% Complete"})]})})]}),(0,s.jsxs)("tr",{className:"border-b border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-blue-600",children:'Example - "I will improve my math skills by 30% by the end of the semester"'}),(0,s.jsxs)("ul",{className:"space-y-1 text-xs text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("li",{children:"\xe2€\xa2 Practice 1 test each week"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Study math for 1 hour daily"}),(0,s.jsx)("li",{children:"\xe2€\xa2 Ask questions to teacher, tutor or classmates when confused"})]})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium text-blue-600",children:"Identifying learning videos"}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Make a 3 month study calendar"})]})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"80% score in mid-term test"})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"31/08/2024"})}),(0,s.jsx)("td",{className:"px-3 py-4 align-top",children:(0,s.jsx)("p",{className:"text-blue-600 text-xs",children:"In Progress"})})]})]})]})})})})]})}),(0,s.jsx)("div",{className:"flex justify-end mb-6",children:(0,s.jsxs)(h.cj,{open:r,onOpenChange:f,children:[(0,s.jsx)(h.CG,{asChild:!0,children:(0,s.jsxs)(m.$,{className:"bg-[#3793F7] hover:bg-blue-600 text-white",onClick:()=>{b(null),D({goal:"",tasks:"",importantTasks:"",difficultyLevel:"",timeToComplete:"",progress:"Not Started"}),S(e=>e+1),f(!0)},children:[(0,s.jsx)(N.A,{className:"w-4 h-4 mr-2"}),"Add Development Item"]})}),(0,s.jsxs)(h.h,{className:"overflow-y-auto p-8 bg-white dark:bg-gray-800",style:{width:"900px !important",maxWidth:"90vw !important",minWidth:"900px"},children:[(0,s.jsx)(i(),{id:"b5f2d5d429a9abbd",children:"[data-radix-dialog-content]{width:900px!important;max-width:90vw!important;min-width:900px!important}.fixed.inset-y-0.right-0.z-50{width:900px!important;max-width:90vw!important}@media(max-width:1024px){[data-radix-dialog-content]{width:90vw!important;min-width:auto!important}.fixed.inset-y-0.right-0.z-50{width:90vw!important}}"}),(0,s.jsxs)(h.Fm,{className:"mb-8",children:[(0,s.jsx)(h.qp,{className:"text-xl",children:y?"Edit Personal Development Item":"Add Personal Development Item"}),(0,s.jsx)(h.Qs,{className:"text-base mt-2",children:y?"Update your goal with specific tasks and timeline.":"Create a new goal with specific tasks and timeline for your personal development plan."})]}),(0,s.jsxs)("form",{onSubmit:O,className:"jsx-b5f2d5d429a9abbd space-y-8",children:[(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"goal",className:"mb-2 block text-base font-medium",children:"Goal"}),(0,s.jsx)("div",{className:"jsx-b5f2d5d429a9abbd mb-4",children:(0,s.jsx)(T,{id:"goal-editor",content:P.goal,onChange:e=>I("goal",e),placeholder:"Describe your specific, measurable goal..."},"goal-".concat(k))})]}),(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"tasks",className:"mb-2 block text-base font-medium",children:"Tasks"}),(0,s.jsx)("div",{className:"jsx-b5f2d5d429a9abbd mb-4",children:(0,s.jsx)(T,{id:"tasks-editor",content:P.tasks,onChange:e=>I("tasks",e),placeholder:"List the specific tasks you will complete..."},"tasks-".concat(k))})]}),(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"importantTasks",className:"mb-2 block text-base font-medium",children:"Most Important Tasks"}),(0,s.jsx)("div",{className:"jsx-b5f2d5d429a9abbd mb-4",children:(0,s.jsx)(T,{id:"important-tasks-editor",content:P.importantTasks,onChange:e=>I("importantTasks",e),placeholder:"Identify the most critical tasks for success..."},"important-tasks-".concat(k))})]}),(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"difficultyLevel",className:"mb-2 block text-base font-medium",children:"Priority Level"}),(0,s.jsxs)(p.l6,{onValueChange:e=>I("difficultyLevel",e),value:P.difficultyLevel,required:!0,children:[(0,s.jsx)(p.bq,{className:"h-11",children:(0,s.jsx)(p.yv,{placeholder:"Select priority level"})}),(0,s.jsxs)(p.gC,{className:"bg-white dark:bg-gray-800",children:[(0,s.jsx)(p.eb,{value:"High Priority",children:"High Priority"}),(0,s.jsx)(p.eb,{value:"Medium Priority",children:"Medium Priority"}),(0,s.jsx)(p.eb,{value:"Low Priority",children:"Low Priority"})]})]})]}),(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"timeToComplete",className:"mb-2 block text-base font-medium",children:"Target Completion Date"}),(0,s.jsx)(x.p,{id:"timeToComplete",type:"date",value:P.timeToComplete,onChange:e=>I("timeToComplete",e.target.value),required:!0,className:"h-11"})]})]}),(0,s.jsxs)("div",{className:"jsx-b5f2d5d429a9abbd",children:[(0,s.jsx)(u.J,{htmlFor:"progress",className:"mb-2 block text-base font-medium",children:"Progress Status"}),(0,s.jsxs)(p.l6,{onValueChange:e=>I("progress",e),value:P.progress,children:[(0,s.jsx)(p.bq,{className:"h-11",children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{className:"bg-white dark:bg-gray-800",children:[(0,s.jsx)(p.eb,{value:"Not Started",children:"Not Started"}),(0,s.jsx)(p.eb,{value:"In Progress",children:"In Progress"}),(0,s.jsx)(p.eb,{value:"Complete",children:"Complete"})]})]})]}),(0,s.jsxs)(h.XW,{className:"flex gap-4 pt-8 mt-8 border-t",children:[(0,s.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>f(!1),className:"px-6 py-2",children:"Cancel"}),(0,s.jsx)(m.$,{type:"submit",className:"bg-[#3793F7] hover:bg-blue-600 px-6 py-2",children:y?"Update Item":"Add Item"})]})]})]})]})}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-8",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:["My Personal Development Goals (",v.length,")"]})}),L?(0,s.jsx)("div",{className:"flex justify-center items-center py-10",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):v.length>0?(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Goal"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Tasks"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Important Tasks"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Priority"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Target Date"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Progress"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:v.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,s.jsx)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-gray-100",children:(0,s.jsx)("div",{className:"max-w-xs",children:(0,s.jsx)("div",{className:"font-medium text-sm prose prose-sm dark:prose-invert max-w-none [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4",dangerouslySetInnerHTML:{__html:e.goal}})})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm text-gray-600 dark:text-gray-300",children:(0,s.jsx)("div",{className:"max-w-xs",children:(0,s.jsx)("div",{className:"text-sm prose prose-sm dark:prose-invert max-w-none line-clamp-3 [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4",dangerouslySetInnerHTML:{__html:e.tasks}})})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm text-gray-600 dark:text-gray-300",children:(0,s.jsx)("div",{className:"max-w-xs",children:(0,s.jsx)("div",{className:"text-sm prose prose-sm dark:prose-invert max-w-none line-clamp-3 [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4",dangerouslySetInnerHTML:{__html:e.importantTasks}})})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,s.jsx)("span",{className:"font-medium ".concat(H(e.difficultyLevel)),children:e.difficultyLevel})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm text-gray-600 dark:text-gray-300",children:new Date(e.timeToComplete).toLocaleDateString()}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(U(e.progress)),children:e.progress})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>F(e),className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",title:"Edit",children:(0,s.jsx)(w.A,{className:"w-4 h-4"})}),(0,s.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>z(e.id),className:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",title:"Delete",children:(0,s.jsx)(C.A,{className:"w-4 h-4"})})]})})]},e.id))})]})}):(0,s.jsx)("div",{className:"px-6 py-8 text-center",children:(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:'No personal development goals added yet. Click "Add Development Item" to create your first goal.'})})]}),(0,s.jsx)("p",{className:"my-8 leading-relaxed max-w-[980px] mx-auto text-gray-900 dark:text-gray-200",children:"Your personal development plan is a living document that should be reviewed and updated regularly. Track your progress, celebrate achievements, and adjust goals as needed to ensure continuous growth and development in both personal and professional areas."}),(0,s.jsx)(o.A,{text:"CONTINUE",href:"/4_5_conclusion"}),(0,s.jsx)(c.A,{})]})})}},2225:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var s=a(5155);a(2115);var r=a(3999);let i={blue:{gradient:"linear-gradient(135deg, rgba(55,147,247,0.5) 0%, rgba(55,147,247,0.05) 30%, transparent 100%)",iconBg:"#3793F7"},orange:{gradient:"linear-gradient(135deg, rgba(255,168,0,0.5) 0%, rgba(255,168,0,0.05) 30%, transparent 100%)",iconBg:"#FFA800"}},l=e=>{let{children:t,className:a="",style:l={},infoIcon:n=!1,infoIconContent:o="i",color:d="blue"}=e,c=i[d]||i.blue;return(0,s.jsxs)("div",{className:(0,r.cn)("relative rounded-2xl p-6 mb-6 shadow-md bg-white dark:bg-gray-800 overflow-hidden max-w-[980px] mx-auto text-gray-900 dark:text-gray-100",a),style:l,children:[(0,s.jsx)("div",{style:{position:"absolute",inset:0,zIndex:0,pointerEvents:"none",borderRadius:"1rem",background:c.gradient}}),n&&(0,s.jsx)("div",{className:"absolute top-5 right-5 w-6 h-6 text-white rounded-full flex items-center justify-center italic font-bold z-10",style:{background:c.iconBg},children:o}),(0,s.jsx)("div",{className:"relative z-[1]",children:t})]})}},2714:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var s=a(5155);a(2115);var r=a(968),i=a(3999);function l(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},3999:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var s=a(2596),r=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},5784:(e,t,a)=>{"use strict";a.d(t,{bq:()=>m,eb:()=>u,gC:()=>x,l6:()=>d,yv:()=>c});var s=a(5155);a(2115);var r=a(5501),i=a(6474),l=a(5196),n=a(7863),o=a(3999);function d(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function m(e){let{className:t,size:a="default",children:l,...n}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[l,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:a,position:i="popper",...l}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...l,children:[(0,s.jsx)(p,{}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(g,{})]})})}function u(e){let{className:t,children:a,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(l.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(n.A,{className:"size-4"})})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(i.A,{className:"size-4"})})}},6490:(e,t,a)=>{"use strict";a.d(t,{$m:()=>d,As:()=>o,nD:()=>n,ub:()=>c});var s=a(5155);a(2115);var r=a(2292),i=a(6474),l=a(3999);function n(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"accordion",...t})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.q7,{"data-slot":"accordion-item",className:(0,l.cn)("border-b last:border-b-0",t),...a})}function d(e){let{className:t,children:a,...n}=e;return(0,s.jsx)(r.Y9,{className:"flex",children:(0,s.jsxs)(r.l9,{"data-slot":"accordion-trigger",className:(0,l.cn)("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",t),...n,children:[a,(0,s.jsx)(i.A,{className:"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"})]})})}function c(e){let{className:t,children:a,...i}=e;return(0,s.jsx)(r.UC,{"data-slot":"accordion-content",className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",...i,children:(0,s.jsx)("div",{className:(0,l.cn)("pt-0 pb-4",t),children:a})})}},7168:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var s=a(5155);a(2115);var r=a(9708),i=a(2085),l=a(3999);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:i,asChild:o=!1,...d}=e,c=o?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,l.cn)(n({variant:a,size:i,className:t})),...d})}},9010:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var s=a(5155);a(2115);var r=a(3999);function i(){return(0,s.jsxs)("div",{className:(0,r.cn)("w-full relative overflow-hidden text-black","py-4 px-8","text-xs leading-relaxed","rounded-t-[40px]","mt-10"),children:[(0,s.jsx)("div",{style:{position:"absolute",inset:0,zIndex:0,pointerEvents:"none",background:"linear-gradient(135deg, rgba(75,159,239,0.9) 0%, rgba(75,159,239,0.3) 100%)"}}),(0,s.jsxs)("div",{className:"max-w-[1200px] mx-auto flex flex-col md:flex-row items-start md:items-center gap-2 md:gap-0 relative z-10",children:[(0,s.jsx)("div",{className:"flex-1",children:"Disclaimer – The report's findings are based on the specific test administered and the responses given. The report is designed to help people identify their natural abilities, strengths, limitations and career interests, and should be used only as a tool for self-discovery"}),(0,s.jsx)("div",{className:"whitespace-nowrap md:ml-4",children:"|   Copyright – TalentMetrix 2025"})]})]})}},9345:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(5155);a(2115);var r=a(6874),i=a.n(r),l=a(7168);let n=function(e){let{text:t,href:a,onClick:r,className:n="",style:o}=e,d=(0,s.jsxs)(s.Fragment,{children:[t,(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 group-hover:translate-x-1 transition-transform duration-150",children:[(0,s.jsx)("path",{d:"M5 12h14"}),(0,s.jsx)("path",{d:"M12 5l7 7-7 7"})]})]}),c="rounded-full border-2 border-gray-800 dark:border-gray-200 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer "+(n||"");return(0,s.jsx)("div",{className:"flex justify-center w-full my-8",children:a?(0,s.jsx)(l.$,{asChild:!0,variant:"outline",className:c,style:o,onClick:r,children:(0,s.jsx)(i(),{href:a,children:d})}):(0,s.jsx)(l.$,{variant:"outline",className:c,style:o,onClick:r,children:d})})}},9848:(e,t,a)=>{Promise.resolve().then(a.bind(a,1350))},9852:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var s=a(5155);a(2115);var r=a(3999);function i(e){let{className:t,type:a,...i}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,6454,1563,8250,7665,5152,1428,599,1450,8441,1684,7358],()=>t(9848)),_N_E=e.O()}]);