(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4198],{1865:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>i});var s=l(5155);l(2115);var r=l(6766),n=l(9010);function i(){return(0,s.jsx)("div",{className:"w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-[1100px] mx-auto px-5 pt-8 relative",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row relative mb-10 mt-5 items-center",children:[(0,s.jsx)("div",{className:"flex-none w-full md:w-[400px] lg:w-[400px] relative",children:(0,s.jsx)("div",{className:"relative w-full h-[400px]",children:(0,s.jsx)(r.default,{src:"/4.3TM.svg",alt:"Conclusion illustration",fill:!0,style:{objectFit:"contain"},priority:!0})})}),(0,s.jsxs)("div",{className:"flex-1 md:pl-10 mt- pt-16 md:mt-0",children:[(0,s.jsx)("h1",{className:"text-[3rem] font-light text-[#3793F7] mb-4 md:text-4xl lg:text-[3rem]",children:"Conclusion"}),(0,s.jsx)("p",{className:"mb-10 leading-relaxed max-w-[600px]",children:"By understanding your strengths, aligning them with suitable career paths, and mastering essential jobsearch skills like resume building and interview preparation, you equip yourself for long-term professional success. With these insights, you can confidently pursue career opportunities that fit your abilities, values, and aspirations while continuously adapting to future growth."})]})]}),(0,s.jsx)("div",{className:"w-full h-px bg-gray-300 my-16"}),(0,s.jsx)(n.A,{})]})})}},3312:(e,t,l)=>{Promise.resolve().then(l.bind(l,1865))},3999:(e,t,l)=>{"use strict";l.d(t,{cn:()=>n});var s=l(2596),r=l(9688);function n(){for(var e=arguments.length,t=Array(e),l=0;l<e;l++)t[l]=arguments[l];return(0,r.QP)((0,s.$)(t))}},6654:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let s=l(2115);function r(e,t){let l=(0,s.useRef)(null),r=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=l.current;e&&(l.current=null,e());let t=r.current;t&&(r.current=null,t())}else e&&(l.current=n(e,s)),t&&(r.current=n(t,s))},[e,t])}function n(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let l=e(t);return"function"==typeof l?l:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9010:(e,t,l)=>{"use strict";l.d(t,{A:()=>n});var s=l(5155);l(2115);var r=l(3999);function n(){return(0,s.jsxs)("div",{className:(0,r.cn)("w-full relative overflow-hidden text-black","py-4 px-8","text-xs leading-relaxed","rounded-t-[40px]","mt-10"),children:[(0,s.jsx)("div",{style:{position:"absolute",inset:0,zIndex:0,pointerEvents:"none",background:"linear-gradient(135deg, rgba(75,159,239,0.9) 0%, rgba(75,159,239,0.3) 100%)"}}),(0,s.jsxs)("div",{className:"max-w-[1200px] mx-auto flex flex-col md:flex-row items-start md:items-center gap-2 md:gap-0 relative z-10",children:[(0,s.jsx)("div",{className:"flex-1",children:"Disclaimer – The report's findings are based on the specific test administered and the responses given. The report is designed to help people identify their natural abilities, strengths, limitations and career interests, and should be used only as a tool for self-discovery"}),(0,s.jsx)("div",{className:"whitespace-nowrap md:ml-4",children:"|   Copyright – TalentMetrix 2025"})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6766,8441,1684,7358],()=>t(3312)),_N_E=e.O()}]);