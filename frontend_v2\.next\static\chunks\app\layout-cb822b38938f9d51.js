(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{51:(e,t,s)=>{"use strict";s.d(t,{AssessmentProvider:()=>u,U:()=>a});var r=s(5155),n=s(2115),o=s(2108),i=s(5695);let l=(0,n.createContext)({assessmentData:null,loading:!1,error:null,fetchAssessment:async()=>{},isInitialized:!1}),a=()=>(0,n.useContext)(l),u=e=>{let{children:t}=e,{data:s,status:a}=(0,o.useSession)(),u=(0,i.useRouter)(),[c,d]=(0,n.useState)(null),[g,h]=(0,n.useState)(!1),[f,m]=(0,n.useState)(null),[v,b]=(0,n.useState)(!1),[p,S]=(0,n.useState)(null),[P,E]=(0,n.useState)(!1),[k,w]=(0,n.useState)(0),[y,A]=(0,n.useState)(!1),x=async()=>{if(!P){console.log("Authentication error detected. Logging out and redirecting to login page..."),E(!0);try{await (0,o.signOut)({redirect:!1}),d(null),S(null),u.push("/login")}catch(e){console.error("Error during logout:",e),u.push("/login")}}},_=async e=>{if(y)return void console.log("Fetching disabled due to previous auth errors");let t=k+1;if(w(t),t>3){console.log("Too many fetch attempts, disabling further fetches"),A(!0),m("Authentication error. Please try logging in again.");return}if(!e){m("No email provided to fetch assessment."),b(!0),h(!1);return}if(P)return void console.log("Already redirecting to login, skipping fetch");try{h(!0),m(null),console.log("Fetching assessment data for ".concat(e,"... (Attempt ").concat(t,"/3)"));let s=await fetch("/api/assessment?email=".concat(encodeURIComponent(e)));if(401===s.status){console.log("Authentication error (401). Disabling further fetches."),A(!0),x();return}if(!s.ok){let e=await s.json().catch(()=>null),t=(null==e?void 0:e.error)||"API error: ".concat(s.status);throw Error(t)}w(0);let r=await s.json();console.log("Assessment data fetched successfully"),d(r),S(e)}catch(t){let e=t instanceof Error?t.message:"An unknown error occurred";m(e),console.error("Error fetching assessment data:",e),t instanceof Error&&t.message.includes("NetworkError")&&(console.log("Network error. Disabling further fetches."),A(!0))}finally{h(!1),b(!0)}};return(0,n.useEffect)(()=>{var e,t;return(console.log("AssessmentContext useEffect",{status:a,session:s?{...s,user:{...null==s?void 0:s.user,access_token:"[REDACTED]"}}:null,fetchedForEmail:p,isRedirecting:P,fetchingDisabled:y,fetchAttempts:k}),y)?void console.log("Skipping effect because fetching is disabled"):P?void console.log("Skipping effect because we are redirecting"):void("authenticated"===a&&(null==s||null==(e=s.user)?void 0:e.email)?s.user.email!==p&&!g&&k<3&&(console.log("Session authenticated, fetching assessment for:",s.user.email),_(s.user.email)):"unauthenticated"===a?(d(null),S(null),b(!0),console.log("User not authenticated, clearing assessment data.")):"loading"===a&&!v&&h(!0),"loading"!==a&&g&&!v&&!(null==s||null==(t=s.user)?void 0:t.email)&&h(!1))},[s,a,p,P,g,y,k]),(0,r.jsx)(l.Provider,{value:{assessmentData:c,loading:g,error:f,fetchAssessment:_,isInitialized:v},children:t})}},6621:(e,t,s)=>{"use strict";s.d(t,{SidebarProvider:()=>i,c:()=>l});var r=s(5155),n=s(2115);let o=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[s,i]=(0,n.useState)(!0);return(0,r.jsx)(o.Provider,{value:{isExpanded:s,toggleSidebar:()=>{i(!s)}},children:t})}function l(){let e=(0,n.useContext)(o);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},7958:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,9324,23)),Promise.resolve().then(s.bind(s,7949)),Promise.resolve().then(s.bind(s,8230)),Promise.resolve().then(s.bind(s,9304)),Promise.resolve().then(s.bind(s,51)),Promise.resolve().then(s.bind(s,6621)),Promise.resolve().then(s.t.bind(s,9840,23))},8230:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(5155),n=s(2108);function o(e){let{children:t,session:s}=e;return(0,r.jsx)(n.SessionProvider,{session:s,children:t})}},9304:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>o});var r=s(5155);s(2115);var n=s(1362);function o(e){let{children:t,...s}=e;return(0,r.jsx)(n.N,{...s,children:t})}},9324:()=>{},9840:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}}},e=>{var t=t=>e(e.s=t);e.O(0,[7385,4277,6874,6454,6766,1563,8250,7665,5152,1055,7949,8441,1684,7358],()=>t(7958)),_N_E=e.O()}]);