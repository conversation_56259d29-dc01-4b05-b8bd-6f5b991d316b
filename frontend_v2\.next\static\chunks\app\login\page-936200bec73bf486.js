(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{2714:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(5155);r(2115);var a=r(968),n=r(3999);function o(e){let{className:t,...r}=e;return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},3999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(2596),a=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},6004:(e,t,r)=>{Promise.resolve().then(r.bind(r,8007))},7168:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var s=r(5155);r(2115);var a=r(9708),n=r(2085),o=r(3999);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:r,size:n,asChild:i=!1,...c}=e,d=i?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(l({variant:r,size:n,className:t})),...c})}},8007:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(5155),a=r(2115),n=r(5695),o=r(2108),l=r(7168),i=r(9852),c=r(2714),d=r(8482),u=r(5339),g=r(1264),m=r(2919),x=r(2138),f=r(9026);function p(){console.log("\uD83C\uDFD7️ LoginPage component rendering/re-rendering");let e=(0,n.useRouter)(),t=(0,n.useSearchParams)().get("callbackUrl")||"/landingpage_v2",[r,p]=(0,a.useState)(""),[v,h]=(0,a.useState)(""),[b,j]=(0,a.useState)(null),[y,N]=(0,a.useState)(!1);(0,a.useEffect)(()=>{console.log("⏳ Loading state changed to:",y)},[y]),console.log("\uD83D\uDD27 Component state:",{email:r,passwordLength:v.length,error:b,loading:y,callbackUrl:t}),(0,a.useEffect)(()=>(console.log("\uD83D\uDD04 useEffect - Component mounted"),console.log("\uD83C\uDF10 Current URL:",window.location.href),console.log("\uD83D\uDCCD Callback URL from params:",t),()=>{console.log("\uD83E\uDDF9 useEffect cleanup - Component unmounting")}),[t]);let w=async s=>{s.preventDefault(),console.log("\uD83D\uDE80 LOGIN FORM SUBMITTED"),console.log("\uD83D\uDCE7 Email:",r),console.log("\uD83D\uDD12 Password length:",v.length),console.log("\uD83D\uDD04 Callback URL:",t),j(null),N(!0),console.log("⏳ Loading state set to true");try{console.log("\uD83D\uDD11 Calling signIn with credentials..."),console.log("\uD83D\uDCE6 SignIn payload:",{provider:"credentials",email:r,passwordLength:v.length,redirect:!1});let s=await (0,o.signIn)("credentials",{email:r,password:v,redirect:!1});console.log("\uD83D\uDCE5 SignIn result received:",s),console.log("❓ Result error:",null==s?void 0:s.error),console.log("✅ Result ok:",null==s?void 0:s.ok),console.log("\uD83D\uDD17 Result url:",null==s?void 0:s.url),console.log("\uD83D\uDCCA Result status:",null==s?void 0:s.status),(null==s?void 0:s.error)?(console.error("❌ Authentication failed with error:",s.error),j(s.error),N(!1),console.log("⏳ Loading state set to false (error case)")):(null==s?void 0:s.ok)?(console.log("✅ Authentication successful!"),console.log("\uD83D\uDD04 Redirecting to:",t),e.push(t),e.refresh(),console.log("\uD83D\uDD04 Router refresh called")):(console.warn("⚠️ Unexpected result state:",s),j("Authentication failed. Please try again."),N(!1),console.log("⏳ Loading state set to false (unexpected case)"))}catch(e){console.error("\uD83D\uDCA5 CATCH BLOCK - Unexpected error during sign in:",e),console.error("\uD83D\uDCA5 Error type:",typeof e),console.error("\uD83D\uDCA5 Error message:",e instanceof Error?e.message:String(e)),console.error("\uD83D\uDCA5 Error stack:",e instanceof Error?e.stack:"No stack trace"),j("An unexpected error occurred."),N(!1),console.log("⏳ Loading state set to false (catch case)")}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-secondary/5 relative",children:(0,s.jsxs)("div",{className:"w-full max-w-md p-4 z-10",children:[(0,s.jsxs)("div",{className:"mb-8 text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Welcome back"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"Sign in to access your account"})]}),(0,s.jsxs)(d.Zp,{className:"shadow-lg border-muted/30",children:[(0,s.jsxs)(d.aR,{className:"space-y-1 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-xl font-semibold",children:"Sign In"}),(0,s.jsx)(d.BT,{children:"Enter your credentials to continue"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:w,className:"space-y-4",children:[b&&(0,s.jsxs)(f.Fc,{variant:"destructive",className:"animate-in slide-in-from-top-1 duration-300",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),(0,s.jsx)(f.TN,{children:b})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"email",className:"text-sm font-medium",children:"Email"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.p,{id:"email",type:"email",value:r,onChange:e=>{console.log("\uD83D\uDCE7 Email input changed:",e.target.value),p(e.target.value)},onFocus:()=>console.log("\uD83D\uDCE7 Email input focused"),onBlur:()=>console.log("\uD83D\uDCE7 Email input blurred"),placeholder:"<EMAIL>",required:!0,disabled:y,className:"pl-10 transition-all focus:ring-2 focus:ring-primary/20"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(c.J,{htmlFor:"password",className:"text-sm font-medium",children:"Password"}),(0,s.jsx)(l.$,{variant:"link",className:"px-0 h-auto text-xs",disabled:y,children:"Forgot password?"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.p,{id:"password",type:"password",value:v,onChange:e=>{console.log("\uD83D\uDD12 Password input changed, length:",e.target.value.length),h(e.target.value)},onFocus:()=>console.log("\uD83D\uDD12 Password input focused"),onBlur:()=>console.log("\uD83D\uDD12 Password input blurred"),placeholder:"••••••••",required:!0,disabled:y,className:"pl-10 transition-all focus:ring-2 focus:ring-primary/20"})]})]}),(0,s.jsx)(l.$,{type:"submit",className:"w-full transition-all hover:shadow-md group",disabled:y,onClick:()=>{console.log("\uD83D\uDDB1️ Sign In button clicked"),console.log("⏳ Current loading state:",y),console.log("\uD83D\uDCE7 Current email:",r),console.log("\uD83D\uDD12 Current password length:",v.length)},children:y?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Signing in..."]}):(0,s.jsxs)("div",{className:"flex items-center justify-center",children:["Sign In",(0,s.jsx)(x.A,{className:"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1"})]})})]})}),(0,s.jsx)(d.wL,{className:"flex justify-center border-t pt-4 text-xs",children:(0,s.jsxs)("p",{className:"text-muted-foreground",children:["Don't have an account? ",(0,s.jsx)("span",{className:"text-primary font-medium",children:"Contact your administrator"})]})})]}),(0,s.jsx)("div",{className:"mt-8 text-center text-xs text-muted-foreground",children:(0,s.jsx)("p",{children:"By signing in, you agree to our Terms of Service and Privacy Policy"})})]})})}},8482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>d});var s=r(5155);r(2115);var a=r(3999);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},9026:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>c,XL:()=>i});var s=r(5155);r(2115);var a=r(2085),n=r(3999);let o=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(o({variant:r}),t),...a})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},9852:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(5155);r(2115);var a=r(3999);function n(e){let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6454,3085,8441,1684,7358],()=>t(6004)),_N_E=e.O()}]);