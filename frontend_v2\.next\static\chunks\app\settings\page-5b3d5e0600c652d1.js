(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4662],{805:(e,t,s)=>{Promise.resolve().then(s.bind(s,6503))},2714:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var r=s(5155);s(2115);var a=s(968),n=s(3999);function i(e){let{className:t,...s}=e;return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},3999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(2596),a=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},6503:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(5155),a=s(2115),n=s(2108),i=s(5695),d=s(1154),l=s(646),o=s(5339),c=s(8749),u=s(2657),x=s(7168),p=s(9852),m=s(2714),g=s(9026),v=s(8482);function h(){let{data:e,status:t}=(0,n.useSession)(),s=(0,i.useRouter)(),[h,f]=(0,a.useState)(!1),[b,w]=(0,a.useState)(null),[j,N]=(0,a.useState)(null),[y,k]=(0,a.useState)(""),[S,_]=(0,a.useState)(""),[C,A]=(0,a.useState)(""),[P,F]=(0,a.useState)(!1),[T,z]=(0,a.useState)(!1),[E,J]=(0,a.useState)(!1),[B,L]=(0,a.useState)(null);(0,a.useEffect)(()=>{"unauthenticated"===t&&s.push("/login")},[t,s]),(0,a.useEffect)(()=>{let e;return b&&(e=setTimeout(()=>{w(null)},3e3)),()=>{e&&clearTimeout(e)}},[b]);let O=()=>y?S?S.length<8?(L("New password must be at least 8 characters"),!1):S!==C?(L("New passwords do not match"),!1):(L(null),!0):(L("New password is required"),!1):(L("Current password is required"),!1),X=async e=>{if(e.preventDefault(),w(null),N(null),O()){f(!0);try{let e=await fetch("/api/reset-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({current_password:y,new_password:S})}),t=await e.json();e.ok?(w("Password changed successfully!"),k(""),_(""),A("")):N(t.error||"Failed to reset password")}catch(e){N("An error occurred. Please try again."),console.error("Password reset error:",e)}finally{f(!1)}}};return"loading"===t?(0,r.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,r.jsx)(d.A,{className:"h-8 w-8 animate-spin text-blue-500"})}):(0,r.jsxs)("div",{className:"container mx-auto py-10 max-w-md",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Settings"}),(0,r.jsxs)(v.Zp,{children:[(0,r.jsxs)(v.aR,{children:[(0,r.jsx)(v.ZB,{children:"Change Password"}),(0,r.jsx)(v.BT,{children:"Update your password by entering your current password and a new password"})]}),(0,r.jsx)(v.Wu,{children:(0,r.jsxs)("form",{onSubmit:X,className:"space-y-4",children:[b&&(0,r.jsxs)(g.Fc,{className:"bg-green-50 border-green-200",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)(g.XL,{className:"text-green-700",children:"Success"}),(0,r.jsx)(g.TN,{className:"text-green-600",children:b})]}),(j||B)&&(0,r.jsxs)(g.Fc,{className:"bg-red-50 border-red-200",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 text-red-500"}),(0,r.jsx)(g.XL,{className:"text-red-700",children:"Error"}),(0,r.jsx)(g.TN,{className:"text-red-600",children:j||B})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"current-password",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.p,{id:"current-password",type:P?"text":"password",value:y,onChange:e=>k(e.target.value),className:"w-full pr-10"}),(0,r.jsx)("button",{type:"button",onClick:()=>F(!P),className:"absolute inset-y-0 right-0 flex items-center pr-3",children:P?(0,r.jsx)(c.A,{className:"h-4 w-4"}):(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"new-password",children:"New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.p,{id:"new-password",type:T?"text":"password",value:S,onChange:e=>_(e.target.value),className:"w-full pr-10"}),(0,r.jsx)("button",{type:"button",onClick:()=>z(!T),className:"absolute inset-y-0 right-0 flex items-center pr-3",children:T?(0,r.jsx)(c.A,{className:"h-4 w-4"}):(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(m.J,{htmlFor:"confirm-password",children:"Confirm New Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.p,{id:"confirm-password",type:E?"text":"password",value:C,onChange:e=>A(e.target.value),className:"w-full pr-10"}),(0,r.jsx)("button",{type:"button",onClick:()=>J(!E),className:"absolute inset-y-0 right-0 flex items-center pr-3",children:E?(0,r.jsx)(c.A,{className:"h-4 w-4"}):(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,r.jsx)(x.$,{type:"submit",className:"w-full",disabled:h,children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):"Update Password"})]})})]})]})}},7168:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(5155);s(2115);var a=s(9708),n=s(2085),i=s(3999);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:n,asChild:l=!1,...o}=e,c=l?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:s,size:n,className:t})),...o})}},8482:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>c});var r=s(5155);s(2115);var a=s(3999);function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s})}function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s})}function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s})}},9026:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>d,TN:()=>o,XL:()=>l});var r=s(5155);s(2115);var a=s(2085),n=s(3999);let i=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:s}),t),...a})}function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...s})}function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...s})}},9852:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(5155);s(2115);var a=s(3999);function n(e){let{className:t,type:s,...n}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6454,568,8441,1684,7358],()=>t(805)),_N_E=e.O()}]);