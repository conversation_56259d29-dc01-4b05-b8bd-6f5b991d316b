(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3011],{1556:(e,s,t)=>{Promise.resolve().then(t.bind(t,9482))},2714:(e,s,t)=>{"use strict";t.d(s,{J:()=>n});var r=t(5155);t(2115);var a=t(968),i=t(3999);function n(e){let{className:s,...t}=e;return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}},3999:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var r=t(2596),a=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}},5784:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>h,gC:()=>x,l6:()=>c,yv:()=>o});var r=t(5155);t(2115);var a=t(5501),i=t(6474),n=t(5196),l=t(7863),d=t(3999);function c(e){let{...s}=e;return(0,r.jsx)(a.bL,{"data-slot":"select",...s})}function o(e){let{...s}=e;return(0,r.jsx)(a.WT,{"data-slot":"select-value",...s})}function u(e){let{className:s,size:t="default",children:n,...l}=e;return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...l,children:[n,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:s,children:t,position:i="popper",...n}=e;return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,...n,children:[(0,r.jsx)(v,{}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(m,{})]})})}function h(e){let{className:s,children:t,...i}=e;return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...i,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function v(e){let{className:s,...t}=e;return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,r.jsx)(l.A,{className:"size-4"})})}function m(e){let{className:s,...t}=e;return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,r.jsx)(i.A,{className:"size-4"})})}},7168:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var r=t(5155);t(2115);var a=t(9708),i=t(2085),n=t(3999);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,size:i,asChild:d=!1,...c}=e,o=d?a.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:s})),...c})}},8482:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>o});var r=t(5155);t(2115);var a=t(3999);function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function l(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...t})}function d(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...t})}function c(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...t})}function o(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",s),...t})}},9026:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>l,TN:()=>c,XL:()=>d});var r=t(5155);t(2115);var a=t(2085),i=t(3999);let n=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),s),...a})}function d(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"alert-title",className:(0,i.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",s),...t})}function c(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...t})}},9482:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>z});var r=t(5155),a=t(7168),i=t(8482),n=t(9852),l=t(2714),d=t(5784);t(2115);var c=t(704),o=t(3999);function u(e){let{className:s,...t}=e;return(0,r.jsx)(c.bL,{"data-slot":"tabs",className:(0,o.cn)("flex flex-col gap-2",s),...t})}function x(e){let{className:s,...t}=e;return(0,r.jsx)(c.B8,{"data-slot":"tabs-list",className:(0,o.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...t})}function h(e){let{className:s,...t}=e;return(0,r.jsx)(c.l9,{"data-slot":"tabs-trigger",className:(0,o.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...t})}function v(e){let{className:s,...t}=e;return(0,r.jsx)(c.UC,{"data-slot":"tabs-content",className:(0,o.cn)("flex-1 outline-none",s),...t})}var m=t(6981),f=t(5196);function g(e){let{className:s,...t}=e;return(0,r.jsx)(m.bL,{"data-slot":"checkbox",className:(0,o.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...t,children:(0,r.jsx)(m.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(f.A,{className:"size-3.5"})})})}var p=t(9663),b=t(9708);let j=(0,t(2085).F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function y(e){let{className:s,variant:t,asChild:a=!1,...i}=e,n=a?b.DX:"span";return(0,r.jsx)(n,{"data-slot":"badge",className:(0,o.cn)(j({variant:t}),s),...i})}var N=t(5339),w=t(646),k=t(9026);function z(){return(0,r.jsxs)("div",{className:"container mx-auto py-10",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"ShadCN UI Components Test"}),(0,r.jsxs)(u,{defaultValue:"cards",className:"w-full mb-10",children:[(0,r.jsxs)(x,{className:"grid w-full grid-cols-4",children:[(0,r.jsx)(h,{value:"cards",children:"Cards"}),(0,r.jsx)(h,{value:"inputs",children:"Inputs"}),(0,r.jsx)(h,{value:"buttons",children:"Buttons"}),(0,r.jsx)(h,{value:"alerts",children:"Alerts"})]}),(0,r.jsxs)(v,{value:"cards",className:"space-y-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold my-4",children:"Card Examples"}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Basic Card"}),(0,r.jsx)(i.BT,{children:"A simple card with title and description"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)("p",{children:"This is a basic card component from shadcn/ui."})}),(0,r.jsx)(i.wL,{children:(0,r.jsx)(a.$,{children:"Action"})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"User Profile"}),(0,r.jsx)(i.BT,{children:"User information display"})]}),(0,r.jsxs)(i.Wu,{className:"flex items-center space-x-4",children:[(0,r.jsxs)(p.eu,{children:[(0,r.jsx)(p.BK,{src:"https://github.com/shadcn.png",alt:"User"}),(0,r.jsx)(p.q5,{children:"CN"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"John Doe"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Product Designer"})]})]}),(0,r.jsxs)(i.wL,{className:"flex justify-between",children:[(0,r.jsx)(a.$,{variant:"outline",children:"Message"}),(0,r.jsx)(a.$,{children:"Follow"})]})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Project Status"}),(0,r.jsx)(i.BT,{children:"Current project metrics"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsx)("span",{children:"75%"})]}),(0,r.jsx)("div",{className:"w-full bg-secondary h-2 rounded-full",children:(0,r.jsx)("div",{className:"bg-primary h-2 rounded-full w-3/4"})}),(0,r.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,r.jsx)(y,{children:"In Progress"}),(0,r.jsx)(y,{variant:"outline",children:"Frontend"})]})]})}),(0,r.jsx)(i.wL,{children:(0,r.jsx)(a.$,{variant:"outline",className:"w-full",children:"View Details"})})]})]})]}),(0,r.jsxs)(v,{value:"inputs",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold my-4",children:"Input Components"}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Input Examples"}),(0,r.jsx)(i.BT,{children:"Various input components from shadcn/ui"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"username",children:"Username"}),(0,r.jsx)(n.p,{id:"username",placeholder:"Enter your username"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(n.p,{id:"email",type:"email",placeholder:"Enter your email"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"role",children:"Role"}),(0,r.jsxs)(d.l6,{children:[(0,r.jsx)(d.bq,{id:"role",children:(0,r.jsx)(d.yv,{placeholder:"Select a role"})}),(0,r.jsxs)(d.gC,{children:[(0,r.jsx)(d.eb,{value:"admin",children:"Admin"}),(0,r.jsx)(d.eb,{value:"user",children:"User"}),(0,r.jsx)(d.eb,{value:"guest",children:"Guest"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(g,{id:"notifications"}),(0,r.jsx)(l.J,{htmlFor:"notifications",children:"Email Notifications"})]})]}),(0,r.jsx)(i.wL,{children:(0,r.jsx)(a.$,{children:"Submit"})})]})]}),(0,r.jsxs)(v,{value:"buttons",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold my-4",children:"Button Variants"}),(0,r.jsxs)("div",{className:"grid gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsx)(a.$,{children:"Default"}),(0,r.jsx)(a.$,{variant:"secondary",children:"Secondary"}),(0,r.jsx)(a.$,{variant:"destructive",children:"Destructive"}),(0,r.jsx)(a.$,{variant:"outline",children:"Outline"}),(0,r.jsx)(a.$,{variant:"ghost",children:"Ghost"}),(0,r.jsx)(a.$,{variant:"link",children:"Link"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsx)(a.$,{size:"sm",children:"Small"}),(0,r.jsx)(a.$,{children:"Default"}),(0,r.jsx)(a.$,{size:"lg",children:"Large"}),(0,r.jsx)(a.$,{size:"icon",children:(0,r.jsx)(N.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsx)(a.$,{disabled:!0,children:"Disabled"}),(0,r.jsx)(a.$,{variant:"outline",disabled:!0,children:"Disabled Outline"})]})]})]}),(0,r.jsxs)(v,{value:"alerts",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold my-4",children:"Alerts"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(k.Fc,{children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),(0,r.jsx)(k.XL,{children:"Information"}),(0,r.jsx)(k.TN,{children:"This is an informational alert that provides neutral guidance."})]}),(0,r.jsxs)(k.Fc,{variant:"destructive",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),(0,r.jsx)(k.XL,{children:"Error"}),(0,r.jsx)(k.TN,{children:"There was an error processing your request. Please try again."})]}),(0,r.jsxs)(k.Fc,{className:"border-green-500 text-green-600",children:[(0,r.jsx)(w.A,{className:"h-4 w-4"}),(0,r.jsx)(k.XL,{children:"Success"}),(0,r.jsx)(k.TN,{children:"Your changes have been saved successfully."})]})]})]})]})]})}},9663:(e,s,t)=>{"use strict";t.d(s,{BK:()=>l,eu:()=>n,q5:()=>d});var r=t(5155);t(2115);var a=t(4011),i=t(3999);function n(e){let{className:s,...t}=e;return(0,r.jsx)(a.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",s),...t})}function l(e){let{className:s,...t}=e;return(0,r.jsx)(a._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full",s),...t})}function d(e){let{className:s,...t}=e;return(0,r.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",s),...t})}},9852:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var r=t(5155);t(2115);var a=t(3999);function i(e){let{className:s,type:t,...i}=e;return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,1563,8250,7665,5152,1428,7867,8441,1684,7358],()=>s(1556)),_N_E=e.O()}]);