"use client";

import React from 'react';
import Image from 'next/image';
import { useAssessment } from '@/context/AssessmentContext';
import NavigationButton from '@/components/NavigationButton';
import Footer from '@/components/Footer';
import { cn } from '@/lib/utils';
import GradientCard from '@/components/GradientCard';

export default function UnderstandYourselfPage() {
  const { assessmentData, loading, error } = useAssessment();

  const description = assessmentData?.assessment?.section_i?.general_description?.description ||
    "Loading your personalized assessment data...";

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/1.1_Understand_yourselfTM.svg"
                alt="Understand Yourself Illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[3rem] font-light text-[#3793F7] dark:text-blue-400 mb-8 md:text-4xl lg:text-[3rem]">
              1.1 Understand Yourself
            </h1>

            <p className="mb-6 leading-relaxed text-gray-900 dark:text-gray-200">
              This segment provides insights into your natural behaviours,
              strengths, and limitations, helping you recognize how you
              interact with others and respond to different situations. This
              segment also explores your communication style, emotional
              patterns, and how you may be perceived under stress, offering
              a deeper understanding of your personal dynamics.
              <br /> <br />
              Based on your responses, the report has selected general statements to provide a broad
              understanding of your behavioural style. These statements identify your basic natural behaviour.
              That is, if left on your own, these statements identify HOW YOU WOULD CHOOSE TO DO
              SOMETHING. Use the general characteristics to gain a better understanding of your natural
              behaviour.
            </p>
          </div>
        </div>

        {/* Card with Assessment Data */}
        <GradientCard color="blue"  >
          {loading ? (
            <div className="text-center p-8 text-[#3793F7] dark:text-blue-400 flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] dark:border-[rgba(255,255,255,0.1)] border-l-[#3793F7] dark:border-l-blue-400 w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading your personalized assessment...</p>
            </div>
          ) : (
            <div className="flex flex-col gap-4">
              {description.split(/(?<=\.)(?=\s)/).map((sentence: string, index: number) => (
                sentence.trim() && (
                  <div className="flex items-start leading-relaxed" key={index}>
                    <span className="text-[#3793F7] dark:text-blue-400 text-lg mr-2.5 flex-shrink-0">•</span>
                    <span className="text-gray-900 dark:text-gray-200">{sentence.trim()}</span>
                  </div>
                )
              ))}
            </div>
          )}
        </GradientCard>

        {/* Footer Text */}
        <p className="my-8 leading-relaxed max-w-[980px] mx-auto text-gray-900 dark:text-gray-200">
          Understanding your natural behaviour is the first step toward recognizing how it influences your
          strengths and areas for growth. Building on this foundation, the next section explores how these
          traits shape your approach to academics, work, and relationships, highlighting both your
          advantages and areas for improvement.
        </p>

        {/* Continue Button */}
        <NavigationButton
          text="CONTINUE"
          href="/1_2_strengths_limitations"
        />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}