"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { useAssessment } from '@/context/AssessmentContext';
import NavigationButton from '@/components/NavigationButton';
import Footer from '@/components/Footer';
import { cn } from '@/lib/utils';
import GradientCard from '@/components/GradientCard';

export default function StrengthsLimitationsPage() {
  const [activeTab, setActiveTab] = useState('STRENGTHS'); // Default to strengths tab

  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Extract strengths and limitations from assessment data
  const strengths = assessmentData?.assessment?.section_i?.strengths?.strengths || [];
  const limitations = assessmentData?.assessment?.section_i?.limitations?.limitations || [];

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">

        {/* Tab styling matching KeyCompetencies */}
        <style jsx>{`
          .tabs-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 30px 0;
            flex-wrap: wrap;
          }

          .tab {
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 500;
            margin: 0 5px;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #e0e0e0 0%, #d0d0d0 100%);
            color: #555;
            border: none;
            outline: none;
            min-width: 150px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
          }

          .tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
              rgba(0, 0, 0, 0.1) 0%,
              rgba(0, 0, 0, 0.05) 10%,
              rgba(0, 0, 0, 0.02) 20%,
              rgba(0, 0, 0, 0) 30%
            );
            border-radius: 20px;
            z-index: 0;
            pointer-events: none;
          }

          .tab-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            z-index: 1;
          }

          .tab-icon {
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
          }

          .tab.active {
            color: white;
            box-shadow: 0 3px 6px rgba(55, 147, 247, 0.3);
            position: relative;
            overflow: hidden;
          }

          .tab.active.strengths-tab {
            background-color: #3793F7;
          }

          .tab.active.limitations-tab {
            background-color: #FF7733;
            box-shadow: 0 3px 6px rgba(255, 119, 51, 0.3);
          }

          .tab.active.strengths-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
              rgba(110, 170, 228, 0.9) 0%,
              rgba(55, 147, 247, 0.8) 10%,
              rgba(55, 147, 247, 0.6) 20%,
              rgba(55, 147, 247, 0.4) 40%,
              rgba(55, 147, 247, 0.15) 60%,
              rgba(55, 147, 247, 0.05) 80%,
              rgba(55, 147, 247, 0) 90%
            );
            border-radius: 20px;
            z-index: 0;
            pointer-events: none;
          }

          .tab.active.limitations-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
              rgba(255, 150, 120, 0.9) 0%,
              rgba(255, 119, 51, 0.8) 10%,
              rgba(255, 119, 51, 0.6) 20%,
              rgba(255, 119, 51, 0.4) 40%,
              rgba(255, 119, 51, 0.15) 60%,
              rgba(255, 119, 51, 0.05) 80%,
              rgba(255, 119, 51, 0) 90%
            );
            border-radius: 20px;
            z-index: 0;
            pointer-events: none;
          }

          .tab.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            z-index: 3;
          }

          .tab.active.strengths-tab::after {
            border-top: 10px solid #3793F7;
          }

          .tab.active.limitations-tab::after {
            border-top: 10px solid #FF7733;
          }

          @media (max-width: 768px) {
            .tab {
              min-width: 120px;
              padding: 10px 15px;
              font-size: 0.9rem;
            }
          }
        `}</style>

        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                // src="/1.2_Strengths_LimitationsTM.svg"
                src="/1.2Ver2TM.png"
                alt="Strengths and Limitations Illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[3rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[3rem]">1.2 Strengths & Limitations</h1>

            <p className="mb-6 leading-relaxed">
              This section provides insights into the key strengths that shape
              your approach to academics, work, and relationships, and areas
              where you may face challenges. Your strengths highlight your
              natural abilities and behavioural tendencies that contribute to your
              success, while your limitations indicate potential areas that may
              require conscious effort to improve. Recognizing both allows you
              to leverage your strengths effectively while overcoming certain
              limitations, to foster both personal and professional growth.
            </p>
          </div>
        </div>

        {/* Tabs styled like KeyCompetencies */}
        <div className="tabs-container">
          <button
            className={`tab ${activeTab === 'STRENGTHS' ? 'active strengths-tab' : ''}`}
            onClick={() => setActiveTab('STRENGTHS')}
          >
            <div className="tab-content">
              <div className="tab-icon">+</div>
              <span>STRENGTHS</span>
            </div>
          </button>
          <button
            className={`tab ${activeTab === 'LIMITATIONS' ? 'active limitations-tab' : ''}`}
            onClick={() => setActiveTab('LIMITATIONS')}
          >
            <div className="tab-content">
              <div className="tab-icon">−</div>
              <span>LIMITATIONS</span>
            </div>
          </button>
        </div>

        {/* Tab Content */}
        <div className="mt-5">
          {activeTab === 'STRENGTHS' ? (
            loading ? (
              <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
                <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
                <p>Loading your strengths...</p>
              </div>
            ) : (
              <div className="space-y-5">
                {strengths.map((strength: any, index: number) => (
                  <GradientCard key={index} color="blue">
                    <h3 className="text-lg font-medium text-[#3793F7] mb-3">{strength.title || strength.name}</h3>
                    <p className="text-[#333] leading-relaxed">{strength.description}</p>
                  </GradientCard>
                ))}
                {strengths.length === 0 && (
                  <GradientCard color="blue">
                    <p className="text-[#333] leading-relaxed">No strengths data available at this time.</p>
                  </GradientCard>
                )}
              </div>
            )
          ) : (
            loading ? (
              <div className="text-center p-8 text-[#FF7733] flex flex-col items-center justify-center">
                <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#FF7733] w-9 h-9 rounded-full animate-spin mb-4"></div>
                <p>Loading your limitations...</p>
              </div>
            ) : (
              <div className="space-y-5">
                {limitations.map((limitation: any, index: number) => (
                  <GradientCard key={index} color="orange">
                    <h3 className="text-lg font-medium text-[#FF7733] mb-3">{limitation.title || limitation.name}</h3>
                    <p className="text-[#333] leading-relaxed">{limitation.description}</p>
                  </GradientCard>
                ))}
                {limitations.length === 0 && (
                  <GradientCard color="orange">
                    <p className="text-[#333] leading-relaxed">No limitations data available at this time.</p>
                  </GradientCard>
                )}
              </div>
            )
          )}
        </div>

        {/* Footer Text */}
        <p className="mb-4">Recognizing your strengths and limitations provides a clear picture of how you navigate academic,
          professional, and personal challenges. Building on this, the next section Your Natural
          Communication Style examines how you communicate and engage with others, offering insights
          to improve your interactions and strengthen your relationships.</p>

        {/* Continue Button */}
        <NavigationButton
          text="CONTINUE"
          href="/1_3_communication_styles"
        />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}