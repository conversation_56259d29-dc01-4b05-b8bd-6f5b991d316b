"use client";

import React from 'react';
import Image from 'next/image';
import Footer from '@/components/Footer';
import NavigationButton from '@/components/NavigationButton';
import GradientCard from '@/components/GradientCard';
import { useAssessment } from '@/context/AssessmentContext';
import { cn } from '@/lib/utils';

export default function CommunicationStylesPage() {
  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Get the communication style from assessment data
  const communicationStyle = assessmentData?.assessment?.section_i?.communication?.style || '';

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1]">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/1.3_Your_natural communication_styleTM.svg"
                alt="Communication Styles Illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[3rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[3rem]">1.3 Communication Styles</h1>

            <p className="mb-6 leading-relaxed">
              This section explores how you naturally express
              yourself, engage in conversations, and interpret
              information in various settings. It highlights whether
              your communication tends to be direct or diplomatic,
              formal or informal, and whether you prefer detailed
              discussions or concise exchanges. Understanding
              your communication style can help you build stronger
              relationships, enhance collaboration, and navigate
              personal and professional interactions more
              effectively.

            </p>
          </div>
        </div>

        {/* Card with Communication Style */}
        <GradientCard color="blue"  >
          {loading ? (
            <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading your communication style...</p>
            </div>
          ) : (
            <div className="flex flex-col gap-4">
              {communicationStyle.split(/(?<=\.)(?=\s)/).map((sentence: string, index: number) => (
                sentence.trim() && (
                  <div className="flex items-start leading-relaxed" key={index}>
                    <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                    <span>{sentence.trim()}</span>
                  </div>
                )
              ))}
            </div>
          )}
        </GradientCard>

        {/* Footer Text */}
        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
          Understanding your communication style provides valuable insights into how you interact with
          others and share information. Building on this, the next section delves into your emotional
          patterns and stress triggers, helping you manage your feelings effectively and maintain balance
          in various situations.

        </p>

        {/* Continue Button */}
        <NavigationButton
          text="CONTINUE"
          href="/1_4_emotional_patterns"
        />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}
