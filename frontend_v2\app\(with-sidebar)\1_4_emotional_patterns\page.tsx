"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { useAssessment } from '@/context/AssessmentContext';
import NavigationButton from '@/components/NavigationButton';
import Footer from '@/components/Footer';
import { cn } from '@/lib/utils';
import GradientCard from '@/components/GradientCard';

export default function EmotionalPatternsPage() {
  const [activeTab, setActiveTab] = useState('EMOTIONS'); // Default to emotions tab

  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Get emotions and stressors from assessment data
  const emotions = assessmentData?.assessment?.section_i?.key_emotions?.emotions || [];
  const stressors = assessmentData?.assessment?.section_i?.stressors || [];

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">

        {/* Tab styling matching StrengthsLimitations */}
        <style jsx>{`
          .tabs-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 30px 0;
            flex-wrap: wrap;
          }

          .tab {
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 500;
            margin: 0 5px;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #e0e0e0 0%, #d0d0d0 100%);
            color: #555;
            border: none;
            outline: none;
            min-width: 150px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
          }

          .tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
              rgba(0, 0, 0, 0.1) 0%,
              rgba(0, 0, 0, 0.05) 10%,
              rgba(0, 0, 0, 0.02) 20%,
              rgba(0, 0, 0, 0) 30%
            );
            border-radius: 20px;
            z-index: 0;
            pointer-events: none;
          }

          .tab-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            z-index: 1;
          }

          .tab-icon {
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
          }

          .tab.active {
            color: white;
            box-shadow: 0 3px 6px rgba(55, 147, 247, 0.3);
            position: relative;
            overflow: hidden;
          }

          .tab.active.emotions-tab {
            background-color: #3793F7;
          }

          .tab.active.stressors-tab {
            background-color: #FF7733;
            box-shadow: 0 3px 6px rgba(255, 119, 51, 0.3);
          }

          .tab.active.emotions-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
              rgba(110, 170, 228, 0.9) 0%,
              rgba(55, 147, 247, 0.8) 10%,
              rgba(55, 147, 247, 0.6) 20%,
              rgba(55, 147, 247, 0.4) 40%,
              rgba(55, 147, 247, 0.15) 60%,
              rgba(55, 147, 247, 0.05) 80%,
              rgba(55, 147, 247, 0) 90%
            );
            border-radius: 20px;
            z-index: 0;
            pointer-events: none;
          }

          .tab.active.stressors-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
              rgba(255, 150, 120, 0.9) 0%,
              rgba(255, 119, 51, 0.8) 10%,
              rgba(255, 119, 51, 0.6) 20%,
              rgba(255, 119, 51, 0.4) 40%,
              rgba(255, 119, 51, 0.15) 60%,
              rgba(255, 119, 51, 0.05) 80%,
              rgba(255, 119, 51, 0) 90%
            );
            border-radius: 20px;
            z-index: 0;
            pointer-events: none;
          }

          .tab.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            z-index: 3;
          }

          .tab.active.emotions-tab::after {
            border-top: 10px solid #3793F7;
          }

          .tab.active.stressors-tab::after {
            border-top: 10px solid #FF7733;
          }

          @media (max-width: 768px) {
            .tab {
              min-width: 120px;
              padding: 10px 15px;
              font-size: 0.9rem;
            }
          }
        `}</style>

        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                // src="/1.4_Your_emotional_patterns_responsesTM.svg"
                src="/1.4Ver2TM.png"
                alt="Emotional Patterns Illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[3rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[3rem]">1.4 Your Emotional Patterns And Responses</h1>

            <p className="mb-6 leading-relaxed">
              This section explores your emotions, including how you
              experience, regulate, and express feelings in different
              situations. Additionally, it identifies key stress factors
              that may influence your well-being, such as pressure from
              deadlines, ambiguity, high expectations, etc.
              Understanding these triggers can help you develop
              strategies for emotional balance and maintaining a
              positive mindset in both personal and professional
              settings.
            </p>
          </div>
        </div>

        {/* Tabs styled like StrengthsLimitations */}
        <div className="tabs-container">
          <button
            className={`tab ${activeTab === 'EMOTIONS' ? 'active emotions-tab' : ''}`}
            onClick={() => setActiveTab('EMOTIONS')}
          >
            <div className="tab-content">
              <svg className="tab-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7Z" />
              </svg>
              <span>EMOTIONS</span>
            </div>
          </button>
          <button
            className={`tab ${activeTab === 'STRESSORS' ? 'active stressors-tab' : ''}`}
            onClick={() => setActiveTab('STRESSORS')}
          >
            <div className="tab-content">
              <svg className="tab-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
              </svg>
              <span>STRESSORS</span>
            </div>
          </button>
        </div>

        {/* Tab Content */}
        <div className="mt-5">
           {activeTab === 'EMOTIONS' ? (
            /* Emotions Content */
            loading ? (
              <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
                <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
                <p>Loading your emotional patterns...</p>
              </div>
            ) : (
              <div className="space-y-5">
                {emotions.map((emotion: any, index: number) => (
                  <GradientCard key={index} color="blue">
                    <h3 className="text-lg font-medium text-[#3793F7] mb-3">{emotion.title || emotion.name}</h3>
                    <p className="text-[#333] leading-relaxed">{emotion.description}</p>
                  </GradientCard>
                ))}
                {emotions.length === 0 && (
                  <GradientCard color="blue">
                    <p className="text-[#333] leading-relaxed">No emotional patterns data available at this time.</p>
                  </GradientCard>
                )}
              </div>
            )
          ) : (
            /* Stressors Content */
            loading ? (
              <div className="text-center p-8 text-[#FF7733] flex flex-col items-center justify-center">
                <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#FF7733] w-9 h-9 rounded-full animate-spin mb-4"></div>
                <p>Loading your stressors...</p>
              </div>
            ) : (
              <div className="space-y-5">
                {stressors.map((stressor: any, index: number) => (
                  <GradientCard key={index} color="orange">
                    <h3 className="text-lg font-medium text-[#FF7733] mb-3">{stressor.title || stressor.name}</h3>
                    <p className="text-[#333] leading-relaxed">{stressor.description}</p>
                  </GradientCard>
                ))}
                {stressors.length === 0 && (
                  <GradientCard color="orange">
                    <p className="text-[#333] leading-relaxed">No stressors data available at this time.</p>
                  </GradientCard>
                )}
              </div>
            )
          )}
        </div>
        {/* Footer Text */}
        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
          Recognizing your emotional patterns and stress triggers is essential for maintaining balance and
          resilience. Building on this, the next section offers insights into how others may perceive your
          behaviour under stress, helping you project a more confident, calm, and professional image.
        </p>

        {/* Continue Button */}
        <NavigationButton
          text="CONTINUE"
          href="/1_5_how_others_percive_you"
        />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}