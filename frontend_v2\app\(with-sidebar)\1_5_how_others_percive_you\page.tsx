"use client";

import React from 'react';
import Image from 'next/image';
import Footer from '@/components/Footer';
import NavigationButton from '@/components/NavigationButton';
import GradientCard from '@/components/GradientCard';
import { useAssessment } from '@/context/AssessmentContext';
import { cn } from '@/lib/utils';

export default function HowOthersPerceiveYouPage() {
  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Get the stress perception from assessment data
  const stressPerception = assessmentData?.assessment?.section_i?.stress_perception?.perception || '';

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1]">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                // src="/1.5._How_others_percieve_your_under_stressTM.svg"
                src="/1.5Ver2TM.png"
                alt="How Others Perceive You Illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[3rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[3rem]">1.5 How Others Perceive You</h1>

            <p className="mb-6 leading-relaxed">
              This section provides information on how others may
              see your behaviour, under certain stressful conditions.
              Understanding this section, will help you present
              yourself in a more confident, calm and professional
              manner.
            </p>
          </div>
        </div>

        {/* Card with Perception Content */}
        <GradientCard color="blue"  >
          {loading ? (
            <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading your perception data...</p>
            </div>
          ) : (
            <div className="flex flex-col gap-4">
              {stressPerception.split(/(?<=\.)(?=\s)/).map((sentence: string, index: number) => (
                sentence.trim() && (
                  <div className="flex items-start leading-relaxed" key={index}>
                    <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                    <span>{sentence.trim()}</span>
                  </div>
                )
              ))}
            </div>
          )}
        </GradientCard>

        {/* Footer Text */}
        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
          By understanding your natural behaviours, communication style, emotional patterns, and how
          others perceive you under stress, you gain valuable self-awareness. This foundation empowers
          you to navigate challenges, build stronger relationships, and present yourself effectively in both
          personal and professional settings.
          <br /><br />
          With a clear understanding of yourself, the next segment explores how your strengths,
          limitations, and stressors influence your academic and professional life. This insight will help you
          identify where you excel, areas for growth, and how to succeed in various environments.
        </p>

        {/* Continue Button */}
        <NavigationButton
          text="CONTINUE"
          href="/2_1_making_impact"
        />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
} 