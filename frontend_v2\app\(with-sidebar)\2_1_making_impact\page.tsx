"use client";

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Footer from '@/components/Footer';
import CommonButton from '@/components/CommonButton';

export default function MakingImpactPage() {
  // Split the content text exactly like in 1_1_understand_yourself
  const impactContent = "Your methodical approach and attention to detail make you particularly effective in situations requiring careful analysis and thorough planning. You excel at creating systems, establishing procedures, and ensuring quality standards are met. In academic and professional settings, you're likely to be valued for your reliability, consistency, and commitment to excellence.";

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1]">
      {/* Apply styled-jsx for the original styling */}
      <style jsx>{`
        /* Card styling */
        .card {
          border-radius: 16px;
          padding: 24px;
          margin-bottom: 24px;
          position: relative;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          background-color: white;
          position: relative;
          overflow: hidden;
          max-width: 980px;
          margin-left: auto;
          margin-right: auto;
        }

        .card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            rgba(55, 147, 247, 0.7) 0%,
            rgba(55, 147, 247, 0.2) 10%,
            rgba(55, 147, 247, 0.05) 20%,
            rgba(55, 147, 247, 0) 30%
          );
          border-radius: 16px;
          z-index: 0;
          pointer-events: none;
        }

        .card-content {
          position: relative;
          z-index: 1;
        }
        
        .info-icon {
          position: absolute;
          top: 20px;
          right: 20px;
          width: 24px;
          height: 24px;
          background-color: #3793F7;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-style: italic;
          font-weight: bold;
          z-index: 2;
        }
        
        /* Bullet list styling */
        .bullet-list {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .bullet-item {
          display: flex;
          align-items: flex-start;
          line-height: 1.5;
        }

        .bullet {
          color: #3793F7;
          font-size: 18px;
          margin-right: 10px;
          flex-shrink: 0;
        }
      `}</style>

      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/2.1TM.svg"
                alt="Making Impact Illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[3rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[3rem]">2.1 How You can make an Impact</h1>

            <p className="mb-6 leading-relaxed">
              This segment examines how your strengths, limitations,
              and stressors influence your academic and professional
              performance. This segment also highlights key
              competencies for workplace success and identifies the
              work environment where you are most likely to thrive.
            </p>
          </div>
        </div>

        {/* Card with Impact Content */}
        {/* <div className="card">
          <div className="info-icon">i</div>
          <div className="card-content">
            <div className="bullet-list">
              {impactContent.split(/(?<=\.)(?=\s)/).map((sentence, index) => (
                sentence.trim() && (
                  <div className="bullet-item" key={index}>
                    <span className="bullet">•</span>
                    <span>{sentence.trim()}</span>
                  </div>
                )
              ))}
            </div>
          </div>
        </div> */}

        {/* Footer Text */}
        {/* <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
          Understanding how you make an impact provides valuable insight into your effectiveness in various contexts.
          Next, let's explore how these tendencies specifically influence your academic performance and learning approach.
        </p> */}

        {/* Continue Button */}
        <div className="flex justify-center w-full my-8">
          <Link href="/2_2_academic_impact">
            <Button
              variant="outline"
              className="rounded-full border-2 border-gray-800 text-gray-900 hover:bg-gray-100 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer"
              style={{ boxShadow: '0 2px 8px rgba(55,147,247,0.10)' }}
            >
              Continue
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-2 group-hover:translate-x-1 transition-transform duration-150"
              >
                <path d="M5 12h14" />
                <path d="M12 5l7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}
