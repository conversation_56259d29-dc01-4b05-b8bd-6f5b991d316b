"use client";

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Footer from '@/components/Footer';
import CommonButton from '@/components/CommonButton';
import GradientCard from '@/components/GradientCard';
import { useAssessment } from '@/context/AssessmentContext';

export default function AcademicImpactPage() {
  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Get study manifestations data from assessment data
  const enablers = assessmentData?.assessment?.section_i?.study_manifestations?.enablers || [];
  const derailers = assessmentData?.assessment?.section_i?.study_manifestations?.derailers || [];
  const improvementSteps = assessmentData?.assessment?.section_i?.study_manifestations?.improvement_steps || [];
  // Get impact strategies for academic settings
  const academicImpactStrategies = assessmentData?.assessment?.section_ii?.impact_strategies?.academic || [];

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1]">
      {/* Apply styled-jsx for the original styling */}
      <style jsx>{`
        .card {
          border-radius: 16px;
          padding: 24px;
          margin-bottom: 24px;
          position: relative;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          background-color: white;
          position: relative;
          overflow: hidden;
          max-width: 980px;
          margin-left: auto;
          margin-right: auto;
        }

        .enablers-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            rgba(55, 147, 247, 0.7) 0%,
            rgba(55, 147, 247, 0.2) 10%,
            rgba(55, 147, 247, 0.05) 20%,
            rgba(55, 147, 247, 0) 30%
          );
          border-radius: 16px;
          z-index: 0;
          pointer-events: none;
        }

        .derailers-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            rgba(255, 152, 0, 0.7) 0%,
            rgba(255, 152, 0, 0.2) 10%,
            rgba(255, 152, 0, 0.05) 20%,
            rgba(255, 152, 0, 0) 30%
          );
          border-radius: 16px;
          z-index: 0;
          pointer-events: none;
        }

        .steps-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            rgba(3, 169, 244, 0.7) 0%,
            rgba(3, 169, 244, 0.2) 10%,
            rgba(3, 169, 244, 0.05) 20%,
            rgba(3, 169, 244, 0) 30%
          );
          border-radius: 16px;
          z-index: 0;
          pointer-events: none;
        }

        .high-impact-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            rgba(158, 158, 158, 0.5) 0%,
            rgba(158, 158, 158, 0.15) 10%,
            rgba(158, 158, 158, 0.05) 20%,
            rgba(158, 158, 158, 0) 30%
          );
          border-radius: 16px;
          z-index: 0;
          pointer-events: none;
        }

        .card-content {
          position: relative;
          z-index: 1;
        }

        .bullet-list {
          list-style-type: none;
          padding-left: 0;
        }
        
        .bullet-list li {
          position: relative;
          padding-left: 20px;
          margin-bottom: 12px;
          line-height: 1.5;
        }

        .bullet-list li::before {
          content: "•";
          position: absolute;
          left: 0;
          color: #3793F7;
          font-weight: bold;
        }

        .bullet-item {
          display: flex;
          margin-bottom: 12px;
          line-height: 1.5;
        }

        .numbered-list {
          list-style-type: none;
          padding-left: 0;
          counter-reset: item;
        }

        .numbered-list li {
          position: relative;
          padding-left: 30px;
          margin-bottom: 12px;
          line-height: 1.5;
          counter-increment: item;
        }

        .numbered-list li::before {
          content: counter(item) ".";
          position: absolute;
          left: 0;
          color: #3793F7;
          font-weight: bold;
        }

        .card-title {
          font-size: 1.2rem;
          font-weight: 500;
          margin-bottom: 16px;
          color: #333;
        }

        .card-description {
          line-height: 1.5;
        }

        .footer-text {
          margin: 32px 0;
          line-height: 1.6;
          max-width: 980px;
          margin-left: auto;
          margin-right: auto;
        }

        .loading-spinner {
          text-align: center;
          padding: 2rem;
          color: #3793F7;
        }

        .section-title {
          font-size: 1.5rem;
          font-weight: 400;
          color: #3793F7;
          margin: 40px 0 20px;
          text-align: left;
        }
      `}</style>

      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/2.2Ver2TM.png"
                alt="Academic Impact Illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[2.5rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[2.5rem]">2.2 Academic Impact</h1>

            <p className="mb-6 leading-relaxed">
              Your academic performance is shaped by a
              combination of your strengths, limitations and stress
              triggers. While strengths, help one excel in subjects or
              areas that align with their natural abilities, certain
              limitations may pose challenges in specific learning
              environments. Stressors, can influence your ability to
              focus and perform at your best. Recognizing these
              factors helps you use your strengths, improve
              limitations, and manage stress for a better academic
              experience.
            </p>
          </div>
        </div>

        {/* Study Enablers */}
        <h2 className="section-title">Manifestation in Studies</h2>

        <GradientCard color="blue"  >
          {loading ? (
            <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading...</p>
            </div>
          ) : (
            <>
              <h3 className="card-title">Enablers in Studies</h3>
              <ul className="bullet-list">
                {enablers.map((enabler: string, index: number) => (
                  <li key={index}>{enabler}</li>
                ))}
              </ul>
            </>
          )}
        </GradientCard>

        <h2 className="section-title">Derailers in Studies</h2>

        <GradientCard color="orange"  >
          {loading ? (
            <div className="text-center p-8 text-[#FFA800] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#FFA800] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading...</p>
            </div>
          ) : (
            <>
              <h3 className="card-title">Derailers in Studies</h3>
              <ul className="bullet-list">
                {derailers.map((derailer: string, index: number) => (
                  <li key={index}>{derailer}</li>
                ))}
              </ul>
            </>
          )}
        </GradientCard>

        <h2 className="section-title">Improvement Steps</h2>

        <GradientCard color="blue"  >
          {loading ? (
            <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading...</p>
            </div>
          ) : (
            <>
              <h3 className="card-title">Steps to Overcome Limitations in Studies</h3>
              <ol className="numbered-list">
                {improvementSteps.map((step: string, index: number) => (
                  <li key={index}>{step}</li>
                ))}
              </ol>
            </>
          )}
        </GradientCard>

        <h2 className="section-title">How To Make A High Impact in Academic Settings</h2>

        <GradientCard color="blue"  >
          {loading ? (
            <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading...</p>
            </div>
          ) : (
            <ul className="bullet-list">
              {academicImpactStrategies.map((strategy: string, index: number) => (
                <li key={index}>{strategy}</li>
              ))}
            </ul>
          )}
        </GradientCard>

        {/* Footer Text */}
        <p className="footer-text">
          Just as your strengths, limitations, and stressors shape your academic performance, they also play
          a crucial role in your professional life. By understanding these factors, you can enhance your
          effectiveness at work, navigate challenges, and maintain a balanced approach to workplace
          demands.
        </p>

        {/* Continue Button */}
        <div className="flex justify-center w-full my-8">
          <Link href="/2_3_professional_impact">
            <Button
              variant="outline"
              className="rounded-full border-2 border-gray-800 text-gray-900 hover:bg-gray-100 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer"
              style={{ boxShadow: '0 2px 8px rgba(55,147,247,0.10)' }}
            >
              Continue
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-2 group-hover:translate-x-1 transition-transform duration-150"
              >
                <path d="M5 12h14" />
                <path d="M12 5l7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}
