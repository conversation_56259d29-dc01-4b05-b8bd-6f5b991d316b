"use client";

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Footer from '@/components/Footer';
import CommonButton from '@/components/CommonButton';
import GradientCard from '@/components/GradientCard';
import { useAssessment } from '@/context/AssessmentContext';

export default function ProfessionalImpactPage() {
  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Get workplace manifestations data from assessment data
  const workplaceEnablers = assessmentData?.assessment?.section_ii?.workplace_manifestations?.enablers || [];
  const workplaceDerailers = assessmentData?.assessment?.section_ii?.workplace_manifestations?.derailers || [];
  // Get professional impact strategies
  const professionalImpactStrategies = assessmentData?.assessment?.section_ii?.impact_strategies?.professional || [];

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1]">
      <style jsx>{`
        .professional-impact-container {
          max-width: 1200px;
          margin: -40px auto;
          padding: 30px 20px 100px;
          position: relative;
          background-color: #f1f1f1;
        }

        .main-content {
          padding: 30px 40px;
          width: 100%;
          max-width: 1100px;
          margin: 0 auto;
          position: relative;
        }

        /* Repositioned layout for title and image */
        .header-section {
          display: flex;
          position: relative;
          margin-bottom: 40px;
        }

        .image-column {
          flex: 0 0 400px;
          position: relative;
        }

        .image-wrapper {
          position: relative;
          width: 100%;
          height: 320px;
          top: -30px; /* Move image up */
        }

        .content-section {
          flex: 1;
          padding-left: 40px;
        }

        .page-title {
          font-size: 2.5rem;
          font-weight: 300;
          color: #3793F7;
          margin-bottom: 30px;
          line-height: 1.2;
        }

        .text-description {
          margin-bottom: 24px;
          line-height: 1.6;
        }

        .section-title {
          font-size: 1.5rem;
          font-weight: 400;
          color: #3793F7;
          margin: 40px 0 20px;
          text-align: left;
        }

        .card {
          border-radius: 16px;
          padding: 24px;
          margin-bottom: 24px;
          position: relative;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          background-color: white;
          position: relative;
          overflow: hidden;
          max-width: 980px;
          margin-left: auto;
          margin-right: auto;
        }

        .enablers-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            rgba(55, 147, 247, 0.7) 0%,
            rgba(55, 147, 247, 0.2) 10%,
            rgba(55, 147, 247, 0.05) 20%,
            rgba(55, 147, 247, 0) 30%
          );
          border-radius: 16px;
          z-index: 0;
          pointer-events: none;
        }

        .derailers-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            rgba(255, 152, 0, 0.7) 0%,
            rgba(255, 152, 0, 0.2) 10%,
            rgba(255, 152, 0, 0.05) 20%,
            rgba(255, 152, 0, 0) 30%
          );
          border-radius: 16px;
          z-index: 0;
          pointer-events: none;
        }

        .high-impact-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            rgba(3, 169, 244, 0.7) 0%,
            rgba(3, 169, 244, 0.2) 10%,
            rgba(3, 169, 244, 0.05) 20%,
            rgba(3, 169, 244, 0) 30%
          );
          border-radius: 16px;
          z-index: 0;
          pointer-events: none;
        }

        .card-title {
          font-size: 1.2rem;
          font-weight: 500;
          margin-bottom: 16px;
          color: #333;
        }

        .card-content {
          position: relative;
          z-index: 1;
        }

        .bullet-list {
          list-style-type: none;
          padding-left: 0;
        }

        .bullet-list li {
          position: relative;
          padding-left: 20px;
          margin-bottom: 12px;
          line-height: 1.5;
        }

        .bullet-list li::before {
          content: "•";
          position: absolute;
          left: 0;
          color: #3793F7;
          font-weight: bold;
        }

        .footer-text {
          margin: 32px 0;
          line-height: 1.6;
          max-width: 980px;
          margin-left: auto;
          margin-right: auto;
        }

        /* Loading indicator */
        .loading-indicator {
          text-align: center;
          padding: 2rem;
          color: #3793F7;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }

        .spinner {
          border: 4px solid rgba(0, 0, 0, 0.1);
          width: 36px;
          height: 36px;
          border-radius: 50%;
          border-left-color: #3793F7;
          animation: spin 1s linear infinite;
          margin-bottom: 1rem;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* For smaller screens, make content columns stack */
        @media (max-width: 768px) {
          .header-section {
            flex-direction: column;
          }

          .image-column {
            width: 100%;
            max-width: 100%;
          }

          .image-wrapper {
            top: 0;
            margin-bottom: 20px;
          }

          .content-section {
            padding-left: 0;
          }

          .main-content {
            padding: 20px;
          }
        }
      `}</style>

      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Restructured layout - Title now to the right of the image */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/2.3TM.svg"
                alt="Professional impact illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[2.5rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[2.5rem]">2.3 Professional Impact</h1>

            <p className="mb-6 leading-relaxed">
              Understanding how your strengths, limitations, and stress
              factors interact allows you to maximize your strengths,
              address areas for growth, and develop strategies to manage
              workplace stress, ensuring greater productivity and job
              satisfaction.

            </p>
          </div>
        </div>

        <h2 className="section-title">Manifestation In Future Workplace</h2>

        <GradientCard color="blue"  >
          {loading ? (
            <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading your personalized assessment...</p>
            </div>
          ) : (
            <>
              <h3 className="card-title">Enablers in Workplace</h3>
              <ul className="bullet-list">
                {workplaceEnablers.map((enabler: string, index: number) => (
                  <li key={index}>{enabler}</li>
                ))}
              </ul>
            </>
          )}
        </GradientCard>

        <GradientCard color="orange"  >
          {loading ? (
            <div className="text-center p-8 text-[#FFA800] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#FFA800] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading your personalized assessment...</p>
            </div>
          ) : (
            <>
              <h3 className="card-title">Derailers in Workplace</h3>
              <ul className="bullet-list">
                {workplaceDerailers.map((derailer: string, index: number) => (
                  <li key={index}>{derailer}</li>
                ))}
              </ul>
            </>
          )}
        </GradientCard>

        <h2 className="section-title">How To Make A High Impact In Professional Work Settings</h2>

        <GradientCard color="blue"  >
          {loading ? (
            <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading your personalized assessment...</p>
            </div>
          ) : (
            <ul className="bullet-list">
              {professionalImpactStrategies.map((strategy: string, index: number) => (
                <li key={index}>{strategy}</li>
              ))}
            </ul>
          )}
        </GradientCard>

        {/* Footer Text */}
        <p className="footer-text">
          Recognizing how your strengths, limitations, and stress factors affect your work is the first step
          toward improving your professional effectiveness. Building on this, the next section assesses key
          workplace competencies, offering insights to enhance your performance and support your career
          growth.
        </p>

        {/* Continue Button */}
        <div className="flex justify-center w-full my-8">
          <Link href="/2_4_key_competencies">
            <Button
              variant="outline"
              className="rounded-full border-2 border-gray-800 text-gray-900 hover:bg-gray-100 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer"
              style={{ boxShadow: '0 2px 8px rgba(55,147,247,0.10)' }}
            >
              Continue
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-2 group-hover:translate-x-1 transition-transform duration-150"
              >
                <path d="M5 12h14" />
                <path d="M12 5l7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}
