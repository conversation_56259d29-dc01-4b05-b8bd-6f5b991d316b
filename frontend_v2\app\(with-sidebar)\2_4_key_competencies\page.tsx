"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { useAssessment } from '@/context/AssessmentContext';
import Footer from '@/components/Footer';
import { cn } from '@/lib/utils';

export default function KeyCompetenciesPage() {
  const [activeTab, setActiveTab] = useState('NETWORKING');

  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Get competency assessment data from assessment context
  const competencyAssessment = assessmentData?.assessment?.section_ii?.competency_assessment || null;

  // Get data for each competency
  const networkingData = competencyAssessment?.networking || { challenges: [], improvements: [] };
  const teamworkData = competencyAssessment?.teamwork || { challenges: [], improvements: [] };
  const conflictHandlingData = competencyAssessment?.conflict_handling || { challenges: [], improvements: [] };
  const timeManagementData = competencyAssessment?.time_management || { challenges: [], improvements: [] };

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  // Loading indicator 
  if (loading) {
    return (
      <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
        <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
        <p>Loading your competency assessment...</p>
      </div>
    );
  }

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">

        {/* Only keep the essential tab styling in CSS */}
        <style jsx>{`
          .tabs-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 30px 0;
            flex-wrap: wrap;
          }

          .tab {
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 500;
            margin: 0 5px;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #e0e0e0 0%, #d0d0d0 100%);
            color: #555;
            border: none;
            outline: none;
            min-width: 150px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
          }

          .tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
              rgba(0, 0, 0, 0.1) 0%,
              rgba(0, 0, 0, 0.05) 10%,
              rgba(0, 0, 0, 0.02) 20%,
              rgba(0, 0, 0, 0) 30%
            );
            border-radius: 20px;
            z-index: 0;
            pointer-events: none;
          }

          .tab-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            z-index: 1;
          }

          .tab-icon {
            width: 18px;
            height: 18px;
          }

          .tab.active {
            background-color: #3793F7;
            color: white;
            box-shadow: 0 3px 6px rgba(55, 147, 247, 0.3);
            position: relative;
            overflow: hidden;
          }

          .tab.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
              rgba(110, 170, 228, 0.9) 0%,
              rgba(55, 147, 247, 0.7) 10%,
              rgba(55, 147, 247, 0.4) 20%,
              rgba(55, 147, 247, 0.2) 40%,
              rgba(55, 147, 247, 0.05) 60%,
              rgba(55, 147, 247, 0) 80%
            );
            border-radius: 20px;
            z-index: 0;
            pointer-events: none;
          }

          .tab.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #3793F7;
            z-index: 3;
          }

          @media (max-width: 768px) {
            .tab {
              min-width: 120px;
              padding: 10px 15px;
              font-size: 0.9rem;
            }
          }
        `}</style>

        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/2.4Ver2TM.png"
                alt="Workplace competencies illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[2.5rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[2.5rem]">2.4 Key Competencies For<br />Workplace Success</h1>

            <p className="mb-6 leading-relaxed">
              This section evaluates your proficiency in essential
              workplace competencies that influence your
              professional effectiveness. Strengthening these
              competencies can enhance your workplace
              performance, career progression, and overall
              success.
            </p>
          </div>
        </div>

        {/* Tabs */}
        <div className="tabs-container">
          <button
            className={`tab ${activeTab === 'NETWORKING' ? 'active' : ''}`}
            onClick={() => handleTabClick('NETWORKING')}
          >
            <div className="tab-content">
              <svg className="tab-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M16 22h2a2 2 0 0 0 2-2v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2a2 2 0 0 0 2 2h2"></path>
                <circle cx="12" cy="9" r="5"></circle>
              </svg>
              <span>NETWORKING</span>
            </div>
          </button>
          <button
            className={`tab ${activeTab === 'TEAMWORK' ? 'active' : ''}`}
            onClick={() => handleTabClick('TEAMWORK')}
          >
            <div className="tab-content">
              <svg className="tab-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
              <span>TEAMWORK</span>
            </div>
          </button>
          <button
            className={`tab ${activeTab === 'CONFLICT HANDLING' ? 'active' : ''}`}
            onClick={() => handleTabClick('CONFLICT HANDLING')}
          >
            <div className="tab-content">
              <svg className="tab-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <span>CONFLICT HANDLING</span>
            </div>
          </button>
          <button
            className={`tab ${activeTab === 'TIME MANAGEMENT' ? 'active' : ''}`}
            onClick={() => handleTabClick('TIME MANAGEMENT')}
          >
            <div className="tab-content">
              <svg className="tab-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <span>TIME MANAGEMENT</span>
            </div>
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === 'NETWORKING' && (
          <>
            {/* Challenges Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(55,147,247,0.7)] via-[rgba(55,147,247,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Challenges and Areas for Improvement</h3>
                <div className="flex flex-col gap-3">
                  {networkingData.challenges && networkingData.challenges.length > 0 ? (
                    networkingData.challenges.map((challenge: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{challenge}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific networking challenges identified.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Improvement Actions Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(255,152,0,0.7)] via-[rgba(255,152,0,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Improvement Actions</h3>
                <div className="flex flex-col gap-3">
                  {networkingData.improvements && networkingData.improvements.length > 0 ? (
                    networkingData.improvements.map((improvement: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{improvement}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific improvement actions available at this time.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

        {activeTab === 'TEAMWORK' && (
          <>
            {/* Challenges Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(55,147,247,0.7)] via-[rgba(55,147,247,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Challenges and Areas for Improvement</h3>
                <div className="flex flex-col gap-3">
                  {teamworkData.challenges && teamworkData.challenges.length > 0 ? (
                    teamworkData.challenges.map((challenge: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{challenge}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific teamwork challenges identified.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Improvement Actions Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(255,152,0,0.7)] via-[rgba(255,152,0,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Improvement Actions</h3>
                <div className="flex flex-col gap-3">
                  {teamworkData.improvements && teamworkData.improvements.length > 0 ? (
                    teamworkData.improvements.map((improvement: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{improvement}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific improvement actions available at this time.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

        {activeTab === 'CONFLICT HANDLING' && (
          <>
            {/* Challenges Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(55,147,247,0.7)] via-[rgba(55,147,247,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Challenges and Areas for Improvement</h3>
                <div className="flex flex-col gap-3">
                  {conflictHandlingData.challenges && conflictHandlingData.challenges.length > 0 ? (
                    conflictHandlingData.challenges.map((challenge: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{challenge}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific conflict handling challenges identified.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Improvement Actions Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(255,152,0,0.7)] via-[rgba(255,152,0,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Improvement Actions</h3>
                <div className="flex flex-col gap-3">
                  {conflictHandlingData.improvements && conflictHandlingData.improvements.length > 0 ? (
                    conflictHandlingData.improvements.map((improvement: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{improvement}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific improvement actions available at this time.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

        {activeTab === 'TIME MANAGEMENT' && (
          <>
            {/* Challenges Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(55,147,247,0.7)] via-[rgba(55,147,247,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Challenges and Areas for Improvement</h3>
                <div className="flex flex-col gap-3">
                  {timeManagementData.challenges && timeManagementData.challenges.length > 0 ? (
                    timeManagementData.challenges.map((challenge: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{challenge}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific time management challenges identified.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Improvement Actions Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(255,152,0,0.7)] via-[rgba(255,152,0,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Improvement Actions</h3>
                <div className="flex flex-col gap-3">
                  {timeManagementData.improvements && timeManagementData.improvements.length > 0 ? (
                    timeManagementData.improvements.map((improvement: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{improvement}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific improvement actions available at this time.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

        {/* Footer Text */}
        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
          While developing key workplace competencies enhances your professional effectiveness, thriving
          also depends on being in the right environment. The next section explores the work environment
          suited to your strengths and preferences, helping you identify where you can excel and stay
          motivated.

        </p>

        {/* Continue Button */}
        <div className="flex justify-center w-full my-8">
          <Link href="/2_5_work_environment">
            <Button
              variant="outline"
              className="rounded-full border-2 border-gray-800 text-gray-900 hover:bg-gray-100 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer"
              style={{ boxShadow: '0 2px 8px rgba(55,147,247,0.10)' }}
            >
              Continue
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-2 group-hover:translate-x-1 transition-transform duration-150"
              >
                <path d="M5 12h14" />
                <path d="M12 5l7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}