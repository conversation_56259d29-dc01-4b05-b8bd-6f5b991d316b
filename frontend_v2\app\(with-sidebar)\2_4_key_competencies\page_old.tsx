"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { useAssessment } from '@/context/AssessmentContext';
import CommonButton from '@/components/CommonButton';
import Footer from '@/components/Footer';
import { cn } from '@/lib/utils';

export default function KeyCompetenciesPage() {
  const [activeTab, setActiveTab] = useState('NETWORKING');

  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();
  
  // Get competency assessment data from assessment context
  const competencyAssessment = assessmentData?.assessment?.section_ii?.competency_assessment || null;
  
  // Get data for each competency
  const networkingData = competencyAssessment?.networking || { challenges: [], improvements: [] };
  const teamworkData = competencyAssessment?.teamwork || { challenges: [], improvements: [] };
  const conflictHandlingData = competencyAssessment?.conflict_handling || { challenges: [], improvements: [] };
  const timeManagementData = competencyAssessment?.time_management || { challenges: [], improvements: [] };

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  // Loading indicator 
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-xl text-blue-500 mb-2">Loading your competency assessment...</p>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/2.4TM.svg"
                alt="Workplace competencies illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[2.5rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[2.5rem]">2.4 Key Competencies For<br />Workplace Success</h1>

            <p className="mb-6 leading-relaxed">
              This section evaluates your proficiency in essential workplace competencies that influence your professional effectiveness. Strengthening these competencies can enhance your workplace performance, career progression, and overall success.
            </p>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex justify-center gap-2.5 my-8 flex-wrap">
          <button
            className={cn(
              "px-5 py-2.5 rounded-[20px] cursor-pointer font-medium mx-1.5 transition-all duration-300 border-none outline-none min-w-[150px] text-center shadow-md relative overflow-hidden",
              activeTab === 'NETWORKING' 
                ? "bg-[#3793F7] text-white shadow-[0_3px_6px_rgba(55,147,247,0.3)]" 
                : "bg-gradient-to-br from-[#e0e0e0] to-[#d0d0d0] text-[#555]"
            )}
            onClick={() => handleTabClick('NETWORKING')}
          >
            {activeTab === 'NETWORKING' && (
              <>
                <div className="absolute inset-0 bg-gradient-to-br from-[rgba(110,170,228,0.9)] via-[rgba(55,147,247,0.7)] via-[10%] to-[rgba(55,147,247,0.2)] to-[40%] rounded-[20px] pointer-events-none"></div>
                <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[10px] border-r-[10px] border-t-[10px] border-l-transparent border-r-transparent border-t-[#3793F7]"></div>
              </>
            )}
            <div className="flex items-center justify-center gap-2 relative z-10">
              <svg className="w-[18px] h-[18px]" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M16 22h2a2 2 0 0 0 2-2v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2a2 2 0 0 0 2 2h2"></path>
                <circle cx="12" cy="9" r="5"></circle>
              </svg>
              <span>NETWORKING</span>
            </div>
          </button>

          <button
            className={cn(
              "px-5 py-2.5 rounded-[20px] cursor-pointer font-medium mx-1.5 transition-all duration-300 border-none outline-none min-w-[150px] text-center shadow-md relative overflow-hidden",
              activeTab === 'TEAMWORK' 
                ? "bg-[#3793F7] text-white shadow-[0_3px_6px_rgba(55,147,247,0.3)]" 
                : "bg-gradient-to-br from-[#e0e0e0] to-[#d0d0d0] text-[#555]"
            )}
            onClick={() => handleTabClick('TEAMWORK')}
          >
            {activeTab === 'TEAMWORK' && (
              <>
                <div className="absolute inset-0 bg-gradient-to-br from-[rgba(110,170,228,0.9)] via-[rgba(55,147,247,0.7)] via-[10%] to-[rgba(55,147,247,0.2)] to-[40%] rounded-[20px] pointer-events-none"></div>
                <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[10px] border-r-[10px] border-t-[10px] border-l-transparent border-r-transparent border-t-[#3793F7]"></div>
              </>
            )}
            <div className="flex items-center justify-center gap-2 relative z-10">
              <svg className="w-[18px] h-[18px]" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
              <span>TEAMWORK</span>
            </div>
          </button>

          <button
            className={cn(
              "px-5 py-2.5 rounded-[20px] cursor-pointer font-medium mx-1.5 transition-all duration-300 border-none outline-none min-w-[150px] text-center shadow-md relative overflow-hidden",
              activeTab === 'CONFLICT HANDLING' 
                ? "bg-[#3793F7] text-white shadow-[0_3px_6px_rgba(55,147,247,0.3)]" 
                : "bg-gradient-to-br from-[#e0e0e0] to-[#d0d0d0] text-[#555]"
            )}
            onClick={() => handleTabClick('CONFLICT HANDLING')}
          >
            {activeTab === 'CONFLICT HANDLING' && (
              <>
                <div className="absolute inset-0 bg-gradient-to-br from-[rgba(110,170,228,0.9)] via-[rgba(55,147,247,0.7)] via-[10%] to-[rgba(55,147,247,0.2)] to-[40%] rounded-[20px] pointer-events-none"></div>
                <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[10px] border-r-[10px] border-t-[10px] border-l-transparent border-r-transparent border-t-[#3793F7]"></div>
              </>
            )}
            <div className="flex items-center justify-center gap-2 relative z-10">
              <svg className="w-[18px] h-[18px]" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <span>CONFLICT HANDLING</span>
            </div>
          </button>

          <button
            className={cn(
              "px-5 py-2.5 rounded-[20px] cursor-pointer font-medium mx-1.5 transition-all duration-300 border-none outline-none min-w-[150px] text-center shadow-md relative overflow-hidden",
              activeTab === 'TIME MANAGEMENT' 
                ? "bg-[#3793F7] text-white shadow-[0_3px_6px_rgba(55,147,247,0.3)]" 
                : "bg-gradient-to-br from-[#e0e0e0] to-[#d0d0d0] text-[#555]"
            )}
            onClick={() => handleTabClick('TIME MANAGEMENT')}
          >
            {activeTab === 'TIME MANAGEMENT' && (
              <>
                <div className="absolute inset-0 bg-gradient-to-br from-[rgba(110,170,228,0.9)] via-[rgba(55,147,247,0.7)] via-[10%] to-[rgba(55,147,247,0.2)] to-[40%] rounded-[20px] pointer-events-none"></div>
                <div className="absolute bottom-[-10px] left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[10px] border-r-[10px] border-t-[10px] border-l-transparent border-r-transparent border-t-[#3793F7]"></div>
              </>
            )}
            <div className="flex items-center justify-center gap-2 relative z-10">
              <svg className="w-[18px] h-[18px]" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <span>TIME MANAGEMENT</span>
            </div>
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === 'NETWORKING' && (
          <>
            {/* Challenges Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(55,147,247,0.7)] via-[rgba(55,147,247,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Challenges and Areas for Improvement</h3>
                <div className="flex flex-col gap-3">
                  {networkingData.challenges && networkingData.challenges.length > 0 ? (
                    networkingData.challenges.map((challenge: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{challenge}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific networking challenges identified.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Improvement Actions Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(255,152,0,0.7)] via-[rgba(255,152,0,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Improvement Actions</h3>
                <div className="flex flex-col gap-3">
                  {networkingData.improvements && networkingData.improvements.length > 0 ? (
                    networkingData.improvements.map((improvement: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{improvement}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific improvement actions available at this time.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

        {activeTab === 'TEAMWORK' && (
          <>
            {/* Challenges Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(55,147,247,0.7)] via-[rgba(55,147,247,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Challenges and Areas for Improvement</h3>
                <div className="flex flex-col gap-3">
                  {teamworkData.challenges && teamworkData.challenges.length > 0 ? (
                    teamworkData.challenges.map((challenge: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{challenge}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific teamwork challenges identified.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Improvement Actions Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(255,152,0,0.7)] via-[rgba(255,152,0,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Improvement Actions</h3>
                <div className="flex flex-col gap-3">
                  {teamworkData.improvements && teamworkData.improvements.length > 0 ? (
                    teamworkData.improvements.map((improvement: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{improvement}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific improvement actions available at this time.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

        {activeTab === 'CONFLICT HANDLING' && (
          <>
            {/* Challenges Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(55,147,247,0.7)] via-[rgba(55,147,247,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Challenges and Areas for Improvement</h3>
                <div className="flex flex-col gap-3">
                  {conflictHandlingData.challenges && conflictHandlingData.challenges.length > 0 ? (
                    conflictHandlingData.challenges.map((challenge: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{challenge}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific conflict handling challenges identified.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Improvement Actions Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(255,152,0,0.7)] via-[rgba(255,152,0,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Improvement Actions</h3>
                <div className="flex flex-col gap-3">
                  {conflictHandlingData.improvements && conflictHandlingData.improvements.length > 0 ? (
                    conflictHandlingData.improvements.map((improvement: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{improvement}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific improvement actions available at this time.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

        {activeTab === 'TIME MANAGEMENT' && (
          <>
            {/* Challenges Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(55,147,247,0.7)] via-[rgba(55,147,247,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Challenges and Areas for Improvement</h3>
                <div className="flex flex-col gap-3">
                  {timeManagementData.challenges && timeManagementData.challenges.length > 0 ? (
                    timeManagementData.challenges.map((challenge: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{challenge}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific time management challenges identified.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Improvement Actions Card */}
            <div className="relative rounded-2xl p-6 mb-6 shadow-md bg-white overflow-hidden max-w-[980px] mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-[rgba(255,152,0,0.7)] via-[rgba(255,152,0,0.2)] via-[10%] to-transparent to-[30%] rounded-2xl pointer-events-none z-0"></div>
              <div className="relative z-[1]">
                <h3 className="text-[1.2rem] font-medium mb-4 text-[#333]">Improvement Actions</h3>
                <div className="flex flex-col gap-3">
                  {timeManagementData.improvements && timeManagementData.improvements.length > 0 ? (
                    timeManagementData.improvements.map((improvement: string, index: number) => (
                      <div className="flex items-start leading-relaxed" key={index}>
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                        <span>{improvement}</span>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0 font-bold">•</span>
                      <span>No specific improvement actions available at this time.</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}

        {/* Footer Text */}
        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
          Understanding and developing these key competencies is crucial for workplace success.
          Next, let's explore the ideal work environment where you're most likely to thrive.
        </p>

        {/* Continue Button */}
        <CommonButton
          text="CONTINUE"
          onClick={() => window.location.href = '/2_5_work_environment'}
        />
        
        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}