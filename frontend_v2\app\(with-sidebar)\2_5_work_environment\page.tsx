"use client";

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Footer from '@/components/Footer';
import CommonButton from '@/components/CommonButton';
import GradientCard from '@/components/GradientCard';
import { useAssessment } from '@/context/AssessmentContext';

export default function WorkEnvironmentFitPage() {
  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Get ideal work environment description from assessment data
  const workEnvironmentDescription = assessmentData?.assessment?.section_ii?.ideal_work_environment?.description || '';

  // Split the description into sentences for bullet points
  const environmentSentences = workEnvironmentDescription
    ? workEnvironmentDescription.split(/\.(?:\s|$)/).filter((sentence: string) => sentence.trim().length > 0)
    : [];

  // Loading indicator 
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-xl text-blue-500 mb-2">Loading your ideal work environment data...</p>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full m-0 p-0 bg-[#f1f1f1] font-['Ubuntu',sans-serif] text-[#212121]">
      {/* Main Content */}
      <div className="w-full mx-auto px-0 pt-[20px] pb-0 relative">
        {/* Header with Image and Title */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-0">
          {/* Image Column */}
          <div className="flex-none w-full md:w-[400px] relative">
            <div className="relative w-full h-[320px] top-[10px]">
              <Image
                src="/2.5Ver2TM.png"
                alt="Work environment illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          {/* Content Column */}
          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[2.5rem] font-light text-[#3793F7] mb-8 px-8 pt-12 leading-tight">2.5 Work Environment Fit</h1>

            <p className="mb-6 px-8 leading-relaxed">
              This section outlines the type of work environment where
              you are most likely to thrive. Understanding these
              preferences helps you identify environments where you can
              perform at your best, stay engaged and achieve long-term
              success.

            </p>
          </div>
        </div>

        {/* Environment Preferences Card */}
        <GradientCard color="blue"  >
          {environmentSentences.length > 0 ? (
            <ul className="list-none pl-0">
              {environmentSentences.map((sentence: string, index: number) => (
                <li key={index} className="pl-5 mb-4 leading-relaxed relative">
                  <span className="absolute left-0 text-[#3793F7] font-bold">•</span>
                  {sentence.trim()}.
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-center text-[#3793F7] p-8">No data available.</div>
          )}
        </GradientCard>

        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
          Understanding how your strengths, limitations, and ideal work environment influence your
          academic and professional life empowers you to perform effectively and navigate challenges. By
          recognizing these factors, you can make informed decisions that enhance your productivity, job
          satisfaction, and long-term success.


        </p>

        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
          Building on your self-awareness and understanding of how you make an impact, the next section
          helps you explore career paths that align with your strengths and preferences. It also provides
          practical guidance on crafting a compelling resume and preparing for job interviews to improve
          your chances of securing a fulfilling career.
        </p>

        {/* Next Chapter Button */}
        <div className="max-w-[980px] mx-auto mb-8">
          <div className="flex justify-center w-full my-8">
            <Link href="/3_1_career_options_for_you">
              <Button
                variant="outline"
                className="rounded-full border-2 border-gray-800 text-gray-900 hover:bg-gray-100 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer"
                style={{ boxShadow: '0 2px 8px rgba(55,147,247,0.10)' }}
              >
                Next Chapter
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="ml-2 group-hover:translate-x-1 transition-transform duration-150"
                >
                  <path d="M5 12h14" />
                  <path d="M12 5l7 7-7 7" />
                </svg>
              </Button>
            </Link>
          </div>
        </div>

        {/* Footer - last component with no space below */}
        <Footer />
      </div>
    </div>
  );
}
