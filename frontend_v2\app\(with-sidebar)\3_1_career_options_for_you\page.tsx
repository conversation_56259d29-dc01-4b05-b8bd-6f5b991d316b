"use client";

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Footer from '@/components/Footer';
import CommonButton from '@/components/CommonButton';

export default function CareerOptionsPage() {
  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1]">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/3.1Ver2TM.svg"
                alt="Career Options illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[3rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[3rem]">3.1 Career Options<br />For You</h1>

            <p className="mb-20 leading-relaxed max-w-[600px]">
              This segment helps you discover careers that align with your
              strengths and preferences. This segment also provides
              guidance on building a strong resume and preparing
              effectively for job interviews.
            </p>

            <div className="mt-5">
              <div className="flex justify-center w-full my-8">
                <Link href="/3_2_recommended_careers">
                  <Button
                    variant="outline"
                    className="rounded-full border-2 border-gray-800 text-gray-900 hover:bg-gray-100 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer"
                    style={{ boxShadow: '0 2px 8px rgba(55,147,247,0.10)' }}
                  >
                    Continue
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth="2.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="ml-2 group-hover:translate-x-1 transition-transform duration-150"
                    >
                      <path d="M5 12h14" />
                      <path d="M12 5l7 7-7 7" />
                    </svg>
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Spacer to push footer down */}
        <div className="h-[120px]"></div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
} 