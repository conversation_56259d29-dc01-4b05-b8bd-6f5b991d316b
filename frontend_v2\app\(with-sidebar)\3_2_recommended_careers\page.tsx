"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import Footer from '@/components/Footer';
import NavigationButton from '@/components/NavigationButton';
import { useAssessment } from '@/context/AssessmentContext';
import { ChevronDown } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

// Define types for our data
interface Role {
  title: string;
}

interface CareerJustification {
  pros: string[];
  cons: string[];
}

interface Career {
  group_name: string;
  roles: Role[];
  justification: CareerJustification;
  mba_specialization: string[];
  suitability: string;
}

export default function RecommendedCareersPage() {
  const [defaultAccordion, setDefaultAccordion] = useState<string>("item-1");

  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();
  
  // Get career rankings from assessment data
  const careerRankings = assessmentData?.assessment?.section_ii?.career_ranking || [];
  
  // Take only top 10 careers
  const topCareers = careerRankings.slice(0, 10);

  // Loading indicator 
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-xl text-blue-500 mb-2">Loading your career recommendations...</p>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full mx-auto pb-0 relative bg-[#f1f1f1]">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/3.2TM.svg"
                alt="Recommended Careers illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[2.5rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[2.5rem]">3.2 Recommended Careers</h1>

            <p className="mb-6 leading-relaxed">
              This section highlights the top career paths that align with your natural behaviors, energizing forces, and work preferences. 
              Understanding these career matches can help you make informed decisions about your professional journey, ensuring greater job 
              satisfaction and long term success.
            </p>
          </div>
        </div>

        {/* Career sections using ShadCN accordion */}
        {topCareers && topCareers.length > 0 ? (
          <Accordion type="single" collapsible defaultValue={defaultAccordion} className="max-w-[980px] mx-auto space-y-4">
            {topCareers.map((career: Career, index: number) => (
              <AccordionItem 
                key={index} 
                value={`item-${index + 1}`}
                className="relative rounded-2xl overflow-hidden shadow-md bg-white"
                style={{
                  background: 'linear-gradient(135deg, rgba(55,147,247,0.5) 0%, rgba(55,147,247,0.05) 30%, transparent 100%)'
                }}
              >
                <AccordionTrigger className="px-6 py-4 text-lg font-medium text-gray-800 hover:no-underline">
                  <span className="text-left">{index + 1}. {career.group_name}</span>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6">
                  {/* Info icon */}
                  {/* <div className="absolute top-5 right-5 w-6 h-6 bg-[#3793F7] text-white rounded-full flex items-center justify-center italic font-bold">
                    i
                  </div>
                   */}
                  <p className="text-gray-800 leading-relaxed mb-5 text-[0.95rem]">
                    {career.justification && career.justification.pros && career.justification.pros[0] || 
                      "This career aligns well with your behavioral profile and work preferences."}
                  </p>
                  
                  {/* Add pros and cons sections */}
                  {career.justification && (
                    <div className="mb-5">
                      {/* Pros section */}
                      {career.justification.pros && career.justification.pros.length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-medium text-gray-800 mb-2 text-base">Advantages:</h4>
                          <ul className="space-y-1.5">
                            {career.justification.pros.map((pro: string, i: number) => (
                              <li key={i} className="flex items-start">
                                <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                                <span className="text-sm leading-tight">{pro}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {/* Cons section */}
                      {career.justification.cons && career.justification.cons.length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-medium text-gray-800 mb-2 text-base">Limitations:</h4>
                          <ul className="space-y-1.5">
                            {career.justification.cons.map((con: string, i: number) => (
                              <li key={i} className="flex items-start">
                                <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                                <span className="text-sm leading-tight">{con}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Roles section */}
                  {career.roles && career.roles.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-800 mb-3 text-base">Roles:</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4">
                        {career.roles.map((role: Role, roleIndex: number) => (
                          <div className="flex items-start" key={roleIndex}>
                            <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                            <span className="text-sm font-medium">{role.title}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          /* No data available message */
          <div className="max-w-[980px] mx-auto text-center py-12">
            <div className="relative rounded-2xl overflow-hidden shadow-md bg-white p-8"
              style={{
                background: 'linear-gradient(135deg, rgba(55,147,247,0.5) 0%, rgba(55,147,247,0.05) 30%, transparent 100%)'
              }}>
              <div>
                <h3 className="text-xl font-medium text-gray-800 mb-4">No Career Data Available</h3>
                <p className="text-gray-600 leading-relaxed">
                  We're unable to display career recommendations at this time. Please try refreshing the page or contact support if the issue persists.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="max-w-[980px] mx-auto my-8">
          <p className="mb-8 leading-relaxed">
            Just as your strengths, limitations, and stressors shape your academic performance, they also play a crucial role in your professional life. By understanding these factors, you can enhance your effectiveness at work, navigate challenges, and maintain a balanced approach to workplace demands.
          </p>

          {/* Continue Button */}
          <NavigationButton
            text="CONTINUE"
            href="/3_3_recommended_specializations"
          />
        </div>
        
        {/* Spacer to push footer down */}
        <div className="h-[120px]"></div>
        
        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}