"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import Footer from '@/components/Footer';
import NavigationButton from '@/components/NavigationButton';
import { useAssessment } from '@/context/AssessmentContext';
import { ChevronDown } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

// Define types for our data
interface Role {
  title: string;
}

interface CareerJustification {
  pros: string[];
  cons: string[];
}

interface Career {
  group_name: string;
  roles: Role[];
  justification: CareerJustification;
  mba_specialization: string[];
  suitability: string;
}

export default function RecommendedCareersPage() {
  const [defaultAccordion, setDefaultAccordion] = useState<string>("item-1"); // Set first section expanded by default

  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();
  
  // Get career rankings from assessment data
  const careerRankings = assessmentData?.assessment?.section_ii?.career_ranking || [];
  
  // Take only top 10 careers
  const topCareers = careerRankings.slice(0, 10);

  // Loading indicator 
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-xl text-blue-500 mb-2">Loading your career recommendations...</p>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full mx-auto pb-0 relative bg-[#f1f1f1]">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/3.2TM.svg"
                alt="Recommended Careers illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[2.5rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[2.5rem]">3.2 Recommended Careers</h1>

            <p className="mb-6 leading-relaxed">
              This section highlights the top career paths that align with your natural behaviors, energizing forces, and work preferences. 
              Understanding these career matches can help you make informed decisions about your professional journey, ensuring greater job 
              satisfaction and long term success.
            </p>
          </div>
        </div>

        {/* Career sections using ShadCN accordion */}
        <Accordion type="single" collapsible defaultValue={defaultAccordion} className="max-w-[980px] mx-auto space-y-4">
          {topCareers && topCareers.length > 0 ? (
            topCareers.map((career: Career, index: number) => (
              <AccordionItem 
                key={index} 
                value={`item-${index + 1}`}
                className="relative rounded-2xl overflow-hidden shadow-md bg-white 
                  before:content-[''] before:absolute before:inset-0 before:bg-gradient-to-br
                  before:from-[rgba(55,147,247,0.65)] before:via-[rgba(55,147,247,0.08)]
                  before:to-transparent before:via-15% before:z-0 before:rounded-2xl before:pointer-events-none"
              >
                <AccordionTrigger className="px-6 py-4 text-lg font-medium text-gray-800 hover:no-underline relative z-10">
                  <span className="text-left">{index + 1}. {career.group_name}</span>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 relative z-10">
                  <p className="text-gray-800 leading-relaxed mb-5 text-[0.95rem]">
                    {career.justification && career.justification.pros && career.justification.pros[0] || 
                      "Your exceptional attention to detail, methodical approach, and high standards for accuracy make this career an excellent fit. You would excel at creating and maintaining systems to ensure consistent quality and identifying potential issues before they become problems. Your analytical mind would thrive in an environment where thoroughness and precision are valued above speed."}
                  </p>
                  
                  {/* Add pros and cons sections */}
                  {career.justification && (
                    <div className="mb-5">
                      {/* Pros section */}
                      {career.justification.pros && career.justification.pros.length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-medium text-gray-800 mb-2 text-base">Advantages:</h4>
                          <ul className="space-y-1.5">
                            {career.justification.pros.map((pro: string, i: number) => (
                              <li key={i} className="flex items-start">
                                <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                                <span className="text-sm leading-tight">{pro}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {/* Cons section */}
                      {career.justification.cons && career.justification.cons.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2 text-base">Limitations:</h4>
                          <ul className="space-y-1.5">
                            {career.justification.cons.map((con: string, i: number) => (
                              <li key={i} className="flex items-start">
                                <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                                <span className="text-sm leading-tight">{con}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-3 text-base">Roles:</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4">
                      {career.roles && career.roles.map((role: Role, roleIndex: number) => (
                        <div className="flex items-start" key={roleIndex}>
                          <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                          <span className="text-sm font-medium">{role.title}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))
          ) : (
            <>
              {/* Default content if no career data is available */}
              <AccordionItem 
                value="item-1"
                className="relative rounded-2xl overflow-hidden shadow-md bg-white 
                  before:content-[''] before:absolute before:inset-0 before:bg-gradient-to-br
                  before:from-[rgba(55,147,247,0.65)] before:via-[rgba(55,147,247,0.08)]
                  before:to-transparent before:via-15% before:z-0 before:rounded-2xl before:pointer-events-none"
              >
                <AccordionTrigger className="px-6 py-4 text-lg font-medium text-gray-800 hover:no-underline relative z-10">
                  <span className="text-left">1. Quality Assurance</span>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 relative z-10">
                  <p className="text-gray-800 leading-relaxed mb-5 text-[0.95rem]">
                    Your exceptional attention to detail, methodical approach, and high standards for accuracy make quality assurance an excellent fit. You would excel at creating and maintaining systems to ensure consistent quality and identifying potential issues before they become problems. Your analytical mind would thrive in an environment where thoroughness and precision are valued above speed.
                  </p>
                  
                  {/* Add default pros and cons */}
                  <div className="mb-5">
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-800 mb-2 text-base">Advantages:</h4>
                      <ul className="space-y-1.5">
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Directly utilizes your precision and attention to detail strengths</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Provides structured processes and clear standards for evaluation</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Allows you to create and maintain systems that ensure quality</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Values thoroughness and accuracy, aligning with your natural approach</span>
                        </li>
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-800 mb-2 text-base">Limitations:</h4>
                      <ul className="space-y-1.5">
                        <li className="flex items-start">
                          <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">May involve confronting others about quality issues, challenging your conflict avoidance</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Could require balancing quality standards with production timelines</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Might involve adapting to changing industry standards and regulations</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-3 text-base">Roles:</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4">
                      <div className="flex items-start">
                        <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                        <span className="text-sm font-medium">Quality Assurance Manager</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                        <span className="text-sm font-medium">Quality Control Specialist</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                        <span className="text-sm font-medium">Quality Systems Analyst</span>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem 
                value="item-2"
                className="relative rounded-2xl overflow-hidden shadow-md bg-white 
                  before:content-[''] before:absolute before:inset-0 before:bg-gradient-to-br
                  before:from-[rgba(55,147,247,0.65)] before:via-[rgba(55,147,247,0.08)]
                  before:to-transparent before:via-15% before:z-0 before:rounded-2xl before:pointer-events-none"
              >
                <AccordionTrigger className="px-6 py-4 text-lg font-medium text-gray-800 hover:no-underline relative z-10">
                  <span className="text-left">2. Data Analytics</span>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 relative z-10">
                  <p className="text-gray-800 leading-relaxed mb-5 text-[0.95rem]">
                    Your logical thinking, analytical abilities, and comfort with structured information make data analytics highly suitable. You would excel at carefully examining data, identifying patterns, and drawing accurate conclusions based on factual evidence. Your methodical approach ensures thoroughness in analysis while your attention to detail prevents errors in data interpretation.
                  </p>
                  
                  <div className="mb-5">
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-800 mb-2 text-base">Advantages:</h4>
                      <ul className="space-y-1.5">
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Leverages your analytical thinking, precision, and attention to detail</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Provides intellectual challenges and continuous learning opportunities</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Allows for methodical work with clear processes and frameworks</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Values your ability to work with data and produce accurate analyses</span>
                        </li>
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-800 mb-2 text-base">Limitations:</h4>
                      <ul className="space-y-1.5">
                        <li className="flex items-start">
                          <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">May involve tight deadlines that challenge your preference for thoroughness</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Could require presenting findings to large groups, challenging your reserved nature</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Might involve ambiguous business problems that lack clear parameters</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-3 text-base">Roles:</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4">
                      <div className="flex items-start">
                        <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                        <span className="text-sm font-medium">Business Analyst / Consultant</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                        <span className="text-sm font-medium">Business Intelligence Analyst</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                        <span className="text-sm font-medium">Research Analyst</span>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
              
              <AccordionItem 
                value="item-3"
                className="relative rounded-2xl overflow-hidden shadow-md bg-white 
                  before:content-[''] before:absolute before:inset-0 before:bg-gradient-to-br
                  before:from-[rgba(55,147,247,0.65)] before:via-[rgba(55,147,247,0.08)]
                  before:to-transparent before:via-15% before:z-0 before:rounded-2xl before:pointer-events-none"
              >
                <AccordionTrigger className="px-6 py-4 text-lg font-medium text-gray-800 hover:no-underline relative z-10">
                  <span className="text-left">3. Financial Control</span>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 relative z-10">
                  <p className="text-gray-800 leading-relaxed mb-5 text-[0.95rem]">
                    Your logical thinking, analytical abilities, and comfort with structured information make financial control highly suitable. You would excel at carefully examining financial data, identifying discrepancies, and ensuring compliance with regulations. Your methodical approach ensures thoroughness while your attention to detail prevents costly errors.
                  </p>
                  
                  <div className="mb-5">
                    <div className="mb-4">
                      <h4 className="font-medium text-gray-800 mb-2 text-base">Advantages:</h4>
                      <ul className="space-y-1.5">
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Leverages your precision, attention to detail, and analytical abilities</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Provides structured environments with clear processes and standards</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Offers intellectual challenges that satisfy your drive for knowledge</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#39B54A] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Values accuracy and thoroughness, aligning with your work style</span>
                        </li>
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-800 mb-2 text-base">Limitations:</h4>
                      <ul className="space-y-1.5">
                        <li className="flex items-start">
                          <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">May involve regulatory deadlines that create time pressure</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Could require explaining complex information to non-technical stakeholders</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-[#E74C3C] mr-2 text-xl leading-none">•</span>
                          <span className="text-sm leading-tight">Might involve navigating ambiguous regulations or guidelines at times</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 mb-3 text-base">Roles:</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4">
                      <div className="flex items-start">
                        <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                        <span className="text-sm font-medium">Financial Controller</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                        <span className="text-sm font-medium">Auditor</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-[#3793F7] mr-2 text-xl font-bold leading-none">•</span>
                        <span className="text-sm font-medium">Compliance Officer</span>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </>
          )}
        </Accordion>

        <div className="max-w-[980px] mx-auto my-8">
          <p className="mb-8 leading-relaxed">
            Just as your strengths, limitations, and stressors shape your academic performance, they also play a crucial role in your professional life. By understanding these factors, you can enhance your effectiveness at work, navigate challenges, and maintain a balanced approach to workplace demands.
          </p>

          {/* Continue Button */}
          <NavigationButton
            text="CONTINUE"
            href="/3_3_recommended_specializations"
          />
        </div>
        
        {/* Spacer to push footer down */}
        <div className="h-[120px]"></div>
        
        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
} 