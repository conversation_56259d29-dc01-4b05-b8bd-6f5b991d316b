"use client";

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Footer from '@/components/Footer';
import CommonButton from '@/components/CommonButton';
import GradientCard from '@/components/GradientCard';
import { useAssessment } from '@/context/AssessmentContext';

export default function RecommendedSpecializationsPage() {
  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Get specialization recommendations from assessment data
  const specializationRecommendations = assessmentData?.assessment?.section_ii?.specialization_recommendations || [];

  // Loading indicator 
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-xl text-blue-500 mb-2">Loading your MBA specialization recommendations...</p>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full mx-auto pb-0 relative bg-[#f1f1f1]">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-2">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/3.3Ver2TM.svg"
                alt="MBA Specializations illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[2.5rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[2.5rem]">3.3 Recommended<br />MBA Specializations</h1>

            <p className="mb-6 leading-relaxed">
              This section suggests MBA Specializations that align
              with your strengths, preferences and professional
              potential. These specializations can maximize your
              potential, enhance your learning experience and open
              doors to fulfilling career opportunities.
              <br />
              <br />
              Based on your personality profile and the career groups listed, the following MBA
              specializations from the provided list would be most beneficial.

            </p>
          </div>
        </div>

        {/* <h2 className="text-[1.8rem] font-light text-[#3793F7] my-8 text-center">Recommended MBA Specializations</h2> */}

        {/* MBA Specializations Card */}
        <GradientCard color="blue">
          <p className="text-base font-medium mb-5">Given your strengths and best-fit careers, the most suitable MBA specializations would be:</p>

          {specializationRecommendations && specializationRecommendations.length > 0 ? (
            specializationRecommendations.map((recommendation: any, index: number) => (
              <div key={index} className="mb-7">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">{index + 1}. {recommendation.specialization}</h3>
                {recommendation.justification && (
                  <p className="mb-4 leading-relaxed">
                    {recommendation.justification}
                  </p>
                )}
              </div>
            ))
          ) : (
            <div className="mb-7">
              <p className="text-gray-600 italic">No specialization recommendations available.</p>
            </div>
          )}
        </GradientCard>

        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">Complementing the recommended careers, the tailored resume-building guidance, in the next
section, ensures you present your skills and achievements effectively, increasing your chances of
securing opportunities in your chosen field.</p>

        <div className="my-8 max-w-[980px] mx-auto">
          <div className="flex justify-center w-full my-8">
            <Link href="/3_4_build_your_resume">
              <Button
                variant="outline"
                className="rounded-full border-2 border-gray-800 text-gray-900 hover:bg-gray-100 font-semibold px-8 py-3 text-base flex items-center gap-2 group transition-colors duration-150 cursor-pointer"
                style={{ boxShadow: '0 2px 8px rgba(55,147,247,0.10)' }}
              >
                Continue
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="ml-2 group-hover:translate-x-1 transition-transform duration-150"
                >
                  <path d="M5 12h14" />
                  <path d="M12 5l7 7-7 7" />
                </svg>
              </Button>
            </Link>
          </div>
        </div>



        {/* Spacer to push footer down */}
        <div className="h-[120px]"></div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
} 