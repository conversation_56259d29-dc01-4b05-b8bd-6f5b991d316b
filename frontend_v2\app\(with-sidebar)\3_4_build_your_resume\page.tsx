"use client";

import React from 'react';
import Image from 'next/image';
import { useAssessment } from '@/context/AssessmentContext';
import CommonButton from '@/components/CommonButton';
import GradientCard from '@/components/GradientCard';
import Footer from '@/components/Footer';
import { cn } from '@/lib/utils';
import Link from 'next/link';

export default function BuildYourResumePage() {
  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Get resume outline content from assessment data
  const resumeOutlineContent = assessmentData?.assessment?.section_ii?.resume_outline?.content || '';

  // Format the resume content for display
  // Replace markdown headers with appropriate HTML elements
  const formatResumeContent = (content: string) => {
    if (!content) return '';

    // Replace markdown headers and styling with HTML
    let formattedContent = content
      .replace(/^# (.*?)$/gm, '<h3 class="text-lg font-bold text-gray-800 mt-5 mb-2">$1</h3>')
      .replace(/^## (.*?)$/gm, '<h4 class="text-base font-semibold text-gray-700 mt-4 mb-1">$1</h4>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\n/g, '<br />');

    return formattedContent;
  };

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/3.4TM.svg"
                alt="Resume building illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[3rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[3rem]">3.4 Build Your Resume</h1>

            <p className="mb-6 leading-relaxed">
              This section provides tailored resume-building guidance for each of your recommended career paths. It highlights key skills, achievements, and experiences that should be emphasized to align with industry expectations. Whether focusing on technical expertise, leadership abilities, or problem-solving skills, this guidance ensures your resume stands out to recruiters and increases your chances of securing job opportunities in your chosen field.
            </p>
          </div>
        </div>

        
        <h2 className="text-2xl font-light text-[#3793F7] mb-6 text-center">Resume Outline</h2>


        

        {/* Card with Resume Outline using GradientCard */}
        <GradientCard color="blue">
          {loading ? (
            <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading your personalized resume outline...</p>
            </div>
          ) : (
            <div className="leading-relaxed">
              {resumeOutlineContent ? (
                <div
                  className="text-base"
                  dangerouslySetInnerHTML={{ __html: formatResumeContent(resumeOutlineContent) }}
                />
              ) : (
                <p>Personalized resume outline is not available. Please contact support.</p>
              )}
            </div>
          )}
        </GradientCard>

        {/* Footer Text */}
        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
          Crafting a strong resume is the first step in showcasing your skills and aligning with industry
          expectations. Building on this, the next section provides key strategies to help you confidently
          present your strengths and competencies during job interviews, increasing your chances of
          securing the right opportunity.
        </p>

        {/* Continue Button */}
        <CommonButton
          text="CONTINUE"
          onClick={() => window.location.href = '/3_5_prepare_for_interviews'}
        />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}