"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useAssessment } from '@/context/AssessmentContext';
import NavigationButton from '@/components/NavigationButton';
import GradientCard from '@/components/GradientCard';
import Footer from '@/components/Footer';
import { cn } from '@/lib/utils';

export default function PrepareForInterviewsPage() {
  // Get assessment data from context
  const { assessmentData, loading, error } = useAssessment();

  // Get interview preparation strategies from assessment data
  const interviewStrategies = assessmentData?.assessment?.section_ii?.interview_preparation?.strategies || [];

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[320px]">
              <Image
                src="/3.5TM.svg"
                alt="Job interview illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[3rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[3rem]">3.5 Prepare<br />For Job Interviews</h1>

            <p className="mb-6 leading-relaxed">
              This section equips you with key insights and
              strategies to excel in job interviews. It covers
              essential aspects such as articulating your
              strengths and showcasing your knowledge,
              skills and competencies effectively. By
              preparing strategically, you can navigate
              interviews with ease and increase your
              chances of securing the right job opportunity.

            </p>
          </div>
        </div>

        <h2 className="text-2xl font-light text-[#3793F7] mb-6 text-center">Job Interview Preparation</h2>

        {/* Interview Strategies Card using GradientCard */}
        <GradientCard color="blue"  >
          {loading ? (
            <div className="text-center p-8 text-[#3793F7] flex flex-col items-center justify-center">
              <div className="border-4 border-[rgba(0,0,0,0.1)] border-l-[#3793F7] w-9 h-9 rounded-full animate-spin mb-4"></div>
              <p>Loading your interview preparation strategies...</p>
            </div>
          ) : (
            <div className="leading-relaxed">
              <p className="mb-4">Considering your limitations and communication style, you should prepare for interviews by:</p>
              <div className="flex flex-col gap-4">
                {interviewStrategies && interviewStrategies.length > 0 ? (
                  interviewStrategies.map((strategy: string, index: number) => (
                    <div className="flex items-start leading-relaxed" key={index}>
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>{strategy}</span>
                    </div>
                  ))
                ) : (
                  <>
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>Practice answering common interview questions with specific examples that highlight your analytical abilities and attention to detail.</span>
                    </div>
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>Prepare concise stories that demonstrate how your methodical approach has solved problems or improved processes in previous academic or work experiences.</span>
                    </div>
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>Research prospective companies thoroughly to understand their procedures, values, and expectations, allowing you to speak knowledgeably about how you would contribute.</span>
                    </div>
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>Develop a structured framework for answering unexpected questions, perhaps using a problem-solution-outcome approach.</span>
                    </div>
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>Practice maintaining appropriate eye contact and engagement cues during mock interviews to overcome any tendency to appear withdrawn.</span>
                    </div>
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>Prepare thoughtful questions about the organization's processes, systems, and quality standards to demonstrate his alignment with structured environments.</span>
                    </div>
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>Request interview schedules in advance when possible and arrive early to reduce anxiety about the unknown.</span>
                    </div>
                    <div className="flex items-start leading-relaxed">
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>Prepare brief, focused responses that highlight your strengths without overloading interviewers with excessive detail.</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
        </GradientCard>

        {/* Footer Text */}
        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
          By exploring career paths aligned with your strengths, crafting a compelling resume, and
          preparing effectively for interviews, you position yourself for success in your chosen field. These
          insights empower you to make informed career decisions and confidently pursue opportunities
          that match your skills and aspirations.
           
        </p>

        <p className="my-8 leading-relaxed max-w-[980px] mx-auto">
           
          Achieving long-term success requires continuous growth and adaptation. This section focuses on
          creating a personalized development plan to enhance your strengths, address areas for
          improvement, and unlock your full potential in both personal and professional settings.

        </p>

        {/* Continue Button */}
        <NavigationButton
          text="CONTINUE"
          href="/4_1_embark_on_success_path"
        />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}