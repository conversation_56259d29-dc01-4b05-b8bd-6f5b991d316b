"use client";

import React from 'react';
import Image from 'next/image';
import { useAssessment } from '@/context/AssessmentContext';
import NavigationButton from '@/components/NavigationButton';
import Footer from '@/components/Footer';
import { cn } from '@/lib/utils';

export default function EmbarkOnSuccessPage() {
  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5 items-center">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[400px]">
              <Image
                src="/4.1Ver2TM.svg"
                alt="Embark on Success Path illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt-4 md:mt-0">
            <h1 className="text-[2.8rem] font-light text-[#3793F7] mb-8 md:text-4xl lg:text-[2.8rem]">4.1 Embark On Your Success Path</h1>

            <p className="mb-10 leading-relaxed max-w-[600px]">
              This segment focuses on creating a
              personalized development plan to enhance
              your strengths and address areas for growth.
              This segment includes a SWOD analysis
              (Strengths, Workarounds, Opportunities, and
              Development areas) and actionable steps to
              support your personal and professional
              advancement.
            </p>

            {/* Continue Button */}
            <NavigationButton
              text="CONTINUE"
              href="/4_2_swod_analysis"
            />
          </div>
        </div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}