"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { useAssessment } from '@/context/AssessmentContext';
import NavigationButton from '@/components/NavigationButton';
import Footer from '@/components/Footer';

// TypeScript interface for SwodCard props
interface SwodCardProps {
  cardId: string;
  type: string;
  icon: string;
  title: string;
  description: string;
  expandedContent: string[];
  color: string;
  bgGradient: {
    from: string;
    via: string;
  };
}

// Independent Card Component - Each card controls its own state
const SwodCard: React.FC<SwodCardProps> = ({
  cardId,
  type,
  icon,
  title,
  description,
  expandedContent,
  color,
  bgGradient
}) => {
  // Each card has its own independent state
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsExpanded(prev => !prev);
  };

  // Inline styles to avoid ANY CSS conflicts
  const cardStyle: React.CSSProperties = {
    position: 'relative',
    borderRadius: '16px',
    padding: '24px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    backgroundColor: 'white',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column',
    minHeight: isExpanded ? 'fit-content' : '320px', // Reduced to 320px when contracted
    height: isExpanded ? 'fit-content' : '320px',
    transition: 'all 0.3s ease',
    background: `linear-gradient(135deg, ${bgGradient.from} 0%, ${bgGradient.via} 20%, transparent 40%), white`,
  };

  const expandedContentStyle: React.CSSProperties = {
    overflow: 'hidden',
    transition: 'all 0.3s ease-in-out',
    maxHeight: isExpanded ? '2000px' : '0px',
    opacity: isExpanded ? 1 : 0,
    marginTop: isExpanded ? '20px' : '0px',
    paddingTop: isExpanded ? '8px' : '0px',
  };

  const buttonStyle: React.CSSProperties = {
    marginTop: 'auto',
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '8px 16px',
    backgroundColor: 'transparent',
    border: `2px solid ${color}`,
    color: color,
    borderRadius: '6px',
    fontSize: '14px',
    fontWeight: '500',
    cursor: 'pointer',
    transition: 'opacity 0.2s ease',
    height: '36px',
    minWidth: '120px',
  };

  // Safe HTML rendering function
  const createMarkup = (html: string) => {
    // Basic sanitization - remove script tags and other dangerous elements
    const sanitized = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    return { __html: sanitized };
  };

  return (
    <div style={cardStyle} data-card-id={cardId} data-expanded={isExpanded}>
      <div style={{ position: 'relative', zIndex: 10, display: 'flex', flexDirection: 'column', flex: 1 }}>
        {/* Icon and Title Row */}
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px', gap: '16px' }}>
          <div style={{ width: '50px', height: '50px', position: 'relative', flexShrink: 0 }}>
            <Image
              src={icon}
              alt={`${title} Icon`}
              fill
              style={{ objectFit: "contain" }}
            />
          </div>
          <h2 style={{ fontSize: '24px', fontWeight: '600', color: '#374151', margin: 0 }}>
            {title}
          </h2>
        </div>
        <p style={{ fontSize: '16px', lineHeight: '1.6', color: '#4B5563', marginBottom: '20px' }}>
          {description}
        </p>

        <button
          style={buttonStyle}
          onClick={handleToggle}
          type="button"
          aria-expanded={isExpanded}
          aria-label={`${isExpanded ? 'Hide' : 'Show'} more ${title.toLowerCase()} details`}
          onMouseEnter={(e) => (e.currentTarget.style.opacity = '0.8')}
          onMouseLeave={(e) => (e.currentTarget.style.opacity = '1')}
        >
          {isExpanded ? 'Show Less' : 'Show More'}
        </button>

        {/* Expanded Content */}
        <div style={expandedContentStyle}>
          <ul style={{ display: 'flex', flexDirection: 'column', gap: '12px', listStyle: 'none', padding: 0, margin: 0 }}>
            {expandedContent.map((item, index) => (
              <li
                key={`${cardId}-${type}-${index}`}
                style={{ display: 'flex', alignItems: 'flex-start', lineHeight: '1.6' }}
              >
                <span
                  style={{
                    color: color,
                    fontSize: '18px',
                    marginRight: '12px',
                    flexShrink: 0
                  }}
                >
                  •
                </span>
                <span dangerouslySetInnerHTML={createMarkup(item)}></span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default function SwodAnalysisPage() {
  const { assessmentData, loading, error } = useAssessment();

  // Show loading state
  if (loading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: '50vh' }}>
        <div style={{ textAlign: 'center' }}>
          <p style={{ fontSize: '1.25rem', color: '#3793F7', marginBottom: '8px' }}>Loading your SWOD analysis...</p>
          <div style={{ 
            width: '48px', 
            height: '48px', 
            border: '4px solid #f3f4f6', 
            borderTop: '4px solid #3793F7', 
            borderRadius: '50%', 
            animation: 'spin 1s linear infinite',
            margin: '0 auto'
          }}></div>
        </div>
      </div>
    );
  }

  // Extract data for different cards with honest fallbacks
  const strengthsData = assessmentData?.assessment?.section_i?.strengths?.strengths || [];
  const strengthsTitle = strengthsData.length > 0 ? strengthsData[0].name : "No Data Available";
  const strengthsDescription = strengthsData.length > 0 ? 
    strengthsData[0].description : "No strengths data could be fetched from the assessment";

  const opportunitiesData = assessmentData?.assessment?.section_i?.study_manifestations?.enablers || [];
  const opportunitiesDescription = opportunitiesData.length > 0 ?
    opportunitiesData[0] : "No opportunities data could be fetched from the assessment";

  const workaroundsData = assessmentData?.assessment?.section_i?.strengths?.critical_actions || [];
  const mitigationSteps = assessmentData?.assessment?.section_i?.stressors?.length > 0 ?
    assessmentData?.assessment?.section_i?.stressors[0]?.mitigation_steps || [] : [];
  const academicStrategies = assessmentData?.assessment?.section_ii?.impact_strategies?.academic || [];
  const workaroundsDescription = workaroundsData.length > 0 ?
    workaroundsData[0] : mitigationSteps.length > 0 ?
      mitigationSteps[0] : academicStrategies.length > 0 ?
        academicStrategies[0] : "No workarounds data could be fetched from the assessment";

  const derailersData = assessmentData?.assessment?.section_i?.study_manifestations?.derailers || [];
  const actionSteps = assessmentData?.assessment?.section_i?.limitations?.action_steps || [];
  const timeManagementImprovements = assessmentData?.assessment?.section_ii?.competency_assessment?.time_management?.improvements || [];
  const developmentDescription = derailersData.length > 0 ?
    derailersData[0] : actionSteps.length > 0 ?
      actionSteps[0] : timeManagementImprovements.length > 0 ?
        timeManagementImprovements[0] : "No development areas data could be fetched from the assessment";

  // Prepare expanded content for each card - ONLY API data
  const strengthsExpandedContent = [
    ...strengthsData.slice(1).map((strength: { name: string; description: string }) => 
      `<strong>${strength.name}:</strong> ${strength.description}`)
  ];

  const workaroundsExpandedContent = [
    ...workaroundsData.slice(1),
    ...mitigationSteps.slice(1),
    ...academicStrategies.slice(1)
  ];

  const opportunitiesExpandedContent = [
    ...opportunitiesData.slice(1)
  ];

  const developmentExpandedContent = [
    ...derailersData.slice(1),
    ...actionSteps.slice(1),
    ...timeManagementImprovements.slice(1)
  ];

  const containerStyle: React.CSSProperties = {
    width: '100%',
    marginTop: '-32px',
    marginLeft: 'auto',
    marginRight: 'auto',
    paddingBottom: '0',
    position: 'relative',
    backgroundColor: '#f1f1f1',
    padding: '16px',
  };

  const mainContentStyle: React.CSSProperties = {
    width: '100%',
    maxWidth: '1100px',
    margin: '0 auto',
    padding: '32px 20px 0'
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '2.8rem',
    fontWeight: '300',
    color: '#3793F7',
    marginBottom: '40px',
    textAlign: 'center'
  };

  const descriptionStyle: React.CSSProperties = {
    marginBottom: '2.5rem',
    lineHeight: '1.625',
    maxWidth: 'none'
  };

  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: '2rem',
    marginBottom: '2.5rem',
    alignItems: 'flex-start'
  };

  // Handle error state
  if (error) {
    return (
      <div style={containerStyle}>
        <div style={mainContentStyle}>
          <h1 style={titleStyle}>4.2 SWOD Analysis</h1>
          <div style={{ textAlign: 'center', padding: '2rem', backgroundColor: '#fef2f2', borderRadius: '8px', border: '1px solid #fecaca' }}>
            <p style={{ color: '#dc2626', fontSize: '1.125rem' }}>
              Unable to load your SWOD analysis. Please try refreshing the page.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={containerStyle}>
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
      
      {/* Main Content */}
      <div style={mainContentStyle}>
        <h1 style={titleStyle}>
          4.2 SWOD Analysis
        </h1>
        <p style={descriptionStyle}>
          Your SWOD is a summarization of your Strengths, Workarounds, Development Areas and
          Opportunities, that have been presented in the various segments and sections of your
          Kaleidoscope.
        </p>

        <div style={gridStyle}>
          {/* Independent Cards */}
          <SwodCard
            cardId="strengths-001"
            type="strengths"
            icon="/StrengthTM.svg"
            title="Strengths"
            description={`${strengthsTitle}: ${strengthsDescription}`}
            expandedContent={strengthsExpandedContent}
            color="#6ec850"
            bgGradient={{
              from: 'rgba(110,200,80,0.7)',
              via: 'rgba(110,200,80,0.2)'
            }}
          />

          <SwodCard
            cardId="workarounds-002"
            type="workarounds"
            icon="/WorkaroundsTM.svg"
            title="Workarounds"
            description={workaroundsDescription}
            expandedContent={workaroundsExpandedContent}
            color="#ff9933"
            bgGradient={{
              from: 'rgba(255,153,51,0.7)',
              via: 'rgba(255,153,51,0.2)'
            }}
          />

          <SwodCard
            cardId="opportunities-003"
            type="opportunities"
            icon="/OpportunitiesTM.svg"
            title="Opportunities"
            description={opportunitiesDescription}
            expandedContent={opportunitiesExpandedContent}
            color="#3793F7"
            bgGradient={{
              from: 'rgba(55,147,247,0.7)',
              via: 'rgba(55,147,247,0.2)'
            }}
          />

          <SwodCard
            cardId="development-004"
            type="development"
            icon="/Dev AreasTM.svg"
            title="Development Areas"
            description={developmentDescription}
            expandedContent={developmentExpandedContent}
            color="#8a67e2"
            bgGradient={{
              from: 'rgba(138,103,226,0.7)',
              via: 'rgba(138,103,226,0.2)'
            }}
          />
        </div>

        {/* Continue Button */}
        <NavigationButton
          text="CONTINUE"
          href="/4_4_conclusion"
        />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}
