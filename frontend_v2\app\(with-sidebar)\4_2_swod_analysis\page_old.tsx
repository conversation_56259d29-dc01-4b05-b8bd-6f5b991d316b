"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useAssessment } from '@/context/AssessmentContext';
import CommonButton from '@/components/CommonButton';
import Footer from '@/components/Footer';
import { cn } from '@/lib/utils';

export default function SwodAnalysisPage() {
  const { assessmentData, loading, error } = useAssessment();
  
  // Separate state for each card
  const [strengthsExpanded, setStrengthsExpanded] = useState(false);
  const [workaroundsExpanded, setWorkaroundsExpanded] = useState(false);
  const [opportunitiesExpanded, setOpportunitiesExpanded] = useState(false);
  const [developmentExpanded, setDevelopmentExpanded] = useState(false);

  // Extract data for different cards
  const strengthsData = assessmentData?.assessment?.section_i?.strengths?.strengths || [];
  const strengthsTitle = strengthsData.length > 0 ? 
    strengthsData[0].name : "Analytical thinking";
  const strengthsDescription = strengthsData.length > 0 ?
    strengthsData[0].description : "Excels at critical thinking and objective analysis, approaching problems methodically and thoroughly.";
  
  const opportunitiesData = assessmentData?.assessment?.section_i?.study_manifestations?.enablers || [];
  const opportunitiesDescription = opportunitiesData.length > 0 ?
    opportunitiesData[0] : "Thorough preparation and detailed note-taking help you master complex academic material";
  
  const workaroundsData = assessmentData?.assessment?.section_i?.strengths?.critical_actions || [];
  const mitigationSteps = assessmentData?.assessment?.section_i?.stressors?.length > 0 ?
    assessmentData?.assessment?.section_i?.stressors[0]?.mitigation_steps || [] : [];
  const academicStrategies = assessmentData?.assessment?.section_ii?.impact_strategies?.academic || [];
  const workaroundsDescription = workaroundsData.length > 0 ?
    workaroundsData[0] : mitigationSteps.length > 0 ? 
      mitigationSteps[0] : academicStrategies.length > 0 ? 
        academicStrategies[0] : "Create structured systems and processes that allow you to maintain precision while still completing work in a timely manner";
  
  const derailersData = assessmentData?.assessment?.section_i?.study_manifestations?.derailers || [];
  const actionSteps = assessmentData?.assessment?.section_i?.limitations?.action_steps || [];
  const timeManagementImprovements = assessmentData?.assessment?.section_ii?.competency_assessment?.time_management?.improvements || [];
  const developmentDescription = derailersData.length > 0 ?
    derailersData[0] : actionSteps.length > 0 ?
      actionSteps[0] : timeManagementImprovements.length > 0 ?
        timeManagementImprovements[0] : "Perfectionism may cause you to spend too much time on assignments or study materials beyond what's necessary for mastery";

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        <h1 className="text-[2.8rem] font-light text-[#3793F7] mb-10 text-center">4.2 SWOD Analysis</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
          {/* Strengths Card */}
          <div className={`relative rounded-2xl p-6 shadow-md bg-white overflow-hidden ${strengthsExpanded ? 'min-h-[400px]' : 'min-h-[280px]'} flex flex-col`}>
            <div className="absolute inset-0 bg-gradient-to-br from-[rgba(110,200,80,0.7)] via-[rgba(110,200,80,0.2)] via-[20%] to-transparent to-[40%] rounded-2xl pointer-events-none z-0"></div>
            <div className="relative z-[1] flex flex-col flex-1">
              <div className="w-[50px] h-[50px] relative mb-4">
                <Image
                  src="/icons/strength.svg"
                  alt="Strengths Icon"
                  fill
                  style={{objectFit: "contain"}}
                />
              </div>
              <h2 className="text-[1.4rem] font-semibold mb-4 text-gray-800">Strengths</h2>
              <p className="text-base leading-relaxed text-gray-700 mb-5">{strengthsTitle}: {strengthsDescription}</p>
              <button 
                className="mt-auto inline-flex items-center justify-center px-4 py-2 bg-transparent border-2 border-[#6ec850] text-[#6ec850] rounded-md text-sm font-medium cursor-pointer transition hover:bg-opacity-5 h-9 min-w-[120px]"
                onClick={(e) => {
                  e.stopPropagation();
                  setStrengthsExpanded(!strengthsExpanded);
                }}
              >
                {strengthsExpanded ? 'Show Less' : 'Show More'}
              </button>

              <div className={`overflow-hidden transition-all duration-300 ${strengthsExpanded ? 'max-h-[1000px] opacity-100 mt-5' : 'max-h-0 opacity-0 mt-0'}`}>
                <ul className="flex flex-col gap-3">
                  {strengthsData.slice(1).map((strength: any, index: number) => (
                    <div className="flex items-start leading-relaxed" key={index}>
                      <span className="text-[#6ec850] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span><strong>{strength.name}:</strong> {strength.description}</span>
                    </div>
                  ))}
                  {strengthsData.length <= 1 && (
                    <>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#6ec850] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span><strong>Detail-oriented:</strong> Your eye for detail ensures you catch important nuances and produce high-quality work.</span>
                      </div>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#6ec850] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span><strong>Thorough researcher:</strong> You excel at gathering and analyzing information before making decisions.</span>
                      </div>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#6ec850] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span><strong>Process-focused:</strong> You create efficient systems and follow protocols precisely.</span>
                      </div>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>

          {/* Workarounds Card */}
          <div className={`relative rounded-2xl p-6 shadow-md bg-white overflow-hidden ${workaroundsExpanded ? 'min-h-[400px]' : 'min-h-[280px]'} flex flex-col`}>
            <div className="absolute inset-0 bg-gradient-to-br from-[rgba(255,153,51,0.7)] via-[rgba(255,153,51,0.2)] via-[20%] to-transparent to-[40%] rounded-2xl pointer-events-none z-0"></div>
            <div className="relative z-[1] flex flex-col flex-1">
              <div className="w-[50px] h-[50px] relative mb-4">
                <Image
                  src="/icons/workaround.svg"
                  alt="Workarounds Icon"
                  fill
                  style={{objectFit: "contain"}}
                />
              </div>
              <h2 className="text-[1.4rem] font-semibold mb-4 text-gray-800">Workarounds</h2>
              <p className="text-base leading-relaxed text-gray-700 mb-5">{workaroundsDescription}</p>
              <button 
                className="mt-auto inline-flex items-center justify-center px-4 py-2 bg-transparent border-2 border-[#ff9933] text-[#ff9933] rounded-md text-sm font-medium cursor-pointer transition hover:bg-opacity-5 h-9 min-w-[120px]"
                onClick={(e) => {
                  e.stopPropagation();
                  setWorkaroundsExpanded(!workaroundsExpanded);
                }}
              >
                {workaroundsExpanded ? 'Show Less' : 'Show More'}
              </button>

              <div className={`overflow-hidden transition-all duration-300 ${workaroundsExpanded ? 'max-h-[1000px] opacity-100 mt-5' : 'max-h-0 opacity-0 mt-0'}`}>
                <ul className="flex flex-col gap-3">
                  {workaroundsData.slice(1).map((item: string, index: number) => (
                    <div className="flex items-start leading-relaxed" key={index}>
                      <span className="text-[#ff9933] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>{item}</span>
                    </div>
                  ))}
                  {mitigationSteps.slice(1).map((item: string, index: number) => (
                    <div className="flex items-start leading-relaxed" key={`m-${index}`}>
                      <span className="text-[#ff9933] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>{item}</span>
                    </div>
                  ))}
                  {academicStrategies.slice(1).map((item: string, index: number) => (
                    <div className="flex items-start leading-relaxed" key={`a-${index}`}>
                      <span className="text-[#ff9933] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>{item}</span>
                    </div>
                  ))}
                  {workaroundsData.length <= 1 && mitigationSteps.length <= 1 && academicStrategies.length <= 1 && (
                    <>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#ff9933] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span>Set time limits for research and analysis phases to avoid analysis paralysis</span>
                      </div>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#ff9933] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span>Use checklists and templates to standardize routine tasks so you can focus attention where it's most needed</span>
                      </div>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#ff9933] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span>Practice delegating non-critical tasks when working in teams to improve overall efficiency</span>
                      </div>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>

          {/* Opportunities Card */}
          <div className={`relative rounded-2xl p-6 shadow-md bg-white overflow-hidden ${opportunitiesExpanded ? 'min-h-[400px]' : 'min-h-[280px]'} flex flex-col`}>
            <div className="absolute inset-0 bg-gradient-to-br from-[rgba(55,147,247,0.7)] via-[rgba(55,147,247,0.2)] via-[20%] to-transparent to-[40%] rounded-2xl pointer-events-none z-0"></div>
            <div className="relative z-[1] flex flex-col flex-1">
              <div className="w-[50px] h-[50px] relative mb-4">
                <Image
                  src="/icons/opportunity.svg"
                  alt="Opportunities Icon"
                  fill
                  style={{objectFit: "contain"}}
                />
              </div>
              <h2 className="text-[1.4rem] font-semibold mb-4 text-gray-800">Opportunities</h2>
              <p className="text-base leading-relaxed text-gray-700 mb-5">{opportunitiesDescription}</p>
              <button 
                className="mt-auto inline-flex items-center justify-center px-4 py-2 bg-transparent border-2 border-[#3793F7] text-[#3793F7] rounded-md text-sm font-medium cursor-pointer transition hover:bg-opacity-5 h-9 min-w-[120px]"
                onClick={(e) => {
                  e.stopPropagation();
                  setOpportunitiesExpanded(!opportunitiesExpanded);
                }}
              >
                {opportunitiesExpanded ? 'Show Less' : 'Show More'}
              </button>

              <div className={`overflow-hidden transition-all duration-300 ${opportunitiesExpanded ? 'max-h-[1000px] opacity-100 mt-5' : 'max-h-0 opacity-0 mt-0'}`}>
                <ul className="flex flex-col gap-3">
                  {opportunitiesData.slice(1).map((item: string, index: number) => (
                    <div className="flex items-start leading-relaxed" key={index}>
                      <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>{item}</span>
                    </div>
                  ))}
                  {opportunitiesData.length <= 1 && (
                    <>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span>Consider roles that value precision and quality control, such as research analysis, data science, or quality assurance</span>
                      </div>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span>Look for workplaces that prize accountability and attention to detail</span>
                      </div>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#3793F7] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span>Seek projects where your methodical approach adds significant value</span>
                      </div>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>

          {/* Development Areas Card */}
          <div className={`relative rounded-2xl p-6 shadow-md bg-white overflow-hidden ${developmentExpanded ? 'min-h-[400px]' : 'min-h-[280px]'} flex flex-col`}>
            <div className="absolute inset-0 bg-gradient-to-br from-[rgba(138,103,226,0.7)] via-[rgba(138,103,226,0.2)] via-[20%] to-transparent to-[40%] rounded-2xl pointer-events-none z-0"></div>
            <div className="relative z-[1] flex flex-col flex-1">
              <div className="w-[50px] h-[50px] relative mb-4">
                <Image
                  src="/icons/development.svg"
                  alt="Development Areas Icon"
                  fill
                  style={{objectFit: "contain"}}
                />
              </div>
              <h2 className="text-[1.4rem] font-semibold mb-4 text-gray-800">Development Areas</h2>
              <p className="text-base leading-relaxed text-gray-700 mb-5">{developmentDescription}</p>
              <button 
                className="mt-auto inline-flex items-center justify-center px-4 py-2 bg-transparent border-2 border-[#8a67e2] text-[#8a67e2] rounded-md text-sm font-medium cursor-pointer transition hover:bg-opacity-5 h-9 min-w-[120px]"
                onClick={(e) => {
                  e.stopPropagation();
                  setDevelopmentExpanded(!developmentExpanded);
                }}
              >
                {developmentExpanded ? 'Show Less' : 'Show More'}
              </button>

              <div className={`overflow-hidden transition-all duration-300 ${developmentExpanded ? 'max-h-[1000px] opacity-100 mt-5' : 'max-h-0 opacity-0 mt-0'}`}>
                <ul className="flex flex-col gap-3">
                  {derailersData.slice(1).map((item: string, index: number) => (
                    <div className="flex items-start leading-relaxed" key={index}>
                      <span className="text-[#8a67e2] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>{item}</span>
                    </div>
                  ))}
                  {actionSteps.slice(1).map((item: string, index: number) => (
                    <div className="flex items-start leading-relaxed" key={`a-${index}`}>
                      <span className="text-[#8a67e2] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>{item}</span>
                    </div>
                  ))}
                  {timeManagementImprovements.slice(1).map((item: string, index: number) => (
                    <div className="flex items-start leading-relaxed" key={`t-${index}`}>
                      <span className="text-[#8a67e2] text-lg mr-2.5 flex-shrink-0">•</span>
                      <span>{item}</span>
                    </div>
                  ))}
                  {derailersData.length <= 1 && actionSteps.length <= 1 && timeManagementImprovements.length <= 1 && (
                    <>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#8a67e2] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span>Work on making quicker decisions when perfect information is not available</span>
                      </div>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#8a67e2] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span>Practice identifying when "good enough" is sufficient versus when perfectionism is warranted</span>
                      </div>
                      <div className="flex items-start leading-relaxed">
                        <span className="text-[#8a67e2] text-lg mr-2.5 flex-shrink-0">•</span>
                        <span>Develop comfort with ambiguity and changing requirements</span>
                      </div>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>

        <p className="my-8 leading-relaxed max-w-[980px] mx-auto text-center">
          This SWOD analysis provides a framework for leveraging your strengths while addressing potential limitations. By implementing the suggested workarounds and focusing on key development areas, you can maximize your effectiveness in both academic and professional settings.
        </p>

        {/* Continue Button */}
        <CommonButton
          text="CONTINUE"
          href="/4_3_conclusion"
        />
        
        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}