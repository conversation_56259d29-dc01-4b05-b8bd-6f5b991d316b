"use client";

import React from 'react';
import { useAssessment } from '@/context/AssessmentContext';
import NavigationButton from '@/components/NavigationButton';
import GradientCard from '@/components/GradientCard';
import Footer from '@/components/Footer';

// Define types for the data structure
interface SubActivity {
  title?: string;
  description?: string;
}

interface Activity {
  title: string;
  sub_activities: string[];
}

export default function TopActivitiesPage() {
  const { assessmentData, loading, error } = useAssessment();

  // Get implementation activities from assessment data
  const implementationActivities = assessmentData?.assessment?.section_ii?.implementation_activities?.activities || [];

  // Show loading state
  if (loading) {
    return (
      <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6">
        <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
          <div className="flex items-center justify-center min-h-[50vh]">
            <div className="text-center">
              <p className="text-xl text-blue-500 dark:text-blue-400 mb-2">Loading your top activities...</p>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 dark:border-blue-400 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6">
        <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
          <h1 className="text-[3rem] font-light text-[#3793F7] dark:text-blue-400 mb-8 text-center">
            4.3 Top 5 Activities
          </h1>
          <div className="text-center p-8 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-600 dark:text-red-400 text-lg">
              Unable to load your top activities. Please try refreshing the page.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Title only (no image) */}
        <div className="text-center mb-10 mt-5">
          <h1 className="text-[3rem] font-light text-[#3793F7] dark:text-blue-400 mb-8 md:text-4xl lg:text-[3rem]">
            4.3 Top 5 Activities
          </h1>
        </div>

        {/* Description text above the card */}
        <p className="mb-6 leading-relaxed text-gray-900 dark:text-gray-200 max-w-[980px] mx-auto">
          Listed below are the top 5 activities that you can immediately implement for the next 30-60 days.
        </p>

        {/* Activities Card */}
        <GradientCard color="blue"  >
          {implementationActivities && implementationActivities.length > 0 ? (
            <div className="space-y-8">
              {implementationActivities.map((activity: Activity, index: number) => (
                <div key={index} className="mb-6 last:mb-0">
                  {/* Activity Title */}
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-start">
                    <span className="text-[#3793F7] dark:text-blue-400 mr-3 text-2xl font-bold flex-shrink-0">
                      {index + 1}.
                    </span>
                    {activity.title}
                  </h3>

                  {/* Sub-activities */}
                  {activity.sub_activities && activity.sub_activities.length > 0 && (
                    <div className="ml-8">
                      {activity.sub_activities.map((subActivity: string, subIndex: number) => (
                        <div key={subIndex} className="flex items-start mb-3 last:mb-0">
                          <span className="text-[#3793F7] dark:text-blue-400 mr-3 text-lg flex-shrink-0">
                            •
                          </span>
                          <span className="text-gray-700 dark:text-gray-300 leading-relaxed">
                            {subActivity}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center p-8">
              <p className="text-gray-600 dark:text-gray-400 text-lg">
                No implementation activities data could be fetched from the assessment.
              </p>
            </div>
          )}
        </GradientCard>

        {/* Footer Text */}
        <p className="my-8 leading-relaxed max-w-[980px] mx-auto text-gray-900 dark:text-gray-200">
          Consider your SWOD as well as the given activities, to work on specific goals that are achievable in the next 60 days. 
          <br /><br />
          Using the given template in the next section, write your goals, outline the specific steps and strategies you will follow to achieve them. Identify the skills you need to develop, resources you will use, and actions you will take. Include timelines for key milestones and specify how you will track your progress. This section should provide a clear, practical roadmap to guide your personal and professional growth.
        </p>

        {/* Continue Button */}
        <NavigationButton
          text="CONTINUE"
          href="/4_4_personal_development_plan"
        />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}