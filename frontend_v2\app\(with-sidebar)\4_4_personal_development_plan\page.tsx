"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useAssessment } from '@/context/AssessmentContext';
import NavigationButton from '@/components/NavigationButton';
import GradientCard from '@/components/GradientCard';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Plus, Edit2, Bold, Italic, List, ListOrdered, RotateCcw, RotateCw, Trash2 } from 'lucide-react';

// Custom Rich Text Editor Component with improved list functionality
interface RichTextEditorProps {
  content: string;
  onChange: (html: string) => void;
  placeholder?: string;
  id: string;
  key?: string; // Add key prop for React reconciliation
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({ content, onChange, placeholder, id }) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [editorContent, setEditorContent] = useState(content);

  // Initialize when component mounts
  useEffect(() => {
    if (editorRef.current) {
      // Set initial content
      editorRef.current.innerHTML = content || '';
      setEditorContent(content || '');
      applyListStyling();
    }
  }, []); // Only run once on mount

  // Update when content prop changes
  useEffect(() => {
    if (editorRef.current && content !== editorContent) {
      // Update content when it changes
      editorRef.current.innerHTML = content || '';
      setEditorContent(content || '');
      applyListStyling();
    }
  }, [content, editorContent]);

  // Helper function to apply list styling
  const applyListStyling = () => {
    if (!editorRef.current) return;

    const lists = editorRef.current.querySelectorAll('ul, ol');
    lists.forEach(list => {
      if (list.tagName === 'UL') {
        (list as HTMLElement).style.listStyleType = 'disc';
        (list as HTMLElement).style.marginLeft = '20px';
        (list as HTMLElement).style.paddingLeft = '0px';
      } else if (list.tagName === 'OL') {
        (list as HTMLElement).style.listStyleType = 'decimal';
        (list as HTMLElement).style.marginLeft = '20px';
        (list as HTMLElement).style.paddingLeft = '0px';
      }
    });
  };

  const handleInput = () => {
    if (editorRef.current) {
      const html = editorRef.current.innerHTML;
      setEditorContent(html);
      onChange(html);
    }
  };

  const executeCommand = (command: string, value?: string) => {
    if (editorRef.current) {
      editorRef.current.focus();

      // Special handling for lists
      if (command === 'insertUnorderedList' || command === 'insertOrderedList') {
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          document.execCommand(command, false, value);

          // Force proper list styling
          setTimeout(() => {
            if (editorRef.current) {
              const lists = editorRef.current.querySelectorAll('ul, ol');
              lists.forEach(list => {
                if (list.tagName === 'UL') {
                  (list as HTMLElement).style.listStyleType = 'disc';
                  (list as HTMLElement).style.marginLeft = '20px';
                  (list as HTMLElement).style.paddingLeft = '0px';
                } else if (list.tagName === 'OL') {
                  (list as HTMLElement).style.listStyleType = 'decimal';
                  (list as HTMLElement).style.marginLeft = '20px';
                  (list as HTMLElement).style.paddingLeft = '0px';
                }
              });
              handleInput();
            }
          }, 10);
        }
      } else {
        document.execCommand(command, false, value);
      }

      handleInput();
    }
  };

  const isCommandActive = (command: string): boolean => {
    try {
      if (editorRef.current && editorRef.current.contains(document.activeElement)) {
        return document.queryCommandState(command);
      }
      return false;
    } catch {
      return false;
    }
  };

  // Handle Enter key in lists to create new list items
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const listItem = range.startContainer.parentElement?.closest('li');

        if (listItem) {
          e.preventDefault();

          // Check if current list item is empty
          if (listItem.textContent?.trim() === '') {
            // Exit the list if empty item
            document.execCommand('outdent');
          } else {
            // Create new list item
            document.execCommand('insertHTML', false, '<br>');
            document.execCommand('insertHTML', false, '</li><li>');
          }
          handleInput();
        }
      }
    }
  };

  return (
    <div className="border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800">
      {/* Toolbar */}
      <div className="border-b border-gray-300 dark:border-gray-600 p-2 flex items-center gap-1 bg-gray-50 dark:bg-gray-700 rounded-t-md">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onMouseDown={(e) => e.preventDefault()}
          onClick={() => executeCommand('bold')}
          className={`h-8 w-8 p-0 ${isCommandActive('bold') ? 'bg-gray-200 dark:bg-gray-600' : ''}`}
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onMouseDown={(e) => e.preventDefault()}
          onClick={() => executeCommand('italic')}
          className={`h-8 w-8 p-0 ${isCommandActive('italic') ? 'bg-gray-200 dark:bg-gray-600' : ''}`}
        >
          <Italic className="h-4 w-4" />
        </Button>
        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1" />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onMouseDown={(e) => e.preventDefault()}
          onClick={() => executeCommand('insertUnorderedList')}
          className={`h-8 w-8 p-0 ${isCommandActive('insertUnorderedList') ? 'bg-gray-200 dark:bg-gray-600' : ''}`}
        >
          <List className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onMouseDown={(e) => e.preventDefault()}
          onClick={() => executeCommand('insertOrderedList')}
          className={`h-8 w-8 p-0 ${isCommandActive('insertOrderedList') ? 'bg-gray-200 dark:bg-gray-600' : ''}`}
        >
          <ListOrdered className="h-4 w-4" />
        </Button>
        <div className="w-px h-6 bg-gray-300 dark:border-gray-600 mx-1" />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onMouseDown={(e) => e.preventDefault()}
          onClick={() => executeCommand('undo')}
          className="h-8 w-8 p-0"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onMouseDown={(e) => e.preventDefault()}
          onClick={() => executeCommand('redo')}
          className="h-8 w-8 p-0"
        >
          <RotateCw className="h-4 w-4" />
        </Button>
      </div>

      {/* Editor Content */}
      <div className="relative">
        <div
          ref={editorRef}
          contentEditable
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className="min-h-[100px] p-3 text-gray-900 dark:text-gray-100 focus:outline-none"
          style={{
            wordBreak: 'break-word',
            overflowWrap: 'break-word',
            direction: 'ltr',
            textAlign: 'left',
            unicodeBidi: 'embed'
          }}
          suppressContentEditableWarning={true}
          data-editor-id={id}
        />
        {!isFocused && (!editorContent || editorContent.trim() === '' || editorContent === '<br>') && (
          <div className="absolute top-3 left-3 text-gray-400 dark:text-gray-500 pointer-events-none">
            {placeholder}
          </div>
        )}
      </div>

      {/* Ensure list styles are applied - scoped to this editor */}
      <style jsx>{`
        div[data-editor-id="${id}"] ul {
          list-style-type: disc !important;
          margin-left: 20px !important;
          padding-left: 0px !important;
        }

        div[data-editor-id="${id}"] ol {
          list-style-type: decimal !important;
          margin-left: 20px !important;
          padding-left: 0px !important;
        }

        div[data-editor-id="${id}"] li {
          margin-bottom: 4px !important;
          display: list-item !important;
        }
      `}</style>
    </div>
  );
};

// Define types for development item
interface DevelopmentItem {
  id: string;
  goal: string;
  tasks: string;
  importantTasks: string;
  difficultyLevel: 'Low Priority' | 'High Priority' | 'Medium Priority';
  timeToComplete: string;
  progress: 'Not Started' | 'In Progress' | 'Complete';
  dateCreated: string;
}

export default function PersonalDevelopmentPlanPage() {
  const { assessmentData, loading, error } = useAssessment();
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<DevelopmentItem | null>(null);
  const [developmentItems, setDevelopmentItems] = useState<DevelopmentItem[]>([]);
  const [editorKey, setEditorKey] = useState(0); // Add this to force editor re-render
  const [isLoading, setIsLoading] = useState(true);
  const [apiError, setApiError] = useState<string | null>(null);

  // Form state for new/edit development item
  const [formData, setFormData] = useState({
    goal: '',
    tasks: '',
    importantTasks: '',
    difficultyLevel: '',
    timeToComplete: '',
    progress: 'Not Started'
  });

  // Function to handle deleting a PDP item
  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this development item?')) {
      try {
        setIsLoading(true);
        setApiError(null);

        // Send delete request to API
        const response = await fetch(`/api/pdp?id=${id}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Error deleting PDP item: ${response.status}`);
        }

        // Remove from local state
        setDevelopmentItems(prev => prev.filter(item => item.id !== id));
      } catch (error) {
        console.error('Error deleting PDP item:', error);
        setApiError('Failed to delete development item. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Generate unique ID for development items (used as fallback)
  const generateId = () => {
    const randomNumber = Math.floor(100000 + Math.random() * 900000);
    return `PDI_${randomNumber}`;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const openAddModal = () => {
    setEditingItem(null);
    setFormData({
      goal: '',
      tasks: '',
      importantTasks: '',
      difficultyLevel: '',
      timeToComplete: '',
      progress: 'Not Started'
    });
    setEditorKey(prev => prev + 1); // Force editor re-render
    setIsSheetOpen(true);
  };

  const openEditModal = (item: DevelopmentItem) => {
    console.log('Opening edit modal for item:', item);

    // Generate a new key to force re-render of editors
    const newKey = Date.now(); // Use timestamp for unique key
    setEditorKey(newKey);

    // Set form data with all fields from the item
    const newFormData = {
      goal: item.goal || '',
      tasks: item.tasks || '',
      importantTasks: item.importantTasks || '',
      difficultyLevel: item.difficultyLevel,
      timeToComplete: item.timeToComplete,
      progress: item.progress
    };

    // Set form data
    setFormData(newFormData);

    // Set editing item
    setEditingItem(item);

    // Log the data we're setting
    console.log('Setting form data for edit:', newFormData);

    // Open sheet after a short delay to ensure state is properly set
    setTimeout(() => {
      setIsSheetOpen(true);
    }, 50);
  };

  // Load PDP items from the backend API
  const loadPDPItems = async () => {
    try {
      setIsLoading(true);
      setApiError(null);

      const response = await fetch('/api/pdp');

      if (!response.ok) {
        throw new Error(`Error fetching PDP items: ${response.status}`);
      }

      const data = await response.json();

      // Map backend data to frontend format
      const mappedItems: DevelopmentItem[] = data.items.map((item: any) => ({
        id: item.id,
        goal: item.goal,
        tasks: item.tasks,
        importantTasks: item.important_tasks || '',
        difficultyLevel: item.priority_level as DevelopmentItem['difficultyLevel'],
        timeToComplete: item.time_to_complete,
        progress: item.progress as DevelopmentItem['progress'],
        dateCreated: new Date(item.date_created).toISOString().split('T')[0]
      }));

      setDevelopmentItems(mappedItems);
    } catch (error) {
      console.error('Failed to load PDP items:', error);
      setApiError('Failed to load development items. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load items when component mounts
  useEffect(() => {
    loadPDPItems();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setApiError(null);

    try {
      if (editingItem) {
        // Map form data to API format for update
        const apiData = {
          goal: formData.goal,
          tasks: formData.tasks,
          important_tasks: formData.importantTasks,
          priority_level: formData.difficultyLevel,
          time_to_complete: formData.timeToComplete,
          progress: formData.progress
        };

        // Send update request to API
        const response = await fetch(`/api/pdp?id=${editingItem.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: editingItem.id, ...apiData }),
        });

        if (!response.ok) {
          throw new Error(`Error updating PDP item: ${response.status}`);
        }

        const updatedData = await response.json();

        // Update in local state
        const updatedItem: DevelopmentItem = {
          id: updatedData.id,
          goal: updatedData.goal,
          tasks: updatedData.tasks,
          importantTasks: updatedData.important_tasks || '',
          difficultyLevel: updatedData.priority_level as DevelopmentItem['difficultyLevel'],
          timeToComplete: updatedData.time_to_complete,
          progress: updatedData.progress as DevelopmentItem['progress'],
          dateCreated: new Date(updatedData.date_created).toISOString().split('T')[0]
        };

        console.log('Development Item Updated:', updatedItem);

        setDevelopmentItems(prev =>
          prev.map(item => item.id === editingItem.id ? updatedItem : item)
        );
      } else {
        // Map form data to API format for create
        const apiData = {
          goal: formData.goal,
          tasks: formData.tasks,
          important_tasks: formData.importantTasks,
          priority_level: formData.difficultyLevel,
          time_to_complete: formData.timeToComplete,
          progress: formData.progress
        };

        // Send create request to API
        const response = await fetch('/api/pdp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiData),
        });

        if (!response.ok) {
          throw new Error(`Error creating PDP item: ${response.status}`);
        }

        const newData = await response.json();

        // Create new item with API response data
        const newItem: DevelopmentItem = {
          id: newData.id,
          goal: newData.goal,
          tasks: newData.tasks,
          importantTasks: newData.important_tasks || '',
          difficultyLevel: newData.priority_level as DevelopmentItem['difficultyLevel'],
          timeToComplete: newData.time_to_complete,
          progress: newData.progress as DevelopmentItem['progress'],
          dateCreated: new Date(newData.date_created).toISOString().split('T')[0]
        };

        console.log('New Development Item Added:', newItem);

        // Add to local state
        setDevelopmentItems(prev => [...prev, newItem]);
      }

      // Reset form and close modal
      setFormData({
        goal: '',
        tasks: '',
        importantTasks: '',
        difficultyLevel: '',
        timeToComplete: '',
        progress: 'Not Started'
      });
      setEditingItem(null);
      setIsSheetOpen(false);
      setEditorKey(prev => prev + 1); // Reset editor key
    } catch (error) {
      console.error('Error saving PDP item:', error);
      setApiError('Failed to save development item. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getPriorityColor = (level: string) => {
    switch (level) {
      case 'High Priority':
        return 'text-red-600 dark:text-red-400';
      case 'Medium Priority':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'Low Priority':
        return 'text-green-600 dark:text-green-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getProgressColor = (progress: string) => {
    switch (progress) {
      case 'Complete':
        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';
      case 'In Progress':
        return 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20';
      case 'Not Started':
        return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800';
    }
  };

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] dark:bg-gray-900 p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section */}
        <div className="text-center mb-10 mt-5">
          <h1 className="text-[3rem] font-light text-[#3793F7] dark:text-blue-400 mb-8 md:text-4xl lg:text-[3rem]">
            4.4 Personal Development Plan
          </h1>
        </div>

        {/* Static Instructions */}
        <GradientCard color="blue" >
          <div className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-start">
                <span className="text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold">1.</span>
                <span className="text-gray-700 dark:text-gray-300">
                  Write 3 unique goals that are important for you to achieve in the next 3-6months, using the SWOD Development Areas.
                </span>
              </div>
              <div className="flex items-start">
                <span className="text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold">2.</span>
                <span className="text-gray-700 dark:text-gray-300">
                  Think â€“ development of skills/tools/methods; acing a particular subject/test/paper; Internship / job placement success.
                </span>
              </div>
              <div className="flex items-start">
                <span className="text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold">3.</span>
                <span className="text-gray-700 dark:text-gray-300">
                  Avoid BIG Goals â€“ such as buying a dream home; becoming the CEO of a company â€“ those are years away if you are being practical. Right now identify goals that will significantly boost your confidence and purpose.
                </span>
              </div>
              <div className="flex items-start">
                <span className="text-[#3793F7] dark:text-blue-400 mr-3 text-lg font-bold">4.</span>
                <span className="text-gray-700 dark:text-gray-300">
                  Once you prepare your SMART Goals, revisit them on a daily basis and track your progress. Do not leave it as an academic or theoretical exercise.
                </span>
              </div>
            </div>
          </div>
        </GradientCard>

        {/* Reference Template Table */}
        <Accordion type="single" collapsible className="mb-8">
          <AccordionItem value="reference-template">
            <AccordionTrigger className="text-lg font-semibold text-gray-900 dark:text-gray-100 px-6 py-4 border-b border-gray-200 dark:border-gray-700">Reference Template</AccordionTrigger>
            <AccordionContent>
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-blue-600 text-white">
                      <tr>
                        <th className="px-3 py-2 text-left font-medium">GOALS</th>
                        <th className="px-3 py-2 text-left font-medium">IMPORTANT TASKS</th>
                        <th className="px-3 py-2 text-left font-medium">DIFFICULTY TO SUCCESS</th>
                        <th className="px-3 py-2 text-left font-medium">TIME TO COMPLETE</th>
                        <th className="px-3 py-2 text-left font-medium">PROGRESS</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800">
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        <td className="px-3 py-4 align-top">
                          <div className="space-y-2">
                            <p className="font-medium text-gray-900 dark:text-gray-100">Must be Specific & achievable</p>
                            <ul className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                              <li>â€¢ High on Time and importance</li>
                              <li>â€¢ Must be specific</li>
                              <li>â€¢ Effort 20% gets Effort 80% tasks</li>
                              <li>â€¢ High Priority</li>
                            </ul>
                          </div>
                        </td>
                        <td className="px-3 py-4 align-top">
                          <div className="space-y-2">
                            <p className="font-medium text-gray-900 dark:text-gray-100">High on Time and importance</p>
                            <ul className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                              <li>â€¢ High on Time and importance</li>
                              <li>â€¢ Must be specific</li>
                              <li>â€¢ Effort 80% tasks</li>
                              <li>â€¢ Low Priority</li>
                            </ul>
                          </div>
                        </td>
                        <td className="px-3 py-4 align-top">
                          <div className="space-y-2">
                            <p className="font-medium text-red-600">Write for EACH GOAL Completion criteria</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">Integrated conceptual learning</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">Feedback</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">Actual score on a test or paper</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">Quality of completeness of tasks in all aspects</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">Within budget</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">Others</p>
                          </div>
                        </td>
                        <td className="px-3 py-4 align-top">
                          <div className="space-y-2">
                            <p className="font-medium text-gray-900 dark:text-gray-100">Write for EACH TASK</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">How much timeâ€”specific time - hours/day/wk, deadlines, as applicable.</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">Budget slack time</p>
                          </div>
                        </td>
                        <td className="px-3 py-4 align-top">
                          <div className="space-y-2">
                            <p className="font-medium text-gray-900 dark:text-gray-100">Write for EACH progress</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">% Complete</p>
                          </div>
                        </td>
                      </tr>
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        <td className="px-3 py-4 align-top">
                          <div className="space-y-2">
                            <p className="font-medium text-blue-600">Example - "I will improve my math skills by 30% by the end of the semester"</p>
                            <ul className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                              <li>â€¢ Practice 1 test each week</li>
                              <li>â€¢ Study math for 1 hour daily</li>
                              <li>â€¢ Ask questions to teacher, tutor or classmates when confused</li>
                            </ul>
                          </div>
                        </td>
                        <td className="px-3 py-4 align-top">
                          <div className="space-y-2">
                            <p className="font-medium text-blue-600">Identifying learning videos</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">Make a 3 month study calendar</p>
                          </div>
                        </td>
                        <td className="px-3 py-4 align-top">
                          <p className="text-xs text-gray-600 dark:text-gray-400">80% score in mid-term test</p>
                        </td>
                        <td className="px-3 py-4 align-top">
                          <p className="text-xs text-gray-600 dark:text-gray-400">31/08/2024</p>
                        </td>
                        <td className="px-3 py-4 align-top">
                          <p className="text-blue-600 text-xs">In Progress</p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {/* Add New Item Button */}
        <div className="flex justify-end mb-6">
          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
            <SheetTrigger asChild>
              <Button className="bg-[#3793F7] hover:bg-blue-600 text-white" onClick={openAddModal}>
                <Plus className="w-4 h-4 mr-2" />
                Add Development Item
              </Button>
            </SheetTrigger>
            <SheetContent 
  className="overflow-y-auto p-8 bg-white dark:bg-gray-800"
  style={{ 
    width: '900px !important', 
    maxWidth: '90vw !important',
    minWidth: '900px'
  }}
>
           
              {/* Force width with CSS */}
              <style jsx>{`
                :global([data-radix-dialog-content]) {
                  width: 900px !important;
                  max-width: 90vw !important;
                  min-width: 900px !important;
                }

                :global(.fixed.inset-y-0.right-0.z-50) {
                  width: 900px !important;
                  max-width: 90vw !important;
                }

                @media (max-width: 1024px) {
                  :global([data-radix-dialog-content]) {
                    width: 90vw !important;
                    min-width: auto !important;
                  }

                  :global(.fixed.inset-y-0.right-0.z-50) {
                    width: 90vw !important;
                  }
                }
              `}</style>

              <SheetHeader className="mb-8">
                <SheetTitle className="text-xl">
                  {editingItem ? 'Edit Personal Development Item' : 'Add Personal Development Item'}
                </SheetTitle>
                <SheetDescription className="text-base mt-2">
                  {editingItem
                    ? 'Update your goal with specific tasks and timeline.'
                    : 'Create a new goal with specific tasks and timeline for your personal development plan.'
                  }
                </SheetDescription>
              </SheetHeader>

              <form onSubmit={handleSubmit} className="space-y-8">
                <div>
                  <Label htmlFor="goal" className="mb-2 block text-base font-medium">Goal</Label>
                  <div className="mb-4">
                    <RichTextEditor
                      key={`goal-${editorKey}`} // Use editorKey for force re-render
                      id="goal-editor"
                      content={formData.goal}
                      onChange={(html) => handleInputChange('goal', html)}
                      placeholder="Describe your specific, measurable goal..."
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="tasks" className="mb-2 block text-base font-medium">Tasks</Label>
                  <div className="mb-4">
                    <RichTextEditor
                      key={`tasks-${editorKey}`} // Use editorKey for force re-render
                      id="tasks-editor"
                      content={formData.tasks}
                      onChange={(html) => handleInputChange('tasks', html)}
                      placeholder="List the specific tasks you will complete..."
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="importantTasks" className="mb-2 block text-base font-medium">Most Important Tasks</Label>
                  <div className="mb-4">
                    <RichTextEditor
                      key={`important-tasks-${editorKey}`} // Use editorKey for force re-render
                      id="important-tasks-editor"
                      content={formData.importantTasks}
                      onChange={(html) => handleInputChange('importantTasks', html)}
                      placeholder="Identify the most critical tasks for success..."
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="difficultyLevel" className="mb-2 block text-base font-medium">Priority Level</Label>
                    <Select onValueChange={(value) => handleInputChange('difficultyLevel', value)} value={formData.difficultyLevel} required>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="Select priority level" />
                      </SelectTrigger>
                      < SelectContent className="bg-white dark:bg-gray-800">
                        <SelectItem value="High Priority">High Priority</SelectItem>
                        <SelectItem value="Medium Priority">Medium Priority</SelectItem>
                        <SelectItem value="Low Priority">Low Priority</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="timeToComplete" className="mb-2 block text-base font-medium">Target Completion Date</Label>
                    <Input
                      id="timeToComplete"
                      type="date"
                      value={formData.timeToComplete}
                      onChange={(e) => handleInputChange('timeToComplete', e.target.value)}
                      required
                      className="h-11"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="progress" className="mb-2 block text-base font-medium">Progress Status</Label>
                  <Select onValueChange={(value) => handleInputChange('progress', value)} value={formData.progress}>
                    <SelectTrigger className="h-11">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-white dark:bg-gray-800">
                      <SelectItem value="Not Started">Not Started</SelectItem>
                      <SelectItem value="In Progress">In Progress</SelectItem>
                      <SelectItem value="Complete">Complete</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <SheetFooter className="flex gap-4 pt-8 mt-8 border-t">
                  <Button type="button" variant="outline" onClick={() => setIsSheetOpen(false)} className="px-6 py-2">
                    Cancel
                  </Button>
                  <Button type="submit" className="bg-[#3793F7] hover:bg-blue-600 px-6 py-2">
                    {editingItem ? 'Update Item' : 'Add Item'}
                  </Button>
                </SheetFooter>
              </form>
            </SheetContent>
          </Sheet>
        </div>

        {/* User's Development Items Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-8">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              My Personal Development Goals ({developmentItems.length})
            </h2>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-10">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : developmentItems.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Goal
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Tasks
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Important Tasks
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Target Date
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {developmentItems.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-4 py-4 text-sm text-gray-900 dark:text-gray-100">
                        <div className="max-w-xs">
                          <div
                            className="font-medium text-sm prose prose-sm dark:prose-invert max-w-none [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4"
                            dangerouslySetInnerHTML={{ __html: item.goal }}
                          />
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-600 dark:text-gray-300">
                        <div className="max-w-xs">
                          <div
                            className="text-sm prose prose-sm dark:prose-invert max-w-none line-clamp-3 [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4"
                            dangerouslySetInnerHTML={{ __html: item.tasks }}
                          />
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-600 dark:text-gray-300">
                        <div className="max-w-xs">
                          <div
                            className="text-sm prose prose-sm dark:prose-invert max-w-none line-clamp-3 [&>ul]:list-disc [&>ol]:list-decimal [&>ul]:ml-4 [&>ol]:ml-4"
                            dangerouslySetInnerHTML={{ __html: item.importantTasks }}
                          />
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm">
                        <span className={`font-medium ${getPriorityColor(item.difficultyLevel)}`}>
                          {item.difficultyLevel}
                        </span>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-600 dark:text-gray-300">
                        {new Date(item.timeToComplete).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-4 text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getProgressColor(item.progress)}`}>
                          {item.progress}
                        </span>
                      </td>
                      <td className="px-4 py-4 text-sm">
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditModal(item)}
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                            title="Edit"
                          >
                            <Edit2 className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(item.id)}
                            className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                            title="Delete"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="px-6 py-8 text-center">
              <p className="text-gray-500 dark:text-gray-400">
                No personal development goals added yet. Click "Add Development Item" to create your first goal.
              </p>
            </div>
          )}
        </div>

        {/* Footer Text */}
        <p className="my-8 leading-relaxed max-w-[980px] mx-auto text-gray-900 dark:text-gray-200">
          Your personal development plan is a living document that should be reviewed and updated regularly.
          Track your progress, celebrate achievements, and adjust goals as needed to ensure continuous growth
          and development in both personal and professional areas.
        </p>

        {/* Continue Button */}
        <NavigationButton
          text="CONTINUE"
          href="/4_5_conclusion"
        />

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}