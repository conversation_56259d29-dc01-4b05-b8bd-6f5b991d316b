"use client";

import React from 'react';
import Image from 'next/image';
import { useAssessment } from '@/context/AssessmentContext';
import Footer from '@/components/Footer';
import { cn } from '@/lib/utils';

export default function ConclusionPage() {
  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6">
      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-8 relative">
        {/* Header Section - Image on left, title on right */}
        <div className="flex flex-col md:flex-row relative mb-10 mt-5 items-center">
          <div className="flex-none w-full md:w-[400px] lg:w-[400px] relative">
            <div className="relative w-full h-[400px]">
              <Image
                src="/4.3TM.svg"
                alt="Conclusion illustration"
                fill
                style={{ objectFit: "contain" }}
                priority
              />
            </div>
          </div>

          <div className="flex-1 md:pl-10 mt- pt-16 md:mt-0">
            <h1 className="text-[3rem] font-light text-[#3793F7] mb-4 md:text-4xl lg:text-[3rem]">Conclusion</h1>

            <p className="mb-10 leading-relaxed max-w-[600px]">
              By understanding your strengths, aligning them with
              suitable career paths, and mastering essential jobsearch skills like resume building and interview
              preparation, you equip yourself for long-term
              professional success. With these insights, you can
              confidently pursue career opportunities that fit your
              abilities, values, and aspirations while continuously
              adapting to future growth.
            </p>
          </div>
        </div>

        {/* Divider */}
        <div className="w-full h-px bg-gray-300 my-16"></div>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
}