"use client";

import React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Footer from '@/components/Footer';
import NavigationButton from '@/components/NavigationButton';
import EyeIcon from '@/components/EyeIcon';
import { useAssessment } from '@/context/AssessmentContext';
import { useSidebar } from '@/context/SidebarContext';

export default function LandingPageV2() {
  // Get assessment data from context
  const { assessmentData, loading, error, isInitialized } = useAssessment();
  const { isExpanded } = useSidebar();
  const router = useRouter();
  
  // Extract first name only from the full name
  // Remove any trailing period and get just the first name
  const firstName = assessmentData?.individual_name 
    ? assessmentData.individual_name.replace(/\.$/, '').split(' ')[0]
    : 'there';

  // Debug - log the assessment data to console
  console.log("Assessment data:", assessmentData);
  console.log("Name from assessment:", assessmentData?.individual_name);
  console.log("First name extracted:", firstName);

  // Loading indicator while fetching data
  if (loading && !isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-xl text-blue-500 mb-2">Loading your personalized report...</p>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        </div>
      </div>
    );
  }

  // Error display
  if (error && !loading) {
    console.error("Error loading assessment data:", error);
    // Continue with default value since we don't want to block the experience
  }

  return (
    <div className="w-full -mt-8 mx-auto pb-0 relative bg-[#f1f1f1] p-4 md:p-6">
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap');

        * {
          box-sizing: border-box;
          font-family: 'Roboto', Arial, sans-serif;
        }

        body {
          color: #212121;
        }
      `}</style>

      {/* Header with Background Image */}
      <div className="relative h-[300px] bg-center bg-cover flex items-center justify-center" 
           style={{ backgroundImage: 'url(/HeaderImage.svg)' }}>
        <div className="text-center flex flex-col items-center justify-center">
          <Image
            src="/Kaleidoscope-Logo_WhiteText.svg"
            alt="Kaleidoscope Logo"
            width={400}
            height={100}
            className="w-[400px] h-[100px] sm:w-[400px] sm:h-[100px]"
            priority
          />
          <div className="mt-3">
            <Image
              src="/TM_Logo_Black.svg"
              alt="TalentMetrix Logo"
              width={150}
              height={40}
              style={{ filter: 'brightness(0) invert(1)' }}
              priority
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full max-w-[1100px] mx-auto px-5 pt-0 mt-0 relative">
        {/* Introduction Section */}
        <div className="flex flex-col md:flex-row gap-0 mb-4 -mb-20 items-center">
          {/* <div className="flex-none w-full md:w-auto relative flex justify-center -ml-[250px]">
            <Image
              src="/KaleidoscopeHexagon.svg"
              alt="Kaleidoscope Hexagon"
              width={760}
              height={760}
              className="max-w-full h-auto"
              priority
            />
          </div> */}

          <div className="flex-1 md:ml-24 mt-8 mb-8 mr-[20px]">
            <h2 className="text-[2.5rem] font-light text-[#3793F7] mb-4 leading-tight">
              Hello {firstName},
            </h2>

            <p className="mb-4 leading-relaxed">
              This report provides a comprehensive analysis of your behavioural
              tendencies – how you typically think, feel, and act across different
              situations, energizing forces – intrinsic and extrinsic, and key
              competencies – Time Management, Networking Skills, Teamwork,
              Conflict Handling, offering valuable insights into your academic
              and professional journey.
            </p>

            <p className="mb-4 leading-relaxed">
              Understanding your strengths and stressors can help you perform
              better and maintain well-being.
            </p>

            <p className="mb-4 leading-relaxed">
              It suggests career paths that match your natural abilities and offers
              tips for resume building and interview preparation. Using these
              insights will help you make informed career decisions and succeed
              with confidence.
            </p>
          </div>
        </div>

        {/* Four Sections */}
        <div className="-mt-8">
          <h2 className="text-[2rem] font-light text-[#3793F7] mb-3 text-center">Your Kaleidoscope</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-3 max-w-[900px] mx-auto">
            {/* Section 1 */}
            <div 
              className="bg-[#4A3F35] p-6 shadow-md rounded-tl-[120px] h-[220px] relative overflow-visible text-white bg-[url('/Quarter1_BG.svg')] bg-contain bg-no-repeat bg-top-left cursor-pointer"
              onClick={() => router.push('/1_1_understand_yourself')}
            >
              <div className="flex items-center justify-center absolute z-5 w-[63px] h-[63px] left-[50px] top-[35px] -translate-x-1/2 -translate-y-1/2">
                <Image
                  src="/Icon1.svg"
                  alt="Understand Yourself"
                  width={63}
                  height={63}
                  className="section-icon"
                />
              </div>
              <div className="relative z-1 pl-[70px] pt-[10px]">
                <div className="text-[1.5rem] font-bold text-[#3793F7] mb-2">01</div>
                <h3 className="text-[1.3rem] font-medium text-white mb-3">Understand Yourself</h3>
                <p className="text-[0.95rem] leading-relaxed text-white/80 line-clamp-4">
                  Provides insights into your natural
                  behaviours, strengths, and limitations,
                  helping you recognize how you interact with
                  others and respond to different situations.
                </p>
              </div>
            </div>

            {/* Section 2 */}
            <div 
              className="bg-[#4A3F35] p-6 shadow-md rounded-tr-[120px] h-[220px] relative overflow-visible text-white bg-[url('/Quarter2_BG.svg')] bg-contain bg-no-repeat bg-top-right cursor-pointer"
              onClick={() => router.push('/2_1_making_impact')}
            >
              <div className="flex items-center justify-center absolute z-5 w-[63px] h-[63px] right-[50px] top-[35px] translate-x-1/2 -translate-y-1/2">
                <Image
                  src="/Icon2.svg"
                  alt="How You Make An Impact"
                  width={63}
                  height={63}
                  className="section-icon"
                />
              </div>
              <div className="relative z-1 text-right pr-[70px] pt-[12px]">
                <div className="text-[1.5rem] font-bold text-[#3793F7] mb-2">02</div>
                <h3 className="text-[1.3rem] font-medium text-white mb-3">How You Make An Impact</h3>
                <p className="text-[0.95rem] leading-relaxed text-white/80 line-clamp-4">
                  Examines how your strengths,
                  limitations, and stressors influence your
                  academic and professional performance.
                </p>
              </div>
            </div>

            {/* Section 4 */}
            <div 
              className="bg-[#4A3F35] p-6 shadow-md rounded-bl-[120px] h-[220px] relative overflow-visible text-white bg-[url('/Quarter4_BG.svg')] bg-contain bg-no-repeat bg-bottom-left cursor-pointer"
              onClick={() => router.push('/4_1_embark_on_success_path')}
            >
              <div className="flex items-center justify-center absolute z-5 w-[63px] h-[63px] left-[55px] bottom-[25px] -translate-x-1/2 translate-y-1/2">
                <Image
                  src="/Icon4.svg"
                  alt="Embark on Your Success Path"
                  width={63}
                  height={63}
                  className="section-icon"
                />
              </div>
              <div className="relative z-1 pl-[70px] pb-[18px] flex flex-col justify-end h-full">
                <div className="text-[1.5rem] font-bold text-[#3793F7] mb-2">04</div>
                <h3 className="text-[1.3rem] font-medium text-white mb-3">Embark on Your Success Path</h3>
                <p className="text-[0.95rem] leading-relaxed text-white/80 line-clamp-4">
                  Focuses on creating a personalized
                  development plan to enhance your
                  strengths and address areas for growth.
                </p>
              </div>
            </div>

            {/* Section 3 */}
            <div 
              className="bg-[#4A3F35] p-6 shadow-md rounded-br-[120px] h-[220px] relative overflow-visible text-white bg-[url('/Quarter3_BG.svg')] bg-contain bg-no-repeat bg-bottom-right cursor-pointer"
              onClick={() => router.push('/3_1_career_options_for_you')}
            >
              <div className="flex items-center justify-center absolute z-5 w-[63px] h-[63px] right-[50px] bottom-[35px] translate-x-1/2 translate-y-1/2">
                <Image
                  src="/Icon3.svg"
                  alt="Career Options for You"
                  width={63}
                  height={63}
                  className="section-icon"
                />
              </div>
              <div className="relative z-1 text-right pr-[70px] pb-[40px] flex flex-col justify-end h-full">
                <div className="text-[1.5rem] font-bold text-[#3793F7] mb-2">03</div>
                <h3 className="text-[1.3rem] font-medium text-white mb-3">Career Options for You</h3>
                <p className="text-[0.95rem] leading-relaxed text-white/80 line-clamp-4">
                  Helps you discover careers that align with
                  your strengths and preferences.
                </p>
              </div>
            </div>
          </div>

          {/* View Report Button */}
          <NavigationButton
            text="VIEW REPORT"
            href="/1_1_understand_yourself"
          />
          
          {/* Footer */}
          <Footer />
        </div>
      </div>
    </div>
  );
}