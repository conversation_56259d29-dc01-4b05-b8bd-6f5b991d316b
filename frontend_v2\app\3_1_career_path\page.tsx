"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import Footer from '@/components/Footer';

export default function CareerPathPage() {
  const [expandedSection, setExpandedSection] = useState<number | null>(1); // Set first section expanded by default

  const toggleSection = (sectionId: number | null) => {
    if (expandedSection === sectionId) {
      setExpandedSection(null);
    } else {
      setExpandedSection(sectionId);
    }
  };

  return (
    <div className="career-path-container">
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap');

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
          font-family: 'Ubuntu', sans-serif;
        }

        body {
          background-color: #f1f1f1;
          color: #212121;
        }
      `}</style>

      <style jsx>{`
        .career-path-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 30px 20px 100px;
          position: relative;
          background-color: #f1f1f1;
        }
        
        .page-title {
          font-size: 2.5rem;
          font-weight: 300;
          color: #3793F7;
          margin-bottom: 20px;
          margin-top: 24px;
        }
        
        .page-subtitle {
          font-size: 1rem;
          color: #212121;
          margin-bottom: 24px;
          line-height: 1.5;
          max-width: 980px;
        }
        
        .section {
          border-radius: 16px;
          margin-bottom: 16px;
          overflow: hidden;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          position: relative;
          background-color: white;
          max-width: 980px;
          margin-left: auto;
          margin-right: auto;
        }
        
        .section::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, 
            rgba(55, 147, 247, 0.7) 0%, 
            rgba(55, 147, 247, 0.2) 10%, 
            rgba(55, 147, 247, 0.05) 20%,
            rgba(55, 147, 247, 0) 30%
          );
          border-radius: 16px;
          z-index: 0;
          pointer-events: none;
        }
        
        .section-header {
          padding: 16px 24px;
          font-size: 1.1rem;
          font-weight: 500;
          color: #212121;
          display: flex;
          justify-content: space-between;
          align-items: center;
          cursor: pointer;
          position: relative;
          z-index: 2;
        }
        
        .section-content {
          padding: 0 24px 24px 24px;
          position: relative;
          z-index: 2;
        }
        
        .description {
          color: #212121;
          line-height: 1.5;
          margin-bottom: 20px;
          font-size: 0.95rem;
        }
        
        .roles {
          margin-top: 16px;
        }
        
        .roles-title {
          font-weight: 500;
          color: #212121;
          margin-bottom: 12px;
        }
        
        .role-list {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }
        
        @media (min-width: 640px) {
          .role-list {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
          }
        }
        
        .role-item {
          display: flex;
          align-items: flex-start;
        }
        
        .role-bullet {
          color: #ffaa00;
          margin-right: 8px;
          font-size: 1.2rem;
          font-weight: bold;
          line-height: 1;
        }
        
        .role-name {
          font-weight: 500;
          color: #212121;
          font-size: 0.9rem;
        }
        
        .continue-button-wrapper {
          display: flex;
          justify-content: center;
          margin-top: 32px;
          margin-bottom: 20px;
        }
        
        .continue-button {
          display: flex;
          align-items: center;
          background-color: #E2E2E2;
          color: #58595B;
          border: 1px solid #AFAFAF;
          border-radius: 24px;
          padding: 8px 20px;
          font-size: 0.9rem;
          font-weight: 400;
          cursor: pointer;
          transition: background-color 0.2s;
          text-decoration: none;
        }
        
        .continue-button:hover {
          background-color: #d0d0d0;
        }
        
        .arrow-icon {
          margin-left: 8px;
          width: 16px;
          height: 16px;
          fill: #58595B;
        }
        
        .down-arrow {
          width: 16px;
          height: 16px;
          display: inline-block;
          color: #58595B;
          transition: transform 0.3s ease;
        }
        
        .rotated {
          transform: rotate(180deg);
        }
      `}</style>

      {/* Page Title and Subtitle */}
      <h1 className="page-title">3.1 Recommended Career Paths</h1>
      <p className="page-subtitle">
        Based on your profile assessment, the following career paths are well-aligned with your skills, 
        preferences, and working style. Explore each option to learn more about potential roles and their fit with your profile.
      </p>

      {/* Quality Assurance Section */}
      <div className="section">
        <div 
          className="section-header"
          onClick={() => toggleSection(1)}
        >
          <span>1. Quality Assurance</span>
          <span className={`down-arrow ${expandedSection === 1 ? 'rotated' : ''}`}>▼</span>
        </div>
        {expandedSection === 1 && (
          <div className="section-content">
            <p className="description">
              You have a natural ability for creating and maintaining systems to ensure consistent 
              quality. You're good at anticipating potential problems. Your analytical mind would thrive 
              in an environment where thoroughness and precision are valued above speed.
            </p>
            
            <div className="roles">
              <h4 className="roles-title">Roles</h4>
              <div className="role-list">
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Quality Assurance<br />Manager</span>
                </div>
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Quality Systems<br />Analyst</span>
                </div>
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Quality Control<br />Specialist</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Data Analytics Section */}
      <div className="section">
        <div 
          className="section-header"
          onClick={() => toggleSection(2)}
        >
          <span>2. Data Analytics</span>
          <span className={`down-arrow ${expandedSection === 2 ? 'rotated' : ''}`}>▼</span>
        </div>
        {expandedSection === 2 && (
          <div className="section-content">
            <p className="description">
              You excel at analyzing and interpreting complex data sets to identify trends and insights.
              Your logical approach to problem-solving and attention to detail would be valuable in roles
              that require making data-driven decisions and communicating findings clearly.
            </p>
            
            <div className="roles">
              <h4 className="roles-title">Roles</h4>
              <div className="role-list">
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Data Analyst</span>
                </div>
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Business Intelligence<br />Specialist</span>
                </div>
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Market Research<br />Analyst</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Financial Control Section */}
      <div className="section">
        <div 
          className="section-header"
          onClick={() => toggleSection(3)}
        >
          <span>3. Financial Control</span>
          <span className={`down-arrow ${expandedSection === 3 ? 'rotated' : ''}`}>▼</span>
        </div>
        {expandedSection === 3 && (
          <div className="section-content">
            <p className="description">
              You have a natural aptitude for managing financial processes and ensuring accuracy in 
              financial reporting. Your methodical approach and eye for detail would be valuable in roles
              focused on maintaining financial integrity and compliance.
            </p>
            
            <div className="roles">
              <h4 className="roles-title">Roles</h4>
              <div className="role-list">
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Financial<br />Controller</span>
                </div>
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Compliance<br />Analyst</span>
                </div>
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Audit<br />Specialist</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Business Financial Services Section */}
      <div className="section">
        <div 
          className="section-header"
          onClick={() => toggleSection(4)}
        >
          <span>4. Business Financial Services</span>
          <span className={`down-arrow ${expandedSection === 4 ? 'rotated' : ''}`}>▼</span>
        </div>
        {expandedSection === 4 && (
          <div className="section-content">
            <p className="description">
              You demonstrate skill in analyzing financial data to drive business strategy and growth.
              Your ability to connect financial insights with business objectives would be valuable in
              roles that bridge finance and other business functions.
            </p>
            
            <div className="roles">
              <h4 className="roles-title">Roles</h4>
              <div className="role-list">
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Financial<br />Analyst</span>
                </div>
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Business Finance<br />Manager</span>
                </div>
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Financial<br />Consultant</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Project Management Section */}
      <div className="section">
        <div 
          className="section-header"
          onClick={() => toggleSection(5)}
        >
          <span>5. Project Management</span>
          <span className={`down-arrow ${expandedSection === 5 ? 'rotated' : ''}`}>▼</span>
        </div>
        {expandedSection === 5 && (
          <div className="section-content">
            <p className="description">
              You show aptitude for coordinating resources, timelines, and deliverables to achieve project goals.
              Your organizational skills and ability to manage competing priorities would be valuable in
              roles that require bringing structure to complex initiatives.
            </p>
            
            <div className="roles">
              <h4 className="roles-title">Roles</h4>
              <div className="role-list">
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Project<br />Manager</span>
                </div>
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Program<br />Coordinator</span>
                </div>
                <div className="role-item">
                  <span className="role-bullet">+</span>
                  <span className="role-name">Operations<br />Specialist</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Continue Button */}
      <div className="continue-button-wrapper">
        <Link href="/3_2_resume_building" className="continue-button">
          CONTINUE
          <svg xmlns="http://www.w3.org/2000/svg" className="arrow-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </Link>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}
