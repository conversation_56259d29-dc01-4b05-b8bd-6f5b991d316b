import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/options';

export async function GET(request: NextRequest) {
  try {
    // Get email from query params
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return NextResponse.json(
        { error: 'Email parameter is required' },
        { status: 400 }
      );
    }

    // Get session and token
    const session = await getServerSession(authOptions);
    const accessToken = session?.user?.access_token;
    if (!accessToken) {
      // Return 401 with a special header to indicate auth error
      const response = NextResponse.json(
        { error: 'Unauthorized: No access token', redirectToLogin: true },
        { status: 401 }
      );
      // Add a special header to indicate this is an auth error
      response.headers.set('X-Auth-Required', 'true');
      return response;
    }

    // Call the backend API endpoint
    const backendUrl = 'http://localhost:8000/api/v1/assessment?force_new=false';
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({ email }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend API error:', errorText);
      return NextResponse.json(
        { error: `Backend API error: ${response.status}` },
        { status: response.status }
      );
    }
    
    // Return the data from the backend
    const assessmentData = await response.json();
    return NextResponse.json(assessmentData);
    
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 
