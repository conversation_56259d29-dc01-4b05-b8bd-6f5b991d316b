import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

// Define authOptions for NextAuth
export const authOptions: NextAuthOptions = {
  debug: true, // Enable debug logs
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req) {
        console.log("🔑 Starting authentication process...");
        console.log("📧 Email being used:", credentials?.email);

        if (!credentials?.email || !credentials?.password) {
          console.error("❌ Missing credentials");
          throw new Error("Missing credentials");
        }

        try {
          const loginUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/auth/login`;
          console.log("🌐 Attempting login at:", loginUrl);

          const formData = new URLSearchParams({
            username: credentials.email,
            password: credentials.password,
          });

          console.log("📦 Request payload:", {
            url: loginUrl,
            method: "POST",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              "Accept": "application/json",
            },
            body: formData.toString()
          });

          const response = await fetch(loginUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              "Accept": "application/json",
            },
            body: formData,
          });

          console.log("📥 Login response status:", response.status);
          const responseText = await response.text();
          console.log("📄 Raw response:", responseText);

          if (!response.ok) {
            console.error("❌ Authentication failed:", responseText);
            // It's often better to return null here and let NextAuth handle the error display
            // throw new Error(responseText); 
            return null; 
          }

          let authResponse;
          try {
            authResponse = JSON.parse(responseText);
            console.log("🔓 Auth response parsed:", authResponse);
          } catch (e) {
            console.error("❌ Failed to parse auth response:", e);
            // return null;
            throw new Error("Invalid response format from server"); // Or return null
          }

          if (!authResponse.access_token) {
            console.error("❌ No access token in response");
            // return null;
            throw new Error("No access token received"); // Or return null
          }

          // Decode JWT to extract role
          let role = 'candidate';
          try {
            const payload = JSON.parse(Buffer.from(authResponse.access_token.split('.')[1], 'base64').toString());
            role = payload.role || 'candidate';
          } catch (e) {
            console.warn('Could not decode JWT for role, defaulting to candidate', e);
          }

          return {
            id: credentials.email, // Should ideally be a unique ID from your backend
            email: credentials.email,
            name: credentials.email.split('@')[0], // Or a name from your backend
            access_token: authResponse.access_token,
            role,
          };

        } catch (error) {
          console.error("❌ Authorization error:", error);
          // In authorize, returning null signals an authentication failure to NextAuth
          // Throwing an error here can sometimes lead to unhandled promise rejections
          // or redirect to an error page with the error message in the URL.
          // Consider returning null for most auth failures.
          return null; 
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      // console.log("🔄 JWT Callback - Input:", { 
      //   token: { ...token, access_token: token.access_token ? '[REDACTED]' : undefined },
      //   user: user ? { ...user, access_token: user.access_token ? '[REDACTED]' : undefined } : undefined 
      // });
      // Simplified logging for user object to avoid potential issues with spreading undefined access_token
      // console.log("User object in JWT callback:", user);


      if (user) {
        token.access_token = (user as any).access_token; // Cast user if access_token is not on default User type
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.role = (user as any).role; // Cast user if role is not on default User type
        
        // if ((user as any).access_token) {
        //   console.log("Raw token first 20 chars:", (user as any).access_token.substring(0, 20));
        // }
      }

      // console.log("🔄 JWT Callback - Output:", { 
      //   ...token, 
      //   access_token: token.access_token ? '[REDACTED]' : undefined 
      // });
      return token;
    },
    async session({ session, token }) {
      // console.log("🔄 Session Callback - Input:", {
      //   session: { ...session, user: { ...session.user, access_token: undefined } },
      //   token: { ...token, access_token: token.access_token ? '[REDACTED]' : undefined }
      // });

      if (token && session.user) { // Ensure session.user exists
        (session.user as any).id = token.id; // Cast session.user to add custom properties
        (session.user as any).access_token = token.access_token;
        session.user.email = token.email; // email and name are usually on the default Session['user']
        session.user.name = token.name;
        (session.user as any).role = token.role;
      }

      // console.log("🔄 Session Callback - Output:", {
      //   ...session,
      //   user: session.user ? { ...session.user, access_token: (session.user as any).access_token ? '[REDACTED]' : undefined } : undefined
      // });
      return session;
    },
  },
  pages: {
    signIn: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET, // IMPORTANT: Ensure this is set
};
