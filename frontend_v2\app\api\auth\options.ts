import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

console.log("🔧 Auth Options - Environment Check:");
console.log("🌐 NEXT_PUBLIC_API_URL:", process.env.NEXT_PUBLIC_API_URL);
console.log("🔐 NEXTAUTH_SECRET:", process.env.NEXTAUTH_SECRET ? "Present" : "Not present");
console.log("🌍 NODE_ENV:", process.env.NODE_ENV);

export const authOptions: NextAuthOptions = {
  debug: true, // Enable debug logs
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req) {
        console.log("🔑 Starting authentication process...");
        console.log("📧 Email being used:", credentials?.email);
        console.log("🔒 Password provided:", !!credentials?.password);
        console.log("📦 Full credentials object:", {
          email: credentials?.email,
          passwordLength: credentials?.password?.length || 0,
          hasPassword: !!credentials?.password
        });
        console.log("🌐 Request object:", req ? "Present" : "Not present");

        if (!credentials?.email || !credentials?.password) {
          console.error("❌ Missing credentials");
          console.error("❌ Email missing:", !credentials?.email);
          console.error("❌ Password missing:", !credentials?.password);
          throw new Error("Missing credentials");
        }

        try {
          const loginUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/auth/login-simple`;
          console.log("🌐 Attempting login at:", loginUrl);
          console.log("🔧 Environment variables:", {
            NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
            NODE_ENV: process.env.NODE_ENV
          });

          const loginPayload = {
            username: credentials.email,
            password: credentials.password,
          };

          console.log("📦 JSON payload being sent:", {
            username: credentials.email,
            passwordLength: credentials.password.length
          });

          console.log("🚀 Making fetch request...");
          const response = await fetch(loginUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Accept": "application/json",
            },
            body: JSON.stringify(loginPayload),
          });

          console.log("📥 Login response received");
          console.log("📊 Response status:", response.status);
          console.log("📊 Response ok:", response.ok);
          console.log("📊 Response headers:", Object.fromEntries(response.headers.entries()));

          const responseText = await response.text();
          console.log("📄 Raw response text:", responseText);

          if (!response.ok) {
            console.error("❌ Authentication failed");
            console.error("❌ Status:", response.status);
            console.error("❌ Status text:", response.statusText);
            console.error("❌ Response body:", responseText);
            return null;
          }

          let authResponse;
          try {
            authResponse = JSON.parse(responseText);
            console.log("🔓 Auth response parsed:", authResponse);
          } catch (e) {
            console.error("❌ Failed to parse auth response:", e);
            throw new Error("Invalid response format from server");
          }

          if (!authResponse.access_token) {
            console.error("❌ No access token in response");
            throw new Error("No access token received");
          }

          // Decode JWT to extract role
          let role = 'candidate';
          try {
            const payload = JSON.parse(Buffer.from(authResponse.access_token.split('.')[1], 'base64').toString());
            role = payload.role || 'candidate';
          } catch (e) {
            console.warn('Could not decode JWT for role, defaulting to candidate', e);
          }

          return {
            id: credentials.email,
            email: credentials.email,
            name: credentials.email.split('@')[0],
            access_token: authResponse.access_token,
            role,
          };

        } catch (error) {
          console.error("❌ Authorization error:", error);
          return null; 
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      console.log("🔄 JWT Callback triggered");
      console.log("👤 User object:", user ? "Present" : "Not present");
      console.log("🎫 Token object keys:", Object.keys(token));

      if (user) {
        console.log("✅ User found, updating token");
        console.log("🔑 User access_token:", (user as any).access_token ? "Present" : "Not present");
        console.log("📧 User email:", user.email);
        console.log("🆔 User id:", user.id);
        console.log("👤 User name:", user.name);
        console.log("🎭 User role:", (user as any).role);

        token.access_token = (user as any).access_token;
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.role = (user as any).role;

        console.log("🎫 Updated token:", {
          access_token: token.access_token ? "Present" : "Not present",
          email: token.email,
          id: token.id,
          name: token.name,
          role: token.role
        });
      } else {
        console.log("ℹ️ No user object, keeping existing token");
      }

      return token;
    },
    async session({ session, token }) {
      console.log("🔄 Session Callback triggered");
      console.log("🎫 Token object:", token ? "Present" : "Not present");
      console.log("👤 Session user before update:", session.user);

      if (token && session.user) {
        console.log("✅ Token and session.user found, updating session");
        console.log("🎫 Token data:", {
          access_token: token.access_token ? "Present" : "Not present",
          email: token.email,
          id: token.id,
          name: token.name,
          role: token.role
        });

        (session.user as any).id = token.id;
        (session.user as any).access_token = token.access_token;
        session.user.email = token.email;
        session.user.name = token.name;
        (session.user as any).role = token.role;

        console.log("👤 Updated session user:", {
          access_token: (session.user as any).access_token ? "Present" : "Not present",
          email: session.user.email,
          id: (session.user as any).id,
          name: session.user.name,
          role: (session.user as any).role
        });
      } else {
        console.log("⚠️ Missing token or session.user");
        console.log("🎫 Token present:", !!token);
        console.log("👤 Session.user present:", !!session.user);
      }

      return session;
    },
  },
  pages: {
    signIn: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
};