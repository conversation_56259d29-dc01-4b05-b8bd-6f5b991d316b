import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

export const authOptions: NextAuthOptions = {
  debug: true, // Enable debug logs
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req) {
        console.log("🔑 Starting authentication process...");
        console.log("📧 Email being used:", credentials?.email);

        if (!credentials?.email || !credentials?.password) {
          console.error("❌ Missing credentials");
          throw new Error("Missing credentials");
        }

        try {
          const loginUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/auth/login`;
          console.log("🌐 Attempting login at:", loginUrl);

          const formData = new URLSearchParams({
            username: credentials.email,
            password: credentials.password,
          });

          const response = await fetch(loginUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
              "Accept": "application/json",
            },
            body: formData,
          });

          console.log("📥 Login response status:", response.status);
          const responseText = await response.text();

          if (!response.ok) {
            console.error("❌ Authentication failed:", responseText);
            return null; 
          }

          let authResponse;
          try {
            authResponse = JSON.parse(responseText);
            console.log("🔓 Auth response parsed:", authResponse);
          } catch (e) {
            console.error("❌ Failed to parse auth response:", e);
            throw new Error("Invalid response format from server");
          }

          if (!authResponse.access_token) {
            console.error("❌ No access token in response");
            throw new Error("No access token received");
          }

          // Decode JWT to extract role
          let role = 'candidate';
          try {
            const payload = JSON.parse(Buffer.from(authResponse.access_token.split('.')[1], 'base64').toString());
            role = payload.role || 'candidate';
          } catch (e) {
            console.warn('Could not decode JWT for role, defaulting to candidate', e);
          }

          return {
            id: credentials.email,
            email: credentials.email,
            name: credentials.email.split('@')[0],
            access_token: authResponse.access_token,
            role,
          };

        } catch (error) {
          console.error("❌ Authorization error:", error);
          return null; 
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.access_token = (user as any).access_token;
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.role = (user as any).role;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        (session.user as any).id = token.id;
        (session.user as any).access_token = token.access_token;
        session.user.email = token.email;
        session.user.name = token.name;
        (session.user as any).role = token.role;
      }
      return session;
    },
  },
  pages: {
    signIn: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
};