import { NextRequest, NextResponse } from 'next/server';

/**
 * Health check endpoint
 * GET /api/hello
 * 
 * This endpoint can be used for:
 * - Health checks
 * - Monitoring
 * - Load balancer checks
 * - Uptime monitoring
 * 
 * No authentication required.
 */
export async function GET(request: NextRequest) {
  try {
    const timestamp = new Date().toISOString();
    const uptime = process.uptime();
    
    return NextResponse.json({
      status: 'ok',
      message: 'Hello! API is running successfully.',
      timestamp: timestamp,
      uptime: `${Math.floor(uptime)} seconds`,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      service: 'TalentMetrix Frontend API'
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'Health check failed',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}

/**
 * Also support POST for more comprehensive health checks
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const timestamp = new Date().toISOString();
    
    return NextResponse.json({
      status: 'ok',
      message: 'Hello! API is running and can process POST requests.',
      timestamp: timestamp,
      receivedData: body,
      service: 'TalentMetrix Frontend API'
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('POST health check error:', error);
    
    return NextResponse.json({
      status: 'error',
      message: 'POST health check failed',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}
