import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/options';

export async function POST(request: NextRequest) {
  try {
    // Get session and token
    const session = await getServerSession(authOptions);
    const accessToken = session?.user?.access_token;
    if (!accessToken) {
      return NextResponse.json(
        { error: 'Unauthorized: No access token' },
        { status: 401 }
      );
    }
    
    // Get password data from request
    const passwordData = await request.json();
    const { current_password, new_password } = passwordData;
    
    // Validate required fields
    if (!current_password || !new_password) {
      return NextResponse.json(
        { error: 'Both current password and new password are required' },
        { status: 400 }
      );
    }
    
    // Basic validation for new password
    if (new_password.length < 8) {
      return NextResponse.json(
        { error: 'New password must be at least 8 characters long' },
        { status: 400 }
      );
    }
    
    // Call backend API to reset password
    const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/auth/reset-password`;
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({ 
        current_password, 
        new_password 
      }),
    });
    
    // Handle response
    if (!response.ok) {
      const errorText = await response.text();
      
      try {
        // Try to parse error response as JSON
        const errorData = JSON.parse(errorText);
        return NextResponse.json(
          { error: errorData.detail || 'Password reset failed' },
          { status: response.status }
        );
      } catch (e) {
        // If can't parse as JSON, return text
        return NextResponse.json(
          { error: errorText || 'Password reset failed' },
          { status: response.status }
        );
      }
    }
    
    // Return success response
    return NextResponse.json({ 
      message: 'Password changed successfully',
      status: 'success'
    });
    
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
