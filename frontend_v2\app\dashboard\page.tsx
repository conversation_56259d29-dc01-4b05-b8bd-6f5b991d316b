"use client";

import { redirect } from "next/navigation"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, BarChart3, Compass, ChevronRight, FileText, Clock } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"

export default function DashboardPage() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
      </div>
    )
  }

  if (!session) {
    redirect("/login")
  }

  return (
    <div className="container py-12 px-4 md:px-8">
      <div className="flex flex-col items-center justify-center text-center mb-12">
        <h1 className="text-4xl font-bold text-primary mb-4">Welcome to TalentMetrix</h1>
        <p className="text-muted-foreground text-lg max-w-2xl">
          Your personalized platform for professional discovery and development
        </p>
      </div>

      {/* View Report Button */}
      <div className="flex justify-center mb-12">
        <Link href="/landingpage_v2">
          <Button size="lg" className="bg-gradient-to-r from-blue-600 to-blue-400 hover:from-blue-700 hover:to-blue-500 text-white text-lg py-6 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all">
            <FileText className="mr-2 h-6 w-6" />
            View My Report
          </Button>
        </Link>
      </div>

      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {/* Redesigned Quick Start Card */}
        <Card className="overflow-hidden border-none shadow-lg rounded-xl transition-all hover:shadow-xl">
          <div className="h-2 bg-gradient-to-r from-blue-500 to-indigo-500"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-2xl font-bold flex items-center">
              <Compass className="mr-2 h-6 w-6 text-blue-500" />
              Quick Start
            </CardTitle>
            <CardDescription className="text-base">
              Begin your journey of self-discovery
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <ul className="space-y-4">
              <li>
                <Link href="/1_1_understand_yourself" className="flex items-center p-3 rounded-lg hover:bg-slate-50 transition-colors group">
                  <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-blue-50 text-blue-500 group-hover:bg-blue-100">
                    <BookOpen className="h-5 w-5" />
                  </div>
                  <div className="ml-4 flex-1">
                    <p className="text-sm font-medium">Understand Yourself</p>
                    <p className="text-xs text-muted-foreground">Discover your natural strengths and limitations</p>
                  </div>
                  <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors" />
                </Link>
              </li>
              <li>
                <Link href="/2_1_making_impact" className="flex items-center p-3 rounded-lg hover:bg-slate-50 transition-colors group">
                  <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-blue-50 text-blue-500 group-hover:bg-blue-100">
                    <LineChart className="h-5 w-5" />
                  </div>
                  <div className="ml-4 flex-1">
                    <p className="text-sm font-medium">Analyze Your Impact</p>
                    <p className="text-xs text-muted-foreground">Understand how you make a difference</p>
                  </div>
                  <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors" />
                </Link>
              </li>
              <li>
                <Link href="/3_1_career_options_for_you" className="flex items-center p-3 rounded-lg hover:bg-slate-50 transition-colors group">
                  <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-blue-50 text-blue-500 group-hover:bg-blue-100">
                    <BarChart3 className="h-5 w-5" />
                  </div>
                  <div className="ml-4 flex-1">
                    <p className="text-sm font-medium">Explore Career Options</p>
                    <p className="text-xs text-muted-foreground">Find the right path for your talents</p>
                  </div>
                  <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors" />
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>

        {/* Progress */}
        <Card className="overflow-hidden border-none shadow-lg rounded-xl">
          <div className="h-2 bg-gradient-to-r from-green-500 to-emerald-500"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-2xl font-bold flex items-center">
              <LineChart className="mr-2 h-6 w-6 text-green-500" />
              Your Progress
            </CardTitle>
            <CardDescription className="text-base">
              Track your journey through the assessment
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="space-y-6">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="font-medium">Self-Understanding</span>
                  <span className="font-bold text-green-600">20%</span>
                </div>
                <div className="h-2 bg-slate-100 rounded-full overflow-hidden">
                  <div className="h-2 bg-gradient-to-r from-green-500 to-emerald-400 rounded-full w-[20%]" />
                </div>
              </div>
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="font-medium">Impact Analysis</span>
                  <span className="font-bold text-slate-400">0%</span>
                </div>
                <div className="h-2 bg-slate-100 rounded-full overflow-hidden">
                  <div className="h-2 bg-gradient-to-r from-slate-300 to-slate-200 rounded-full w-0" />
                </div>
              </div>
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="font-medium">Career Guidance</span>
                  <span className="font-bold text-slate-400">0%</span>
                </div>
                <div className="h-2 bg-slate-100 rounded-full overflow-hidden">
                  <div className="h-2 bg-gradient-to-r from-slate-300 to-slate-200 rounded-full w-0" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="overflow-hidden border-none shadow-lg rounded-xl">
          <div className="h-2 bg-gradient-to-r from-purple-500 to-indigo-500"></div>
          <CardHeader className="pb-2">
            <CardTitle className="text-2xl font-bold flex items-center">
              <Clock className="mr-2 h-6 w-6 text-purple-500" />
              Recent Activity
            </CardTitle>
            <CardDescription className="text-base">
              Your latest assessment progress
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="space-y-4">
              <div className="flex items-center gap-4 p-3 rounded-lg bg-slate-50">
                <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-purple-100 text-purple-500">
                  <BookOpen className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium">Started Self-Understanding module</p>
                  <p className="text-xs text-muted-foreground">2 days ago</p>
                </div>
              </div>
              <div className="flex items-center gap-4 p-3 rounded-lg bg-slate-50">
                <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-purple-100 text-purple-500">
                  <FileText className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium">Completed initial assessment</p>
                  <p className="text-xs text-muted-foreground">3 days ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 