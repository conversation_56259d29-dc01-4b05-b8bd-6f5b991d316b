'use client';

import { useState, FormEvent, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { signIn } from 'next-auth/react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, Mail, Lock, ArrowRight } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function LoginPage() {
  console.log("🏗️ LoginPage component rendering/re-rendering");

  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/landingpage_v2';
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Debug loading state changes
  useEffect(() => {
    console.log("⏳ Loading state changed to:", loading);
  }, [loading]);

  console.log("🔧 Component state:", {
    email: email,
    passwordLength: password.length,
    error: error,
    loading: loading,
    callbackUrl: callbackUrl
  });

  useEffect(() => {
    console.log("🔄 useEffect - Component mounted");
    console.log("🌐 Current URL:", window.location.href);
    console.log("📍 Callback URL from params:", callbackUrl);

    return () => {
      console.log("🧹 useEffect cleanup - Component unmounting");
    };
  }, [callbackUrl]);

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault();
    console.log("🚀 LOGIN FORM SUBMITTED");
    console.log("📧 Email:", email);
    console.log("🔒 Password length:", password.length);
    console.log("🔄 Callback URL:", callbackUrl);

    setError(null);
    setLoading(true);
    console.log("⏳ Loading state set to true");

    try {
      console.log("🔑 Calling signIn with credentials...");
      console.log("📦 SignIn payload:", {
        provider: 'credentials',
        email: email,
        passwordLength: password.length,
        redirect: false
      });

      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      console.log("📥 SignIn result received:", result);
      console.log("❓ Result error:", result?.error);
      console.log("✅ Result ok:", result?.ok);
      console.log("🔗 Result url:", result?.url);
      console.log("📊 Result status:", result?.status);

      if (result?.error) {
        console.error("❌ Authentication failed with error:", result.error);
        setError(result.error);
        setLoading(false);
        console.log("⏳ Loading state set to false (error case)");
      } else if (result?.ok) {
        console.log("✅ Authentication successful!");
        console.log("🔄 Redirecting to:", callbackUrl);
        router.push(callbackUrl);
        router.refresh();
        console.log("🔄 Router refresh called");
      } else {
        console.warn("⚠️ Unexpected result state:", result);
        setError('Authentication failed. Please try again.');
        setLoading(false);
        console.log("⏳ Loading state set to false (unexpected case)");
      }
    } catch (err) {
      console.error("💥 CATCH BLOCK - Unexpected error during sign in:", err);
      console.error("💥 Error type:", typeof err);
      console.error("💥 Error message:", err instanceof Error ? err.message : String(err));
      console.error("💥 Error stack:", err instanceof Error ? err.stack : 'No stack trace');
      setError('An unexpected error occurred.');
      setLoading(false);
      console.log("⏳ Loading state set to false (catch case)");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-secondary/5 relative">
      {/* Login Card */}
      <div className="w-full max-w-md p-4 z-10">
        <div className="mb-8 text-center">
          <h2 className="text-2xl font-bold tracking-tight">Welcome back</h2>
          <p className="text-muted-foreground mt-2">Sign in to access your account</p>
        </div>
        
        <Card className="shadow-lg border-muted/30">
          <CardHeader className="space-y-1 pb-2">
            <CardTitle className="text-xl font-semibold">Sign In</CardTitle>
            <CardDescription>
              Enter your credentials to continue
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert variant="destructive" className="animate-in slide-in-from-top-1 duration-300">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium">Email</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                    <Mail className="h-4 w-4" />
                  </div>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => {
                      console.log("📧 Email input changed:", e.target.value);
                      setEmail(e.target.value);
                    }}
                    onFocus={() => console.log("📧 Email input focused")}
                    onBlur={() => console.log("📧 Email input blurred")}
                    placeholder="<EMAIL>"
                    required
                    disabled={loading}
                    className="pl-10 transition-all focus:ring-2 focus:ring-primary/20"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password" className="text-sm font-medium">Password</Label>
                  <Button variant="link" className="px-0 h-auto text-xs" disabled={loading}>
                    Forgot password?
                  </Button>
                </div>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                    <Lock className="h-4 w-4" />
                  </div>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => {
                      console.log("🔒 Password input changed, length:", e.target.value.length);
                      setPassword(e.target.value);
                    }}
                    onFocus={() => console.log("🔒 Password input focused")}
                    onBlur={() => console.log("🔒 Password input blurred")}
                    placeholder="••••••••"
                    required
                    disabled={loading}
                    className="pl-10 transition-all focus:ring-2 focus:ring-primary/20"
                  />
                </div>
              </div>
              <Button
                type="submit"
                className="w-full transition-all hover:shadow-md group"
                disabled={loading}
                onClick={() => {
                  console.log("🖱️ Sign In button clicked");
                  console.log("⏳ Current loading state:", loading);
                  console.log("📧 Current email:", email);
                  console.log("🔒 Current password length:", password.length);
                }}
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Signing in...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    Sign In
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </div>
                )}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex justify-center border-t pt-4 text-xs">
            <p className="text-muted-foreground">
              Don't have an account? <span className="text-primary font-medium">Contact your administrator</span>
            </p>
          </CardFooter>
        </Card>
        
        <div className="mt-8 text-center text-xs text-muted-foreground">
          <p>By signing in, you agree to our Terms of Service and Privacy Policy</p>
        </div>
      </div>
    </div>
  );
} 