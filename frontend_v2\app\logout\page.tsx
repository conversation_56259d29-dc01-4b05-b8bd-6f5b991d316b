"use client";

import { useEffect } from "react";
import { Loader2 } from "lucide-react";

export default function LogoutPage() {
  useEffect(() => {
    // Immediately redirect to external assessment site
    console.log('🚪 Logout page: Redirecting to external assessment site');
    window.location.href = "https://assessments.talentmetrixsp.co.in";
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4" />
        <h1 className="text-xl font-semibold text-gray-800 mb-2">Logging out...</h1>
        <p className="text-gray-600">Redirecting you to the assessment portal...</p>
      </div>
    </div>
  );
}
