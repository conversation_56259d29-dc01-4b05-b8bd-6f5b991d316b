"use client";

import React from 'react';
import { cn } from '@/lib/utils';

export default function Footer() {
  return (
    <div className={cn(
      "w-full relative overflow-hidden text-black",
      "py-4 px-8",
      "text-xs leading-relaxed",
      "rounded-t-[40px]",
      "mt-10"
    )}>
      {/* Gradient background */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          zIndex: 0,
          pointerEvents: 'none',
          background: 'linear-gradient(135deg, rgba(75,159,239,0.9) 0%, rgba(75,159,239,0.3) 100%)',
        }}
      />
      <div className="max-w-[1200px] mx-auto flex flex-col md:flex-row items-start md:items-center gap-2 md:gap-0 relative z-10">
        <div className="flex-1">
          Disclaimer – The report's findings are based on the specific test administered and the responses given. The report is designed to help people identify their natural abilities, strengths, limitations and career interests, and should be used only as a tool for self-discovery
        </div>
        <div className="whitespace-nowrap md:ml-4">
          |   Copyright – TalentMetrix 2025
        </div>
      </div>
    </div>
  );
}