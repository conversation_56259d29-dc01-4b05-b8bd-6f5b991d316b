"use client";
import React from "react";
import { cn } from "@/lib/utils";

interface GradientCardProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  infoIcon?: boolean;
  infoIconContent?: React.ReactNode;
  color?: "blue" | "orange";
}

/**
 * GradientCard - Standardized card with gradient background and info icon (optional).
 * Usage: Wrap dynamic content inside <GradientCard>...</GradientCard>
 */
const colorMap = {
  blue: {
    gradient: "linear-gradient(135deg, rgba(55,147,247,0.5) 0%, rgba(55,147,247,0.05) 30%, transparent 100%)",
    iconBg: "#3793F7",
  },
  orange: {
    gradient: "linear-gradient(135deg, rgba(255,168,0,0.5) 0%, rgba(255,168,0,0.05) 30%, transparent 100%)",
    iconBg: "#FFA800",
  },
};

const GradientCard: React.FC<GradientCardProps> = ({
  children,
  className = "",
  style = {},
  infoIcon = false,
  infoIconContent = "i",
  color = "blue",
}) => {
  const theme = colorMap[color] || colorMap.blue;
  
  return (
    <div
      className={cn(
        "relative rounded-2xl p-6 mb-6 shadow-md bg-white dark:bg-gray-800 overflow-hidden max-w-[980px] mx-auto text-gray-900 dark:text-gray-100",
        className
      )}
      style={style}
    >
      {/* Gradient Background */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          zIndex: 0,
          pointerEvents: 'none',
          borderRadius: '1rem',
          background: theme.gradient,
        }}
      />
      {infoIcon && (
        <div
          className="absolute top-5 right-5 w-6 h-6 text-white rounded-full flex items-center justify-center italic font-bold z-10"
          style={{ background: theme.iconBg }}
        >
          {infoIconContent}
        </div>
      )}
      <div className="relative z-[1]">{children}</div>
    </div>
  );
};

export default GradientCard;