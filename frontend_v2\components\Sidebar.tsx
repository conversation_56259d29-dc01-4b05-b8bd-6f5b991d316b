"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSidebar } from '@/context/SidebarContext';
import {
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  User,
  Book,
  BarChart2 as <PERSON><PERSON><PERSON>,
  Briefcase,
  MessageCircle,
  Heart,
  Award,
  Star,
  GraduationCap,
  ClipboardList,
  BookOpen,
  Layers,
  CheckSquare,
  Map,
  FileText,
  Lightbulb,
  Eye,
  Menu
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

const sections = [
  {
    title: "Self-Understanding",
    icon: User,
    items: [
      { name: "Understand Yourself", path: "/1_1_understand_yourself", icon: User },
      { name: "Strengths & Limitations", path: "/1_2_strengths_limitations", icon: Star },
      { name: "Communication Styles", path: "/1_3_communication_styles", icon: MessageCircle },
      { name: "Emotional Patterns", path: "/1_4_emotional_patterns", icon: Heart },
      { name: "Others' Perception", path: "/1_5_how_others_percive_you", icon: Eye }
    ]
  },
  {
    title: "Impact Analysis",
    icon: BarChart,
    items: [
      { name: "Making Impact", path: "/2_1_making_impact", icon: Award },
      { name: "Academic Impact", path: "/2_2_academic_impact", icon: Book },
      { name: "Professional Impact", path: "/2_3_professional_impact", icon: Briefcase },
      { name: "Key Competencies", path: "/2_4_key_competencies", icon: CheckSquare },
      { name: "Work Environment", path: "/2_5_work_environment", icon: Layers }
    ]
  },
  {
    title: "Career Guidance",
    icon: Briefcase,
    items: [
      { name: "Career Options", path: "/3_1_career_options_for_you", icon: Map },
      { name: "Recommended Careers", path: "/3_2_recommended_careers", icon: Award },
      { name: "Specializations", path: "/3_3_recommended_specializations", icon: GraduationCap },
      { name: "Build Your Resume", path: "/3_4_build_your_resume", icon: FileText },
      { name: "Interview Preparation", path: "/3_5_prepare_for_interviews", icon: ClipboardList }
    ]
  },
  {
    title: "Action Plan",
    icon: CheckSquare,
    items: [
      { name: "Embark on Success", path: "/4_1_embark_on_success_path", icon: Award },
      { name: "SWOD Analysis", path: "/4_2_swod_analysis", icon: Lightbulb },
      { name: "Top Activities", path: "/4_3_top_activities", icon: Lightbulb },
      { name: "Personal Development Plan", path: "/4_4_personal_development_plan", icon: Lightbulb },
      { name: "Conclusion", path: "/4_5_conclusion", icon: BookOpen }
    ]
  }
];

export default function Sidebar() {
  const { isExpanded, toggleSidebar } = useSidebar();
  const pathname = usePathname();
  const [openSection, setOpenSection] = useState<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  // Check if we're on the dashboard page - only hide sidebar on the dashboard
  const isDashboardPage = pathname === "/dashboard" || pathname.startsWith("/dashboard/");
  
  // Don't render the sidebar on the dashboard
  if (isDashboardPage) {
    return null;
  }

  // Check if we're on mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Auto-open current section
  useEffect(() => {
    const sectionIndex = sections.findIndex((section, idx) => 
      pathname.startsWith(`/${idx + 1}_`)
    );
    if (sectionIndex !== -1) {
      setOpenSection(sectionIndex);
    }
  }, [pathname]);

  const SidebarContent = () => (
    <div className={cn(
      "sticky top-16",
      "h-[calc(100vh-4rem)]",
      "bg-sidebar backdrop-blur-none",
      isExpanded ? "w-64" : "w-[70px]",
      "transition-all duration-300 ease-in-out",
      "flex flex-col"
    )}>
      {/* Toggle Button */}
      <div className="sticky top-0 flex h-12 items-center justify-end border-b bg-sidebar px-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleSidebar}
          className="h-8 w-8 rounded-full hover:bg-muted"
        >
          {isExpanded ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
        </Button>
      </div>

      {/* Navigation - Make it scrollable */}
      <div className="flex-1 overflow-y-auto py-2">
        {sections.map((section, index) => {
          const SectionIcon = section.icon;
          const isSectionOpen = openSection === index;
          const isActive = pathname.startsWith(`/${index + 1}_`);

          return (
            <div key={index} className="px-2 mb-2">
              <Collapsible
                open={isExpanded && isSectionOpen}
                onOpenChange={() => isExpanded && setOpenSection(isSectionOpen ? null : index)}
              >
                <CollapsibleTrigger asChild>
                  <Button
                    variant={isActive ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start gap-2 rounded-lg transition-colors",
                      !isExpanded && "justify-center px-2",
                      isActive && "bg-primary/10"
                    )}
                  >
                    <SectionIcon className={cn(
                      "h-4 w-4 shrink-0",
                      isActive && "text-primary"
                    )} />
                    {isExpanded && (
                      <>
                        <span className="flex-1 truncate text-sm">{section.title}</span>
                        <ChevronDown
                          className={cn(
                            "h-4 w-4 shrink-0 transition-transform",
                            isSectionOpen && "rotate-180"
                          )}
                        />
                      </>
                    )}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-1">
                  {section.items.map((item, itemIndex) => {
                    const ItemIcon = item.icon;
                    const isItemActive = pathname === item.path;

                    return (
                      <Link
                        key={itemIndex}
                        href={item.path}
                        className={cn(
                          "flex items-center gap-2 rounded-lg px-4 py-2 text-sm transition-colors",
                          "ml-4",
                          "hover:bg-muted/50",
                          isItemActive
                            ? "bg-primary/10 text-primary font-medium"
                            : "text-muted-foreground hover:text-foreground"
                        )}
                      >
                        <ItemIcon className="h-4 w-4 shrink-0" />
                        <span className="truncate">{item.name}</span>
                      </Link>
                    );
                  })}
                </CollapsibleContent>
              </Collapsible>
            </div>
          );
        })}
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 lg:hidden"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle Menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0">
          <SidebarContent />
        </SheetContent>

        {/* Desktop sidebar */}
        <div className="hidden lg:block" data-sidebar-expanded={isExpanded}>
          <SidebarContent />
        </div>
      </Sheet>
    );
  }

  return <SidebarContent />;
} 