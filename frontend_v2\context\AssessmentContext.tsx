"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';

// Define assessment data type
type AssessmentData = {
  email: string;
  individual_name: string;
  assessment: {
    section_i: any;
    section_ii: any;
  };
  timestamp: string;
  cached: boolean;
} | null;

// Create context with default values
type AssessmentContextType = {
  assessmentData: AssessmentData;
  loading: boolean;
  error: string | null;
  fetchAssessment: (email: string) => Promise<void>;
  isInitialized: boolean;
};

const AssessmentContext = createContext<AssessmentContextType>({
  assessmentData: null,
  loading: false,
  error: null,
  fetchAssessment: async () => {},
  isInitialized: false
});

// Create hook for using the context
export const useAssessment = () => useContext(AssessmentContext);

// Create provider component
export const AssessmentProvider = ({ children }: { children: ReactNode }) => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [assessmentData, setAssessmentData] = useState<AssessmentData>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [fetchedForEmail, setFetchedForEmail] = useState<string | null>(null);
  // Add a flag to track if we're already redirecting to prevent infinite loops
  const [isRedirecting, setIsRedirecting] = useState(false);
  // Add a counter to track fetch attempts and prevent infinite loops
  const [fetchAttempts, setFetchAttempts] = useState(0);
  // Add a flag to disable fetching after authentication errors
  const [fetchingDisabled, setFetchingDisabled] = useState(false);
  
  // Function to handle authentication errors
  const handleAuthError = async () => {
    // Only handle auth error once
    if (isRedirecting) return;
    
    console.log('Authentication error detected. Logging out and redirecting to login page...');
    setIsRedirecting(true);
    
    try {
      // Sign out the user
      await signOut({ redirect: false });

      // Clear any assessment data
      setAssessmentData(null);
      setFetchedForEmail(null);

      // Redirect to external assessment site
      window.location.href = 'https://assessments.talentmetrixsp.co.in';
    } catch (err) {
      console.error('Error during logout:', err);
      // If signOut fails, redirect directly to external site
      window.location.href = 'https://assessments.talentmetrixsp.co.in';
    }
  };

  const fetchAssessment = async (email: string) => {
    // Skip fetching if disabled due to previous auth errors
    if (fetchingDisabled) {
      console.log('Fetching disabled due to previous auth errors');
      return;
    }

    // Increment fetch attempts
    const currentAttempts = fetchAttempts + 1;
    setFetchAttempts(currentAttempts);
    
    // If we've tried too many times, disable fetching
    if (currentAttempts > 3) {
      console.log('Too many fetch attempts, disabling further fetches');
      setFetchingDisabled(true);
      setError('Authentication error. Please try logging in again.');
      return;
    }

    if (!email) {
      setError("No email provided to fetch assessment.");
      setIsInitialized(true);
      setLoading(false);
      return;
    }
    
    // Prevent multiple fetch attempts if we're already redirecting
    if (isRedirecting) {
      console.log('Already redirecting to login, skipping fetch');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      console.log(`Fetching assessment data for ${email}... (Attempt ${currentAttempts}/3)`);
      
      const response = await fetch(`/api/assessment?email=${encodeURIComponent(email)}`);
      
      // Handle 401 Unauthorized (expired token)
      if (response.status === 401) {
        console.log('Authentication error (401). Disabling further fetches.');
        setFetchingDisabled(true);
        // Call the auth error handler to logout and redirect
        handleAuthError();
        return;
      }
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        const errorMessage = errorData?.error || `API error: ${response.status}`;
        throw new Error(errorMessage);
      }
      
      // Reset fetch attempts on success
      setFetchAttempts(0);
      
      const data = await response.json();
      console.log('Assessment data fetched successfully');
      setAssessmentData(data);
      setFetchedForEmail(email);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching assessment data:', errorMessage);
      
      // If we get a network error, disable further fetches
      if (err instanceof Error && err.message.includes('NetworkError')) {
        console.log('Network error. Disabling further fetches.');
        setFetchingDisabled(true);
      }
    } finally {
      setLoading(false);
      setIsInitialized(true);
    }
  };

  useEffect(() => {
    console.log("AssessmentContext useEffect", { 
      status, 
      session: session ? { ...session, user: { ...session?.user, access_token: '[REDACTED]' } } : null, 
      fetchedForEmail, 
      isRedirecting,
      fetchingDisabled,
      fetchAttempts
    });
    
    // Skip if fetching is disabled due to errors
    if (fetchingDisabled) {
      console.log('Skipping effect because fetching is disabled');
      return;
    }
    
    // If we're already redirecting, don't do anything
    if (isRedirecting) {
      console.log('Skipping effect because we are redirecting');
      return;
    }
    
    // Handle authenticated state
    if (status === "authenticated" && session?.user?.email) {
      // Only fetch if we haven't fetched for this email yet and we're not already loading
      // Also check if we haven't exceeded the maximum fetch attempts
      if (session.user.email !== fetchedForEmail && !loading && fetchAttempts < 3) {
        console.log('Session authenticated, fetching assessment for:', session.user.email);
        fetchAssessment(session.user.email);
      }
    } 
    // Handle unauthenticated state
    else if (status === "unauthenticated") {
      setAssessmentData(null);
      setFetchedForEmail(null);
      setIsInitialized(true);
      console.log("User not authenticated, clearing assessment data.");
    } 
    // Handle loading state
    else if (status === "loading" && !isInitialized) {
      setLoading(true);
    }

    // Clean up loading state if needed
    if (status !== "loading" && loading && !isInitialized && !session?.user?.email) {
      setLoading(false);
    }

  }, [session, status, fetchedForEmail, isRedirecting, loading, fetchingDisabled, fetchAttempts]);

  return (
    <AssessmentContext.Provider value={{ 
      assessmentData, 
      loading, 
      error, 
      fetchAssessment,
      isInitialized 
    }}>
      {children}
    </AssessmentContext.Provider>
  );
}; 