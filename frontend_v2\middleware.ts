import { getToken } from 'next-auth/jwt';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  console.log('🔍 Middleware running for:', request.nextUrl.pathname);

  const token = await getToken({ req: request });
  const isAuthenticated = !!token;

  // Public paths that don't require authentication
  const publicPaths = ['/login', '/api/auth', '/api/hello'];
  const isPublicPath = publicPaths.some(path => request.nextUrl.pathname.startsWith(path));

  console.log('🔐 Auth check:', {
    pathname: request.nextUrl.pathname,
    isAuthenticated,
    isPublicPath,
    publicPaths
  });

  // Root path should redirect to external site when not authenticated
  const isRootPath = request.nextUrl.pathname === '/';

  // If the user is trying to access a protected route without authentication
  if (!isAuthenticated && !isPublicPath) {
    if (isRootPath) {
      console.log('❌ Redirecting root to external site:', request.nextUrl.pathname);
      return NextResponse.redirect(new URL('https://assessments.talentmetrixsp.co.in'));
    } else {
      console.log('❌ Redirecting to external site:', request.nextUrl.pathname);
      return NextResponse.redirect(new URL('https://assessments.talentmetrixsp.co.in'));
    }
  }

  // If the user is authenticated and trying to access login page
  if (isAuthenticated && request.nextUrl.pathname === '/login') {
    console.log('✅ Redirecting authenticated user to landing page');
    return NextResponse.redirect(new URL('/landingpage_v2', request.url));
  }

  console.log('✅ Allowing request to proceed:', request.nextUrl.pathname);
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api/auth|api/hello|_next/static|_next/image|favicon.ico).*)'],
};