import { getToken } from 'next-auth/jwt';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  console.log('🔍 Middleware running for:', request.nextUrl.pathname);

  const token = await getToken({ req: request });
  const isAuthenticated = !!token;

  // Public paths that don't require authentication
  const publicPaths = ['/login', '/api/auth', '/api/hello'];
  const isPublicPath = publicPaths.some(path => request.nextUrl.pathname.startsWith(path));

  console.log('🔐 Auth check:', {
    pathname: request.nextUrl.pathname,
    isAuthenticated,
    isPublicPath,
    publicPaths
  });

  // Root path is a special case, as the client-side redirection will handle it
  const isRootPath = request.nextUrl.pathname === '/';

  // If the user is trying to access a protected route (not public and not root) without authentication
  if (!isAuthenticated && !isPublicPath && !isRootPath) {
    console.log('❌ Redirecting to login:', request.nextUrl.pathname);
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // If the user is authenticated and trying to access login page
  if (isAuthenticated && request.nextUrl.pathname === '/login') {
    console.log('✅ Redirecting authenticated user to landing page');
    return NextResponse.redirect(new URL('/landingpage_v2', request.url));
  }

  console.log('✅ Allowing request to proceed:', request.nextUrl.pathname);
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api/auth|api/hello|_next/static|_next/image|favicon.ico).*)'],
};