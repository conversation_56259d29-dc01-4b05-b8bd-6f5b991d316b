import type { NextConfig } from "next";
import path from 'path';

const nextConfig: NextConfig = {
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname, './'),
    };
    return config;
  },
  // Add TypeScript configuration to ignore build errors
  typescript: {
    ignoreBuildErrors: true,
  },
  // Add ESLint configuration to ignore errors during build
  eslint: {
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;



