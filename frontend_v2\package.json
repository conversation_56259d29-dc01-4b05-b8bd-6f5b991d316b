{"name": "frontend_v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start -p 3008", "lint": "next lint"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-react": "^9.5.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-tooltip": "^1.2.6", "@react-pdf/renderer": "^4.3.0", "@tinymce/tinymce-react": "^6.1.0", "@types/react-dropzone": "^4.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "lucide-react": "^0.511.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "puppeteer": "^24.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-day-picker": "^8.10.1", "react-hook-form": "^7.56.4", "react-pdf": "^9.2.1", "react-resizable-panels": "^3.0.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "zod": "^3.24.4"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}